(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d20ec91"],{b19e:function(t,a,e){"use strict";e.r(a);e("b0c0");var r=function(){var t=this,a=t._self._c;return a("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(a){t.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&t._k(a.keyCode,"enter",13,a.key,"Enter")?null:t.dataFormSubmit()}}},[a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"名称"},model:{value:t.dataForm.name,callback:function(a){t.$set(t.dataForm,"name",a)},expression:"dataForm.name"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){t.visible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},o=[],i={data:function(){return{visible:!1,dataForm:{repeatToken:"",id:0,appid:"",name:""},dataRule:{appid:[{required:!0,message:"appid不能为空",trigger:"blur"}],name:[{required:!0,message:"名称不能为空",trigger:"blur"}]}}},methods:{init:function(t){var a=this;this.getToken(),this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/price/pricetransformunit/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.dataForm.appid=e.priceTransformUnit.appid,a.dataForm.name=e.priceTransformUnit.name)}))}))},getToken:function(){var t=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(a){var e=a.data;e&&200===e.code&&(t.dataForm.repeatToken=e.result)}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(a){a&&t.$http({url:t.$http.adornUrl("/price/pricetransformunit/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({repeatToken:t.dataForm.repeatToken,id:t.dataForm.id||void 0,appid:t.$cookie.get("appid"),name:t.dataForm.name})}).then((function(a){var e=a.data;e&&200===e.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList",e.result)}}):(t.$message.error(e.msg),"不能重复提交"!=e.msg&&t.getToken())}))}))}}},n=i,s=e("2877"),d=Object(s["a"])(n,r,o,!1,null,null,null);a["default"]=d.exports}}]);
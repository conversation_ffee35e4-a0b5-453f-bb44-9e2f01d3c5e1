(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-32e58f13"],{"25af":function(e,t,i){"use strict";i("5bb2")},"386d":function(e,t,i){"use strict";var a=i("cb7c"),n=i("83a1"),s=i("5f1b");i("214f")("search",1,(function(e,t,i,o){return[function(i){var a=e(this),n=void 0==i?void 0:i[t];return void 0!==n?n.call(i,a):new RegExp(i)[t](String(a))},function(e){var t=o(i,e,this);if(t.done)return t.value;var l=a(e),c=String(this),r=l.lastIndex;n(r,0)||(l.lastIndex=0);var u=s(l,c);return n(l.lastIndex,r)||(l.lastIndex=r),null===u?-1:u.index}]}))},"5bb2":function(e,t,i){},"7e76":function(e,t,i){"use strict";i.r(t);i("7f7f");var a,n=function(){var e=this,t=e._self._c;return t("div",{class:e.isMobilePhone?"page":"page pc-container",style:e.isMobilePhone&&e.activityInfo.applyBackground?{backgroundImage:"url("+e.activityInfo.applyBackground+")",backgroundSize:"100% 100%"}:""},[e.isMobilePhone?e._e():t("pcheader"),e.activityInfo.showApplyNumber?t("div",{staticStyle:{"background-color":"rgba(32, 33, 36, 0.5)","border-radius":"30px",padding:"0.2rem 0.5rem","font-size":".15rem",display:"flex",position:"absolute","z-index":"999","align-items":"center","justify-content":"center",right:"10px",top:"10px"}},[t("span",{staticStyle:{color:"white","margin-left":"5px","font-size":"14px"}},[e._v("报名人数："+e._s(e.applyNumber)+"人")])]):e._e(),t("div",[e._m(0),t("van-radio-group",{model:{value:e.channelId,callback:function(t){e.channelId=t},expression:"channelId"}},[t("van-cell-group",{attrs:{inset:""}},e._l(e.channelList,(function(i,a){return t("van-cell",{key:i.id,attrs:{title:i.name,clickable:""},on:{click:function(t){return e.chooseChannel(i.id,a)}},scopedSlots:e._u([i.children?null:{key:"right-icon",fn:function(){return[t("van-radio",{attrs:{name:i.id}})]},proxy:!0}],null,!0)},[t("div",{attrs:{slot:"label"},slot:"label"},[i.children?t("div",[t("van-radio-group",{model:{value:e.channelId,callback:function(t){e.channelId=t},expression:"channelId"}},[t("van-cell-group",e._l(i.children,(function(i,n){return t("van-cell",{key:i.id,attrs:{title:i.name,clickable:""},on:{click:function(t){return t.stopPropagation(),e.chooseChannelChild(i.id,a,n)}},scopedSlots:e._u([{key:"right-icon",fn:function(){return[t("van-radio",{attrs:{name:i.id}})]},proxy:!0}],null,!0)},[t("div",{attrs:{slot:"label"},slot:"label"},[i.description?t("div",[e._v(e._s(i.description))]):e._e(),i.price>0?t("div",[e._v("￥"+e._s(i.price)+"元")]):e._e()])])})),1)],1)],1):t("div",[i.description?t("div",[e._v(e._s(i.description))]):e._e(),i.price>0?t("div",[e._v("￥"+e._s(i.price)+"元")]):e._e()])])])})),1)],1)],1),e._m(1),t("van-cell-group",{attrs:{inset:""}},[t("van-field",{attrs:{name:"姓名",label:"姓名",required:"",placeholder:"姓名",rules:[{required:!0,message:"请填写姓名"}]},model:{value:e.userInfo.contact,callback:function(t){e.$set(e.userInfo,"contact",t)},expression:"userInfo.contact"}}),t("van-field",{attrs:{disabled:!e.isMobilePhone,name:"手机号",label:"手机号",required:"",placeholder:"手机号",rules:[{required:!0,message:"请填写手机号"}]},model:{value:e.userInfo.mobile,callback:function(t){e.$set(e.userInfo,"mobile",t)},expression:"userInfo.mobile"}}),t("van-field",{directives:[{name:"show",rawName:"v-show",value:e.isSms,expression:"isSms"}],attrs:{center:"",clearable:"",maxlength:"6",label:"短信验证码",required:"",placeholder:"请输入短信验证码"},scopedSlots:e._u([{key:"button",fn:function(){return[t("van-button",{attrs:{size:"small",type:"primary",disabled:e.waiting},on:{click:function(t){return e.doSendSmsCode()}}},[e.waiting?t("span",[e._v(e._s(e.waitingTime)+"秒后重发")]):t("span",{staticStyle:{"font-size":"13px"}},[e._v("获取验证码")])])]},proxy:!0}]),model:{value:e.userInfo.code,callback:function(t){e.$set(e.userInfo,"code",t)},expression:"userInfo.code"}})],1),t("van-form",{on:{submit:e.onSubmit}},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.applyContentVisible,expression:"applyContentVisible"}]},[t("div",{staticClass:"nav-title",staticStyle:{"margin-top":"8px"}},[t("div",{staticClass:"color"}),t("div",{staticClass:"text"},[e._v("报名信息填写")])]),t("van-cell-group",{attrs:{inset:""}},[e._l(e.applyActivityConfigList,(function(i){return t("div",{key:i.id,staticClass:"dyn-item"},[0==i.type?t("div",[t("van-field",{attrs:{name:i.finalName,label:i.finalName,required:1==i.required,placeholder:i.placeholder||i.finalName,rules:[{required:1==i.required,message:"请填写"+i.finalName}]},model:{value:e.userInfo[i.applyConfigFieldName],callback:function(t){e.$set(e.userInfo,i.applyConfigFieldName,t)},expression:"userInfo[item.applyConfigFieldName]"}})],1):1==i.type?t("div",[t("van-cell",{attrs:{required:1==i.required,title:i.finalName,"is-link":""},on:{click:function(t){return e.showRadio(i)}},model:{value:e.userInfo[i.applyConfigFieldName],callback:function(t){e.$set(e.userInfo,i.applyConfigFieldName,t)},expression:"userInfo[item.applyConfigFieldName]"}})],1):2==i.type?t("div",[t("van-cell",{attrs:{required:1==i.required,title:i.finalName,"is-link":""},nativeOn:{click:function(t){return e.showCheckBox(i)}},model:{value:e.userInfo[i.applyConfigFieldName],callback:function(t){e.$set(e.userInfo,i.applyConfigFieldName,t)},expression:"userInfo[item.applyConfigFieldName]"}})],1):5==i.type?t("div",[t("van-cell",{attrs:{required:1==i.required,title:i.finalName,"is-link":""},nativeOn:{click:function(t){return e.showDate(i)}},model:{value:e.userInfo[i.applyConfigFieldName],callback:function(t){e.$set(e.userInfo,i.applyConfigFieldName,t)},expression:"userInfo[item.applyConfigFieldName]"}})],1):4==i.type?t("div",["area"==i.applyConfigFieldName?t("van-cell",{attrs:{required:1==i.required,title:i.finalName,"is-link":""},on:{click:function(t){return e.showArea(i)}},model:{value:e.userInfo.areaName,callback:function(t){e.$set(e.userInfo,"areaName",t)},expression:"userInfo.areaName"}}):e._e(),"isHotel"==i.applyConfigFieldName?t("van-cell",{attrs:{required:1==i.required,title:i.finalName,"is-link":""},on:{click:function(t){return e.showYesOrNo(i)}},model:{value:e.userInfo[i.applyConfigFieldName],callback:function(t){e.$set(e.userInfo,i.applyConfigFieldName,t)},expression:"userInfo[item.applyConfigFieldName]"}}):e._e()],1):e._e()])})),e._l(e.extraResult,(function(i,a){return t("div",{key:a,staticClass:"dyn-item"},[0==i.type?t("div",[t("van-field",{attrs:{name:i.finalName,label:i.finalName,required:1==i.required,placeholder:i.placeholder||i.finalName,rules:[{required:1==i.required,message:"请填写"+i.finalName}]},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"extraItem.value"}})],1):1==i.type?t("div",[t("van-cell",{attrs:{required:1==i.required,title:i.finalName,"is-link":""},on:{click:function(t){return e.showExtraRadio(i)}},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"extraItem.value"}})],1):2==i.type?t("div",[t("van-cell",{attrs:{required:1==i.required,title:i.finalName,"is-link":""},nativeOn:{click:function(t){return e.showExtraCheckBox(i)}},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"extraItem.value"}})],1):5==i.type?t("div",[t("van-cell",{attrs:{required:1==i.required,title:i.finalName,"is-link":""},nativeOn:{click:function(t){return e.showExtraDate(i)}},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"extraItem.value"}})],1):e._e()])})),e.indexChannel.isNeedInvite>0?t("div",[t("van-field",{attrs:{name:"inviteCode",label:"邀请码",required:!0,placeholder:"请输入邀请码",rules:[{required:!0,message:"请填写邀请码"}]},model:{value:e.userInfo.inviteCode,callback:function(t){e.$set(e.userInfo,"inviteCode",t)},expression:"userInfo.inviteCode"}})],1):e._e(),e.indexChannel.isVerify?t("div",[t("van-cell",{attrs:{title:e.indexChannel.verifyName?e.indexChannel.verifyName:"审核材料"}},[t("van-uploader",{attrs:{"after-read":e.afterRead,name:"credit","before-read":e.beforeRead,accept:"image/*"}},[e.userInfo.credit?t("van-image",{attrs:{height:"50px",src:e.userInfo.credit,fit:"contain"}}):t("van-icon",{staticStyle:{"margin-left":"5px"},attrs:{slot:"default",name:"add-o",size:"50px"},slot:"default"})],1)],1)],1):e._e()],2)],1),e.indexChannel.isInvoice?t("div",[t("div",{staticClass:"nav-title",staticStyle:{"margin-top":"8px"}},[t("div",{staticClass:"color"}),t("div",{staticClass:"text"},[e._v("发票信息填写")])]),t("van-cell-group",{attrs:{inset:""}},[t("van-field",{attrs:{required:"",label:"发票抬头",placeholder:"请输入发票抬头",clearable:"",rules:[{required:!0,message:"请填写发票抬头"}]},on:{clear:e.cancel,input:e.autoCompleteList},model:{value:e.userInfo.invoiceName,callback:function(t){e.$set(e.userInfo,"invoiceName",t)},expression:"userInfo.invoiceName"}}),t("van-cell",{directives:[{name:"show",rawName:"v-show",value:e.autoCompleteListView,expression:"autoCompleteListView"}],staticStyle:{height:"62px"},attrs:{title:""}},[e._l(e.invoicelist,(function(i){return t("div",{key:i.id,attrs:{title:i.invoiceName,clearable:""},on:{click:function(t){return e.invoiceChoose(i)}}},[t("div",{staticStyle:{flex:"3"}}),t("div",{staticStyle:{flex:"6",height:"23px",overflow:"hidden","line-height":"22px"}},[e._v(e._s(i.invoiceName))]),t("div",{staticStyle:{"font-size":"12px",color:"#949494","line-height":"17px"}},[e._v(e._s(i.invoiceCode))])])}))],2),t("van-field",{attrs:{label:"纳税人识别号",clearable:"",required:"",placeholder:"请输入纳税人识别号",rules:[{required:!0,message:"请输入纳税人识别号"}]},model:{value:e.userInfo.invoiceCode,callback:function(t){e.$set(e.userInfo,"invoiceCode",t)},expression:"userInfo.invoiceCode"}}),t("van-field",{attrs:{label:"注册地址(专票)",clearable:"",placeholder:"请输入注册地址(专票)"},model:{value:e.userInfo.invoiceAddress,callback:function(t){e.$set(e.userInfo,"invoiceAddress",t)},expression:"userInfo.invoiceAddress"}}),t("van-field",{attrs:{label:"注册电话(专票)",clearable:"",placeholder:"请输入注册电话(专票)"},model:{value:e.userInfo.invoiceMobile,callback:function(t){e.$set(e.userInfo,"invoiceMobile",t)},expression:"userInfo.invoiceMobile"}}),t("van-field",{attrs:{label:"开户银行(专票)",clearable:"",placeholder:"请输入开户银行(专票)"},model:{value:e.userInfo.invoiceBank,callback:function(t){e.$set(e.userInfo,"invoiceBank",t)},expression:"userInfo.invoiceBank"}}),t("van-field",{attrs:{label:"银行账户(专票)",clearable:"",placeholder:"请输入银行账户(专票)"},model:{value:e.userInfo.invoiceAccount,callback:function(t){e.$set(e.userInfo,"invoiceAccount",t)},expression:"userInfo.invoiceAccount"}}),t("van-field",{attrs:{label:"邮箱",clearable:"",placeholder:"请输入邮箱"},model:{value:e.userInfo.email,callback:function(t){e.$set(e.userInfo,"email",t)},expression:"userInfo.email"}})],1)],1):e._e(),t("div",{staticStyle:{margin:"16px"}},[t("van-button",{attrs:{disabled:e.submitDisabled,round:"",block:"",type:"info","native-type":"submit",loading:e.loading,"loading-text":"提交中"}},[e._v("提交")])],1)]),t("van-popup",{style:{height:"45%"},attrs:{closeable:"",position:"bottom"},model:{value:e.radioVisible,callback:function(t){e.radioVisible=t},expression:"radioVisible"}},[t("div",{staticClass:"popup-title"},[e._v(e._s(e.chooseResult.finalName))]),t("div",{staticStyle:{"padding-top":"5px"}},[t("van-radio-group",{model:{value:e.userInfo[e.chooseResult.applyConfigFieldName],callback:function(t){e.$set(e.userInfo,e.chooseResult.applyConfigFieldName,t)},expression:"userInfo[chooseResult.applyConfigFieldName]"}},[t("van-cell-group",e._l(e.chooseResult.selectData,(function(i){return t("van-cell",{key:i,attrs:{title:i,clickable:""},on:{click:function(t){return e.choose(i)}},scopedSlots:e._u([{key:"right-icon",fn:function(){return[t("van-radio",{attrs:{name:i}})]},proxy:!0}],null,!0)})})),1)],1)],1)]),t("van-popup",{style:{height:"45%"},attrs:{closeable:"","close-icon":"http://mpjoy.oss-cn-beijing.aliyuncs.com/20211108/186452e1a5de47759e81e0c439343da3.png",position:"bottom"},on:{"click-close-icon":e.chooseConfirm},model:{value:e.checkBoxVisible,callback:function(t){e.checkBoxVisible=t},expression:"checkBoxVisible"}},[t("div",{staticClass:"popup-title"},[e._v(e._s(e.chooseResult.finalName))]),t("div",{staticStyle:{"padding-top":"5px"}},[t("van-checkbox-group",{attrs:{change:"onChange"},model:{value:e.checkBoxResult,callback:function(t){e.checkBoxResult=t},expression:"checkBoxResult"}},[t("van-cell-group",e._l(e.chooseResult.selectData,(function(i,a){return t("van-cell",{key:i,attrs:{title:i,clickable:"","data-index":a},on:{click:e.toggle},scopedSlots:e._u([{key:"right-icon",fn:function(){return[t("van-checkbox",{ref:"checkboxes",refInFor:!0,attrs:{name:i}})]},proxy:!0}],null,!0)})})),1)],1)],1)]),t("van-calendar",{attrs:{"min-date":e.minDate,"max-date":e.maxDate},on:{confirm:e.dateSelect},model:{value:e.dateVisible,callback:function(t){e.dateVisible=t},expression:"dateVisible"}}),t("van-calendar",{attrs:{"min-date":e.minDate,"max-date":e.maxDate},on:{confirm:e.dateExtraSelect},model:{value:e.extraDateVisible,callback:function(t){e.extraDateVisible=t},expression:"extraDateVisible"}}),t("van-popup",{style:{height:"45%"},attrs:{closeable:"",position:"bottom"},model:{value:e.extraRadioVisible,callback:function(t){e.extraRadioVisible=t},expression:"extraRadioVisible"}},[t("div",{staticClass:"popup-title"},[e._v(e._s(e.extraChooseResult.finalName))]),t("div",{staticStyle:{"padding-top":"5px"}},[t("van-radio-group",{model:{value:e.extraChooseResult.value,callback:function(t){e.$set(e.extraChooseResult,"value",t)},expression:"extraChooseResult.value"}},[t("van-cell-group",e._l(e.extraChooseResult.selectData,(function(i){return t("van-cell",{key:i,attrs:{title:i,clickable:""},on:{click:function(t){return e.chooseExtra(i)}},scopedSlots:e._u([{key:"right-icon",fn:function(){return[t("van-radio",{attrs:{name:i}})]},proxy:!0}],null,!0)})})),1)],1)],1)]),t("van-popup",{style:{height:"45%"},attrs:{closeable:"","close-icon":"http://mpjoy.oss-cn-beijing.aliyuncs.com/20211108/186452e1a5de47759e81e0c439343da3.png",position:"bottom"},on:{"click-close-icon":e.chooseExtraConfirm},model:{value:e.extraCheckBoxVisible,callback:function(t){e.extraCheckBoxVisible=t},expression:"extraCheckBoxVisible"}},[t("div",{staticClass:"popup-title"},[e._v(e._s(e.extraChooseResult.finalName))]),t("div",{staticStyle:{"padding-top":"5px"}},[t("van-checkbox-group",{attrs:{change:"onChangeExtra"},model:{value:e.extraCheckBoxResult,callback:function(t){e.extraCheckBoxResult=t},expression:"extraCheckBoxResult"}},[t("van-cell-group",e._l(e.extraChooseResult.selectData,(function(i,a){return t("van-cell",{key:i,attrs:{title:i,clickable:"","data-index":a},on:{click:e.toggleExtra},scopedSlots:e._u([{key:"right-icon",fn:function(){return[t("van-checkbox",{ref:"checkboxesExtra",refInFor:!0,attrs:{name:i}})]},proxy:!0}],null,!0)})})),1)],1)],1)]),t("van-popup",{style:{height:"45%"},attrs:{position:"bottom"},model:{value:e.areaVisible,callback:function(t){e.areaVisible=t},expression:"areaVisible"}},[t("van-area",{attrs:{title:"省市区选择","area-list":e.areaList,value:e.areaCode},on:{cancel:function(t){e.areaVisible=!1},confirm:e.areaSelect}})],1),0x16eba6c96f403000!=e.activityId&&0x173d6b4cea590000!=e.activityId?t("img",{staticClass:"back",attrs:{src:e.activityInfo.backImg,alt:""},on:{click:e.cmsTurnBack}}):e._e(),e.activityInfo.applyYhy?t("div",{staticClass:"bottomdiy"},[t("a",{staticClass:"item",attrs:{href:"https://zhaoshengniuren.com/mp_yqh/#/yunhuiyi"}},[e._v("技术支持：云会易")])]):e._e(),t("img",{staticClass:"back",attrs:{src:e.activityInfo.backImg,alt:""},on:{click:e.cmsTurnBack}}),t("van-popup",{style:{height:"35%"},attrs:{closeable:"",position:"bottom"},on:{close:e.turnSuccess},model:{value:e.payVisible,callback:function(t){e.payVisible=t},expression:"payVisible"}},[t("div",{staticClass:"popup-title"},[e._v("选择支付方式")]),t("div",{staticStyle:{"padding-top":"5px"}},[t("van-radio-group",{model:{value:e.payModel,callback:function(t){e.payModel=t},expression:"payModel"}},[t("van-cell-group",[e.indexChannel.isWechatPay?t("van-cell",{attrs:{title:"微信支付",clickable:""},on:{click:function(t){return e.weixin()}},scopedSlots:e._u([{key:"right-icon",fn:function(){return[t("van-radio",{attrs:{name:"微信支付"}})]},proxy:!0}],null,!1,**********)}):e._e(),e.indexChannel.isAliPay?t("van-cell",{attrs:{title:"支付宝支付",clickable:""},on:{click:function(t){return e.ali()}},scopedSlots:e._u([{key:"right-icon",fn:function(){return[t("van-radio",{attrs:{name:"支付宝支付"}})]},proxy:!0}],null,!1,**********)}):e._e(),e.indexChannel.isBankTransfer?t("van-cell",{attrs:{title:"银行转账",clickable:""},on:{click:function(t){return e.turnSuccess()}},scopedSlots:e._u([{key:"right-icon",fn:function(){return[t("van-radio",{attrs:{name:"银行转账"}})]},proxy:!0}],null,!1,**********)}):e._e()],1)],1)],1)])],1)},s=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"nav-title",staticStyle:{"margin-top":"8px"}},[t("div",{staticClass:"color"}),t("div",{staticClass:"text"},[e._v("报名通道选择")])])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"nav-title",staticStyle:{"margin-top":"8px"}},[t("div",{staticClass:"color"}),t("div",{staticClass:"text"},[e._v("手机验证")])])}],o=i("ade3"),l=(i("386d"),i("6b54"),i("ac6a"),i("28a5"),i("a481"),i("7de9")),c=i("cacf"),r=i("434d"),u=i("1b69"),d=i("66c7"),h={components:{pcheader:u["default"]},data:function(){return{minDate:new Date(2023,0,1),maxDate:new Date(2028,0,31),orderId:"",payModel:0,paySize:0,payVisible:!1,autoCompleteListView:!1,invoicelist:[],submitDisabled:!0,activityInfo:{},yesOrNoText:l["j"],isMobilePhone:Object(c["c"])(),isSms:Object(c["c"])(),openid:void 0,waiting:!1,isSend:!1,areaVisible:!1,areaCode:"110101",waitingTime:60,activityId:void 0,channelId:void 0,inChannelId:void 0,loading:!1,isPay:0,userInfo:{invoiceName:"",invoiceCode:"",invoiceBank:"",invoiceAddress:"",invoiceMobile:"",invoiceAccount:"",email:"",credit:""},indexChannel:{},channelList:[],applyActivityConfigList:[],extraResult:[],channelVisible:!0,applyContentVisible:!0,radioVisible:!1,dateVisible:!1,extraDateVisible:!1,checkBoxVisible:!1,chooseResult:{},checkBoxResult:[],extraRadioVisible:!1,extraCheckBoxVisible:!1,extraChooseResult:{},extraCheckBoxResult:[],areaList:r["a"],applyNumber:0}},mounted:function(){document.title="报名信息填写",this.activityId=this.$route.query.id,this.channelId=this.$route.query.channelId||"",this.inChannelId=this.$route.query.channelId||"",this.openid=this.$cookie.get("openid"),this.rebuildUrl(),this.checkApply()},methods:(a={cmsTurnBack:function(){this.activityInfo.backUrl?window.open(this.activityInfo.backUrl):this.$router.replace({name:"cmsIndex",query:{id:this.activityInfo.id}})},autoCompleteList:function(){var e=this;this.userInfo.invoiceName&&this.$fly.get("/pyp/web/invoice/findByInvoiceName/",{invoiceName:this.userInfo.invoiceName}).then((function(t){200==t.code&&(console.log(t.result.length),0!=t.result.length?(e.invoicelist=t.result,e.autoCompleteListView=!0):e.invoicelist=[])}))},invoiceChoose:function(e){this.userInfo.invoiceName=e.invoiceName,this.userInfo.invoiceCode=e.invoiceCode,this.userInfo.invoiceAccount=e.invoiceAccount,this.userInfo.invoiceBank=e.invoiceBank,this.userInfo.invoiceAddress=e.invoiceAddress,this.userInfo.invoiceMobile=e.invoiceMobile,this.autoCompleteListView=!1},cancel:function(){this.autoCompleteListView=!1,this.invoicelist=[]},getActivityInfo:function(){var e=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(t){200==t.code&&(e.activityInfo=t.activity,e.activityInfo.backImg=e.activityInfo.backImg||"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/9c9298f1d3474660977a02c8a84aafa8.png",t.activity.applyNotify&&vant.Dialog.alert({title:"报名须知",message:t.activity.applyNotify}).then((function(){})),t.activity.showApplyNumber&&e.getApplyCount())}))},getApplyCount:function(){var e=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/count/".concat(this.activityId)).then((function(t){200==t.code&&(e.applyNumber=t.result)}))},checkApply:function(){var e=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(t){if(200==t.code)if(e.isPay=t.isPay,e.$store.commit("apply/update",e.isPay),1==e.isPay){if(26==e.activityId)return location.href="https://wx.vzan.com/live/tvchat-*********?v=637903014806319913",!1;if(78==e.activityId)return location.href="https://wx.vzan.com/live/mk/aggspread/*********/f9dfa6f6-5cbf-11ed-93c5-043f72d45e40?v=*************",!1;e.$router.push({name:"applySuccess",query:{orderId:t.result,id:e.activityId}})}else 2==e.isPay?vant.Dialog.alert({title:"提示",message:"您有一笔注册费待支付，点击跳转"}).then((function(){e.$router.push({name:"applySuccess",query:{orderId:t.result,id:e.activityId}})})):(e.getUserActivityInfo(),e.getActivityInfo());else vant.Toast(t.msg)}))},getUserActivityInfo:function(){var e=this;this.$fly.get("/pyp/activity/activityuser/findByUserIdAndActivityId",{activityId:this.activityId}).then((function(t){200==t.code?(e.userInfo=t.result,e.userInfo.contact==e.userInfo.mobile&&(e.userInfo.contact=""),e.getApplyActivityChannelConfig()):vant.Toast(t.msg)}))},getApplyActivityChannelConfig:function(){var e=this;this.$fly.get("/pyp/web/apply/applyactivitychannelconfig/findByActivityId/".concat(this.activityId),{channelId:this.inChannelId}).then((function(t){200==t.code?(e.channelList=t.result,1==e.channelList.length?(e.channelVisible=!1,e.chooseChannel(e.channelList[0].id,0)):0==e.channelList.length?(e.channelVisible=!1,e.applyContentVisible=!1):e.inChannelId&&(e.channelVisible=!1,e.channelList.filter((function(t,i){t.id==e.inChannelId&&e.chooseChannel(e.inChannelId,i)})))):vant.Toast(t.msg)}))},chooseChannel:function(e,t){if(this.channelList[t].children)return vant.Toast("请选择子报名通道"),!1;this.indexChannel=this.channelList[t],this.channelId=e,1==Object(c["c"])()&&(this.isSms=this.indexChannel.isSms),this.paySize=0,this.indexChannel.price>0&&this.indexChannel.isWechatPay&&(this.paySize+=1),this.indexChannel.price>0&&this.indexChannel.isAliPay&&(this.paySize+=1),this.indexChannel.price>0&&this.indexChannel.isBankTransfer&&(this.paySize+=1),this.applyActivityConfigList=[],this.extraResult=[],this.getApplyActivityConfig()},chooseChannelChild:function(e,t,i){this.channelId=e,this.indexChannel=this.channelList[t].children[i],1==Object(c["c"])()&&(this.isSms=this.indexChannel.isSms),this.paySize=0,this.indexChannel.price>0&&this.indexChannel.isWechatPay&&(this.paySize+=1),this.indexChannel.price>0&&this.indexChannel.isAliPay&&(this.paySize+=1),this.indexChannel.price>0&&this.indexChannel.isBankTransfer&&(this.paySize+=1),this.applyActivityConfigList=[],this.extraResult=[],this.getApplyActivityConfig()},choose:function(e){this.$set(this.userInfo,this.chooseResult.applyConfigFieldName,e),this.radioVisible=!1},dateSelect:function(e){this.$set(this.userInfo,this.chooseResult.applyConfigFieldName,d["a"].formatDate.format(new Date(e),"yyyy/MM/dd")),this.dateVisible=!1},showRadio:function(e){this.chooseResult=e,this.radioVisible=!0},showDate:function(e){this.chooseResult=e,this.dateVisible=!0},showYesOrNo:function(e){this.chooseResult=e,this.$set(this.chooseResult,"selectData",this.yesOrNoText),this.radioVisible=!0},onChange:function(e){this.setData({checkBoxResult:e.detail})},toggle:function(e){var t=e.currentTarget.dataset.index;this.$refs.checkboxes[t].toggle()},chooseConfirm:function(){var e="";if(this.checkBoxResult.length>1)for(var t=0;t<this.checkBoxResult.length;t++)t===this.checkBoxResult.length-1?e+=this.checkBoxResult[t]:e=e+this.checkBoxResult[t]+",";else e=this.checkBoxResult[0];this.$set(this.userInfo,this.chooseResult.applyConfigFieldName,e),this.checkBoxVisible=!1},showCheckBox:function(e){this.chooseResult=e;var t=this.userInfo[this.chooseResult.applyConfigFieldName];this.checkBoxResult=t?t.split(","):[],this.checkBoxVisible=!0},showArea:function(e){this.chooseResult=e,this.areaCode=this.userInfo.area?this.userInfo.area.split(",")[2]:"110101",this.areaVisible=!0},areaSelect:function(e){var t=e[0].code+","+e[1].code+","+e[2].code,i=e[0].name+","+e[1].name+","+e[2].name;this.areaCode=e[2].code,this.$set(this.userInfo,this.chooseResult.applyConfigFieldName,t),this.$set(this.userInfo,"areaName",i),this.areaVisible=!1},chooseExtra:function(e){var t=this;this.extraResult.forEach((function(i){i.finalName==t.extraChooseResult.finalName&&(i.value=e)})),this.extraRadioVisible=!1},dateExtraSelect:function(e){var t=this;console.log(e),this.extraResult.forEach((function(i){i.finalName==t.extraChooseResult.finalName&&(i.value=d["a"].formatDate.format(new Date(e),"yyyy/MM/dd"))})),this.extraDateVisible=!1},showExtraRadio:function(e){this.extraChooseResult=e,this.extraRadioVisible=!0},showExtraDate:function(e){this.extraChooseResult=e,this.extraDateVisible=!0},onChangeExtra:function(e){this.setData({extraCheckBoxResult:e.detail})},toggleExtra:function(e){var t=e.currentTarget.dataset.index;this.$refs.checkboxesExtra[t].toggle()},chooseExtraConfirm:function(){var e=this,t="";if(this.extraCheckBoxResult.length>1)for(var i=0;i<this.extraCheckBoxResult.length;i++)i===this.extraCheckBoxResult.length-1?t+=this.extraCheckBoxResult[i]:t=t+this.extraCheckBoxResult[i]+",";else t=this.extraCheckBoxResult[0];this.extraResult.forEach((function(i){i.finalName==e.extraChooseResult.finalName&&(i.value=t)})),this.extraCheckBoxVisible=!1},showExtraCheckBox:function(e){this.extraChooseResult=e;var t=e.value;this.extraCheckBoxResult=t?t.split(","):[],this.extraCheckBoxVisible=!0},getApplyActivityConfig:function(){var e=this;this.submitDisabled=!0,this.$fly.get("/pyp/apply/applyactivityconfig/findByChannelId/".concat(this.channelId)).then((function(t){200==t.code?(e.submitDisabled=!1,e.applyActivityConfigList=t.result,e.applyActivityConfigList.forEach((function(t){1==t.type||2==t.type?t.selectData=t.selectData.replace(/，/gi,",").split(","):3==t.type&&t.extra&&(e.extraResult=JSON.parse(t.extra),e.extraResult.forEach((function(t){if(0x173d6b4cea590000==e.activityId){var i=e.$route.query.diy;i&&"推荐人"==t.finalName&&(t.value=decodeURIComponent(i))}(1==t.type||2==t.type)&&(t.selectData=t.selectData.replace(/，/gi,",").split(","))})))}))):vant.Toast(t.msg)}))},onSubmit:function(){var e=this;if(!this.userInfo.contact)return vant.Toast("请输入姓名"),!1;if(!this.userInfo.mobile)return vant.Toast("请输入手机号"),!1;if(!Object(c["b"])(this.userInfo.mobile))return vant.Toast("手机号格式错误"),!1;if(!this.userInfo.code&&this.isSms)return vant.Toast("请输入验证码"),!1;if(!/^\d{6}$/.test(this.userInfo.code)&&this.isSms)return vant.Toast("验证码错误"),!1;if(this.channelList.length>0&&!this.channelId)return vant.Toast("请选择报名通道"),!1;if(this.indexChannel.isNeedInvite>0&&!this.userInfo.inviteCode)return vant.Toast("请输入邀请码"),!1;for(var t=0;t<this.applyActivityConfigList.length;t++)if(3!=this.applyActivityConfigList[t].type&&this.applyActivityConfigList[t].required&&!this.userInfo[this.applyActivityConfigList[t].applyConfigFieldName])return vant.Toast("请输入"+this.applyActivityConfigList[t].finalName),!1;for(t=0;t<this.extraResult.length;t++)if(3!=this.extraResult[t].type&&this.extraResult[t].required&&!this.extraResult[t].value)return vant.Toast("请输入"+this.extraResult[t].finalName),!1;for(t=0;t<this.extraResult.length;t++)this.extraResult[t].selectData=this.extraResult[t].selectData?this.extraResult[t].selectData.toString():null;this.userInfo.activityId=this.activityId,this.userInfo.applyActivityChannelConfigId=this.channelId,this.userInfo.applyExtraVos=this.extraResult,this.userInfo.isMobilePhone=this.isSms,this.loading=!0;this.$fly.post("/pyp/web/activity/activityuserapplyorder/createOrder",this.userInfo).then((function(t){if(t&&200===t.code){if(t.token&&e.$cookie.set("token",t.token),26==e.activityId&&!t.isNeedPay)return location.href="https://wx.vzan.com/live/tvchat-*********?v=637903014806319913",!1;if(78==e.activityId&&!t.isNeedPay)return location.href="https://wx.vzan.com/live/mk/aggspread/*********/f9dfa6f6-5cbf-11ed-93c5-043f72d45e40?v=*************",!1;e.orderId=t.result,t.isNeedPay&&0==e.paySize?e.$router.push({name:"applySuccess",query:{orderId:e.orderId,id:e.activityId}}):t.isNeedPay&&1==e.paySize?(e.indexChannel.isBankTransfer&&e.$router.push({name:"applySuccess",query:{orderId:e.orderId,id:e.activityId}}),e.indexChannel.isWechatPay&&e.weixin(e.orderId),e.indexChannel.isAliPay&&e.ali(e.orderId)):t.isNeedPay&&e.paySize>1?e.payVisible=!0:e.userInfo.isHotel&&"是"==e.userInfo.isHotel?(vant.Toast("报名成功，即将跳转酒店预定页面"),setTimeout((function(){return e.$router.push({name:"hotelIndex",query:{id:e.activityId}}),!1}),1e3)):(vant.Toast("报名成功"),e.$router.push({name:"applySuccess",query:{orderId:e.orderId,id:e.activityId}}))}else vant.Toast(t.msg);e.loading=!1}))},turnSuccess:function(){this.$router.push({name:"applySuccess",query:{id:this.activityId,orderId:this.orderId}})},ali:function(){var e=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/payAli",{orderId:this.orderId}).then((function(t){t&&200===t.code?e.$router.push({name:"commonAlipay",query:{form:encodeURIComponent(t.result)}}):vant.Toast(t.msg)}))},weixin:function(){var e=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/pay",{orderId:this.orderId}).then((function(t){t&&200===t.code?WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:t.result.appId,timeStamp:t.result.timeStamp,nonceStr:t.result.nonceStr,package:t.result.packageValue,signType:t.result.signType,paySign:t.result.paySign},(function(t){if(console.log("开始支付"),"get_brand_wcpay_request:ok"==t.err_msg){var i=window.location.href.split("#")[0];location.href=i+"#/apply/success?id="+e.activityId+"&orderId="+e.orderId}else if("get_brand_wcpay_request:cancel"==t.err_msg){i=window.location.href.split("#")[0];location.href=i+"#/apply/success?id="+e.activityId+"&orderId="+e.orderId}else{i=window.location.href.split("#")[0];location.href=i+"#/apply/success?id="+e.activityId+"&orderId="+e.orderId}})):vant.Toast(t.msg)}))},doSendSmsCode:function(){var e=this;return this.userInfo.mobile?Object(c["b"])(this.userInfo.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.userInfo.mobile,activityId:this.activityId}).then((function(t){t&&200===t.code?(e.isSend=!0,e.countdown(),vant.Toast("发送验证码成功")):vant.Toast(t.msg)})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var e=this;this.waiting=!0;var t=window.setInterval((function(){e.waitingTime--,e.waitingTime<0&&(window.clearInterval(t),e.waitingTime=60,e.waiting=!1)}),1e3)},verifyApplyByMobile:function(e){var t=this;6==e.length&&1==this.isSend&&this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApplyByMobile",{activityId:this.activityId,mobile:this.userInfo.mobile,code:e}).then((function(i){200==i.code&&0!=i.isPay&&vant.Dialog.alert({title:"提示",message:"您已存在报名订单，请点击“确定”关联"}).then((function(){t.$fly.get("/pyp/web/activity/activityuserapplyorder/connectApply",{activityId:t.activityId,mobile:t.userInfo.mobile,code:e}).then((function(e){200==e.code?location.reload():vant.Toast(e.msg)}))}))}))}},Object(o["a"])(a,"cmsTurnBack",(function(){this.activityInfo.backUrl?window.open(this.activityInfo.backUrl):this.$router.replace({name:"cmsIndex",query:{id:this.activityInfo.id}})})),Object(o["a"])(a,"rebuildUrl",(function(){var e=window.location,t=e.href,i=e.protocol,a=e.host,n=e.pathname,s=e.search,o=e.hash;console.log(window.location),s=s||"?";var l="".concat(i,"//").concat(a).concat(n).concat(s).concat(o);console.log(l),l!==t&&window.location.replace(l)})),Object(o["a"])(a,"afterRead",(function(e,t){var i=this,a=t.name;e.status="uploading",e.message="上传中...";var n=new FormData;n.append("file",e.file),this.$fly.post("/pyp/web/upload",n).then((function(e){e&&200===e.code&&i.$set(i.userInfo,a,e.result)}))})),Object(o["a"])(a,"beforeRead",(function(e){return!0})),a)},p=h,f=(i("25af"),i("2877")),v=Object(f["a"])(p,n,s,!1,null,"a13e04a8",null);t["default"]=v.exports},"83a1":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},ac6a:function(e,t,i){for(var a=i("cadf"),n=i("0d58"),s=i("2aba"),o=i("7726"),l=i("32e9"),c=i("84f2"),r=i("2b4c"),u=r("iterator"),d=r("toStringTag"),h=c.Array,p={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},f=n(p),v=0;v<f.length;v++){var y,m=f[v],x=p[m],b=o[m],g=b&&b.prototype;if(g&&(g[u]||l(g,u,h),g[d]||l(g,d,m),c[m]=h,x))for(y in a)g[y]||s(g,y,a[y],!0)}}}]);
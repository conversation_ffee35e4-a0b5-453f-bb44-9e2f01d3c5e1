(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d2299c5"],{ddd3:function(e,a,t){"use strict";t.r(a);t("b0c0");var r=function(){var e=this,a=e._self._c;return a("el-dialog",{attrs:{title:e.dataForm.id?"修改扩展字段配置":"新增扩展字段配置","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(a){e.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"类型",prop:"type"}},[a("el-select",{model:{value:e.dataForm.type,callback:function(a){e.$set(e.dataForm,"type",a)},expression:"dataForm.type"}},e._l(e.applyTypeList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id,disabled:e.disabled}})})),1)],1),!e.dataForm.type||1!=e.dataForm.type&&2!=e.dataForm.type?e._e():a("el-form-item",{attrs:{label:"选项数据",prop:"selectData"}},[a("el-input",{attrs:{placeholder:"选项数据，请使用“,”隔开"},model:{value:e.dataForm.selectData,callback:function(a){e.$set(e.dataForm,"selectData",a)},expression:"dataForm.selectData"}})],1),a("el-form-item",{attrs:{label:"字段名称",prop:"finalName"}},[a("el-input",{attrs:{placeholder:"字段最终名称"},model:{value:e.dataForm.finalName,callback:function(a){e.$set(e.dataForm,"finalName",a)},expression:"dataForm.finalName"}})],1),a("el-form-item",{attrs:{label:"提示文字信息",prop:"placeholder"}},[a("el-input",{attrs:{placeholder:"字段最终名称"},model:{value:e.dataForm.placeholder,callback:function(a){e.$set(e.dataForm,"placeholder",a)},expression:"dataForm.placeholder"}})],1),a("el-form-item",{attrs:{label:"是否必填",prop:"required"}},[a("el-select",{model:{value:e.dataForm.required,callback:function(a){e.$set(e.dataForm,"required",a)},expression:"dataForm.required"}},e._l(e.requiredList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),a("el-form-item",{attrs:{label:"排序",prop:"paixu"}},[a("el-input-number",{attrs:{min:0,max:100,label:"排序"},model:{value:e.dataForm.paixu,callback:function(a){e.$set(e.dataForm,"paixu",a)},expression:"dataForm.paixu"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){e.visible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},l=[],i={data:function(){return{visible:!1,dataForm:{id:0,defaultValue:"",selectData:"",required:1,type:0,finalName:"",extra:"",placeholder:"",paixu:0,row:0},applyTypeList:[{id:0,name:"填空"},{id:1,name:"单选"},{id:2,name:"多选"},{id:3,name:"扩展",disabled:!0},{id:4,name:"特殊",disabled:!0},{id:5,name:"日期"}],requiredList:[{id:0,name:"否"},{id:1,name:"是"}],dataRule:{type:[{required:!0,message:"类型不能为空",trigger:"blur"}],finalName:[{required:!0,message:"字段最终名称不能为空",trigger:"blur"}],paixu:[{required:!0,message:"排序不能为空",trigger:"blur"}],selectData:[{required:!0,message:"选择数据不能为空",trigger:"blur"}],required:[{required:!0,message:"是否必填不能为空",trigger:"blur"}]}}},methods:{init:function(e,a){this.dataForm=e,this.dataForm.row=a,this.visible=!0},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(a){a&&e.$http({url:e.$http.adornUrl("/apply/applyactivityconfig/updateExtra"),method:"post",data:e.$http.adornData({id:e.dataForm.id,type:e.dataForm.type,finalName:e.dataForm.finalName,extra:e.dataForm.extra,paixu:e.dataForm.paixu,required:e.dataForm.required,defaultValue:e.dataForm.defaultValue,selectData:e.dataForm.selectData,row:e.dataForm.row,placeholder:e.dataForm.placeholder})}).then((function(a){var t=a.data;t&&200===t.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(t.msg)}))}))}}},o=i,d=t("2877"),s=Object(d["a"])(o,r,l,!1,null,null,null);a["default"]=s.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0cf760"],{"648d":function(t,n,o){"use strict";o.r(n);var e=function(){var t=this,n=t._self._c;return n("van-dialog",{attrs:{title:"长按二维码关注公众号",confirmButtonText:"关闭"},on:{confirm:function(n){return t.$emit("close")}},model:{value:t.show,callback:function(n){t.show=n},expression:"show"}},[n("div",{staticClass:"text-center padding"},[n("van-image",{attrs:{width:"200",src:t.qrcodeImgUrl},scopedSlots:t._u([{key:"error",fn:function(){return[t._v("二维码加载失败\n            ")]},proxy:!0}])}),n("div",{staticStyle:{color:"grey","font-size":"14px"}},[t._v("长按二维码关注公众号,了解会议实时动态")])],1)])},r=[],l={name:"FollowModal",props:{show:{type:Boolean,default:!1},qrcodeImgUrl:{type:String,default:""}},data:function(){return{}}},a=l,c=o("2877"),s=Object(c["a"])(a,e,r,!1,null,null,null);n["default"]=s.exports}}]);
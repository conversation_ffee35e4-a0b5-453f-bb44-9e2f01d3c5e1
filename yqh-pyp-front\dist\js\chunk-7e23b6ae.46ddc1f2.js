(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7e23b6ae"],{"09fa":function(t,n,e){var i=e("4588"),o=e("9def");t.exports=function(t){if(void 0===t)return 0;var n=i(t),e=o(n);if(n!==e)throw RangeError("Wrong length!");return e}},"0f88":function(t,n,e){var i,o=e("7726"),r=e("32e9"),c=e("ca5a"),s=c("typed_array"),a=c("view"),f=!(!o.ArrayBuffer||!o.DataView),u=f,h=0,l=9,d="Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array".split(",");while(h<l)(i=o[d[h++]])?(r(i.prototype,s,!0),r(i.prototype,a,!0)):u=!1;t.exports={ABV:f,CONSTR:u,TYPED:s,VIEW:a}},"11e9":function(t,n,e){var i=e("52a7"),o=e("4630"),r=e("6821"),c=e("6a99"),s=e("69a8"),a=e("c69a"),f=Object.getOwnPropertyDescriptor;n.f=e("9e1e")?f:function(t,n){if(t=r(t),n=c(n,!0),a)try{return f(t,n)}catch(e){}if(s(t,n))return o(!i.f.call(t,n),t[n])}},"34ef":function(t,n,e){e("ec30")("Uint8",1,(function(t){return function(n,e,i){return t(this,n,e,i)}}))},"36bd":function(t,n,e){"use strict";var i=e("4bf8"),o=e("77f1"),r=e("9def");t.exports=function(t){var n=i(this),e=r(n.length),c=arguments.length,s=o(c>1?arguments[1]:void 0,e),a=c>2?arguments[2]:void 0,f=void 0===a?e:o(a,e);while(f>s)n[s++]=t;return n}},9093:function(t,n,e){var i=e("ce10"),o=e("e11e").concat("length","prototype");n.f=Object.getOwnPropertyNames||function(t){return i(t,o)}},ba92:function(t,n,e){"use strict";var i=e("4bf8"),o=e("77f1"),r=e("9def");t.exports=[].copyWithin||function(t,n){var e=i(this),c=r(e.length),s=o(t,c),a=o(n,c),f=arguments.length>2?arguments[2]:void 0,u=Math.min((void 0===f?c:o(f,c))-a,c-s),h=1;a<s&&s<a+u&&(h=-1,a+=u-1,s+=u-1);while(u-- >0)a in e?e[s]=e[a]:delete e[s],s+=h,a+=h;return e}},ec30:function(t,n,e){"use strict";if(e("9e1e")){var i=e("2d00"),o=e("7726"),r=e("79e5"),c=e("5ca1"),s=e("0f88"),a=e("ed0b"),f=e("9b43"),u=e("f605"),h=e("4630"),l=e("32e9"),d=e("dcbc"),g=e("4588"),v=e("9def"),p=e("09fa"),w=e("77f1"),y=e("6a99"),m=e("69a8"),b=e("23c6"),x=e("d3f4"),_=e("4bf8"),S=e("33a4"),T=e("2aeb"),W=e("38fd"),E=e("9093").f,P=e("27ee"),I=e("ca5a"),C=e("2b4c"),R=e("0a49"),O=e("c366"),M=e("ebd6"),D=e("cadf"),F=e("84f2"),j=e("5cc5"),A=e("7a56"),B=e("36bd"),U=e("ba92"),L=e("86cc"),Y=e("11e9"),k=L.f,X=Y.f,N=o.RangeError,$=o.TypeError,V=o.Uint8Array,z="ArrayBuffer",H="Shared"+z,J="BYTES_PER_ELEMENT",q="prototype",G=Array[q],K=a.ArrayBuffer,Q=a.DataView,Z=R(0),tt=R(2),nt=R(3),et=R(4),it=R(5),ot=R(6),rt=O(!0),ct=O(!1),st=D.values,at=D.keys,ft=D.entries,ut=G.lastIndexOf,ht=G.reduce,lt=G.reduceRight,dt=G.join,gt=G.sort,vt=G.slice,pt=G.toString,wt=G.toLocaleString,yt=C("iterator"),mt=C("toStringTag"),bt=I("typed_constructor"),xt=I("def_constructor"),_t=s.CONSTR,St=s.TYPED,Tt=s.VIEW,Wt="Wrong length!",Et=R(1,(function(t,n){return Ot(M(t,t[xt]),n)})),Pt=r((function(){return 1===new V(new Uint16Array([1]).buffer)[0]})),It=!!V&&!!V[q].set&&r((function(){new V(1).set({})})),Ct=function(t,n){var e=g(t);if(e<0||e%n)throw N("Wrong offset!");return e},Rt=function(t){if(x(t)&&St in t)return t;throw $(t+" is not a typed array!")},Ot=function(t,n){if(!x(t)||!(bt in t))throw $("It is not a typed array constructor!");return new t(n)},Mt=function(t,n){return Dt(M(t,t[xt]),n)},Dt=function(t,n){var e=0,i=n.length,o=Ot(t,i);while(i>e)o[e]=n[e++];return o},Ft=function(t,n,e){k(t,n,{get:function(){return this._d[e]}})},jt=function(t){var n,e,i,o,r,c,s=_(t),a=arguments.length,u=a>1?arguments[1]:void 0,h=void 0!==u,l=P(s);if(void 0!=l&&!S(l)){for(c=l.call(s),i=[],n=0;!(r=c.next()).done;n++)i.push(r.value);s=i}for(h&&a>2&&(u=f(u,arguments[2],2)),n=0,e=v(s.length),o=Ot(this,e);e>n;n++)o[n]=h?u(s[n],n):s[n];return o},At=function(){var t=0,n=arguments.length,e=Ot(this,n);while(n>t)e[t]=arguments[t++];return e},Bt=!!V&&r((function(){wt.call(new V(1))})),Ut=function(){return wt.apply(Bt?vt.call(Rt(this)):Rt(this),arguments)},Lt={copyWithin:function(t,n){return U.call(Rt(this),t,n,arguments.length>2?arguments[2]:void 0)},every:function(t){return et(Rt(this),t,arguments.length>1?arguments[1]:void 0)},fill:function(t){return B.apply(Rt(this),arguments)},filter:function(t){return Mt(this,tt(Rt(this),t,arguments.length>1?arguments[1]:void 0))},find:function(t){return it(Rt(this),t,arguments.length>1?arguments[1]:void 0)},findIndex:function(t){return ot(Rt(this),t,arguments.length>1?arguments[1]:void 0)},forEach:function(t){Z(Rt(this),t,arguments.length>1?arguments[1]:void 0)},indexOf:function(t){return ct(Rt(this),t,arguments.length>1?arguments[1]:void 0)},includes:function(t){return rt(Rt(this),t,arguments.length>1?arguments[1]:void 0)},join:function(t){return dt.apply(Rt(this),arguments)},lastIndexOf:function(t){return ut.apply(Rt(this),arguments)},map:function(t){return Et(Rt(this),t,arguments.length>1?arguments[1]:void 0)},reduce:function(t){return ht.apply(Rt(this),arguments)},reduceRight:function(t){return lt.apply(Rt(this),arguments)},reverse:function(){var t,n=this,e=Rt(n).length,i=Math.floor(e/2),o=0;while(o<i)t=n[o],n[o++]=n[--e],n[e]=t;return n},some:function(t){return nt(Rt(this),t,arguments.length>1?arguments[1]:void 0)},sort:function(t){return gt.call(Rt(this),t)},subarray:function(t,n){var e=Rt(this),i=e.length,o=w(t,i);return new(M(e,e[xt]))(e.buffer,e.byteOffset+o*e.BYTES_PER_ELEMENT,v((void 0===n?i:w(n,i))-o))}},Yt=function(t,n){return Mt(this,vt.call(Rt(this),t,n))},kt=function(t){Rt(this);var n=Ct(arguments[1],1),e=this.length,i=_(t),o=v(i.length),r=0;if(o+n>e)throw N(Wt);while(r<o)this[n+r]=i[r++]},Xt={entries:function(){return ft.call(Rt(this))},keys:function(){return at.call(Rt(this))},values:function(){return st.call(Rt(this))}},Nt=function(t,n){return x(t)&&t[St]&&"symbol"!=typeof n&&n in t&&String(+n)==String(n)},$t=function(t,n){return Nt(t,n=y(n,!0))?h(2,t[n]):X(t,n)},Vt=function(t,n,e){return!(Nt(t,n=y(n,!0))&&x(e)&&m(e,"value"))||m(e,"get")||m(e,"set")||e.configurable||m(e,"writable")&&!e.writable||m(e,"enumerable")&&!e.enumerable?k(t,n,e):(t[n]=e.value,t)};_t||(Y.f=$t,L.f=Vt),c(c.S+c.F*!_t,"Object",{getOwnPropertyDescriptor:$t,defineProperty:Vt}),r((function(){pt.call({})}))&&(pt=wt=function(){return dt.call(this)});var zt=d({},Lt);d(zt,Xt),l(zt,yt,Xt.values),d(zt,{slice:Yt,set:kt,constructor:function(){},toString:pt,toLocaleString:Ut}),Ft(zt,"buffer","b"),Ft(zt,"byteOffset","o"),Ft(zt,"byteLength","l"),Ft(zt,"length","e"),k(zt,mt,{get:function(){return this[St]}}),t.exports=function(t,n,e,a){a=!!a;var f=t+(a?"Clamped":"")+"Array",h="get"+t,d="set"+t,g=o[f],w=g||{},y=g&&W(g),m=!g||!s.ABV,_={},S=g&&g[q],P=function(t,e){var i=t._d;return i.v[h](e*n+i.o,Pt)},I=function(t,e,i){var o=t._d;a&&(i=(i=Math.round(i))<0?0:i>255?255:255&i),o.v[d](e*n+o.o,i,Pt)},C=function(t,n){k(t,n,{get:function(){return P(this,n)},set:function(t){return I(this,n,t)},enumerable:!0})};m?(g=e((function(t,e,i,o){u(t,g,f,"_d");var r,c,s,a,h=0,d=0;if(x(e)){if(!(e instanceof K||(a=b(e))==z||a==H))return St in e?Dt(g,e):jt.call(g,e);r=e,d=Ct(i,n);var w=e.byteLength;if(void 0===o){if(w%n)throw N(Wt);if(c=w-d,c<0)throw N(Wt)}else if(c=v(o)*n,c+d>w)throw N(Wt);s=c/n}else s=p(e),c=s*n,r=new K(c);l(t,"_d",{b:r,o:d,l:c,e:s,v:new Q(r)});while(h<s)C(t,h++)})),S=g[q]=T(zt),l(S,"constructor",g)):r((function(){g(1)}))&&r((function(){new g(-1)}))&&j((function(t){new g,new g(null),new g(1.5),new g(t)}),!0)||(g=e((function(t,e,i,o){var r;return u(t,g,f),x(e)?e instanceof K||(r=b(e))==z||r==H?void 0!==o?new w(e,Ct(i,n),o):void 0!==i?new w(e,Ct(i,n)):new w(e):St in e?Dt(g,e):jt.call(g,e):new w(p(e))})),Z(y!==Function.prototype?E(w).concat(E(y)):E(w),(function(t){t in g||l(g,t,w[t])})),g[q]=S,i||(S.constructor=g));var R=S[yt],O=!!R&&("values"==R.name||void 0==R.name),M=Xt.values;l(g,bt,!0),l(S,St,f),l(S,Tt,!0),l(S,xt,g),(a?new g(1)[mt]==f:mt in S)||k(S,mt,{get:function(){return f}}),_[f]=g,c(c.G+c.W+c.F*(g!=w),_),c(c.S,f,{BYTES_PER_ELEMENT:n}),c(c.S+c.F*r((function(){w.of.call(g,1)})),f,{from:jt,of:At}),J in S||l(S,J,n),c(c.P,f,Lt),A(f),c(c.P+c.F*It,f,{set:kt}),c(c.P+c.F*!O,f,Xt),i||S.toString==pt||(S.toString=pt),c(c.P+c.F*r((function(){new g(1).slice()})),f,{slice:Yt}),c(c.P+c.F*(r((function(){return[1,2].toLocaleString()!=new g([1,2]).toLocaleString()}))||!r((function(){S.toLocaleString.call([1,2])}))),f,{toLocaleString:Ut}),F[f]=O?R:M,i||O||l(S,yt,M)}}else t.exports=function(){}},ed0b:function(t,n,e){"use strict";var i=e("7726"),o=e("9e1e"),r=e("2d00"),c=e("0f88"),s=e("32e9"),a=e("dcbc"),f=e("79e5"),u=e("f605"),h=e("4588"),l=e("9def"),d=e("09fa"),g=e("9093").f,v=e("86cc").f,p=e("36bd"),w=e("7f20"),y="ArrayBuffer",m="DataView",b="prototype",x="Wrong length!",_="Wrong index!",S=i[y],T=i[m],W=i.Math,E=i.RangeError,P=i.Infinity,I=S,C=W.abs,R=W.pow,O=W.floor,M=W.log,D=W.LN2,F="buffer",j="byteLength",A="byteOffset",B=o?"_b":F,U=o?"_l":j,L=o?"_o":A;function Y(t,n,e){var i,o,r,c=new Array(e),s=8*e-n-1,a=(1<<s)-1,f=a>>1,u=23===n?R(2,-24)-R(2,-77):0,h=0,l=t<0||0===t&&1/t<0?1:0;for(t=C(t),t!=t||t===P?(o=t!=t?1:0,i=a):(i=O(M(t)/D),t*(r=R(2,-i))<1&&(i--,r*=2),t+=i+f>=1?u/r:u*R(2,1-f),t*r>=2&&(i++,r/=2),i+f>=a?(o=0,i=a):i+f>=1?(o=(t*r-1)*R(2,n),i+=f):(o=t*R(2,f-1)*R(2,n),i=0));n>=8;c[h++]=255&o,o/=256,n-=8);for(i=i<<n|o,s+=n;s>0;c[h++]=255&i,i/=256,s-=8);return c[--h]|=128*l,c}function k(t,n,e){var i,o=8*e-n-1,r=(1<<o)-1,c=r>>1,s=o-7,a=e-1,f=t[a--],u=127&f;for(f>>=7;s>0;u=256*u+t[a],a--,s-=8);for(i=u&(1<<-s)-1,u>>=-s,s+=n;s>0;i=256*i+t[a],a--,s-=8);if(0===u)u=1-c;else{if(u===r)return i?NaN:f?-P:P;i+=R(2,n),u-=c}return(f?-1:1)*i*R(2,u-n)}function X(t){return t[3]<<24|t[2]<<16|t[1]<<8|t[0]}function N(t){return[255&t]}function $(t){return[255&t,t>>8&255]}function V(t){return[255&t,t>>8&255,t>>16&255,t>>24&255]}function z(t){return Y(t,52,8)}function H(t){return Y(t,23,4)}function J(t,n,e){v(t[b],n,{get:function(){return this[e]}})}function q(t,n,e,i){var o=+e,r=d(o);if(r+n>t[U])throw E(_);var c=t[B]._b,s=r+t[L],a=c.slice(s,s+n);return i?a:a.reverse()}function G(t,n,e,i,o,r){var c=+e,s=d(c);if(s+n>t[U])throw E(_);for(var a=t[B]._b,f=s+t[L],u=i(+o),h=0;h<n;h++)a[f+h]=u[r?h:n-h-1]}if(c.ABV){if(!f((function(){S(1)}))||!f((function(){new S(-1)}))||f((function(){return new S,new S(1.5),new S(NaN),S.name!=y}))){S=function(t){return u(this,S),new I(d(t))};for(var K,Q=S[b]=I[b],Z=g(I),tt=0;Z.length>tt;)(K=Z[tt++])in S||s(S,K,I[K]);r||(Q.constructor=S)}var nt=new T(new S(2)),et=T[b].setInt8;nt.setInt8(0,2147483648),nt.setInt8(1,2147483649),!nt.getInt8(0)&&nt.getInt8(1)||a(T[b],{setInt8:function(t,n){et.call(this,t,n<<24>>24)},setUint8:function(t,n){et.call(this,t,n<<24>>24)}},!0)}else S=function(t){u(this,S,y);var n=d(t);this._b=p.call(new Array(n),0),this[U]=n},T=function(t,n,e){u(this,T,m),u(t,S,m);var i=t[U],o=h(n);if(o<0||o>i)throw E("Wrong offset!");if(e=void 0===e?i-o:l(e),o+e>i)throw E(x);this[B]=t,this[L]=o,this[U]=e},o&&(J(S,j,"_l"),J(T,F,"_b"),J(T,j,"_l"),J(T,A,"_o")),a(T[b],{getInt8:function(t){return q(this,1,t)[0]<<24>>24},getUint8:function(t){return q(this,1,t)[0]},getInt16:function(t){var n=q(this,2,t,arguments[1]);return(n[1]<<8|n[0])<<16>>16},getUint16:function(t){var n=q(this,2,t,arguments[1]);return n[1]<<8|n[0]},getInt32:function(t){return X(q(this,4,t,arguments[1]))},getUint32:function(t){return X(q(this,4,t,arguments[1]))>>>0},getFloat32:function(t){return k(q(this,4,t,arguments[1]),23,4)},getFloat64:function(t){return k(q(this,8,t,arguments[1]),52,8)},setInt8:function(t,n){G(this,1,t,N,n)},setUint8:function(t,n){G(this,1,t,N,n)},setInt16:function(t,n){G(this,2,t,$,n,arguments[2])},setUint16:function(t,n){G(this,2,t,$,n,arguments[2])},setInt32:function(t,n){G(this,4,t,V,n,arguments[2])},setUint32:function(t,n){G(this,4,t,V,n,arguments[2])},setFloat32:function(t,n){G(this,4,t,H,n,arguments[2])},setFloat64:function(t,n){G(this,8,t,z,n,arguments[2])}});w(S,y),w(T,m),s(T[b],c.VIEW,!0),n[y]=S,n[m]=T},f4b0:function(t,n,e){(function(n,e){t.exports=e()})("undefined"!==typeof self&&self,(function(){return function(t){var n={};function e(i){if(n[i])return n[i].exports;var o=n[i]={i:i,l:!1,exports:{}};return t[i].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=t,e.c=n,e.d=function(t,n,i){e.o(t,n)||Object.defineProperty(t,n,{enumerable:!0,get:i})},e.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},e.t=function(t,n){if(1&n&&(t=e(t)),8&n)return t;if(4&n&&"object"===typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(e.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&n&&"string"!=typeof t)for(var o in t)e.d(i,o,function(n){return t[n]}.bind(null,o));return i},e.n=function(t){var n=t&&t.__esModule?function(){return t["default"]}:function(){return t};return e.d(n,"a",n),n},e.o=function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},e.p="",e(e.s="fb15")}({"0bfb":function(t,n,e){"use strict";var i=e("cb7c");t.exports=function(){var t=i(this),n="";return t.global&&(n+="g"),t.ignoreCase&&(n+="i"),t.multiline&&(n+="m"),t.unicode&&(n+="u"),t.sticky&&(n+="y"),n}},"230e":function(t,n,e){var i=e("d3f4"),o=e("7726").document,r=i(o)&&i(o.createElement);t.exports=function(t){return r?o.createElement(t):{}}},"2aba":function(t,n,e){var i=e("7726"),o=e("32e9"),r=e("69a8"),c=e("ca5a")("src"),s=e("fa5b"),a="toString",f=(""+s).split(a);e("8378").inspectSource=function(t){return s.call(t)},(t.exports=function(t,n,e,s){var a="function"==typeof e;a&&(r(e,"name")||o(e,"name",n)),t[n]!==e&&(a&&(r(e,c)||o(e,c,t[n]?""+t[n]:f.join(String(n)))),t===i?t[n]=e:s?t[n]?t[n]=e:o(t,n,e):(delete t[n],o(t,n,e)))})(Function.prototype,a,(function(){return"function"==typeof this&&this[c]||s.call(this)}))},"2d00":function(t,n){t.exports=!1},"32e9":function(t,n,e){var i=e("86cc"),o=e("4630");t.exports=e("9e1e")?function(t,n,e){return i.f(t,n,o(1,e))}:function(t,n,e){return t[n]=e,t}},3846:function(t,n,e){e("9e1e")&&"g"!=/./g.flags&&e("86cc").f(RegExp.prototype,"flags",{configurable:!0,get:e("0bfb")})},4630:function(t,n){t.exports=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}}},5537:function(t,n,e){var i=e("8378"),o=e("7726"),r="__core-js_shared__",c=o[r]||(o[r]={});(t.exports=function(t,n){return c[t]||(c[t]=void 0!==n?n:{})})("versions",[]).push({version:i.version,mode:e("2d00")?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},"69a8":function(t,n){var e={}.hasOwnProperty;t.exports=function(t,n){return e.call(t,n)}},"6a99":function(t,n,e){var i=e("d3f4");t.exports=function(t,n){if(!i(t))return t;var e,o;if(n&&"function"==typeof(e=t.toString)&&!i(o=e.call(t)))return o;if("function"==typeof(e=t.valueOf)&&!i(o=e.call(t)))return o;if(!n&&"function"==typeof(e=t.toString)&&!i(o=e.call(t)))return o;throw TypeError("Can't convert object to primitive value")}},"6b54":function(t,n,e){"use strict";e("3846");var i=e("cb7c"),o=e("0bfb"),r=e("9e1e"),c="toString",s=/./[c],a=function(t){e("2aba")(RegExp.prototype,c,t,!0)};e("79e5")((function(){return"/a/b"!=s.call({source:"a",flags:"b"})}))?a((function(){var t=i(this);return"/".concat(t.source,"/","flags"in t?t.flags:!r&&t instanceof RegExp?o.call(t):void 0)})):s.name!=c&&a((function(){return s.call(this)}))},7726:function(t,n){var e=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=e)},"79e5":function(t,n){t.exports=function(t){try{return!!t()}catch(n){return!0}}},"7f7f":function(t,n,e){var i=e("86cc").f,o=Function.prototype,r=/^\s*function ([^ (]*)/,c="name";c in o||e("9e1e")&&i(o,c,{configurable:!0,get:function(){try{return(""+this).match(r)[1]}catch(t){return""}}})},8378:function(t,n){var e=t.exports={version:"2.6.10"};"number"==typeof __e&&(__e=e)},"86cc":function(t,n,e){var i=e("cb7c"),o=e("c69a"),r=e("6a99"),c=Object.defineProperty;n.f=e("9e1e")?Object.defineProperty:function(t,n,e){if(i(t),n=r(n,!0),i(e),o)try{return c(t,n,e)}catch(s){}if("get"in e||"set"in e)throw TypeError("Accessors not supported!");return"value"in e&&(t[n]=e.value),t}},"9e1e":function(t,n,e){t.exports=!e("79e5")((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},c69a:function(t,n,e){t.exports=!e("9e1e")&&!e("79e5")((function(){return 7!=Object.defineProperty(e("230e")("div"),"a",{get:function(){return 7}}).a}))},ca5a:function(t,n){var e=0,i=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++e+i).toString(36))}},cb7c:function(t,n,e){var i=e("d3f4");t.exports=function(t){if(!i(t))throw TypeError(t+" is not an object!");return t}},d3f4:function(t,n){t.exports=function(t){return"object"===typeof t?null!==t:"function"===typeof t}},f6fd:function(t,n){(function(t){var n="currentScript",e=t.getElementsByTagName("script");n in t||Object.defineProperty(t,n,{get:function(){try{throw new Error}catch(i){var t,n=(/.*at [^\(]*\((.*):.+:.+\)$/gi.exec(i.stack)||[!1])[1];for(t in e)if(e[t].src==n||"interactive"==e[t].readyState)return e[t];return null}}})})(document)},fa5b:function(t,n,e){t.exports=e("5537")("native-function-to-string",Function.toString)},fb15:function(t,n,e){"use strict";var i;e.r(n),"undefined"!==typeof window&&(e("f6fd"),(i=window.document.currentScript)&&(i=i.src.match(/(.+\/)[^/]+\.js(\?.*)?$/))&&(e.p=i[1])),e("7f7f");var o=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("canvas",{ref:t.domId,staticClass:"app-sign-canvas",attrs:{id:t.domId},on:{mousedown:function(n){return n.preventDefault(),n.stopPropagation(),t.handleMousedown(n)},mousemove:function(n){return n.preventDefault(),n.stopPropagation(),t.handleMousemove(n)},mouseup:function(n){return n.preventDefault(),n.stopPropagation(),t.handleMouseup(n)},mouseleave:function(n){return n.preventDefault(),n.stopPropagation(),t.handleMouseleave(n)},touchstart:function(n){return n.preventDefault(),n.stopPropagation(),t.handleTouchstart(n)},touchmove:function(n){return n.preventDefault(),n.stopPropagation(),t.handleTouchmove(n)},touchend:function(n){return n.preventDefault(),n.stopPropagation(),t.handleTouchend(n)}}},[t._v("\n    您的浏览器不支持canvas技术,请升级浏览器!\n")])},r=[],c=(e("6b54"),{name:"SignCanvas",model:{value:"image",event:"confirm"},props:{image:{required:!1,type:[String],default:null},options:{required:!1,type:[Object],default:function(){return null}}},data:function(){return{value:null,domId:"sign-canvas-".concat(Math.random().toString(36).substr(2)),canvas:null,context:null,dpr:1,config:{isFullScreen:!1,isFullCover:!1,isDpr:!1,lastWriteSpeed:1,lastWriteWidth:2,lineCap:"round",lineJoin:"round",canvasWidth:600,canvasHeight:600,isShowBorder:!0,bgColor:"#fcc",borderWidth:1,borderColor:"#ff787f",writeWidth:5,maxWriteWidth:30,minWriteWidth:5,writeColor:"#101010",isSign:!1,imgType:"png"},resizeTimer:null}},mounted:function(){var t=this;this.init(),window.addEventListener("resize",(function(){t.resizeTimer&&clearTimeout(t.resizeTimer),t.resizeTimer=setTimeout((function(){t.init()}),100)}))},watch:{options:{handler:function(){this.init()},deep:!0}},methods:{init:function(){var t=this.options;if(t)for(var n in t)this.config[n]=t[n];this.dpr="undefined"!==typeof window&&this.config.isDpr&&(window.devicePixelRatio||window.webkitDevicePixelRatio||window.mozDevicePixelRatio)||1,this.canvas=document.getElementById(this.domId),this.context=this.canvas.getContext("2d"),this.canvas.style.background=this.config.bgColor,this.config.isFullScreen&&(this.config.canvasWidth=window.innerWidth||document.body.clientWidth,this.config.canvasHeight=window.innerHeight||document.body.clientHeight,this.config.isFullCover&&(this.canvas.style.position="fixed",this.canvas.style.top=0,this.canvas.style.left=0,this.canvas.style.margin=0,this.canvas.style.zIndex=20001)),this.canvas.height=this.config.canvasWidth,this.canvas.width=this.config.canvasHeight,this.canvasInit(),this.canvasClear()},setLineWidth:function(){var t=(new Date).getTime(),n=t-this.config.lastWriteTime;this.config.lastWriteTime=t;var e=this.config.minWriteWidth+(this.config.maxWriteWidth-this.config.minWriteWidth)*n/30;if(e<this.config.minWriteWidth?e=this.config.minWriteWidth:e>this.config.maxWriteWidth&&(e=this.config.maxWriteWidth),e=e.toFixed(2),this.config.isSign)this.context.lineWidth=this.config.writeWidth*this.dpr;else{var i=this.config.lastWriteWidth=this.config.lastWriteWidth/4*3+e/4;this.context.lineWidth=i*this.dpr}},writeBegin:function(t){this.config.isWrite=!0,this.config.lastWriteTime=(new Date).getTime(),this.config.lastPoint=t,this.writeContextStyle()},writing:function(t){this.context.beginPath(),this.context.moveTo(this.config.lastPoint.x*this.dpr,this.config.lastPoint.y*this.dpr),this.context.lineTo(t.x*this.dpr,t.y*this.dpr),this.setLineWidth(),this.context.stroke(),this.config.lastPoint=t,this.context.closePath()},writeEnd:function(t){this.config.isWrite=!1,this.config.lastPoint=t,this.saveAsImg()},writeContextStyle:function(){this.context.beginPath(),this.context.strokeStyle=this.config.writeColor,this.context.lineCap=this.config.lineCap,this.context.lineJoin=this.config.lineJoin},canvasClear:function(){this.context.save(),this.context.strokeStyle=this.config.writeColor,this.context.clearRect(0,0,this.canvas.width,this.canvas.height),this.context.beginPath(),this.context.lineWidth=this.config.borderWidth*this.dpr,this.context.strokeStyle=this.config.borderColor;var t=this.config.borderWidth/2*this.dpr;this.config.isShowBorder&&(this.context.moveTo(t,t),this.context.lineTo(this.canvas.width-t,t),this.context.lineTo(this.canvas.width-t,this.canvas.height-t),this.context.lineTo(t,this.canvas.height-t),this.context.closePath(),this.context.stroke()),this.config.isShowBorder&&!this.config.isSign&&(this.context.moveTo(0,0),this.context.lineTo(this.canvas.width,this.canvas.height),this.context.lineTo(this.canvas.width,this.canvas.height/2),this.context.lineTo(0,this.canvas.height/2),this.context.lineTo(0,this.canvas.height),this.context.lineTo(this.canvas.width,0),this.context.lineTo(this.canvas.width/2,0),this.context.lineTo(this.canvas.width/2,this.canvas.height),this.context.stroke()),this.$emit("confirm",null),this.context.restore()},saveAsImg:function(){var t=new Image;return t.src=this.canvas.toDataURL("image/".concat(this.config.imgType)),this.$emit("confirm",t.src),t.src},canvasInit:function(){this.canvas.width=this.config.canvasWidth*this.dpr,this.canvas.height=this.config.canvasHeight*this.dpr,this.canvas.style.width="".concat(this.config.canvasWidth,"px"),this.canvas.style.height="".concat(this.config.canvasHeight,"px"),this.config.emptyCanvas=this.canvas.toDataURL("image/".concat(this.config.imgType))},handleMousedown:function(t){this.writeBegin({x:t.offsetX||t.clientX,y:t.offsetY||t.clientY})},handleMousemove:function(t){this.config.isWrite&&this.writing({x:t.offsetX,y:t.offsetY})},handleMouseup:function(t){this.writeEnd({x:t.offsetX,y:t.offsetY})},handleMouseleave:function(t){this.config.isWrite=!1,this.config.lastPoint={x:t.offsetX,y:t.offsetY}},handleTouchstart:function(t){var n=t.targetTouches[0],e=n.clientX?n.clientX-this.getRect().left:n.pageX-this.offset(n.target,"left"),i=n.clientY?n.clientY-this.getRect().top:n.pageY-this.offset(n.target,"top");this.writeBegin({x:e,y:i})},handleTouchmove:function(t){var n=t.targetTouches[0],e=n.clientX?n.clientX-this.getRect().left:n.pageX-this.offset(n.target,"left"),i=n.clientY?n.clientY-this.getRect().top:n.pageY-this.offset(n.target,"top");this.config.isWrite&&this.writing({x:e,y:i})},handleTouchend:function(t){var n=t.targetTouches,e=t.changedTouches,i=n&&n.length&&n[0]||e&&e.length&&e[0],o=i.clientX?i.clientX-this.getRect().left:i.pageX-this.offset(i.target,"left"),r=i.clientY?i.clientY-this.getRect().top:i.pageY-this.offset(i.target,"top");this.writeEnd({x:o,y:r})},downloadSignImg:function(t){var n=document.getElementById(this.domId),e=n.toDataURL("image/png");this.saveFile(e,t?"".concat(t,".").concat(this.config.imgType):"".concat(Date.parse(new Date),".").concat(this.config.imgType))},saveFile:function(t,n){var e=document.createElementNS("http://www.w3.org/1999/xhtml","a");e.href=t,e.download=n;var i=document.createEvent("MouseEvents");i.initMouseEvent("click",!0,!1,window,0,0,0,0,0,!1,!1,!1,!1,0,null),e.dispatchEvent(i)},getRect:function(){return this.$refs[this.domId].getBoundingClientRect()},offset:function(t,n){var e="offset"+n[0].toUpperCase()+n.substring(1),i=t[e],o=t.offsetParent;while(null!=o)i+=o[e],o=o.offsetParent;return i}}}),s=c;function a(t,n,e,i,o,r,c,s){var a,f="function"===typeof t?t.options:t;if(n&&(f.render=n,f.staticRenderFns=e,f._compiled=!0),i&&(f.functional=!0),r&&(f._scopeId="data-v-"+r),c?(a=function(t){t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,t||"undefined"===typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),o&&o.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(c)},f._ssrRegister=a):o&&(a=s?function(){o.call(this,this.$root.$options.shadowRoot)}:o),a)if(f.functional){f._injectStyles=a;var u=f.render;f.render=function(t,n){return a.call(n),u(t,n)}}else{var h=f.beforeCreate;f.beforeCreate=h?[].concat(h,a):[a]}return{exports:t,options:f}}var f=a(s,o,r,!1,null,null,null),u=f.exports;u.install=function(t){t.component(u.name,u)};var h=u;n["default"]=h}})}))}}]);
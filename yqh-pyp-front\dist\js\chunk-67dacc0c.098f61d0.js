(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-67dacc0c","chunk-37a545c8"],{"1b69":function(t,i,e){"use strict";e.r(i);e("7f7f");var s,n=function(){var t=this,i=t._self._c;return i("div",{staticStyle:{background:"white","margin-bottom":"20px"}},[i("van-row",{attrs:{gutter:"20"}},[i("van-col",{attrs:{span:"16"}},[i("van-swipe",{staticStyle:{width:"100%"},attrs:{autoplay:3e3}},t._l(t.activityInfo.appFileList,(function(t,e){return i("van-swipe-item",{key:e},[i("van-image",{attrs:{width:"100%",src:t.url}})],1)})),1)],1),i("van-col",{attrs:{span:"8"}},[i("div",{staticStyle:{"margin-top":"20px"}},[i("van-cell",{attrs:{title:"会议名称",value:t.activityInfo.name}}),t.activityInfo.address?i("van-cell",{attrs:{title:"会议地点",value:t.activityInfo.address}}):t._e(),i("van-cell",{attrs:{title:"会议开始时间",value:t.activityInfo.startTime}}),i("van-cell",{attrs:{title:"会议结束时间",value:t.activityInfo.endTime}}),i("van-cell",{attrs:{title:"登录信息"},scopedSlots:t._u([{key:"default",fn:function(){return[t.userInfo?i("van-button",{attrs:{type:"primary",size:"small",round:""}},[t._v(t._s(t.userInfo.username)+"("+t._s(t.userInfo.mobile)+")")]):i("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.showLogin}},[t._v("未登录(点击登录)")])]},proxy:!0}])}),"1736999159118508033"!=t.activityId?i("van-cell",{attrs:{title:"报名状态"},scopedSlots:t._u([{key:"default",fn:function(){return[1==t.isPay?i("van-button",{attrs:{type:"primary",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名")]):2==t.isPay?i("van-button",{attrs:{type:"warning",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名未交费")]):i("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.turnApply}},[t._v("未报名")])]},proxy:!0}],null,!1,567203517)}):t._e()],1)])],1),i("van-tabs",{staticClass:"nav",on:{click:t.onClick},model:{value:t.cmsId,callback:function(i){t.cmsId=i},expression:"cmsId"}},t._l(t.cmsList,(function(t){return i("van-tab",{key:t.id,attrs:{title:t.title,name:t.id}})})),1),i("pclogin")],1)},a=[],o=e("ade3"),c=(e("a481"),e("6762"),e("2fdb"),e("cacf")),r=e("7dcb"),l=function(){var t=this,i=t._self._c;return i("van-dialog",{attrs:{title:"登录操作",width:"400px"},on:{confirm:t.login},model:{value:t.showPcLogin,callback:function(i){t.showPcLogin=i},expression:"showPcLogin"}},[i("div",{staticClass:"text-center padding"},[i("van-cell-group",{attrs:{inset:""}},[i("van-field",{attrs:{name:"手机号",label:"手机号",required:"",placeholder:"手机号",rules:[{required:!0,message:"请填写手机号"}]},model:{value:t.mobile,callback:function(i){t.mobile=i},expression:"mobile"}}),"1736999159118508033"!=t.activityId?i("van-field",{attrs:{center:"",clearable:"",maxlength:"6",label:"短信验证码",required:"",placeholder:"请输入短信验证码"},scopedSlots:t._u([{key:"button",fn:function(){return[i("van-button",{attrs:{size:"small",type:"primary",disabled:t.waiting},on:{click:function(i){return t.doSendSmsCode()}}},[t.waiting?i("span",[t._v(t._s(t.waitingTime)+"秒后重发")]):i("span",{staticStyle:{"font-size":"13px"}},[t._v("获取验证码")])])]},proxy:!0}],null,!1,850170227),model:{value:t.code,callback:function(i){t.code=i},expression:"code"}}):t._e()],1)],1)])},u=[],d={name:"pclogin",computed:{showPcLogin:{get:function(){return this.$store.state.user.showPcLogin},set:function(t){this.$store.commit("user/changePcLogin",t)}}},data:function(){return{activityId:void 0,waiting:!1,waitingTime:60,mobile:"",code:""}},mounted:function(){this.activityId=this.$route.query.id},methods:{login:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?this.code||0x181b0f883e693000==this.activityId?/^\d{6}$/.test(this.code)||0x181b0f883e693000==this.activityId?void this.$fly.post("/pyp/web/user/pcLogin",{mobile:this.mobile,code:0x181b0f883e693000==this.activityId?"121212":this.code}).then((function(i){i&&200===i.code?(vant.Toast("登录成功"),t.$store.commit("user/update",i.userInfo),t.showPcLogin=!1,"applyIndex"!=t.$route.name&&"livesDetail"!=t.$route.name||location.reload()):vant.Toast(i.msg)})):(vant.Toast("验证码错误"),!1):(vant.Toast("请输入验证码"),!1):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},doSendSmsCode:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.mobile,activityId:this.activityId}).then((function(i){i&&200===i.code?(t.countdown(),vant.Toast("发送验证码成功")):vant.Toast(i.msg)})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var t=this;this.waiting=!0;var i=window.setInterval((function(){t.waitingTime--,t.waitingTime<0&&(window.clearInterval(i),t.waitingTime=60,t.waiting=!1)}),1e3)}}},v=d,m=e("2877"),h=Object(m["a"])(v,l,u,!1,null,null,null),f=h.exports,g={components:{pclogin:f},data:function(){return{activityId:void 0,bannerIndex:0,cmsId:void 0,cmsList:[],cmsInfo:{},activityInfo:{}}},computed:{userInfo:{get:function(){return this.$store.state.user.userInfo},set:function(t){this.$store.commit("user/update",t)}},isPay:{get:function(){return this.$store.state.apply.isPay},set:function(t){this.$store.commit("apply/update",t)}}},mounted:function(){var t=(new Date).getTime();localStorage.setItem("logAddTime",t+36e5),this.openid=this.$cookie.get("openid"),this.activityId=this.$route.query.id,this.cmsId=sessionStorage.getItem("cmsId")||this.$route.query.cmsId,this.checkLogin(),this.getActivityInfo(),this.activityLogCount()},methods:(s={showLogin:function(){this.$store.commit("user/changePcLogin",!0)},checkLogin:function(){var t=this;this.$fly.get("/pyp/web/user/checkLogin").then((function(i){t.userInfo=i.result,t.userInfo?t.checkApply():t.isPay=0}))},bannerIndexChange:function(t){this.bannerIndex=t},checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(i){200==i.code?(t.isPay=i.isPay,1==t.isPay||2==t.isPay&&vant.Dialog.alert({title:"提示",message:"您有一笔注册费待支付，点击跳转"}).then((function(){t.$router.push({name:"applySuccess",query:{orderId:i.result,id:t.activityId}})}))):vant.Toast(i.msg)}))},changeShowType:function(t){this.$emit("changeShowType",t)},turnApply:function(){var t=this.cmsList.filter((function(t){return t.model&&t.model.includes("applyIndex")}));if(t){var i=t[0];sessionStorage.setItem("cmsId",i.id);var e=i.model.replace("${activityId}",i.activityId);this.$router.push(JSON.parse(e))}else vant.Toast("暂未开启报名")},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(i){if(t.loading=!1,200==i.code){t.activityInfo=i.activity,document.title=t.activityInfo.name;var e=t.activityInfo.startTime,s=new Date(e.replace(/-/g,"/")),n=new Date,a=s.getTime()-n.getTime();t.dateCompare=a>0?a:0,t.getCmsList()}}))},activityLogCount:function(){var t=localStorage.getItem("logAddTime"),i=(new Date).getTime();i>t&&this.$fly.post("/pyp/activity/activityviewlog/count",{activityId:this.activityId,device:r["a"].getVersion()}).then((function(t){}))},getCmsList:function(){var t=this;this.$fly.get("/pyp/cms/cms/findByActivityId/".concat(this.activityId)).then((function(i){t.loading=!1,200==i.code?(t.cmsList=i.result,t.cmsId?t.changeShowType(t.cmsId):t.changeShowType(t.cmsList[0].id)):(vant.Toast(i.msg),t.cmsList=[])}))},onClick:function(t){if(console.log(t),sessionStorage.setItem("cmsId",t),this.cmsInfo=this.cmsList.filter((function(i){return i.id==t}))[0],this.cmsInfo.url&&Object(c["d"])(this.cmsInfo.url))location.href=this.cmsInfo.url;else if(this.cmsInfo.model&&this.isJSON(this.cmsInfo.model)){var i=this.cmsInfo.model.replace("${activityId}",this.cmsInfo.activityId);this.$router.push(JSON.parse(i))}else"cmsIndex"==this.$route.name?this.changeShowType(t):this.$router.push({name:"cmsIndex",query:{id:this.activityId,cmsId:t}})}},Object(o["a"])(s,"bannerIndexChange",(function(t){this.bannerIndex=t})),Object(o["a"])(s,"isJSON",(function(t){try{return JSON.parse(t),!0}catch(i){return!1}})),s)},p=g,y=(e("dd7a"),Object(m["a"])(p,n,a,!1,null,"7bd3d808",null));i["default"]=y.exports},"7dcb":function(t,i,e){"use strict";e("a481"),e("4917");i["a"]={getVersion:function(){var t=navigator.userAgent||window.navigator.userAgent,i=/HUAWEI|HONOR/gi,e=/[^;]+(?= Build)/gi,s=/CPU iPhone OS \d[_\d]*/gi,n=/CPU OS \d[_\d]*/gi,a=/Windows NT \d[\.\d]*/gi,o=/Linux x\d[_\d]*/gi,c=/Mac OS X \d[_|\.\d]*/gi;return/Android/gi.test(t)?i.test(t)?t.match(i)[0]+t.match(e)[0]:t.match(e)[0]:/iPhone/gi.test(t)?t.match(s)[0].replace(/CPU iPhone OS/gi,"iPhone").replace(/_/g,"."):/iPad/gi.test(t)?t.match(n)[0].replace(/CPU OS/gi,"iPad").replace(/_/g,"."):/Windows/gi.test(t)?"Windows "+(Math.min(parseInt(t.match(a)[0].replace(/Windows NT /,""))+1,10)<7?"XP":Math.min(parseInt(t.match(a)[0].replace(/Windows NT /,""))+1,10)):/Linux/gi.test(t)?t.match(o)[0]:/Macintosh/gi.test(t)?t.match(c)[0].replace(/_/g,"."):"unknown"}}},ac67:function(t,i,e){"use strict";e("cd11")},ade3:function(t,i,e){"use strict";e.d(i,"a",(function(){return o}));var s=e("53ca");function n(t,i){if("object"!==Object(s["a"])(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,i||"default");if("object"!==Object(s["a"])(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===i?String:Number)(t)}function a(t){var i=n(t,"string");return"symbol"===Object(s["a"])(i)?i:String(i)}function o(t,i,e){return i=a(i),i in t?Object.defineProperty(t,i,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[i]=e,t}},c579:function(t,i,e){"use strict";e.r(i);e("7f7f");var s=function(){var t=this,i=t._self._c;return i("div",{staticClass:"profile-page"},[t.isMobilePhone?t._e():i("pcheader"),i("div",{staticClass:"header-section"},[i("div",{staticClass:"header-bg"},[i("div",{staticClass:"user-card"},[i("div",{staticClass:"user-avatar"},[t.userInfo.headimgurl?i("img",{attrs:{src:t.userInfo.headimgurl,alt:"头像"}}):i("van-icon",{attrs:{name:"user-circle-o",size:"60"}})],1),i("div",{staticClass:"user-info"},[i("h2",{staticClass:"user-name"},[t._v(t._s(t.userInfo.nickname||"用户"))]),i("div",{staticClass:"user-contact"},[t.userInfo.mobile?i("div",{staticClass:"mobile-info"},[i("van-icon",{attrs:{name:"phone-o",size:"14"}}),i("span",[t._v(t._s(t.userInfo.mobile))])],1):i("div",{staticClass:"mobile-warning",on:{click:t.goToBindMobile}},[i("van-icon",{attrs:{name:"warning-o",size:"14"}}),i("span",[t._v("未绑定手机号，点击绑定")]),i("van-icon",{attrs:{name:"arrow",size:"12"}})],1)])])])])]),i("div",{staticClass:"content-section"},[t.isSalesman?i("div",{staticClass:"salesman-section"},[i("div",{staticClass:"salesman-card"},[i("div",{staticClass:"card-header"},[i("div",{staticClass:"header-icon"},[i("van-icon",{attrs:{name:"manager-o",size:"20"}})],1),t._m(0)]),i("div",{staticClass:"salesman-info"},[i("div",{staticClass:"info-grid"},[i("div",{staticClass:"info-item"},[i("span",{staticClass:"label"},[t._v("姓名")]),i("span",{staticClass:"value"},[t._v(t._s(t.salesmanInfo.name))])]),i("div",{staticClass:"info-item"},[i("span",{staticClass:"label"},[t._v("编号")]),i("span",{staticClass:"value"},[t._v(t._s(t.salesmanInfo.code))])]),t.salesmanInfo.department?i("div",{staticClass:"info-item"},[i("span",{staticClass:"label"},[t._v("部门")]),i("span",{staticClass:"value"},[t._v(t._s(t.salesmanInfo.department))])]):t._e(),t.salesmanInfo.position?i("div",{staticClass:"info-item"},[i("span",{staticClass:"label"},[t._v("职位")]),i("span",{staticClass:"value"},[t._v(t._s(t.salesmanInfo.position))])]):t._e(),i("div",{staticClass:"info-item"},[i("span",{staticClass:"label"},[t._v("层级")]),i("span",{staticClass:"value"},[t._v("第"+t._s(t.salesmanInfo.level)+"级")])]),i("div",{staticClass:"info-item",on:{click:t.goToCommissionRules}},[i("span",{staticClass:"label"},[t._v("抽成规则")]),i("span",{staticClass:"value"},[t._v("查看")])])])])]),i("div",{staticClass:"stats-card",on:{click:t.goToOrderList}},[i("div",{staticClass:"card-header"},[i("div",{staticClass:"header-icon stats-icon"},[i("van-icon",{attrs:{name:"chart-trending-o",size:"20"}})],1),t._m(1)]),i("div",{staticClass:"stats-grid"},[i("div",{staticClass:"stat-item"},[i("div",{staticClass:"stat-number"},[t._v(t._s(t.salesmanStats.totalOrders||0))]),i("div",{staticClass:"stat-label"},[t._v("总订单")])]),i("div",{staticClass:"stat-item"},[i("div",{staticClass:"stat-number"},[t._v("¥"+t._s((t.salesmanStats.totalAmount||0).toFixed(2)))]),i("div",{staticClass:"stat-label"},[t._v("销售额")])]),i("div",{staticClass:"stat-item"},[i("div",{staticClass:"stat-number"},[t._v("¥"+t._s((t.salesmanStats.totalCommission||0).toFixed(2)))]),i("div",{staticClass:"stat-label"},[t._v("佣金收入")])]),i("div",{staticClass:"stat-item"},[i("div",{staticClass:"stat-number"},[t._v(t._s(t.childrenCount||0))]),i("div",{staticClass:"stat-label"},[t._v("下级业务员")])])])]),i("div",{staticClass:"actions-card"},[i("div",{staticClass:"actions-grid"},[i("div",{staticClass:"action-item",on:{click:t.goToInviteCustomer}},[i("div",{staticClass:"action-icon customer-icon"},[i("van-icon",{attrs:{name:"user-o",size:"24"}})],1),i("span",{staticClass:"action-text"},[t._v("邀请客户")])]),i("div",{staticClass:"action-item",on:{click:t.goToMyCustomers}},[i("div",{staticClass:"action-icon customers-icon"},[i("van-icon",{attrs:{name:"contact",size:"24"}})],1),i("span",{staticClass:"action-text"},[t._v("我的客户")])]),i("div",{staticClass:"action-item",on:{click:t.goToCommissionRules}},[i("div",{staticClass:"action-icon commission-icon"},[i("van-icon",{attrs:{name:"gold-coin-o",size:"24"}})],1),i("span",{staticClass:"action-text"},[t._v("抽成规则")])])])])]):t._e(),i("div",{staticClass:"settings-section"},[i("div",{staticClass:"settings-card"},[i("div",{staticClass:"card-header"},[i("div",{staticClass:"header-icon settings-icon"},[i("van-icon",{attrs:{name:"setting-o",size:"20"}})],1),t._m(2)]),i("div",{staticClass:"settings-list"},[i("div",{staticClass:"setting-item",on:{click:t.goToUserAgreement}},[i("div",{staticClass:"setting-content"},[i("div",{staticClass:"setting-icon"},[i("van-icon",{attrs:{name:"description",size:"18"}})],1),i("span",{staticClass:"setting-text"},[t._v("用户协议")])]),i("van-icon",{attrs:{name:"arrow",size:"16"}})],1),i("div",{staticClass:"setting-item",on:{click:t.goToPrivacyPolicy}},[i("div",{staticClass:"setting-content"},[i("div",{staticClass:"setting-icon"},[i("van-icon",{attrs:{name:"shield-o",size:"18"}})],1),i("span",{staticClass:"setting-text"},[t._v("隐私政策")])]),i("van-icon",{attrs:{name:"arrow",size:"16"}})],1),i("div",{staticClass:"setting-item logout-item",on:{click:t.handleLogout}},[i("div",{staticClass:"setting-content"},[i("div",{staticClass:"setting-icon logout-icon"},[i("van-icon",{attrs:{name:"stop-circle-o",size:"18"}})],1),i("span",{staticClass:"setting-text"},[t._v("退出登录")])]),i("van-icon",{attrs:{name:"arrow",size:"16"}})],1)])])])])],1)},n=[function(){var t=this,i=t._self._c;return i("div",{staticClass:"header-text"},[i("h3",[t._v("业务员中心")]),i("p",[t._v("专属业务员信息")])])},function(){var t=this,i=t._self._c;return i("div",{staticClass:"header-text"},[i("h3",[t._v("业务统计")]),i("p",[t._v("数据概览")])])},function(){var t=this,i=t._self._c;return i("div",{staticClass:"header-text"},[i("h3",[t._v("设置")]),i("p",[t._v("账户设置与帮助")])])}],a=(e("a481"),e("96cf"),e("1da1")),o=e("cacf"),c=e("1b69"),r=e("3e34"),l={components:{pcheader:c["default"]},data:function(){return{isMobilePhone:Object(o["c"])(),userInfo:{},activityId:null,isSalesman:!1,salesmanInfo:{},salesmanStats:{},childrenCount:0,showSalesmanDetails:!1}},computed:{userActivities:function(){return this.$store.state.activity.userActivities},selectedActivityId:function(){return this.$store.state.activity.selectedActivityId},currentActivity:function(){return this.$store.state.activity.currentActivity},activityOptions:function(){return this.$store.state.activity.activityOptions},currentActivityName:function(){return this.$store.getters["activity/currentActivityName"]},hasActivities:function(){return this.$store.getters["activity/hasActivities"]}},watch:{currentActivity:{handler:function(t,i){t&&i&&t.id!==i.id&&(console.log("Profile页面检测到活动变化:",t),this.activityId=t.id)},deep:!0},selectedActivityId:function(t,i){t!==i&&t&&(console.log("Profile页面检测到选中活动ID变化:",t),this.activityId=t)}},mounted:function(){document.title="我的",this.$wxShare(this.$cookie.get("accountName"),this.$cookie.get("logo"),this.$cookie.get("slog"));var t=this.$route.query.activityId;if(t)this.activityId=t;else{var i=this.$store.state.activity.selectedActivityId;i&&(this.activityId=i)}this.checkMobileBinding()},activated:function(){console.log("Profile页面被激活，检查活动状态同步"),this.checkActivitySync()},methods:{checkMobileBinding:function(){var t=this;console.log("开始检查用户手机号绑定状态"),Object(r["checkAndRedirectToBindMobile"])(window.location.href).then((function(i){i&&(console.log("用户已绑定手机号，继续加载页面数据"),t.getUserInfo())})).catch((function(i){console.error("检查手机号绑定状态失败:",i),t.getUserInfo()}))},goToOrderList:function(){this.$router.push({name:"salesmanOrders"})},getUserInfo:function(){var t=this;this.$fly.get("/pyp/wxUser/getUserInfo").then((function(i){200==i.code?(t.userInfo=i.data,t.initActivity(),t.checkSalesmanStatus()):Object(r["handleMobileError"])(i,window.location.href)||vant.Toast(i.msg)})).catch((function(t){console.error("获取用户信息失败:",t),vant.Toast("获取用户信息失败，请重试")}))},initActivity:function(){var t=Object(a["a"])(regeneratorRuntime.mark((function t(){var i,e,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return console.log("开始初始化活动数据...","preferredActivityId:",this.activityId),t.prev=1,i={api:this.$fly,toast:this.$toast},e=this.$route.query.activityId,e&&"undefined"!==e&&null!==e?(i.preferredActivityId=parseInt(e),console.log("使用URL参数作为首选活动ID:",i.preferredActivityId)):console.log("不传递preferredActivityId，让系统使用缓存的活动ID"),t.next=7,this.$store.dispatch("activity/initializeActivity",i);case 7:s=t.sent,s.success?(console.log("活动数据初始化成功:",s.data),this.activityId=this.selectedActivityId):console.error("活动数据初始化失败:",s.error),t.next=14;break;case 11:t.prev=11,t.t0=t["catch"](1),console.error("初始化活动数据时发生错误:",t.t0);case 14:case"end":return t.stop()}}),t,this,[[1,11]])})));function i(){return t.apply(this,arguments)}return i}(),checkActivitySync:function(){this.hasActivities?this.currentActivity&&this.activityId!==this.currentActivity.id&&(console.log("Profile页面检测到活动状态不一致，同步状态"),this.activityId=this.currentActivity.id):(console.log("Profile页面检测到没有活动数据，重新初始化"),this.initActivity())},onActivityChange:function(){var t=Object(a["a"])(regeneratorRuntime.mark((function t(i){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return console.log("Activity changed to:",i),t.prev=1,t.next=4,this.$store.dispatch("activity/switchActivity",i);case 4:e=t.sent,e.success?(console.log("活动切换成功:",e.activity),this.activityId=i):(console.error("活动切换失败:",e.error),vant.Toast("切换活动失败")),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](1),console.error("切换活动时发生错误:",t.t0),vant.Toast("切换活动失败");case 12:case"end":return t.stop()}}),t,this,[[1,8]])})));function i(i){return t.apply(this,arguments)}return i}(),goToBindMobile:function(){this.$router.push({name:"bindMobile",query:{returnUrl:encodeURIComponent(window.location.href)}})},goToUserAgreement:function(){this.$router.push({name:"userAgreement"})},goToPrivacyPolicy:function(){this.$router.push({name:"privacyPolicy"})},handleLogout:function(){var t=this;vant.Dialog.confirm({title:"确认退出",message:"确定要退出登录吗？",confirmButtonText:"确定",cancelButtonText:"取消"}).then((function(){t.performLogout()})).catch((function(){console.log("用户取消退出登录")}))},performLogout:function(){var t=Object(a["a"])(regeneratorRuntime.mark((function t(){var i,e=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$fly.post("/pyp/sys/logout");case 3:i=t.sent,200===i.code?(this.clearLocalData(),vant.Toast("退出登录成功"),setTimeout((function(){e.navigateToLogin()}),1e3)):(console.warn("后端退出登录接口失败，但仍清除本地数据:",i.msg),this.clearLocalData(),vant.Toast("退出登录成功"),setTimeout((function(){e.navigateToLogin()}),1e3)),t.next=13;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("退出登录请求失败:",t.t0),this.clearLocalData(),vant.Toast("退出登录成功"),setTimeout((function(){e.navigateToLogin()}),1e3);case 13:case"end":return t.stop()}}),t,this,[[0,7]])})));function i(){return t.apply(this,arguments)}return i}(),clearLocalData:function(){this.$cookie.delete("token"),localStorage.removeItem("userInfo"),sessionStorage.removeItem("userInfo"),this.$store.commit("user/logout"),this.$store.commit("activity/SET_USER_ACTIVITIES",[]),this.$store.commit("activity/SET_SELECTED_ACTIVITY",{activityId:null,activity:null})},navigateToLogin:function(){try{console.log("=== profile 跳转登录开始 ==="),console.log("当前URL:",window.location.href);var t=encodeURIComponent(window.location.href);console.log("编码后的returnUrl:",t),this.isMobilePhone?this.$router.replace({name:"mobileLogin",query:{returnUrl:t}}):this.$store.commit("user/changePcLogin",!0),console.log("=== profile 跳转登录结束 ===")}catch(i){console.error("跳转登录页面失败:",i),window.location.reload()}},checkSalesmanStatus:function(){var t=Object(a["a"])(regeneratorRuntime.mark((function t(){var i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$fly.get("/pyp/web/salesman/checkSalesmanStatus");case 3:i=t.sent,200===i.code&&i.isSalesman?(this.isSalesman=!0,this.salesmanInfo=i.salesman,this.salesmanStats=i.stats||{},this.childrenCount=i.childrenCount||0,console.log("业务员身份验证成功:",i)):(this.isSalesman=!1,console.log("用户不是业务员:",i.message)),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("检查业务员身份失败:",t.t0),this.isSalesman=!1;case 11:case"end":return t.stop()}}),t,this,[[0,7]])})));function i(){return t.apply(this,arguments)}return i}(),goToChildrenManage:function(){this.$router.push({name:"salesmanChildren",query:{}})},goToInviteManage:function(){this.$router.push({name:"salesmanInvite",query:{}})},goToInviteCustomer:function(){this.$router.push({name:"salesmanInviteCustomer",query:{}})},goToMyCustomers:function(){this.$router.push({name:"salesmanMyCustomers",query:{}})},goToCommissionRules:function(){this.$router.push({name:"salesmanCommissionRules"})}}},u=l,d=(e("ac67"),e("2877")),v=Object(d["a"])(u,s,n,!1,null,"540ac7d6",null);i["default"]=v.exports},cad8:function(t,i,e){},cd11:function(t,i,e){},dd7a:function(t,i,e){"use strict";e("cad8")}}]);
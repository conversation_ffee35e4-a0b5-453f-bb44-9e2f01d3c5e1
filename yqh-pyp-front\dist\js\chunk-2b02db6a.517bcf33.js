(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2b02db6a","chunk-37a545c8"],{"1b69":function(t,i,e){"use strict";e.r(i);e("7f7f");var n,a=function(){var t=this,i=t._self._c;return i("div",{staticStyle:{background:"white","margin-bottom":"20px"}},[i("van-row",{attrs:{gutter:"20"}},[i("van-col",{attrs:{span:"16"}},[i("van-swipe",{staticStyle:{width:"100%"},attrs:{autoplay:3e3}},t._l(t.activityInfo.appFileList,(function(t,e){return i("van-swipe-item",{key:e},[i("van-image",{attrs:{width:"100%",src:t.url}})],1)})),1)],1),i("van-col",{attrs:{span:"8"}},[i("div",{staticStyle:{"margin-top":"20px"}},[i("van-cell",{attrs:{title:"会议名称",value:t.activityInfo.name}}),t.activityInfo.address?i("van-cell",{attrs:{title:"会议地点",value:t.activityInfo.address}}):t._e(),i("van-cell",{attrs:{title:"会议开始时间",value:t.activityInfo.startTime}}),i("van-cell",{attrs:{title:"会议结束时间",value:t.activityInfo.endTime}}),i("van-cell",{attrs:{title:"登录信息"},scopedSlots:t._u([{key:"default",fn:function(){return[t.userInfo?i("van-button",{attrs:{type:"primary",size:"small",round:""}},[t._v(t._s(t.userInfo.username)+"("+t._s(t.userInfo.mobile)+")")]):i("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.showLogin}},[t._v("未登录(点击登录)")])]},proxy:!0}])}),"1736999159118508033"!=t.activityId?i("van-cell",{attrs:{title:"报名状态"},scopedSlots:t._u([{key:"default",fn:function(){return[1==t.isPay?i("van-button",{attrs:{type:"primary",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名")]):2==t.isPay?i("van-button",{attrs:{type:"warning",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名未交费")]):i("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.turnApply}},[t._v("未报名")])]},proxy:!0}],null,!1,567203517)}):t._e()],1)])],1),i("van-tabs",{staticClass:"nav",on:{click:t.onClick},model:{value:t.cmsId,callback:function(i){t.cmsId=i},expression:"cmsId"}},t._l(t.cmsList,(function(t){return i("van-tab",{key:t.id,attrs:{title:t.title,name:t.id}})})),1),i("pclogin")],1)},s=[],o=e("ade3"),c=(e("a481"),e("6762"),e("2fdb"),e("cacf")),r=e("7dcb"),u=function(){var t=this,i=t._self._c;return i("van-dialog",{attrs:{title:"登录操作",width:"400px"},on:{confirm:t.login},model:{value:t.showPcLogin,callback:function(i){t.showPcLogin=i},expression:"showPcLogin"}},[i("div",{staticClass:"text-center padding"},[i("van-cell-group",{attrs:{inset:""}},[i("van-field",{attrs:{name:"手机号",label:"手机号",required:"",placeholder:"手机号",rules:[{required:!0,message:"请填写手机号"}]},model:{value:t.mobile,callback:function(i){t.mobile=i},expression:"mobile"}}),"1736999159118508033"!=t.activityId?i("van-field",{attrs:{center:"",clearable:"",maxlength:"6",label:"短信验证码",required:"",placeholder:"请输入短信验证码"},scopedSlots:t._u([{key:"button",fn:function(){return[i("van-button",{attrs:{size:"small",type:"primary",disabled:t.waiting},on:{click:function(i){return t.doSendSmsCode()}}},[t.waiting?i("span",[t._v(t._s(t.waitingTime)+"秒后重发")]):i("span",{staticStyle:{"font-size":"13px"}},[t._v("获取验证码")])])]},proxy:!0}],null,!1,850170227),model:{value:t.code,callback:function(i){t.code=i},expression:"code"}}):t._e()],1)],1)])},l=[],d={name:"pclogin",computed:{showPcLogin:{get:function(){return this.$store.state.user.showPcLogin},set:function(t){this.$store.commit("user/changePcLogin",t)}}},data:function(){return{activityId:void 0,waiting:!1,waitingTime:60,mobile:"",code:""}},mounted:function(){this.activityId=this.$route.query.id},methods:{login:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?this.code||0x181b0f883e693000==this.activityId?/^\d{6}$/.test(this.code)||0x181b0f883e693000==this.activityId?void this.$fly.post("/pyp/web/user/pcLogin",{mobile:this.mobile,code:0x181b0f883e693000==this.activityId?"121212":this.code}).then((function(i){i&&200===i.code?(vant.Toast("登录成功"),t.$store.commit("user/update",i.userInfo),t.showPcLogin=!1,"applyIndex"!=t.$route.name&&"livesDetail"!=t.$route.name||location.reload()):vant.Toast(i.msg)})):(vant.Toast("验证码错误"),!1):(vant.Toast("请输入验证码"),!1):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},doSendSmsCode:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.mobile,activityId:this.activityId}).then((function(i){i&&200===i.code?(t.countdown(),vant.Toast("发送验证码成功")):vant.Toast(i.msg)})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var t=this;this.waiting=!0;var i=window.setInterval((function(){t.waitingTime--,t.waitingTime<0&&(window.clearInterval(i),t.waitingTime=60,t.waiting=!1)}),1e3)}}},f=d,v=e("2877"),h=Object(v["a"])(f,u,l,!1,null,null,null),g=h.exports,m={components:{pclogin:g},data:function(){return{activityId:void 0,bannerIndex:0,cmsId:void 0,cmsList:[],cmsInfo:{},activityInfo:{}}},computed:{userInfo:{get:function(){return this.$store.state.user.userInfo},set:function(t){this.$store.commit("user/update",t)}},isPay:{get:function(){return this.$store.state.apply.isPay},set:function(t){this.$store.commit("apply/update",t)}}},mounted:function(){var t=(new Date).getTime();localStorage.setItem("logAddTime",t+36e5),this.openid=this.$cookie.get("openid"),this.activityId=this.$route.query.id,this.cmsId=sessionStorage.getItem("cmsId")||this.$route.query.cmsId,this.checkLogin(),this.getActivityInfo(),this.activityLogCount()},methods:(n={showLogin:function(){this.$store.commit("user/changePcLogin",!0)},checkLogin:function(){var t=this;this.$fly.get("/pyp/web/user/checkLogin").then((function(i){t.userInfo=i.result,t.userInfo?t.checkApply():t.isPay=0}))},bannerIndexChange:function(t){this.bannerIndex=t},checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(i){200==i.code?(t.isPay=i.isPay,1==t.isPay||2==t.isPay&&vant.Dialog.alert({title:"提示",message:"您有一笔注册费待支付，点击跳转"}).then((function(){t.$router.push({name:"applySuccess",query:{orderId:i.result,id:t.activityId}})}))):vant.Toast(i.msg)}))},changeShowType:function(t){this.$emit("changeShowType",t)},turnApply:function(){var t=this.cmsList.filter((function(t){return t.model&&t.model.includes("applyIndex")}));if(t){var i=t[0];sessionStorage.setItem("cmsId",i.id);var e=i.model.replace("${activityId}",i.activityId);this.$router.push(JSON.parse(e))}else vant.Toast("暂未开启报名")},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(i){if(t.loading=!1,200==i.code){t.activityInfo=i.activity,document.title=t.activityInfo.name;var e=t.activityInfo.startTime,n=new Date(e.replace(/-/g,"/")),a=new Date,s=n.getTime()-a.getTime();t.dateCompare=s>0?s:0,t.getCmsList()}}))},activityLogCount:function(){var t=localStorage.getItem("logAddTime"),i=(new Date).getTime();i>t&&this.$fly.post("/pyp/activity/activityviewlog/count",{activityId:this.activityId,device:r["a"].getVersion()}).then((function(t){}))},getCmsList:function(){var t=this;this.$fly.get("/pyp/cms/cms/findByActivityId/".concat(this.activityId)).then((function(i){t.loading=!1,200==i.code?(t.cmsList=i.result,t.cmsId?t.changeShowType(t.cmsId):t.changeShowType(t.cmsList[0].id)):(vant.Toast(i.msg),t.cmsList=[])}))},onClick:function(t){if(console.log(t),sessionStorage.setItem("cmsId",t),this.cmsInfo=this.cmsList.filter((function(i){return i.id==t}))[0],this.cmsInfo.url&&Object(c["d"])(this.cmsInfo.url))location.href=this.cmsInfo.url;else if(this.cmsInfo.model&&this.isJSON(this.cmsInfo.model)){var i=this.cmsInfo.model.replace("${activityId}",this.cmsInfo.activityId);this.$router.push(JSON.parse(i))}else"cmsIndex"==this.$route.name?this.changeShowType(t):this.$router.push({name:"cmsIndex",query:{id:this.activityId,cmsId:t}})}},Object(o["a"])(n,"bannerIndexChange",(function(t){this.bannerIndex=t})),Object(o["a"])(n,"isJSON",(function(t){try{return JSON.parse(t),!0}catch(i){return!1}})),n)},y=m,p=(e("dd7a"),Object(v["a"])(y,a,s,!1,null,"7bd3d808",null));i["default"]=p.exports},"28a5":function(t,i,e){"use strict";var n=e("aae3"),a=e("cb7c"),s=e("ebd6"),o=e("0390"),c=e("9def"),r=e("5f1b"),u=e("520a"),l=e("79e5"),d=Math.min,f=[].push,v="split",h="length",g="lastIndex",m=4294967295,y=!l((function(){RegExp(m,"y")}));e("214f")("split",2,(function(t,i,e,l){var p;return p="c"=="abbc"[v](/(b)*/)[1]||4!="test"[v](/(?:)/,-1)[h]||2!="ab"[v](/(?:ab)*/)[h]||4!="."[v](/(.?)(.?)/)[h]||"."[v](/()()/)[h]>1||""[v](/.?/)[h]?function(t,i){var a=String(this);if(void 0===t&&0===i)return[];if(!n(t))return e.call(a,t,i);var s,o,c,r=[],l=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),d=0,v=void 0===i?m:i>>>0,y=new RegExp(t.source,l+"g");while(s=u.call(y,a)){if(o=y[g],o>d&&(r.push(a.slice(d,s.index)),s[h]>1&&s.index<a[h]&&f.apply(r,s.slice(1)),c=s[0][h],d=o,r[h]>=v))break;y[g]===s.index&&y[g]++}return d===a[h]?!c&&y.test("")||r.push(""):r.push(a.slice(d)),r[h]>v?r.slice(0,v):r}:"0"[v](void 0,0)[h]?function(t,i){return void 0===t&&0===i?[]:e.call(this,t,i)}:e,[function(e,n){var a=t(this),s=void 0==e?void 0:e[i];return void 0!==s?s.call(e,a,n):p.call(String(a),e,n)},function(t,i){var n=l(p,t,this,i,p!==e);if(n.done)return n.value;var u=a(t),f=String(this),v=s(u,RegExp),h=u.unicode,g=(u.ignoreCase?"i":"")+(u.multiline?"m":"")+(u.unicode?"u":"")+(y?"y":"g"),I=new v(y?u:"^(?:"+u.source+")",g),b=void 0===i?m:i>>>0;if(0===b)return[];if(0===f.length)return null===r(I,f)?[f]:[];var k=0,w=0,x=[];while(w<f.length){I.lastIndex=y?w:0;var S,L=r(I,y?f:f.slice(w));if(null===L||(S=d(c(I.lastIndex+(y?0:w)),f.length))===k)w=o(f,w,h);else{if(x.push(f.slice(k,w)),x.length===b)return x;for(var _=1;_<=L.length-1;_++)if(x.push(L[_]),x.length===b)return x;w=k=S}}return x.push(f.slice(k)),x}]}))},2961:function(t,i,e){"use strict";e("a435")},"66c7":function(t,i,e){"use strict";e("4917"),e("a481");var n=/([yMdhsm])(\1*)/g,a="yyyy-MM-dd";function s(t,i){i-=(t+"").length;for(var e=0;e<i;e++)t="0"+t;return t}i["a"]={formatDate:{format:function(t,i){return i=i||a,i.replace(n,(function(i){switch(i.charAt(0)){case"y":return s(t.getFullYear(),i.length);case"M":return s(t.getMonth()+1,i.length);case"d":return s(t.getDate(),i.length);case"w":return t.getDay()+1;case"h":return s(t.getHours(),i.length);case"m":return s(t.getMinutes(),i.length);case"s":return s(t.getSeconds(),i.length)}}))},parse:function(t,i){var e=i.match(n),a=t.match(/(\d)+/g);if(e.length==a.length){for(var s=new Date(1970,0,1),o=0;o<e.length;o++){var c=parseInt(a[o]),r=e[o];switch(r.charAt(0)){case"y":s.setFullYear(c);break;case"M":s.setMonth(c-1);break;case"d":s.setDate(c);break;case"h":s.setHours(c);break;case"m":s.setMinutes(c);break;case"s":s.setSeconds(c);break}}return s}return null},toWeek:function(t){var i=new Date(t).getDay(),e="";switch(i){case 0:e="s";break;case 1:e="m";break;case 2:e="t";break;case 3:e="w";break;case 4:e="t";break;case 5:e="f";break;case 6:e="s";break}return e}},toUserLook:function(t){var i=Math.floor(t/3600%24),e=Math.floor(t/60%60);return i<1?e+"分":i+"时"+e+"分"}}},"7dcb":function(t,i,e){"use strict";e("a481"),e("4917");i["a"]={getVersion:function(){var t=navigator.userAgent||window.navigator.userAgent,i=/HUAWEI|HONOR/gi,e=/[^;]+(?= Build)/gi,n=/CPU iPhone OS \d[_\d]*/gi,a=/CPU OS \d[_\d]*/gi,s=/Windows NT \d[\.\d]*/gi,o=/Linux x\d[_\d]*/gi,c=/Mac OS X \d[_|\.\d]*/gi;return/Android/gi.test(t)?i.test(t)?t.match(i)[0]+t.match(e)[0]:t.match(e)[0]:/iPhone/gi.test(t)?t.match(n)[0].replace(/CPU iPhone OS/gi,"iPhone").replace(/_/g,"."):/iPad/gi.test(t)?t.match(a)[0].replace(/CPU OS/gi,"iPad").replace(/_/g,"."):/Windows/gi.test(t)?"Windows "+(Math.min(parseInt(t.match(s)[0].replace(/Windows NT /,""))+1,10)<7?"XP":Math.min(parseInt(t.match(s)[0].replace(/Windows NT /,""))+1,10)):/Linux/gi.test(t)?t.match(o)[0]:/Macintosh/gi.test(t)?t.match(c)[0].replace(/_/g,"."):"unknown"}}},a435:function(t,i,e){},ade3:function(t,i,e){"use strict";e.d(i,"a",(function(){return o}));var n=e("53ca");function a(t,i){if("object"!==Object(n["a"])(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var a=e.call(t,i||"default");if("object"!==Object(n["a"])(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===i?String:Number)(t)}function s(t){var i=a(t,"string");return"symbol"===Object(n["a"])(i)?i:String(i)}function o(t,i,e){return i=s(i),i in t?Object.defineProperty(t,i,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[i]=e,t}},cad8:function(t,i,e){},dd7a:function(t,i,e){"use strict";e("cad8")},eb34:function(t,i,e){"use strict";e.r(i);e("a481"),e("7f7f");var n=function(){var t=this,i=t._self._c;return i("div",{class:t.isMobilePhone?"":"pc-container"},[t.isMobilePhone?t._e():i("pcheader"),i("van-card",{staticStyle:{background:"white"},attrs:{thumb:t.guestInfo&&t.guestInfo.avatar?t.guestInfo.avatar:"http://mpjoy.oss-cn-beijing.aliyuncs.com/20240917/d9301e81ab674112b3acf4a84aff4e96.png"}},[i("div",{staticStyle:{"font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.guestInfo.name))]),i("div",{staticStyle:{"padding-top":"10px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[t.guestInfo.unit?i("van-tag",{staticStyle:{margin:"5px 10px 5px 0px"},attrs:{size:"medium",round:"",type:"primary",plain:""}},[t._v(t._s(t.guestInfo.unit))]):t._e(),t.guestInfo.duties?i("van-tag",{staticStyle:{margin:"5px 10px 5px 0px"},attrs:{size:"medium",round:"",type:"warning",plain:""}},[t._v(t._s(t.guestInfo.duties))]):t._e()],1)]),t._m(0),i("van-cell-group",{attrs:{inset:""}},[i("van-cell",{attrs:{center:"",title:"是否需要接站/接机"},scopedSlots:t._u([{key:"right-icon",fn:function(){return[i("van-switch",{attrs:{size:"24"},model:{value:t.guestInfo.isLinkStart,callback:function(i){t.$set(t.guestInfo,"isLinkStart",i)},expression:"guestInfo.isLinkStart"}})]},proxy:!0}])}),t.guestInfo.isLinkStart?i("van-field",{attrs:{name:"接站/接机地点",label:"接站/接机地点",placeholder:"请输入接站/接机地点",rules:[{required:!0,message:"请输入接站/接机地点"}]},model:{value:t.guestInfo.linkStart,callback:function(i){t.$set(t.guestInfo,"linkStart",i)},expression:"guestInfo.linkStart"}}):t._e(),i("van-cell",{attrs:{center:"",title:"是否需要送站/送机"},scopedSlots:t._u([{key:"right-icon",fn:function(){return[i("van-switch",{attrs:{size:"24"},model:{value:t.guestInfo.isLinkEnd,callback:function(i){t.$set(t.guestInfo,"isLinkEnd",i)},expression:"guestInfo.isLinkEnd"}})]},proxy:!0}])}),t.guestInfo.isLinkEnd?i("van-field",{attrs:{name:"送站/送机地点",label:"送站/送机地点",placeholder:"请输入送站/送机地点",rules:[{required:!0,message:"请输入送站/送机地点"}]},model:{value:t.guestInfo.linkEnd,callback:function(i){t.$set(t.guestInfo,"linkEnd",i)},expression:"guestInfo.linkEnd"}}):t._e()],1),i("div",{staticStyle:{margin:"16px"}},[i("van-button",{attrs:{round:"",block:"",type:"info",loading:t.loading,"loading-text":"提交中"},on:{click:t.submit}},[t._v("核对无误，请点击确认")])],1),i("div",{staticStyle:{margin:"16px"}},[i("van-button",{attrs:{round:"",block:"",type:"primary"},on:{click:function(i){return t.$router.replace({path:"/schedules/expertIndex",query:{detailId:t.id}})}}},[t._v("返回上一页面")])],1)],1)},a=[function(){var t=this,i=t._self._c;return i("div",{staticClass:"nav-title",staticStyle:{"margin-top":"8px"}},[i("div",{staticClass:"color"}),i("div",{staticClass:"text"},[t._v("接送确认填写")])])}],s=(e("28a5"),e("6762"),e("2fdb"),e("66c7")),o=e("cacf"),c=e("1b69"),r={components:{pcheader:c["default"]},data:function(){return{activityConfig:{},loading:!1,signVisible:!1,isMobilePhone:Object(o["c"])(),openid:void 0,activeName:["1","2","3"],activityId:void 0,id:void 0,guestInfo:{isLinkStart:!1,isLinkEnd:!1,linkStart:"",linkEnd:""},activityInfo:{}}},mounted:function(){this.id=this.$route.query.detailId,this.openid=this.$cookie.get("openid"),this.getActivityList()},methods:{preImage:function(t){vant.ImagePreview({images:[t],closeable:!0})},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(i){if(t.loading=!1,200==i.code){t.activityInfo=i.activity,t.activityInfo.backImg=t.activityInfo.backImg||"http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png",document.title="接送确认填写-"+t.guestInfo.name;var e=s["a"].formatDate.format(new Date(t.activityInfo.startTime),"yyyy年MM月dd日"),n=s["a"].formatDate.format(new Date(t.activityInfo.endTime),"MM月dd日");if(e.includes(n)){var a="时间:"+e+"\n地址:"+t.activityInfo.address;t.$wxShare(t.guestInfo.name+"-接送确认填写-"+t.activityInfo.name,t.activityInfo.shareUrl?t.activityInfo.shareUrl:t.activityInfo.mobileBanner.split(",")[0],a)}else{var o="时间:"+e+"-"+n+"\n地址:"+t.activityInfo.address;t.$wxShare(t.guestInfo.name+"-接送确认填写-"+t.activityInfo.name,t.activityInfo.shareUrl?t.activityInfo.shareUrl:t.activityInfo.mobileBanner.split(",")[0],o)}}else vant.Toast(i.msg),t.activityInfo={}}))},getActivityList:function(){var t=this;this.$fly.get("/pyp/web/activity/activityguest/getById/".concat(this.id)).then((function(i){200==i.code?(t.guestInfo=i.result,t.guestInfo.isLinkStart=!!t.guestInfo.isLinkStart,t.guestInfo.isLinkEnd=!!t.guestInfo.isLinkEnd,t.activityId=i.result.activityId,t.getActivityInfo(),t.getActivityConfig()):(vant.Toast(i.msg),t.guestInfo={})}))},getActivityConfig:function(){var t=this;this.$fly.get("/pyp/web/activity/activityConfig/check",{activityId:this.activityId,guestId:this.guestInfo.id}).then((function(i){200==i.code?(t.activityConfig=i.result,t.activityConfig.linkStart||(t.guestInfo.linkStart=t.activityConfig.linkStart),t.activityConfig.linkEnd||(t.guestInfo.linkEnd=t.activityConfig.linkEnd)):(vant.Toast(i.msg),t.activityConfig={})}))},showTask:function(){this.$router.push({name:"schedulesExpertDetail",query:{detailId:this.id,id:this.activityId}})},cmsTurnBack:function(){this.activityInfo.backUrl?window.open(this.activityInfo.backUrl):this.$router.replace({name:"cmsIndex",query:{id:this.activityInfo.id}})},submit:function(){var t=this;return this.guestInfo.isLinkStart&&!this.guestInfo.linkStart?(vant.Toast("请输入接站地点"),!1):this.guestInfo.isLinkEnd&&!this.guestInfo.linkEnd?(vant.Toast("请输入送站地点"),!1):(this.guestInfo.isLinkStart=this.guestInfo.isLinkStart?1:0,this.guestInfo.isLinkEnd=this.guestInfo.isLinkEnd?1:0,this.guestInfo.isLink=1,this.guestInfo.isLinkTime=s["a"].formatDate.format(new Date,"yyyy/MM/dd hh:mm:ss"),this.loading=!0,void this.$fly.post("/pyp/web/activity/activityguest/updateInfo",this.guestInfo).then((function(i){t.loading=!1,i&&200===i.code?vant.Dialog.confirm({title:"更新成功",message:"点击确定，返回继续完善其他信息"}).then((function(){t.$router.replace({path:"/schedules/expertIndex",query:{detailId:t.id}})})).catch((function(){t.getActivityList()})):vant.Toast(i.msg)})))}}},u=r,l=(e("2961"),e("2877")),d=Object(l["a"])(u,n,a,!1,null,"dd1bb100",null);i["default"]=d.exports}}]);
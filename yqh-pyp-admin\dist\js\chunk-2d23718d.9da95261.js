(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d23718d"],{fa51:function(t,a,e){"use strict";e.r(a);var i=function(){var t=this,a=t._self._c;return a("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(a){t.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&t._k(a.keyCode,"enter",13,a.key,"Enter")?null:t.dataFormSubmit()}}},[a("el-form-item",{attrs:{label:"标题",prop:"title"}},[a("el-input",{attrs:{placeholder:"标题"},model:{value:t.dataForm.title,callback:function(a){t.$set(t.dataForm,"title",a)},expression:"dataForm.title"}})],1),a("el-form-item",{attrs:{label:"排序",prop:"paixu"}},[a("el-input",{attrs:{placeholder:"排序"},model:{value:t.dataForm.paixu,callback:function(a){t.$set(t.dataForm,"paixu",a)},expression:"dataForm.paixu"}})],1),a("el-form-item",{attrs:{label:"详细介绍",prop:"content"}},[a("tinymce-editor",{ref:"editor",model:{value:t.dataForm.content,callback:function(a){t.$set(t.dataForm,"content",a)},expression:"dataForm.content"}})],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"经度",prop:"longitude"}},[a("el-input",{attrs:{placeholder:"经度"},model:{value:t.dataForm.longitude,callback:function(a){t.$set(t.dataForm,"longitude",a)},expression:"dataForm.longitude"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"纬度",prop:"latitude"}},[a("el-input",{attrs:{placeholder:"纬度"},model:{value:t.dataForm.latitude,callback:function(a){t.$set(t.dataForm,"latitude",a)},expression:"dataForm.latitude"}})],1)],1)],1),a("a",{staticStyle:{color:"red","margin-left":"50px"},attrs:{target:"_blank",href:"https://lbs.qq.com/tool/getpoint/index.html"}},[t._v("腾讯地图坐标拾取工具")])],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){t.visible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},o=[],r=(e("b0c0"),e("d3b7"),e("3ca3"),e("ddb0"),e("7c8d")),n=e.n(r),l={data:function(){return{visible:!1,dataForm:{repeatToken:"",id:0,title:"",activityId:"",paixu:"",longitude:"",latitude:"",color:"",content:""},dataRule:{title:[{required:!0,message:"标题不能为空",trigger:"blur"}],activityId:[{required:!0,message:"活动表id不能为空",trigger:"blur"}],paixu:[{required:!0,message:"排序不能为空",trigger:"blur"}],longitude:[{required:!0,message:"经度不能为空",trigger:"blur"}],latitude:[{required:!0,message:"纬度不能为空",trigger:"blur"}],color:[{required:!0,message:"背景颜色不能为空",trigger:"blur"}],content:[{required:!0,message:"内容不能为空",trigger:"blur"}]}}},components:{TinymceEditor:function(){return Promise.all([e.e("chunk-03be236c"),e.e("chunk-2d0a4b8c")]).then(e.bind(null,"26dc"))}},methods:{init:function(t,a){var e=this;this.getToken(),this.dataForm.id=a||0,this.dataForm.activityId=t,this.visible=!0,this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.dataForm.id&&e.$http({url:e.$http.adornUrl("/activity/activitynav/info/".concat(e.dataForm.id)),method:"get",params:e.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm.title=a.activityNav.title,e.dataForm.activityId=a.activityNav.activityId,e.dataForm.paixu=a.activityNav.paixu,e.dataForm.longitude=a.activityNav.longitude,e.dataForm.latitude=a.activityNav.latitude,e.dataForm.color=a.activityNav.color,e.dataForm.content=a.activityNav.content)}))}))},getToken:function(){var t=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(a){var e=a.data;e&&200===e.code&&(t.dataForm.repeatToken=e.result)}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(a){a&&t.$http({url:t.$http.adornUrl("/activity/activitynav/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({repeatToken:t.dataForm.repeatToken,id:t.dataForm.id||void 0,title:t.dataForm.title,activityId:t.dataForm.activityId,paixu:t.dataForm.paixu,longitude:t.dataForm.longitude,latitude:t.dataForm.latitude,color:t.dataForm.color,content:t.dataForm.content})}).then((function(a){var e=a.data;e&&200===e.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):(t.$message.error(e.msg),"不能重复提交"!=e.msg&&t.getToken())}))}))},checkFileSize:function(t){return t.size/1024/1024>6?(this.$message.error("".concat(t.name,"文件大于6MB，请选择小于6MB大小的图片")),!1):!(t.size/1024>100)||new Promise((function(a,e){new n.a(t,{quality:.8,success:function(t){a(t)}})}))},backgroundSuccessHandle:function(t,a,e){t&&200===t.code?(this.dataForm.picUrl=t.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(t.msg)}}},d=l,s=e("2877"),c=Object(s["a"])(d,i,o,!1,null,null,null);a["default"]=c.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1fd031eb","chunk-37a545c8"],{"1b69":function(t,e,i){"use strict";i.r(e);i("7f7f");var n,a=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{background:"white","margin-bottom":"20px"}},[e("van-row",{attrs:{gutter:"20"}},[e("van-col",{attrs:{span:"16"}},[e("van-swipe",{staticStyle:{width:"100%"},attrs:{autoplay:3e3}},t._l(t.activityInfo.appFileList,(function(t,i){return e("van-swipe-item",{key:i},[e("van-image",{attrs:{width:"100%",src:t.url}})],1)})),1)],1),e("van-col",{attrs:{span:"8"}},[e("div",{staticStyle:{"margin-top":"20px"}},[e("van-cell",{attrs:{title:"会议名称",value:t.activityInfo.name}}),t.activityInfo.address?e("van-cell",{attrs:{title:"会议地点",value:t.activityInfo.address}}):t._e(),e("van-cell",{attrs:{title:"会议开始时间",value:t.activityInfo.startTime}}),e("van-cell",{attrs:{title:"会议结束时间",value:t.activityInfo.endTime}}),e("van-cell",{attrs:{title:"登录信息"},scopedSlots:t._u([{key:"default",fn:function(){return[t.userInfo?e("van-button",{attrs:{type:"primary",size:"small",round:""}},[t._v(t._s(t.userInfo.username)+"("+t._s(t.userInfo.mobile)+")")]):e("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.showLogin}},[t._v("未登录(点击登录)")])]},proxy:!0}])}),"1736999159118508033"!=t.activityId?e("van-cell",{attrs:{title:"报名状态"},scopedSlots:t._u([{key:"default",fn:function(){return[1==t.isPay?e("van-button",{attrs:{type:"primary",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名")]):2==t.isPay?e("van-button",{attrs:{type:"warning",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名未交费")]):e("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.turnApply}},[t._v("未报名")])]},proxy:!0}],null,!1,567203517)}):t._e()],1)])],1),e("van-tabs",{staticClass:"nav",on:{click:t.onClick},model:{value:t.cmsId,callback:function(e){t.cmsId=e},expression:"cmsId"}},t._l(t.cmsList,(function(t){return e("van-tab",{key:t.id,attrs:{title:t.title,name:t.id}})})),1),e("pclogin")],1)},o=[],s=i("ade3"),c=(i("a481"),i("6762"),i("2fdb"),i("cacf")),r=i("7dcb"),u=function(){var t=this,e=t._self._c;return e("van-dialog",{attrs:{title:"登录操作",width:"400px"},on:{confirm:t.login},model:{value:t.showPcLogin,callback:function(e){t.showPcLogin=e},expression:"showPcLogin"}},[e("div",{staticClass:"text-center padding"},[e("van-cell-group",{attrs:{inset:""}},[e("van-field",{attrs:{name:"手机号",label:"手机号",required:"",placeholder:"手机号",rules:[{required:!0,message:"请填写手机号"}]},model:{value:t.mobile,callback:function(e){t.mobile=e},expression:"mobile"}}),"1736999159118508033"!=t.activityId?e("van-field",{attrs:{center:"",clearable:"",maxlength:"6",label:"短信验证码",required:"",placeholder:"请输入短信验证码"},scopedSlots:t._u([{key:"button",fn:function(){return[e("van-button",{attrs:{size:"small",type:"primary",disabled:t.waiting},on:{click:function(e){return t.doSendSmsCode()}}},[t.waiting?e("span",[t._v(t._s(t.waitingTime)+"秒后重发")]):e("span",{staticStyle:{"font-size":"13px"}},[t._v("获取验证码")])])]},proxy:!0}],null,!1,850170227),model:{value:t.code,callback:function(e){t.code=e},expression:"code"}}):t._e()],1)],1)])},l=[],d={name:"pclogin",computed:{showPcLogin:{get:function(){return this.$store.state.user.showPcLogin},set:function(t){this.$store.commit("user/changePcLogin",t)}}},data:function(){return{activityId:void 0,waiting:!1,waitingTime:60,mobile:"",code:""}},mounted:function(){this.activityId=this.$route.query.id},methods:{login:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?this.code||0x181b0f883e693000==this.activityId?/^\d{6}$/.test(this.code)||0x181b0f883e693000==this.activityId?void this.$fly.post("/pyp/web/user/pcLogin",{mobile:this.mobile,code:0x181b0f883e693000==this.activityId?"121212":this.code}).then((function(e){e&&200===e.code?(vant.Toast("登录成功"),t.$store.commit("user/update",e.userInfo),t.showPcLogin=!1,"applyIndex"!=t.$route.name&&"livesDetail"!=t.$route.name||location.reload()):vant.Toast(e.msg)})):(vant.Toast("验证码错误"),!1):(vant.Toast("请输入验证码"),!1):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},doSendSmsCode:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.mobile,activityId:this.activityId}).then((function(e){e&&200===e.code?(t.countdown(),vant.Toast("发送验证码成功")):vant.Toast(e.msg)})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var t=this;this.waiting=!0;var e=window.setInterval((function(){t.waitingTime--,t.waitingTime<0&&(window.clearInterval(e),t.waitingTime=60,t.waiting=!1)}),1e3)}}},m=d,h=i("2877"),p=Object(h["a"])(m,u,l,!1,null,null,null),f=p.exports,v={components:{pclogin:f},data:function(){return{activityId:void 0,bannerIndex:0,cmsId:void 0,cmsList:[],cmsInfo:{},activityInfo:{}}},computed:{userInfo:{get:function(){return this.$store.state.user.userInfo},set:function(t){this.$store.commit("user/update",t)}},isPay:{get:function(){return this.$store.state.apply.isPay},set:function(t){this.$store.commit("apply/update",t)}}},mounted:function(){var t=(new Date).getTime();localStorage.setItem("logAddTime",t+36e5),this.openid=this.$cookie.get("openid"),this.activityId=this.$route.query.id,this.cmsId=sessionStorage.getItem("cmsId")||this.$route.query.cmsId,this.checkLogin(),this.getActivityInfo(),this.activityLogCount()},methods:(n={showLogin:function(){this.$store.commit("user/changePcLogin",!0)},checkLogin:function(){var t=this;this.$fly.get("/pyp/web/user/checkLogin").then((function(e){t.userInfo=e.result,t.userInfo?t.checkApply():t.isPay=0}))},bannerIndexChange:function(t){this.bannerIndex=t},checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(e){200==e.code?(t.isPay=e.isPay,1==t.isPay||2==t.isPay&&vant.Dialog.alert({title:"提示",message:"您有一笔注册费待支付，点击跳转"}).then((function(){t.$router.push({name:"applySuccess",query:{orderId:e.result,id:t.activityId}})}))):vant.Toast(e.msg)}))},changeShowType:function(t){this.$emit("changeShowType",t)},turnApply:function(){var t=this.cmsList.filter((function(t){return t.model&&t.model.includes("applyIndex")}));if(t){var e=t[0];sessionStorage.setItem("cmsId",e.id);var i=e.model.replace("${activityId}",e.activityId);this.$router.push(JSON.parse(i))}else vant.Toast("暂未开启报名")},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(e){if(t.loading=!1,200==e.code){t.activityInfo=e.activity,document.title=t.activityInfo.name;var i=t.activityInfo.startTime,n=new Date(i.replace(/-/g,"/")),a=new Date,o=n.getTime()-a.getTime();t.dateCompare=o>0?o:0,t.getCmsList()}}))},activityLogCount:function(){var t=localStorage.getItem("logAddTime"),e=(new Date).getTime();e>t&&this.$fly.post("/pyp/activity/activityviewlog/count",{activityId:this.activityId,device:r["a"].getVersion()}).then((function(t){}))},getCmsList:function(){var t=this;this.$fly.get("/pyp/cms/cms/findByActivityId/".concat(this.activityId)).then((function(e){t.loading=!1,200==e.code?(t.cmsList=e.result,t.cmsId?t.changeShowType(t.cmsId):t.changeShowType(t.cmsList[0].id)):(vant.Toast(e.msg),t.cmsList=[])}))},onClick:function(t){if(console.log(t),sessionStorage.setItem("cmsId",t),this.cmsInfo=this.cmsList.filter((function(e){return e.id==t}))[0],this.cmsInfo.url&&Object(c["d"])(this.cmsInfo.url))location.href=this.cmsInfo.url;else if(this.cmsInfo.model&&this.isJSON(this.cmsInfo.model)){var e=this.cmsInfo.model.replace("${activityId}",this.cmsInfo.activityId);this.$router.push(JSON.parse(e))}else"cmsIndex"==this.$route.name?this.changeShowType(t):this.$router.push({name:"cmsIndex",query:{id:this.activityId,cmsId:t}})}},Object(s["a"])(n,"bannerIndexChange",(function(t){this.bannerIndex=t})),Object(s["a"])(n,"isJSON",(function(t){try{return JSON.parse(t),!0}catch(e){return!1}})),n)},g=v,y=(i("dd7a"),Object(h["a"])(g,a,o,!1,null,"7bd3d808",null));e["default"]=y.exports},"5aac":function(t,e,i){"use strict";i("7564")},7564:function(t,e,i){},"7dcb":function(t,e,i){"use strict";i("a481"),i("4917");e["a"]={getVersion:function(){var t=navigator.userAgent||window.navigator.userAgent,e=/HUAWEI|HONOR/gi,i=/[^;]+(?= Build)/gi,n=/CPU iPhone OS \d[_\d]*/gi,a=/CPU OS \d[_\d]*/gi,o=/Windows NT \d[\.\d]*/gi,s=/Linux x\d[_\d]*/gi,c=/Mac OS X \d[_|\.\d]*/gi;return/Android/gi.test(t)?e.test(t)?t.match(e)[0]+t.match(i)[0]:t.match(i)[0]:/iPhone/gi.test(t)?t.match(n)[0].replace(/CPU iPhone OS/gi,"iPhone").replace(/_/g,"."):/iPad/gi.test(t)?t.match(a)[0].replace(/CPU OS/gi,"iPad").replace(/_/g,"."):/Windows/gi.test(t)?"Windows "+(Math.min(parseInt(t.match(o)[0].replace(/Windows NT /,""))+1,10)<7?"XP":Math.min(parseInt(t.match(o)[0].replace(/Windows NT /,""))+1,10)):/Linux/gi.test(t)?t.match(s)[0]:/Macintosh/gi.test(t)?t.match(c)[0].replace(/_/g,"."):"unknown"}}},ade3:function(t,e,i){"use strict";i.d(e,"a",(function(){return s}));var n=i("53ca");function a(t,e){if("object"!==Object(n["a"])(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var a=i.call(t,e||"default");if("object"!==Object(n["a"])(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function o(t){var e=a(t,"string");return"symbol"===Object(n["a"])(e)?e:String(e)}function s(t,e,i){return e=o(e),e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}},cad8:function(t,e,i){},dd7a:function(t,e,i){"use strict";i("cad8")},fadd:function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{class:t.isMobilePhone?"page":"page pc-container"},[t.isMobilePhone?t._e():e("pcheader"),e("van-card",{staticStyle:{background:"white"},attrs:{thumb:t.userInfo.headimgurl?t.userInfo.headimgurl:"photo-o"}},[e("div",{staticStyle:{"padding-top":"10px","padding-left":"10px","font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.userInfo.nickname))]),e("div",{staticStyle:{"padding-top":"10px","padding-left":"10px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[t._v(t._s(t.userInfo.mobile))])]),e("van-grid",{staticStyle:{"margin-top":"20px"},attrs:{"column-num":3,"icon-size":"50px",gutter:8}},[e("van-grid-item",{attrs:{icon:"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/a162a57b18304c32af3334075ec19dc8.png",text:"我的会议"},on:{click:function(e){return t.$router.push({name:"meApplyList"})}}}),e("van-grid-item",{attrs:{icon:"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/efb283dcc4a745aeae429f3101b2e89a.png",text:"系统说明"},on:{click:function(e){return t.$router.push({name:"help"})}}}),e("van-grid-item",{attrs:{icon:"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/40e379749bac45eb93adc0ed9be95482.png",text:"联系我们"},on:{click:function(e){return t.$router.push({name:"contact"})}}})],1)],1)},a=[],o=i("cacf"),s=i("1b69"),c={components:{pcheader:s["default"]},data:function(){return{isMobilePhone:Object(o["c"])(),userInfo:{}}},mounted:function(){document.title="我的",this.$wxShare(this.$cookie.get("accountName"),this.$cookie.get("logo"),this.$cookie.get("slog")),this.getUserInfo()},methods:{getUserInfo:function(){var t=this;this.$fly.get("/pyp/wxUser/getUserInfo").then((function(e){200==e.code?t.userInfo=e.data:vant.Toast(e.msg)}))}}},r=c,u=(i("5aac"),i("2877")),l=Object(u["a"])(r,n,a,!1,null,"598fd036",null);e["default"]=l.exports}}]);
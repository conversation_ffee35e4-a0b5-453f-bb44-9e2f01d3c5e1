(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5f6622de"],{"00c5":function(t,s,i){},"0655":function(t,s,i){"use strict";i.r(s);var a=function(){var t=this,s=t._self._c;return s("div",{staticClass:"container"},[s("div",{staticClass:"background-animation"}),s("div",{staticClass:"card-wrapper"},[s("div",{staticClass:"card"},["preparing"===t.status?s("div",{key:"preparing"},[t._m(0),s("h2",[t._v("准备中")]),s("p",{staticClass:"subtitle"},[t._v("正在准备发布环境，请稍候...")])]):"publishing"===t.status?s("div",{key:"publishing"},[t._m(1),s("h2",[t._v("正在发布视频")]),s("p",{staticClass:"subtitle"},[t._v("请勿离开或刷新页面...")])]):"success"===t.status?s("div",{key:"success"},[s("div",{staticClass:"icon success-icon"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}},[s("path",{attrs:{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14"}}),s("polyline",{attrs:{points:"22 4 12 14.01 9 11.01"}})])]),s("h2",[t._v("发布成功！")]),s("p",{staticClass:"subtitle"},[t._v("您的视频已成功发布。")]),s("button",{staticClass:"btn",on:{click:t.goBack}},[t._v("返回")])]):"error"===t.status?s("div",{key:"error"},[s("div",{staticClass:"icon error-icon"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}},[s("circle",{attrs:{cx:"12",cy:"12",r:"10"}}),s("line",{attrs:{x1:"15",y1:"9",x2:"9",y2:"15"}}),s("line",{attrs:{x1:"9",y1:"9",x2:"15",y2:"15"}})])]),s("h2",[t._v("发布失败")]),s("p",{staticClass:"subtitle"},[t._v(t._s(t.errorMsg))]),s("div",{staticClass:"button-group"},[s("button",{staticClass:"btn",on:{click:t.uploadVideo}},[t._v("重试")]),s("button",{staticClass:"btn btn-secondary",on:{click:t.goBack}},[t._v("返回")])])]):t._e()])])])},r=[function(){var t=this,s=t._self._c;return s("div",{staticClass:"pulse-loader"},[s("span"),s("span"),s("span")])},function(){var t=this,s=t._self._c;return s("div",{staticClass:"pulse-loader"},[s("span"),s("span"),s("span")])}],e=(i("a481"),{components:{},data:function(){return{activityId:void 0,code:void 0,status:"preparing",errorMsg:""}},mounted:function(){document.title="发布视频",this.activityId=this.$route.query.id,this.code=this.$route.query.code,this.code&&this.uploadVideo()},methods:{uploadVideo:function(){var t=this;this.status="publishing",this.errorMsg="",this.$fly.get("/pyp/web/kuaishou/upload",{activityId:this.activityId,code:this.code}).then((function(s){200==s.code?t.status="success":(t.status="error",t.errorMsg=s.msg||"未知错误")})).catch((function(s){t.status="error",t.errorMsg=s.message||"网络请求失败"}))},goBack:function(){this.$router.replace({path:"/cms/index",query:{id:this.activityId}})}}}),o=e,n=(i("35ac"),i("2877")),c=Object(n["a"])(o,a,r,!1,null,"9cfcd3a6",null);s["default"]=c.exports},"35ac":function(t,s,i){"use strict";i("00c5")}}]);
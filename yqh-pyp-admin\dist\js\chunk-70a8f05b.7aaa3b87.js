(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-70a8f05b"],{1548:function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.modeDesc[t.mode]+"用户标签","close-on-click-modal":!1,visible:t.dialogVisible},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("div",[e("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择标签"},model:{value:t.selectedTagid,callback:function(e){t.selectedTagid=e},expression:"selectedTagid"}},t._l(t.tagidsInOption,(function(i){return e("el-option",{key:i,attrs:{label:t.getTagName(i),value:i}})})),1),e("div",{staticStyle:{"margin-top":"20px"}},[t._v("已选择用户数："+t._s(t.wxUsers.length))])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("关闭")]),e("el-button",{attrs:{type:"primary",disabled:t.submitting},on:{click:function(e){return t.dataFormSubmit()}}},[t._v(t._s(t.submitting?"保存中...":"确定"))])],1)])},a=[],s=(i("4de4"),i("7db0"),i("a630"),i("d81d"),i("b0c0"),i("d3b7"),i("6062"),i("1e70"),i("79a4"),i("c1a1"),i("8b00"),i("a4e7"),i("1e5a"),i("72c35"),i("3ca3"),i("0643"),i("76d6"),i("2382"),i("fffc"),i("4e3e"),i("a573"),i("159b"),i("ddb0"),i("2f62")),r={name:"wx-user-tagging",props:{wxUsers:Array},data:function(){return{mode:"tagging",modeDesc:{tagging:"绑定",untagging:"解绑"},selectedTagid:"",dialogVisible:!1,submitting:!1}},computed:Object(s["b"])({wxUserTags:function(t){return t.wxUserTags.tags},tagidsInOption:function(){var t=this.wxUsers.map((function(t){return t.tagidList||[]}));if("tagging"==this.mode){var e=this.wxUserTags.map((function(t){return t.id}));return e.filter((function(e){return!t.every((function(t){return t.indexOf(e)>-1}))}))}if("untagging"==this.mode){var i=new Set;return t.forEach((function(t){t.forEach((function(t){return i.add(t)}))})),Array.from(i)}return[]}}),methods:{init:function(t){if("tagging"!=t&&"untagging"!=t)throw"mode参数有误";this.mode=t,this.dialogVisible=!0},getTagName:function(t){var e=this.wxUserTags.find((function(e){return e.id==t}));return e?e.name:"?"},dataFormSubmit:function(){var t=this;if(!this.submitting)if(this.selectedTagid){this.submitting=!0;var e=this.wxUsers.map((function(t){return t.openid}));this.$http({url:this.$http.adornUrl("/manage/wxUserTags/".concat("tagging"==this.mode?"batchTagging":"batchUnTagging")),method:"post",data:this.$http.adornData({tagid:this.selectedTagid,openidList:e})}).then((function(e){var i=e.data;t.submitting=!1,i&&200===i.code?t.$message({message:"操作成功,列表数据需稍后刷新查看",type:"success",onClose:function(){return t.dialogVisible=!1}}):t.$message.error(i.msg)}))}else this.$message.error("未选择标签")}}},o=r,d=i("2877"),g=Object(d["a"])(o,n,a,!1,null,null,null);e["default"]=g.exports},"76d6":function(t,e,i){"use strict";i("d866")},d866:function(t,e,i){"use strict";var n=i("23e7"),a=i("2266"),s=i("59ed"),r=i("825a"),o=i("46c4");n({target:"Iterator",proto:!0,real:!0},{every:function(t){r(this),s(t);var e=o(this),i=0;return!a(e,(function(e,n){if(!t(e,i++))return n()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})}}]);
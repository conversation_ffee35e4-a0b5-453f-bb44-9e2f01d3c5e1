(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d22d076"],{f66f:function(t,r,a){"use strict";a.r(r);var e=function(){var t=this,r=t._self._c;return r("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(r){t.visible=r}}},[r("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"},nativeOn:{keyup:function(r){return!r.type.indexOf("key")&&t._k(r.keyCode,"enter",13,r.key,"Enter")?null:t.dataFormSubmit()}}},[r("el-form-item",{attrs:{label:"机场名称",prop:"airportName"}},[r("el-input",{attrs:{placeholder:"机场名称"},model:{value:t.dataForm.airportName,callback:function(r){t.$set(t.dataForm,"airportName",r)},expression:"dataForm.airportName"}})],1),r("el-form-item",{attrs:{label:"机场简称",prop:"airportShortName"}},[r("el-input",{attrs:{placeholder:"机场简称"},model:{value:t.dataForm.airportShortName,callback:function(r){t.$set(t.dataForm,"airportShortName",r)},expression:"dataForm.airportShortName"}})],1),r("el-form-item",{attrs:{label:"机场三字码",prop:"airportCode"}},[r("el-input",{attrs:{placeholder:"机场三字码"},model:{value:t.dataForm.airportCode,callback:function(r){t.$set(t.dataForm,"airportCode",r)},expression:"dataForm.airportCode"}})],1),r("el-form-item",{attrs:{label:"城市名称",prop:"cityName"}},[r("el-input",{attrs:{placeholder:"城市名称"},model:{value:t.dataForm.cityName,callback:function(r){t.$set(t.dataForm,"cityName",r)},expression:"dataForm.cityName"}})],1),r("el-form-item",{attrs:{label:"城市三字码",prop:"cityCode"}},[r("el-input",{attrs:{placeholder:"城市三字码"},model:{value:t.dataForm.cityCode,callback:function(r){t.$set(t.dataForm,"cityCode",r)},expression:"dataForm.cityCode"}})],1),r("el-form-item",{attrs:{label:"城市拼音",prop:"cityPinYin"}},[r("el-input",{attrs:{placeholder:"城市拼音"},model:{value:t.dataForm.cityPinYin,callback:function(r){t.$set(t.dataForm,"cityPinYin",r)},expression:"dataForm.cityPinYin"}})],1),r("el-form-item",{attrs:{label:"城市拼音简称",prop:"cityShortChar"}},[r("el-input",{attrs:{placeholder:"城市拼音简称"},model:{value:t.dataForm.cityShortChar,callback:function(r){t.$set(t.dataForm,"cityShortChar",r)},expression:"dataForm.cityShortChar"}})],1),r("el-form-item",{attrs:{label:"所属国家名称",prop:"countryName"}},[r("el-input",{attrs:{placeholder:"所属国家名称"},model:{value:t.dataForm.countryName,callback:function(r){t.$set(t.dataForm,"countryName",r)},expression:"dataForm.countryName"}})],1),r("el-form-item",{attrs:{label:"所属国家代码",prop:"countryCode"}},[r("el-input",{attrs:{placeholder:"所属国家代码"},model:{value:t.dataForm.countryCode,callback:function(r){t.$set(t.dataForm,"countryCode",r)},expression:"dataForm.countryCode"}})],1),r("el-form-item",{attrs:{label:"所属洲",prop:"continent"}},[r("el-input",{attrs:{placeholder:"所属洲"},model:{value:t.dataForm.continent,callback:function(r){t.$set(t.dataForm,"continent",r)},expression:"dataForm.continent"}})],1),r("el-form-item",{attrs:{label:"是否国内（大陆）机场城市",prop:"isInternal"}},[r("el-input",{attrs:{placeholder:"是否国内（大陆）机场城市"},model:{value:t.dataForm.isInternal,callback:function(r){t.$set(t.dataForm,"isInternal",r)},expression:"dataForm.isInternal"}})],1)],1),r("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(r){t.visible=!1}}},[t._v("取消")]),r("el-button",{attrs:{type:"primary"},on:{click:function(r){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},o=[],i={data:function(){return{visible:!1,dataForm:{repeatToken:"",id:0,airportName:"",airportShortName:"",airportCode:"",cityName:"",cityCode:"",cityPinYin:"",cityShortChar:"",countryName:"",countryCode:"",continent:"",isInternal:""},dataRule:{airportName:[{required:!0,message:"机场名称不能为空",trigger:"blur"}],airportShortName:[{required:!0,message:"机场简称不能为空",trigger:"blur"}],airportCode:[{required:!0,message:"机场三字码不能为空",trigger:"blur"}],cityName:[{required:!0,message:"城市名称不能为空",trigger:"blur"}],cityCode:[{required:!0,message:"城市三字码不能为空",trigger:"blur"}],cityPinYin:[{required:!0,message:"城市拼音不能为空",trigger:"blur"}],cityShortChar:[{required:!0,message:"城市拼音简称不能为空",trigger:"blur"}],countryName:[{required:!0,message:"所属国家名称不能为空",trigger:"blur"}],countryCode:[{required:!0,message:"所属国家代码不能为空",trigger:"blur"}],continent:[{required:!0,message:"所属洲不能为空",trigger:"blur"}],isInternal:[{required:!0,message:"是否国内（大陆）机场城市不能为空",trigger:"blur"}]}}},methods:{init:function(t){var r=this;this.getToken(),this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){r.$refs["dataForm"].resetFields(),r.dataForm.id&&r.$http({url:r.$http.adornUrl("/config/configairport/info/".concat(r.dataForm.id)),method:"get",params:r.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(r.dataForm.airportName=a.configAirPort.airportName,r.dataForm.airportShortName=a.configAirPort.airportShortName,r.dataForm.airportCode=a.configAirPort.airportCode,r.dataForm.cityName=a.configAirPort.cityName,r.dataForm.cityCode=a.configAirPort.cityCode,r.dataForm.cityPinYin=a.configAirPort.cityPinYin,r.dataForm.cityShortChar=a.configAirPort.cityShortChar,r.dataForm.countryName=a.configAirPort.countryName,r.dataForm.countryCode=a.configAirPort.countryCode,r.dataForm.continent=a.configAirPort.continent,r.dataForm.isInternal=a.configAirPort.isInternal)}))}))},getToken:function(){var t=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(r){var a=r.data;a&&200===a.code&&(t.dataForm.repeatToken=a.result)}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(r){r&&t.$http({url:t.$http.adornUrl("/config/configairport/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({repeatToken:t.dataForm.repeatToken,id:t.dataForm.id||void 0,airportName:t.dataForm.airportName,airportShortName:t.dataForm.airportShortName,airportCode:t.dataForm.airportCode,cityName:t.dataForm.cityName,cityCode:t.dataForm.cityCode,cityPinYin:t.dataForm.cityPinYin,cityShortChar:t.dataForm.cityShortChar,countryName:t.dataForm.countryName,countryCode:t.dataForm.countryCode,continent:t.dataForm.continent,isInternal:t.dataForm.isInternal})}).then((function(r){var a=r.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):(t.$message.error(a.msg),"不能重复提交"!=a.msg&&t.getToken())}))}))}}},n=i,d=a("2877"),l=Object(d["a"])(n,e,o,!1,null,null,null);r["default"]=l.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ed4a8b9e","chunk-0e36eb78"],{"02d7":function(e,t,a){"use strict";a.d(t,"d",(function(){return r})),a.d(t,"c",(function(){return i})),a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return l}));var r=[{key:0,value:"考卷"},{key:1,value:"问卷"}],i=[{key:0,value:"统一时间考试"},{key:1,value:"随时考试"}],n=[{key:0,value:"单选"},{key:1,value:"多选"},{key:2,value:"填空"}],l=[{key:0,value:"未提交"},{key:1,value:"已提交"},{key:2,value:"已通过"},{key:3,value:"未通过"},{key:4,value:"已超时"},{key:5,value:"作废"}]},"299a":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"mod-config"},[t("el-form",{attrs:{inline:!0,model:e.dataForm}},[t("el-form-item",[t("el-input",{attrs:{placeholder:"参数名",clearable:""},model:{value:e.dataForm.key,callback:function(t){e.$set(e.dataForm,"key",t)},expression:"dataForm.key"}})],1),t("el-form-item",[t("el-button",{on:{click:function(t){return e.getDataList()}}},[e._v("查询")]),e.isAuth("exam:exam:save")?t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addOrUpdateHandle()}}},[e._v("新增")]):e._e(),e.isAuth("exam:exam:delete")?t("el-button",{attrs:{type:"danger",disabled:e.dataListSelections.length<=0},on:{click:function(t){return e.deleteHandle()}}},[e._v("批量删除")]):e._e()],1),t("el-form-item",{staticStyle:{float:"right"}},[t("el-button",{attrs:{type:"success"},on:{click:function(t){return e.$router.go(-1)}}},[e._v("返回")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:e.dataList,border:""},on:{"selection-change":e.selectionChangeHandle}},[t("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),t("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"考卷名称"}}),t("el-table-column",{attrs:{prop:"type","header-align":"center",align:"center",label:"类型"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[t("el-tag",{class:"tag-color tag-color-"+a.row.type,attrs:{type:"primary"}},[e._v(e._s(e.examType[a.row.type].value))])],1)}}])}),t("el-table-column",{attrs:{prop:"examType","header-align":"center",align:"center",width:"150",label:"时间类型"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[t("el-tag",{class:"tag-color tag-color-"+a.row.examType,attrs:{type:"primary"}},[e._v(e._s(e.examTimeType[a.row.examType].value))])],1)}}])}),t("el-table-column",{attrs:{prop:"examStartTime","header-align":"center",align:"center",label:"考试开始时间"}}),t("el-table-column",{attrs:{prop:"examEndTime","header-align":"center",align:"center",label:"考试结束时间"}}),t("el-table-column",{attrs:{prop:"examTime","header-align":"center",align:"center",label:"考试时长"}}),t("el-table-column",{attrs:{prop:"fullMark","header-align":"center",align:"center",label:"满分分数"}}),t("el-table-column",{attrs:{prop:"passMark","header-align":"center",align:"center",label:"及格分数"}}),t("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",label:"创建时间"}}),t("el-table-column",{attrs:{prop:"updateOn","header-align":"center",align:"center",label:"更新时间"}}),t("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"230",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.examUserHandle(a.row.id)}}},[e._v("考试管理")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.questionHandle(a.row.id)}}},[e._v("题目管理")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.addOrUpdateHandle(a.row.id)}}},[e._v("修改")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHandle(a.row.id)}}},[e._v("删除")])]}}])})],1),t("el-pagination",{attrs:{"current-page":e.pageIndex,"page-sizes":[10,20,50,100],"page-size":e.pageSize,total:e.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle}}),e.addOrUpdateVisible?t("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:e.getDataList}}):e._e()],1)},i=[],n=(a("99af"),a("a15b"),a("d81d"),a("14d9"),a("a573"),a("3d8a")),l=a("02d7"),o={data:function(){return{examType:l["d"],examTimeType:l["c"],dataForm:{key:"",activityId:void 0},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1}},components:{AddOrUpdate:n["default"]},activated:function(){this.dataForm.activityId=this.$route.query.activityId,this.getDataList()},methods:{getDataList:function(){var e=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/exam/exam/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,key:this.dataForm.key,activityId:this.dataForm.activityId})}).then((function(t){var a=t.data;a&&200===a.code?(e.dataList=a.page.list,e.totalPage=a.page.totalCount):(e.dataList=[],e.totalPage=0),e.dataListLoading=!1}))},sizeChangeHandle:function(e){this.pageSize=e,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(e){this.pageIndex=e,this.getDataList()},selectionChangeHandle:function(e){this.dataListSelections=e},addOrUpdateHandle:function(e){var t=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){t.$refs.addOrUpdate.init(t.dataForm.activityId,e)}))},questionHandle:function(e){this.$router.push({name:"examquestion",query:{examId:e,activityId:this.dataForm.activityId}})},examUserHandle:function(e){this.$router.push({name:"examactivityuser",query:{examId:e,activityId:this.dataForm.activityId}})},deleteHandle:function(e){var t=this,a=e?[e]:this.dataListSelections.map((function(e){return e.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(e?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:t.$http.adornUrl("/exam/exam/delete"),method:"post",data:t.$http.adornData(a,!1)}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.getDataList()}}):t.$message.error(a.msg)}))}))}}},s=o,d=a("2877"),m=Object(d["a"])(s,r,i,!1,null,null,null);t["default"]=m.exports},"3d8a":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"考卷名称",prop:"name"}},[t("el-input",{attrs:{placeholder:"考卷名称"},model:{value:e.dataForm.name,callback:function(t){e.$set(e.dataForm,"name",t)},expression:"dataForm.name"}})],1),t("el-form-item",{attrs:{label:"类型",prop:"type"}},[t("el-select",{attrs:{placeholder:"类型",filterable:""},model:{value:e.dataForm.type,callback:function(t){e.$set(e.dataForm,"type",t)},expression:"dataForm.type"}},e._l(e.examType,(function(e){return t("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"时间类型",prop:"examType"}},[t("el-select",{attrs:{placeholder:"时间类型",filterable:""},model:{value:e.dataForm.examType,callback:function(t){e.$set(e.dataForm,"examType",t)},expression:"dataForm.examType"}},e._l(e.examTimeType,(function(e){return t("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1),0==e.dataForm.examType?t("el-form-item",{attrs:{label:"考试时间",prop:"times"}},[t("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetimerange","value-format":"yyyy/MM/dd HH:mm:ss","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:e.dateChange},model:{value:e.dataForm.times,callback:function(t){e.$set(e.dataForm,"times",t)},expression:"dataForm.times"}})],1):e._e(),t("el-form-item",{attrs:{label:"考试时长",prop:"examTime"}},[t("el-input",{attrs:{placeholder:"考试时长"},model:{value:e.dataForm.examTime,callback:function(t){e.$set(e.dataForm,"examTime",t)},expression:"dataForm.examTime"}},[t("template",{slot:"append"},[e._v("分钟")])],2)],1),t("el-form-item",{attrs:{label:"满分分数",prop:"fullMark"}},[t("el-input",{attrs:{placeholder:"满分分数"},model:{value:e.dataForm.fullMark,callback:function(t){e.$set(e.dataForm,"fullMark",t)},expression:"dataForm.fullMark"}})],1),t("el-form-item",{attrs:{label:"及格分数",prop:"passMark"}},[t("el-input",{attrs:{placeholder:"及格分数"},model:{value:e.dataForm.passMark,callback:function(t){e.$set(e.dataForm,"passMark",t)},expression:"dataForm.passMark"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},i=[],n=a("02d7"),l={data:function(){return{examType:n["d"],examTimeType:n["c"],visible:!1,dataForm:{id:0,times:[],name:"",activityId:"",type:0,examType:0,examStartTime:"",examTime:"",examEndTime:"",fullMark:0,passMark:0,examCount:1},dataRule:{times:[{required:!0,message:"考试时间不能为空",trigger:"blur"}],name:[{required:!0,message:"考卷名称不能为空",trigger:"blur"}],activityId:[{required:!0,message:"活动表id不能为空",trigger:"blur"}],type:[{required:!0,message:"类型",trigger:"blur"}],examType:[{required:!0,message:"考试时间类型",trigger:"blur"}],examStartTime:[{required:!0,message:"考试开始时间",trigger:"blur"}],examTime:[{required:!0,message:"考试时长",trigger:"blur"}],examEndTime:[{required:!0,message:"考试结束时间",trigger:"blur"}],fullMark:[{required:!0,message:"满分分数不能为空",trigger:"blur"}],passMark:[{required:!0,message:"及格分数不能为空",trigger:"blur"}]}}},methods:{init:function(e,t){var a=this;this.dataForm.activityId=e,this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/exam/exam/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(e){var t=e.data;t&&200===t.code&&(a.$set(a.dataForm,"times",t.exam.examStartTime?[t.exam.examStartTime,t.exam.examEndTime]:[]),a.dataForm.name=t.exam.name,a.dataForm.activityId=t.exam.activityId,a.dataForm.type=t.exam.type,a.dataForm.examType=t.exam.examType,a.dataForm.examStartTime=t.exam.examStartTime,a.dataForm.examTime=t.exam.examTime,a.dataForm.examEndTime=t.exam.examEndTime,a.dataForm.fullMark=t.exam.fullMark,a.dataForm.passMark=t.exam.passMark,a.dataForm.examCount=t.exam.examCount)}))}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$http({url:e.$http.adornUrl("/exam/exam/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,name:e.dataForm.name,activityId:e.dataForm.activityId,type:e.dataForm.type,examType:e.dataForm.examType,examStartTime:e.dataForm.examStartTime,examTime:e.dataForm.examTime,examEndTime:e.dataForm.examEndTime,fullMark:e.dataForm.fullMark,passMark:e.dataForm.passMark,examCount:e.dataForm.examCount})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(a.msg)}))}))},dateChange:function(e){this.dataForm.examStartTime=e[0],this.dataForm.examEndTime=e[1],console.log(e)}}},o=l,s=a("2877"),d=Object(s["a"])(o,r,i,!1,null,null,null);t["default"]=d.exports},a15b:function(e,t,a){"use strict";var r=a("23e7"),i=a("e330"),n=a("44ad"),l=a("fc6a"),o=a("a640"),s=i([].join),d=n!==Object,m=d||!o("join",",");r({target:"Array",proto:!0,forced:m},{join:function(e){return s(l(this),void 0===e?",":e)}})},a573:function(e,t,a){"use strict";a("ab43")},ab43:function(e,t,a){"use strict";var r=a("23e7"),i=a("d024"),n=a("c430");r({target:"Iterator",proto:!0,real:!0,forced:n},{map:i})},d024:function(e,t,a){"use strict";var r=a("c65b"),i=a("59ed"),n=a("825a"),l=a("46c4"),o=a("c5cc"),s=a("9bdd"),d=o((function(){var e=this.iterator,t=n(r(this.next,e)),a=this.done=!!t.done;if(!a)return s(e,this.mapper,[t.value,this.counter++],!0)}));e.exports=function(e){return n(this),i(e),new d(l(this),{mapper:e})}},d81d:function(e,t,a){"use strict";var r=a("23e7"),i=a("b727").map,n=a("1dde"),l=n("map");r({target:"Array",proto:!0,forced:!l},{map:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}})}}]);
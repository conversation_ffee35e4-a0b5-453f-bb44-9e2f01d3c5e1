(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-572fc711","chunk-0506e191","chunk-0506e191"],{1148:function(t,e,a){"use strict";var s=a("5926"),n=a("577e"),l=a("1d80"),r=RangeError;t.exports=function(t){var e=n(l(this)),a="",i=s(t);if(i<0||i===1/0)throw new r("Wrong number of repetitions");for(;i>0;(i>>>=1)&&(e+=e))1&i&&(a+=e);return a}},"143e":function(t,e,a){"use strict";a.r(e);a("b0c0"),a("b680");var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-commission-settlement"},[e("el-row",{staticStyle:{"margin-bottom":"20px"},attrs:{gutter:20}},[e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v(t._s(t.stats.totalBatches||0))]),e("div",{staticClass:"stats-label"},[t._v("总批次数")])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v("¥"+t._s((t.stats.totalAmount||0).toFixed(2)))]),e("div",{staticClass:"stats-label"},[t._v("总结算金额")])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v(t._s(t.stats.completedBatches||0))]),e("div",{staticClass:"stats-label"},[t._v("已完成批次")])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v(t._s(t.stats.pendingBatches||0))]),e("div",{staticClass:"stats-label"},[t._v("待处理批次")])])])],1)],1),e("el-form",{attrs:{inline:!0,model:t.dataForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.getDataList()}}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"批次号",clearable:""},model:{value:t.dataForm.batchNo,callback:function(e){t.$set(t.dataForm,"batchNo",e)},expression:"dataForm.batchNo"}})],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"状态",clearable:""},model:{value:t.dataForm.status,callback:function(e){t.$set(t.dataForm,"status",e)},expression:"dataForm.status"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"待结算",value:0}}),e("el-option",{attrs:{label:"已结算",value:1}}),e("el-option",{attrs:{label:"已取消",value:2}})],1)],1),e("el-form-item",[e("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.createBatchHandle()}}},[t._v("创建结算批次")]),e("el-button",{on:{click:function(e){return t.getStats()}}},[t._v("刷新统计")])],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""}},[e("el-table-column",{attrs:{prop:"batchNo","header-align":"center",align:"center",label:"批次号"}}),e("el-table-column",{attrs:{prop:"settlementDate","header-align":"center",align:"center",label:"结算日期"}}),e("el-table-column",{attrs:{prop:"salesmanCount","header-align":"center",align:"center",label:"业务员数量"}}),e("el-table-column",{attrs:{prop:"recordCount","header-align":"center",align:"center",label:"记录数量"}}),e("el-table-column",{attrs:{prop:"totalAmount","header-align":"center",align:"center",label:"结算金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" ¥"+t._s(e.row.totalAmount.toFixed(2))+" ")]}}])}),e("el-table-column",{attrs:{prop:"statusDesc","header-align":"center",align:"center",label:"状态"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:t.getStatusTagType(a.row.status)}},[t._v(" "+t._s(a.row.statusDesc)+" ")])]}}])}),e("el-table-column",{attrs:{prop:"settlementTime","header-align":"center",align:"center",width:"150",label:"完成时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.settlementTime||"-")+" ")]}}])}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"200",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.viewDetails(a.row)}}},[t._v("详情")]),0===a.row.status?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.executeSettlement(a.row)}}},[t._v(" 执行结算 ")]):t._e(),2!==a.row.status?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.cancelSettlement(a.row)}}},[t._v(" 取消 ")]):t._e(),2===a.row.status?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v(" 删除 ")]):t._e()]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),e("el-dialog",{attrs:{title:"创建结算批次",visible:t.createBatchDialogVisible,width:"50%"},on:{"update:visible":function(e){t.createBatchDialogVisible=e}}},[e("el-form",{ref:"batchForm",attrs:{model:t.batchForm,rules:t.batchRule,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"业务员",prop:"salesmanIds"}},[e("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"请选择业务员（可多选）"},model:{value:t.batchForm.salesmanIds,callback:function(e){t.$set(t.batchForm,"salesmanIds",e)},expression:"batchForm.salesmanIds"}},t._l(t.salesmanList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name+"("+t.code+")",value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"时间范围",prop:"dateRange"}},[e("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.batchForm.dateRange,callback:function(e){t.$set(t.batchForm,"dateRange",e)},expression:"batchForm.dateRange"}})],1),e("el-form-item",{attrs:{label:"备注",prop:"remarks"}},[e("el-input",{attrs:{type:"textarea",placeholder:"结算备注"},model:{value:t.batchForm.remarks,callback:function(e){t.$set(t.batchForm,"remarks",e)},expression:"batchForm.remarks"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.createBatchDialogVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.createBatchSubmit()}}},[t._v("确定")])],1)],1),e("el-dialog",{attrs:{title:"结算批次详情",visible:t.detailsDialogVisible,width:"60%"},on:{"update:visible":function(e){t.detailsDialogVisible=e}}},[e("el-descriptions",{attrs:{column:2,border:""}},[e("el-descriptions-item",{attrs:{label:"批次号"}},[t._v(t._s(t.selectedBatch.batchNo))]),e("el-descriptions-item",{attrs:{label:"结算日期"}},[t._v(t._s(t.selectedBatch.settlementDate))]),e("el-descriptions-item",{attrs:{label:"业务员数量"}},[t._v(t._s(t.selectedBatch.salesmanCount))]),e("el-descriptions-item",{attrs:{label:"记录数量"}},[t._v(t._s(t.selectedBatch.recordCount))]),e("el-descriptions-item",{attrs:{label:"结算金额"}},[t._v("¥"+t._s((t.selectedBatch.totalAmount||0).toFixed(2)))]),e("el-descriptions-item",{attrs:{label:"状态"}},[e("el-tag",{attrs:{type:t.getStatusTagType(t.selectedBatch.status)}},[t._v(" "+t._s(t.selectedBatch.statusDesc)+" ")])],1),e("el-descriptions-item",{attrs:{label:"结算类型"}},[t._v(t._s(t.selectedBatch.settlementTypeDesc))]),e("el-descriptions-item",{attrs:{label:"完成时间"}},[t._v(t._s(t.selectedBatch.settlementTime||"-"))]),e("el-descriptions-item",{attrs:{label:"创建时间"}},[t._v(t._s(t.selectedBatch.createOn))]),e("el-descriptions-item",{attrs:{label:"备注",span:2}},[t._v(t._s(t.selectedBatch.remarks||"-"))])],1)],1)],1)},n=[],l={data:function(){return{dataForm:{batchNo:"",status:""},dateRange:[],dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,stats:{},createBatchDialogVisible:!1,detailsDialogVisible:!1,selectedBatch:{},batchForm:{salesmanIds:[],dateRange:[],remarks:""},batchRule:{salesmanIds:[{required:!0,message:"请选择业务员",trigger:"change"}],dateRange:[{required:!0,message:"请选择时间范围",trigger:"change"}]},salesmanList:[]}},activated:function(){this.getDataList(),this.getStats(),this.getSalesmanList()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0;var e={page:this.pageIndex,limit:this.pageSize,batchNo:this.dataForm.batchNo,status:this.dataForm.status};this.dateRange&&2===this.dateRange.length&&(e.startDate=this.dateRange[0],e.endDate=this.dateRange[1]),this.$http({url:this.$http.adornUrl("/salesman/commission/settlement/list"),method:"get",params:this.$http.adornParams(e)}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},getStats:function(){var t=this,e={};this.dateRange&&2===this.dateRange.length&&(e.startDate=this.dateRange[0],e.endDate=this.dateRange[1]),this.$http({url:this.$http.adornUrl("/salesman/commission/settlement/stats"),method:"get",params:this.$http.adornParams(e)}).then((function(e){var a=e.data;a&&200===a.code&&(t.stats=a.stats)}))},getSalesmanList:function(){var t=this;this.$http({url:this.$http.adornUrl("/salesman/salesman/list"),method:"get",params:this.$http.adornParams({page:1,limit:1e3})}).then((function(e){var a=e.data;a&&200===a.code&&(t.salesmanList=a.page.list)}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},createBatchHandle:function(){var t=this;this.createBatchDialogVisible=!0,this.$nextTick((function(){t.$refs["batchForm"].resetFields()}))},createBatchSubmit:function(){var t=this;this.$refs["batchForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/salesman/commission/settlement/createBatch"),method:"post",data:t.$http.adornData({salesmanIds:t.batchForm.salesmanIds,startTime:t.batchForm.dateRange[0],endTime:t.batchForm.dateRange[1],remarks:t.batchForm.remarks})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"创建结算批次成功",type:"success",duration:1500,onClose:function(){t.createBatchDialogVisible=!1,t.getDataList(),t.getStats()}}):t.$message.error(a.msg)}))}))},executeSettlement:function(t){var e=this;this.$confirm("确定执行批次[".concat(t.batchNo,"]的结算操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/salesman/commission/settlement/execute"),method:"post",params:e.$http.adornParams({batchNo:t.batchNo})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"执行结算成功",type:"success",duration:1500,onClose:function(){e.getDataList(),e.getStats()}}):e.$message.error(a.msg)}))}))},cancelSettlement:function(t){var e=this;this.$confirm("确定取消批次[".concat(t.batchNo,"]的结算?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/salesman/commission/settlement/cancel"),method:"post",params:e.$http.adornParams({batchNo:t.batchNo})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"取消结算成功",type:"success",duration:1500,onClose:function(){e.getDataList(),e.getStats()}}):e.$message.error(a.msg)}))}))},viewDetails:function(t){this.selectedBatch=t,this.detailsDialogVisible=!0},deleteHandle:function(t){var e=this;this.$confirm("确定删除该结算批次?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/salesman/commission/settlement/delete"),method:"post",data:e.$http.adornData([t],!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"删除成功",type:"success",duration:1500,onClose:function(){e.getDataList(),e.getStats()}}):e.$message.error(a.msg)}))}))},getStatusTagType:function(t){switch(t){case 0:return"warning";case 1:return"success";case 2:return"danger";default:return"info"}}}},r=l,i=(a("516b"),a("2877")),o=Object(i["a"])(r,s,n,!1,null,"610721c3",null);e["default"]=o.exports},"516b":function(t,e,a){"use strict";a("521a")},"521a":function(t,e,a){},b680:function(t,e,a){"use strict";var s=a("23e7"),n=a("e330"),l=a("5926"),r=a("408a"),i=a("1148"),o=a("d039"),c=RangeError,d=String,u=Math.floor,m=n(i),h=n("".slice),g=n(1..toFixed),p=function(t,e,a){return 0===e?a:e%2===1?p(t,e-1,a*t):p(t*t,e/2,a)},b=function(t){var e=0,a=t;while(a>=4096)e+=12,a/=4096;while(a>=2)e+=1,a/=2;return e},f=function(t,e,a){var s=-1,n=a;while(++s<6)n+=e*t[s],t[s]=n%1e7,n=u(n/1e7)},v=function(t,e){var a=6,s=0;while(--a>=0)s+=t[a],t[a]=u(s/e),s=s%e*1e7},_=function(t){var e=6,a="";while(--e>=0)if(""!==a||0===e||0!==t[e]){var s=d(t[e]);a=""===a?s:a+m("0",7-s.length)+s}return a},y=o((function(){return"0.000"!==g(8e-5,3)||"1"!==g(.9,0)||"1.25"!==g(1.255,2)||"1000000000000000128"!==g(0xde0b6b3a7640080,0)}))||!o((function(){g({})}));s({target:"Number",proto:!0,forced:y},{toFixed:function(t){var e,a,s,n,i=r(this),o=l(t),u=[0,0,0,0,0,0],g="",y="0";if(o<0||o>20)throw new c("Incorrect fraction digits");if(i!==i)return"NaN";if(i<=-1e21||i>=1e21)return d(i);if(i<0&&(g="-",i=-i),i>1e-21)if(e=b(i*p(2,69,1))-69,a=e<0?i*p(2,-e,1):i/p(2,e,1),a*=4503599627370496,e=52-e,e>0){f(u,0,a),s=o;while(s>=7)f(u,1e7,0),s-=7;f(u,p(10,s,1),0),s=e-1;while(s>=23)v(u,1<<23),s-=23;v(u,1<<s),f(u,1,1),v(u,2),y=_(u)}else f(u,0,a),f(u,1<<-e,0),y=_(u)+m("0",o);return o>0?(n=y.length,y=g+(n<=o?"0."+m("0",o-n)+y:h(y,0,n-o)+"."+h(y,n-o))):y=g+y,y}})}}]);
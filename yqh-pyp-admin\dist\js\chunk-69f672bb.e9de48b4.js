(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-69f672bb","chunk-2d21dc15"],{"8c90":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"mod-config"},[t("el-form",{attrs:{inline:!0,model:e.dataForm}},[t("el-form-item",[t("el-input",{attrs:{placeholder:"文件名",clearable:""},model:{value:e.dataForm.filename,callback:function(t){e.$set(e.dataForm,"filename",t)},expression:"dataForm.filename"}})],1),t("el-form-item",[t("el-button",{on:{click:function(t){return e.getDataList()}}},[e._v("查询")]),t("el-button",{attrs:{type:"success"},on:{click:function(t){return e.pullVideo()}}},[e._v("拉取视频")])],1),t("el-form-item",[t("el-upload",{staticClass:"upload-demo",attrs:{data:{placeId:e.placeId,activityId:e.activityId},"on-success":e.handleVodUploadSuccess,"on-remove":e.handleVodRemove,"before-remove":e.beforeVodRemove,"on-exceed":e.handleUploadExceed,"file-list":e.fileList,action:e.url,limit:1}},[t("el-button",{attrs:{type:"primary"}},[e._v("上传视频")]),t("el-tooltip",{attrs:{placement:"right-end"}},[t("div",{attrs:{slot:"content"},slot:"content"},[e._v(" 最大支持10G，"),t("br"),e._v(" 支持3GP、ASF、AVI、DAT、DV、F4V、"),t("br"),e._v(" GIF、M2T、M4V、MJ2、MJPEG、MKV、MOV、MP4、"),t("br"),e._v(" MPE、MPG、MPEG、MTS、OGG、QT、RM、RMVB、"),t("br"),e._v(" SWF、TS、VOB、WMV、WEBM 等视频格式上传 ")]),t("i",{staticClass:"el-icon-question"})])],1)],1),t("el-form-item",{staticStyle:{float:"right"}},[t("el-button",{attrs:{type:"success"},on:{click:function(t){return e.$router.go(-1)}}},[e._v("返回")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:e.dataList,border:""},on:{"selection-change":e.selectionChangeHandle}},[t("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),t("el-table-column",{attrs:{prop:"id","header-align":"center",align:"center",label:"id"}}),t("el-table-column",{attrs:{prop:"fileId","header-align":"center",align:"center",width:"280px","show-overflow-tooltip":"",label:"视频fileID"}}),t("el-table-column",{attrs:{prop:"filename","header-align":"center",align:"center","show-overflow-tooltip":"",label:"视频名称"}}),t("el-table-column",{attrs:{prop:"fileSize","header-align":"center",align:"center",label:"文件大小"}}),t("el-table-column",{attrs:{prop:"duration","header-align":"center",align:"center",label:"视频时长"}}),t("el-table-column",{attrs:{prop:"mediaUrl","header-align":"center","show-overflow-tooltip":"",align:"center",label:"视频url"}}),t("el-table-column",{attrs:{prop:"frameRate","header-align":"center",align:"center",label:"帧率"}}),t("el-table-column",{attrs:{prop:"paixu","header-align":"center",align:"center",label:"排序"}}),t("el-table-column",{attrs:{prop:"createOn","show-overflow-tooltip":"","header-align":"center",align:"center",label:"创建时间"}}),t("el-table-column",{attrs:{prop:"updateOn","show-overflow-tooltip":"","header-align":"center",align:"center",label:"更新时间"}}),t("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.download(a.row.mediaUrl)}}},[e._v("下载视频")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.addOrUpdateHandle(a.row.id)}}},[e._v("修改")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHandle(a.row.id)}}},[e._v("删除")])]}}])})],1),t("el-pagination",{attrs:{"current-page":e.pageIndex,"page-sizes":[10,20,50,100],"page-size":e.pageSize,total:e.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle}}),e.addOrUpdateVisible?t("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:e.getDataList}}):e._e()],1)},o=[],r=(a("99af"),a("a15b"),a("d81d"),a("b0c0"),a("a573"),a("d33c")),l={data:function(){return{pushKey:"",dataForm:{filename:""},placeId:"",activityId:"",dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1}},components:{AddOrUpdate:r["default"]},activated:function(){this.placeId=this.$route.query.placeId,this.activityId=this.$route.query.activityId,this.pushKey=this.$route.query.pushKey,this.url=this.$http.adornUrl("/place/placeactivityvideo/uploadAlyVideo?token=".concat(this.$cookie.get("token"))),this.getDataList()},methods:{getDataList:function(){var e=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/place/placeactivityvideo/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,placeId:this.placeId,filename:this.dataForm.filename})}).then((function(t){var a=t.data;a&&200===a.code?(e.dataList=a.page.list,e.totalPage=a.page.totalCount):(e.dataList=[],e.totalPage=0),e.dataListLoading=!1}))},download:function(e){window.open(e)},pullVideo:function(){var e=this;this.$http({url:this.$http.adornUrl("/place/placeactivityvideo/pullVideo"),method:"get",params:this.$http.adornParams({pushKey:this.pushKey})}).then((function(t){var a=t.data;a&&200===a.code?e.getDataList():e.$message.error(a.msg)}))},sizeChangeHandle:function(e){this.pageSize=e,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(e){this.pageIndex=e,this.getDataList()},selectionChangeHandle:function(e){this.dataListSelections=e},addOrUpdateHandle:function(e){var t=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){t.$refs.addOrUpdate.init(e)}))},deleteHandle:function(e){var t=this,a=e?[e]:this.dataListSelections.map((function(e){return e.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(e?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:t.$http.adornUrl("/place/placeactivityvideo/delete"),method:"post",data:t.$http.adornData(a,!1)}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.getDataList()}}):(t.$message.error(a.msg),t.getDataList())}))}))},handleVodUploadSuccess:function(e,t,a){e&&200===e.code?this.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){location.reload()}}):this.$message.error(e.msg)},handleUploadExceed:function(e,t){this.$message.warning("想要重新上传视频，请先删除已上传的视频")},handleVodRemove:function(){var e=this;video.deleteAlyVideo(this.video.videoSourceId).then((function(t){e.$message({type:"success",message:"删除视频成功！！!"}),e.fileList=[],e.video.videoSourceId="",e.video.videoOriginalName=""}))},beforeVodRemove:function(e,t){return this.$confirm("确定移除".concat(e.name,"吗？？？"))}}},n=l,d=a("2877"),s=Object(d["a"])(n,i,o,!1,null,null,null);t["default"]=s.exports},a15b:function(e,t,a){"use strict";var i=a("23e7"),o=a("e330"),r=a("44ad"),l=a("fc6a"),n=a("a640"),d=o([].join),s=r!==Object,c=s||!n("join",",");i({target:"Array",proto:!0,forced:c},{join:function(e){return d(l(this),void 0===e?",":e)}})},a573:function(e,t,a){"use strict";a("ab43")},ab43:function(e,t,a){"use strict";var i=a("23e7"),o=a("d024"),r=a("c430");i({target:"Iterator",proto:!0,real:!0,forced:r},{map:o})},d024:function(e,t,a){"use strict";var i=a("c65b"),o=a("59ed"),r=a("825a"),l=a("46c4"),n=a("c5cc"),d=a("9bdd"),s=n((function(){var e=this.iterator,t=r(i(this.next,e)),a=this.done=!!t.done;if(!a)return d(e,this.mapper,[t.value,this.counter++],!0)}));e.exports=function(e){return r(this),o(e),new s(l(this),{mapper:e})}},d33c:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"视频fileID",prop:"fileId"}},[t("el-input",{attrs:{placeholder:"视频fileID"},model:{value:e.dataForm.fileId,callback:function(t){e.$set(e.dataForm,"fileId",t)},expression:"dataForm.fileId"}})],1),t("el-form-item",{attrs:{label:"视频名称",prop:"filename"}},[t("el-input",{attrs:{placeholder:"视频名称"},model:{value:e.dataForm.filename,callback:function(t){e.$set(e.dataForm,"filename",t)},expression:"dataForm.filename"}})],1),t("el-form-item",{attrs:{label:"文件大小 单位 bit",prop:"fileSize"}},[t("el-input",{attrs:{placeholder:"文件大小 单位 bit"},model:{value:e.dataForm.fileSize,callback:function(t){e.$set(e.dataForm,"fileSize",t)},expression:"dataForm.fileSize"}})],1),t("el-form-item",{attrs:{label:"视频时长 单位 秒",prop:"duration"}},[t("el-input",{attrs:{placeholder:"视频时长 单位 秒"},model:{value:e.dataForm.duration,callback:function(t){e.$set(e.dataForm,"duration",t)},expression:"dataForm.duration"}})],1),t("el-form-item",{attrs:{label:"视频url",prop:"mediaUrl"}},[t("el-input",{attrs:{placeholder:"视频url"},model:{value:e.dataForm.mediaUrl,callback:function(t){e.$set(e.dataForm,"mediaUrl",t)},expression:"dataForm.mediaUrl"}})],1),t("el-form-item",{attrs:{label:"帧率",prop:"frameRate"}},[t("el-input",{attrs:{placeholder:"帧率"},model:{value:e.dataForm.frameRate,callback:function(t){e.$set(e.dataForm,"frameRate",t)},expression:"dataForm.frameRate"}})],1),t("el-form-item",{attrs:{label:"排序",prop:"paixu"}},[t("el-input",{attrs:{placeholder:"排序"},model:{value:e.dataForm.paixu,callback:function(t){e.$set(e.dataForm,"paixu",t)},expression:"dataForm.paixu"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},o=[],r={data:function(){return{visible:!1,dataForm:{id:0,fileId:"",filename:"",fileSize:"",duration:"",mediaUrl:"",expireTime:"",streamName:"",frameRate:"",paixu:0,activityId:"",placeId:""},dataRule:{}}},methods:{init:function(e){var t=this;this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){t.$refs["dataForm"].resetFields(),t.dataForm.id&&t.$http({url:t.$http.adornUrl("/place/placeactivityvideo/info/".concat(t.dataForm.id)),method:"get",params:t.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.fileId=a.placeActivityVideo.fileId,t.dataForm.filename=a.placeActivityVideo.filename,t.dataForm.fileSize=a.placeActivityVideo.fileSize,t.dataForm.duration=a.placeActivityVideo.duration,t.dataForm.mediaUrl=a.placeActivityVideo.mediaUrl,t.dataForm.expireTime=a.placeActivityVideo.expireTime,t.dataForm.streamName=a.placeActivityVideo.streamName,t.dataForm.frameRate=a.placeActivityVideo.frameRate,t.dataForm.paixu=a.placeActivityVideo.paixu,t.dataForm.activityId=a.placeActivityVideo.activityId,t.dataForm.placeId=a.placeActivityVideo.placeId)}))}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$http({url:e.$http.adornUrl("/place/placeactivityvideo/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,fileId:e.dataForm.fileId,filename:e.dataForm.filename,fileSize:e.dataForm.fileSize,duration:e.dataForm.duration,mediaUrl:e.dataForm.mediaUrl,expireTime:e.dataForm.expireTime,streamName:e.dataForm.streamName,frameRate:e.dataForm.frameRate,paixu:e.dataForm.paixu,activityId:e.dataForm.activityId,placeId:e.dataForm.placeId})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(a.msg)}))}))}}},l=r,n=a("2877"),d=Object(n["a"])(l,i,o,!1,null,null,null);t["default"]=d.exports},d81d:function(e,t,a){"use strict";var i=a("23e7"),o=a("b727").map,r=a("1dde"),l=r("map");i({target:"Array",proto:!0,forced:!l},{map:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}})}}]);
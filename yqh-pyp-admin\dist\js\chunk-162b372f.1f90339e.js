(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-162b372f","chunk-08fc2c05"],{"110f":function(t,e,a){},"498a":function(t,e,a){"use strict";var i=a("23e7"),r=a("58a8").trim,n=a("c8d2");i({target:"String",proto:!0,forced:n("trim")},{trim:function(){return r(this)}})},5140:function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.dataFormSubmit()}}},[e("el-form-item",{attrs:{label:"提示词",prop:"prompt"}},[e("el-input",{attrs:{type:"textarea",rows:8,placeholder:"提示词"},model:{value:t.dataForm.prompt,callback:function(e){t.$set(t.dataForm,"prompt",e)},expression:"dataForm.prompt"}})],1),e("el-form-item",{attrs:{label:"标题",prop:"name"}},[e("el-input",{attrs:{placeholder:"标题"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",{attrs:{label:"提示词",prop:"title"}},[e("el-input",{attrs:{placeholder:"提示词"},model:{value:t.dataForm.title,callback:function(e){t.$set(t.dataForm,"title",e)},expression:"dataForm.title"}})],1),e("el-form-item",{attrs:{label:"文案",prop:"result"}},[e("el-input",{attrs:{type:"textarea",rows:8,placeholder:"文案内容将在这里显示，也可以手动编辑"},model:{value:t.dataForm.result,callback:function(e){t.$set(t.dataForm,"result",e)},expression:"dataForm.result"}})],1),e("el-form-item",{attrs:{label:"使用次数",prop:"useCount"}},[e("el-input",{attrs:{placeholder:"使用次数"},model:{value:t.dataForm.useCount,callback:function(e){t.$set(t.dataForm,"useCount",e)},expression:"dataForm.useCount"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],n=(a("a15b"),a("b64b"),a("498a"),{data:function(){return{visible:!1,generating:!1,dataForm:{repeatToken:"",id:0,activityId:"",paixu:"",useCount:"",model:"",prompt:"",resultSummary:"",searchResults:"",thinkProcess:"",query:"",name:"",title:"",result:""},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],paixu:[{required:!0,message:"不能为空",trigger:"blur"}],useCount:[{required:!0,message:"使用次数不能为空",trigger:"blur"}],model:[{required:!0,message:"使用模型不能为空",trigger:"blur"}],prompt:[{required:!0,message:"提示词不能为空",trigger:"blur"}],resultSummary:[{required:!0,message:"结果摘要不能为空",trigger:"blur"}],searchResults:[{required:!0,message:"搜索结果(JSON格式)不能为空",trigger:"blur"}],thinkProcess:[{required:!0,message:"AI思考过程不能为空",trigger:"blur"}],query:[{required:!0,message:"自定义输入不能为空",trigger:"blur"}],name:[{required:!0,message:"标题不能为空",trigger:"blur"}],title:[{required:!0,message:"提示词不能为空",trigger:"blur"}],result:[{required:!0,message:"文案不能为空",trigger:"blur"}]}}},methods:{init:function(t,e){var a=this;this.getToken(),this.dataForm.id=t||0,this.dataForm.activityId=e,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id||(a.dataForm.title='根据"易企化,碰一碰AI爆店码,获客营销"这个提示词，帮我生成以下内容，并以JSON格式返回（键名使用英文）：- 标题（title，20字以内）- 内容（content，100字以内）- 话题（topics，10个，用逗号分隔，不带#）'),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/activity/activitytext/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.dataForm.activityId=e.activityText.activityId,a.dataForm.paixu=e.activityText.paixu,a.dataForm.useCount=e.activityText.useCount,a.dataForm.model=e.activityText.model,a.dataForm.prompt=e.activityText.prompt,a.dataForm.resultSummary=e.activityText.resultSummary,a.dataForm.searchResults=e.activityText.searchResults,a.dataForm.thinkProcess=e.activityText.thinkProcess,a.dataForm.query=e.activityText.query,a.dataForm.name=e.activityText.name,a.dataForm.title=e.activityText.title,a.dataForm.result=e.activityText.result)}))}))},getToken:function(){var t=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.repeatToken=a.result)}))},generateContent:function(){var t=this;if(this.dataForm.title&&""!==this.dataForm.title.trim()){var e='根据"易企化,碰一碰AI爆店码,获客营销"这个提示词，帮我生成以下内容，并以JSON格式返回（键名使用英文）：- 标题（title，20字以内）- 内容（content，100字以内）- 话题（topics，10个，用逗号分隔，不带#）',a=this.dataForm.title.trim()||e;this.generating=!0,this.$http({url:this.$http.adornUrl("/activity/activitytext/generate"),method:"post",data:this.$http.adornData({prompt:a,appid:this.$cookie.get("appid")})}).then((function(e){var a=e.data;if(t.generating=!1,a&&200===a.code)try{var i="string"===typeof a.result?JSON.parse(a.result):a.result,r=t.formatGeneratedContent(i);t.dataForm.result=r,!t.dataForm.name&&i.title&&(t.dataForm.name=i.title),t.$message.success("文案生成成功！")}catch(n){console.error("解析生成结果失败:",n),t.$message.error("生成结果解析失败，请重试")}else t.$message.error(a.msg||"文案生成失败，请重试")})).catch((function(e){t.generating=!1,console.error("生成文案请求失败:",e),t.$message.error("网络请求失败，请检查网络连接")}))}else this.$message.warning("请先输入提示词")},formatGeneratedContent:function(t){var e="";if(t.title&&(e+="标题：".concat(t.title,"\n\n")),t.content&&(e+="内容：".concat(t.content,"\n\n")),t.topics){var a=Array.isArray(t.topics)?t.topics.join(", "):t.topics;e+="话题：".concat(a)}return e},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/activity/activitytext/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({repeatToken:t.dataForm.repeatToken,id:t.dataForm.id||void 0,activityId:t.dataForm.activityId,paixu:t.dataForm.paixu,useCount:t.dataForm.useCount,model:t.dataForm.model,prompt:t.dataForm.prompt,resultSummary:t.dataForm.resultSummary,searchResults:t.dataForm.searchResults,thinkProcess:t.dataForm.thinkProcess,query:t.dataForm.query,name:t.dataForm.name,title:t.dataForm.title,result:t.dataForm.result})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):(t.$message.error(a.msg),"不能重复提交"!=a.msg&&t.getToken())}))}))}}}),o=n,s=a("2877"),l=Object(s["a"])(o,i,r,!1,null,null,null);e["default"]=l.exports},"7db0":function(t,e,a){"use strict";var i=a("23e7"),r=a("b727").find,n=a("44d2"),o="find",s=!0;o in[]&&Array(1)[o]((function(){s=!1})),i({target:"Array",proto:!0,forced:s},{find:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}}),n(o)},a15b:function(t,e,a){"use strict";var i=a("23e7"),r=a("e330"),n=a("44ad"),o=a("fc6a"),s=a("a640"),l=r([].join),c=n!==Object,d=c||!s("join",",");i({target:"Array",proto:!0,forced:d},{join:function(t){return l(o(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var i=a("23e7"),r=a("d024"),n=a("c430");i({target:"Iterator",proto:!0,real:!0,forced:n},{map:r})},c8d2:function(t,e,a){"use strict";var i=a("5e77").PROPER,r=a("d039"),n=a("5899"),o="​᠎";t.exports=function(t){return r((function(){return!!n[t]()||o[t]()!==o||i&&n[t].name!==t}))}},d024:function(t,e,a){"use strict";var i=a("c65b"),r=a("59ed"),n=a("825a"),o=a("46c4"),s=a("c5cc"),l=a("9bdd"),c=s((function(){var t=this.iterator,e=n(i(this.next,t)),a=this.done=!!e.done;if(!a)return l(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return n(this),r(t),new c(o(this),{mapper:t})}},d064:function(t,e,a){"use strict";a("110f")},d81d:function(t,e,a){"use strict";var i=a("23e7"),r=a("b727").map,n=a("1dde"),o=n("map");i({target:"Array",proto:!0,forced:!o},{map:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})},e6c7:function(t,e,a){"use strict";a.r(e);a("a4d3"),a("e01a"),a("b0c0"),a("498a");var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.onSearch()}}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"关键词",clearable:""},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",[e("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"广告类型",clearable:""},model:{value:t.dataForm.adType,callback:function(e){t.$set(t.dataForm,"adType",e)},expression:"dataForm.adType"}},t._l(t.adTypeOptions,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.onSearch()}}},[t._v("查询")]),t.isAuth("activity:activitytext:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("activity:activitytext:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e(),t.isAuth("activity:activitytext:update")?e("el-button",{attrs:{type:"warning",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.batchSetAiTagHandle()}}},[t._v("批量设置AI标签")]):t._e()],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("el-card",{staticClass:"generate-content-card",staticStyle:{"margin-bottom":"20px"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v("AI文案生成")])]),e("el-form",{attrs:{model:t.generateForm,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"广告类型："}},[e("el-select",{staticStyle:{width:"400px"},attrs:{placeholder:"请选择广告类型"},model:{value:t.generateForm.adType,callback:function(e){t.$set(t.generateForm,"adType",e)},expression:"generateForm.adType"}},t._l(t.adTypeOptions,(function(a){return e("el-option",{key:a.value,attrs:{label:a.label,value:a.value}},[e("span",{staticStyle:{float:"left"}},[t._v(t._s(a.label))]),e("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v(t._s(a.description))])])})),1)],1),e("el-form-item",{attrs:{label:"标题生成："}},[e("el-radio-group",{model:{value:t.generateForm.nameMode,callback:function(e){t.$set(t.generateForm,"nameMode",e)},expression:"generateForm.nameMode"}},[e("el-radio",{attrs:{label:"ai"}},[t._v("AI生成")]),e("el-radio",{attrs:{label:"manual"}},[t._v("手动填写")])],1)],1),"manual"===t.generateForm.nameMode?e("el-form-item",{attrs:{label:"标题："}},[e("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:t.activityConfig.defaultName?"默认：".concat(t.activityConfig.defaultName):"请输入标题（20字以内）",maxlength:"20","show-word-limit":""},model:{value:t.generateForm.manualTitle,callback:function(e){t.$set(t.generateForm,"manualTitle",e)},expression:"generateForm.manualTitle"}})],1):t._e(),e("el-form-item",{attrs:{label:"提示词："}},[e("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:t.activityConfig.defaultTitle?"默认：".concat(t.activityConfig.defaultTitle):"请输入提示词，如：易企化,碰一碰AI爆店码,获客营销"},model:{value:t.generateForm.promptKeyword,callback:function(e){t.$set(t.generateForm,"promptKeyword",e)},expression:"generateForm.promptKeyword"}})],1),e("el-form-item",{attrs:{label:"自定义补充："}},[e("el-input",{staticStyle:{width:"600px"},attrs:{type:"textarea",placeholder:t.activityConfig.defaultUserInput||"可以在这里补充您的想法或特殊要求，比如：突出产品特色、针对特定人群、特定风格等",rows:3},model:{value:t.generateForm.userCustomInput,callback:function(e){t.$set(t.generateForm,"userCustomInput",e)},expression:"generateForm.userCustomInput"}}),e("div",{staticStyle:{"margin-top":"5px","font-size":"12px",color:"#909399"}},[e("i",{staticClass:"el-icon-info"}),t._v(" 这里的内容会和上面的提示词一起组装发送给AI ")])],1),e("el-form-item",[e("el-button",{attrs:{type:"primary",loading:t.generating},on:{click:t.generateContent}},[t._v(" "+t._s(t.generating?"生成中...":"生成文案")+" ")]),e("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"info"},on:{click:t.resetToDefault}},[t._v(" 重置为默认配置 ")])],1)],1),t.generatedResult?e("div",{staticStyle:{"margin-top":"20px",padding:"15px","background-color":"#f5f7fa","border-radius":"4px"}},[e("h4",{staticStyle:{"margin-top":"0",color:"#409EFF"}},[t._v("生成结果：")]),e("div",{staticStyle:{"white-space":"pre-line","line-height":"1.6"}},[t._v(t._s(t.formattedResult))])]):t._e()],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"adType","header-align":"center",align:"center",label:"广告类型"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:t.getAdTypeTagType(a.row.adType)}},[t._v(" "+t._s(t.getAdTypeLabel(a.row.adType))+" ")])]}}])}),e("el-table-column",{attrs:{prop:"prompt","show-overflow-tooltip":!0,"header-align":"center",align:"center",label:"提示词"}}),e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"标题"}}),e("el-table-column",{attrs:{prop:"title","header-align":"center",align:"center",label:"提示词"}}),e("el-table-column",{attrs:{prop:"result","show-overflow-tooltip":!0,"header-align":"center",align:"center",label:"文案"}}),e("el-table-column",{attrs:{prop:"useCount","header-align":"center",align:"center",label:"使用次数"}}),e("el-table-column",{attrs:{prop:"aiTag","header-align":"center",align:"center",width:"150",label:"AI标签"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.aiTag?e("div",{staticClass:"ai-tags"},t._l(a.row.aiTag.split(","),(function(a){return e("el-tag",{key:a,staticStyle:{margin:"2px"},attrs:{size:"mini"}},[t._v(" "+t._s(a.trim())+" ")])})),1):e("span",{staticClass:"no-tags"},[t._v("通用文案")])]}}])}),e("el-table-column",{attrs:{prop:"createOn","show-overflow-tooltip":!0,"header-align":"center",align:"center",label:"创建时间"}}),e("el-table-column",{attrs:{prop:"updateOn","show-overflow-tooltip":!0,"header-align":"center",align:"center",label:"更新时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e(),e("el-dialog",{attrs:{title:"批量设置AI标签",visible:t.batchAiTagVisible,width:"500px","append-to-body":""},on:{"update:visible":function(e){t.batchAiTagVisible=e}}},[e("el-form",{attrs:{model:t.batchAiTagForm,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"选择AI标签"}},[e("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"从活动AI标签中选择"},model:{value:t.batchAiTagForm.selectedTags,callback:function(e){t.$set(t.batchAiTagForm,"selectedTags",e)},expression:"batchAiTagForm.selectedTags"}},t._l(t.activityAiTags,(function(t){return e("el-option",{key:t,attrs:{label:t,value:t}})})),1),e("div",{staticStyle:{"margin-top":"10px","font-size":"12px",color:"#909399"}},[e("i",{staticClass:"el-icon-info"}),t._v(" 将为选中的 "+t._s(t.dataListSelections.length)+" 条文案设置AI标签。不选择任何标签表示设置为通用文案 ")])],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.batchAiTagVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary",loading:t.batchAiTagSubmitting},on:{click:t.confirmBatchSetAiTag}},[t._v(" "+t._s(t.batchAiTagSubmitting?"设置中...":"确定")+" ")])],1)],1)],1)},r=[],n=(a("99af"),a("7db0"),a("a15b"),a("d81d"),a("d3b7"),a("0643"),a("fffc"),a("a573"),a("5140")),o={data:function(){return{dataForm:{name:"",appid:"",adType:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,activityId:"",generateForm:{adType:"douyin",nameMode:"ai",manualTitle:"",promptKeyword:"易企化,碰一碰AI爆店码,获客营销",userCustomInput:""},activityConfig:{nameMode:"ai",defaultName:"",defaultTitle:"",defaultUserInput:""},generating:!1,generatedResult:null,formattedResult:"",adTypeOptions:[],batchAiTagVisible:!1,batchAiTagSubmitting:!1,activityAiTags:[],batchAiTagForm:{selectedTags:[]}}},components:{AddOrUpdate:n["default"]},activated:function(){this.activityId=this.$route.query.activityId,this.loadActivityConfig(),this.loadAdTypeConfigs(),this.loadActivityAiTags(),this.getDataList()},methods:{onSearch:function(){this.pageIndex=1,this.getDataList()},loadActivityConfig:function(){var t=this;this.activityId?this.$http({url:this.$http.adornUrl("/activity/activity/info/".concat(this.activityId)),method:"get"}).then((function(e){var a=e.data;if(a&&200===a.code){var i=a.activity;t.activityConfig={nameMode:i.nameMode||"ai",defaultName:i.defaultName||"",defaultTitle:i.defaultTitle||"",defaultUserInput:i.defaultUserInput||""},t.applyActivityConfig()}else console.error("加载活动配置失败:",a.msg)})).catch((function(t){console.error("加载活动配置失败:",t)})):console.warn("活动ID为空，无法加载活动配置")},applyActivityConfig:function(){this.generateForm.nameMode=this.activityConfig.nameMode,"manual"===this.activityConfig.nameMode&&this.activityConfig.defaultName&&(this.generateForm.manualTitle=this.activityConfig.defaultName),this.activityConfig.defaultTitle&&(this.generateForm.promptKeyword=this.activityConfig.defaultTitle),this.activityConfig.defaultUserInput&&(this.generateForm.userCustomInput=this.activityConfig.defaultUserInput),console.log("已应用活动配置:",this.activityConfig)},resetToDefault:function(){var t=this;this.$confirm("确定要重置为活动的默认配置吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.applyActivityConfig(),t.$message.success("已重置为默认配置")})).catch((function(){}))},loadAdTypeConfigs:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/adtypeconfig/enabled"),method:"get"}).then((function(e){var a=e.data;a&&200===a.code?(t.adTypeOptions=(a.list||[]).map((function(t){return{value:t.typeCode,label:t.typeName,description:"适合".concat(t.platform,"的").concat(t.contentType,"文案")}})),t.adTypeOptions.length>0&&!t.generateForm.adType&&(t.generateForm.adType=t.adTypeOptions[0].value)):(console.error("加载广告类型配置失败:",a.msg),t.adTypeOptions=[])})).catch((function(e){console.error("加载广告类型配置失败:",e),t.adTypeOptions=[]}))},getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/activity/activitytext/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,name:this.dataForm.name,activityId:this.activityId,appid:this.$cookie.get("appid"),adType:this.dataForm.adType})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(t,e.activityId)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/activity/activitytext/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},generateContent:function(){var t=this;if(this.generateForm.adType)if(this.generateForm.promptKeyword&&""!==this.generateForm.promptKeyword.trim()){if("manual"===this.generateForm.nameMode){if(!this.generateForm.manualTitle||""===this.generateForm.manualTitle.trim())return void this.$message.warning("请先输入标题");if(this.generateForm.manualTitle.length>20)return void this.$message.warning("标题不能超过20个字符")}this.generating=!0,this.generatedResult=null,this.formattedResult="",this.$http({url:this.$http.adornUrl("/activity/activitytext/generate"),method:"post",data:this.$http.adornData({activityId:this.activityId,model:"",appid:this.$cookie.get("appid"),nameMode:this.generateForm.nameMode,name:"manual"===this.generateForm.nameMode?this.generateForm.manualTitle.trim():null,query:this.generateForm.promptKeyword.trim(),adType:this.generateForm.adType,userCustomInput:this.generateForm.userCustomInput.trim()})}).then((function(e){var a=e.data;t.generating=!1,a&&200===a.code?(t.getDataList(),t.$message.success("文案生成成功！")):t.$message.error(a.msg||"文案生成失败，请重试")})).catch((function(e){t.generating=!1,console.error("生成文案请求失败:",e),t.$message.error("网络请求失败，请检查网络连接")}))}else this.$message.warning("请先输入提示词");else this.$message.warning("请先选择广告类型")},getAdTypeLabel:function(t){var e=this.adTypeOptions.find((function(e){return e.value===t}));return e?e.label:"通用文案"},getAdTypeTagType:function(t){var e={douyin:"danger",xiaohongshu:"warning",kuaishou:"info",dianping:"success",meituan:"warning",douyin_review:"primary",weixin:"success",weibo:"primary",bilibili:"danger",zhihu:"primary",taobao:"warning",jingdong:"danger",general:"info"};return e[t]||"info"},loadActivityAiTags:function(){var t=this;this.activityId&&this.$http({url:this.$http.adornUrl("/web/activity/activitytext/getAiTags"),method:"get",params:this.$http.adornParams({activityId:this.activityId})}).then((function(e){var a=e.data;a&&200===a.code&&(t.activityAiTags=a.tags||[])})).catch((function(e){console.error("加载活动AI标签失败:",e),t.activityAiTags=[]}))},batchSetAiTagHandle:function(){this.dataListSelections.length<=0?this.$message.warning("请选择要设置标签的文案"):0!==this.activityAiTags.length?(this.batchAiTagForm.selectedTags=[],this.batchAiTagVisible=!0):this.$message.warning("当前活动未配置AI标签，请先在活动管理中配置AI标签")},confirmBatchSetAiTag:function(){var t=this;this.batchAiTagSubmitting=!0;var e=this.dataListSelections.map((function(t){return t.id})),a=this.batchAiTagForm.selectedTags.join(",");this.$http({url:this.$http.adornUrl("/activity/activitytext/batchSetAiTag"),method:"post",data:this.$http.adornData({textIds:e,aiTag:a})}).then((function(e){var a=e.data;t.batchAiTagSubmitting=!1,a&&200===a.code?(t.$message.success("批量设置AI标签成功"),t.batchAiTagVisible=!1,t.getDataList()):t.$message.error(a.msg||"批量设置AI标签失败")})).catch((function(e){t.batchAiTagSubmitting=!1,console.error("批量设置AI标签失败:",e),t.$message.error("批量设置AI标签失败")}))}}},s=o,l=(a("d064"),a("2877")),c=Object(l["a"])(s,i,r,!1,null,"735019a7",null);e["default"]=c.exports},f665:function(t,e,a){"use strict";var i=a("23e7"),r=a("2266"),n=a("59ed"),o=a("825a"),s=a("46c4");i({target:"Iterator",proto:!0,real:!0},{find:function(t){o(this),n(t);var e=s(this),a=0;return r(e,(function(e,i){if(t(e,a++))return i(e)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},fffc:function(t,e,a){"use strict";a("f665")}}]);
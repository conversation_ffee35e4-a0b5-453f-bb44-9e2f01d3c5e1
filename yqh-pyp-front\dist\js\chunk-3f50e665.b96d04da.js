(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3f50e665","chunk-37a545c8"],{"02d7":function(t,i,e){"use strict";e.d(i,"a",(function(){return a}));var a=[{key:0,value:"未提交"},{key:1,value:"已提交"},{key:2,value:"已通过"},{key:3,value:"未通过"},{key:4,value:"已超时"},{key:5,value:"作废"}]},"1b69":function(t,i,e){"use strict";e.r(i);e("7f7f");var a,n=function(){var t=this,i=t._self._c;return i("div",{staticStyle:{background:"white","margin-bottom":"20px"}},[i("van-row",{attrs:{gutter:"20"}},[i("van-col",{attrs:{span:"16"}},[i("van-swipe",{staticStyle:{width:"100%"},attrs:{autoplay:3e3}},t._l(t.activityInfo.appFileList,(function(t,e){return i("van-swipe-item",{key:e},[i("van-image",{attrs:{width:"100%",src:t.url}})],1)})),1)],1),i("van-col",{attrs:{span:"8"}},[i("div",{staticStyle:{"margin-top":"20px"}},[i("van-cell",{attrs:{title:"会议名称",value:t.activityInfo.name}}),t.activityInfo.address?i("van-cell",{attrs:{title:"会议地点",value:t.activityInfo.address}}):t._e(),i("van-cell",{attrs:{title:"会议开始时间",value:t.activityInfo.startTime}}),i("van-cell",{attrs:{title:"会议结束时间",value:t.activityInfo.endTime}}),i("van-cell",{attrs:{title:"登录信息"},scopedSlots:t._u([{key:"default",fn:function(){return[t.userInfo?i("van-button",{attrs:{type:"primary",size:"small",round:""}},[t._v(t._s(t.userInfo.username)+"("+t._s(t.userInfo.mobile)+")")]):i("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.showLogin}},[t._v("未登录(点击登录)")])]},proxy:!0}])}),"1736999159118508033"!=t.activityId?i("van-cell",{attrs:{title:"报名状态"},scopedSlots:t._u([{key:"default",fn:function(){return[1==t.isPay?i("van-button",{attrs:{type:"primary",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名")]):2==t.isPay?i("van-button",{attrs:{type:"warning",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名未交费")]):i("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.turnApply}},[t._v("未报名")])]},proxy:!0}],null,!1,567203517)}):t._e()],1)])],1),i("van-tabs",{staticClass:"nav",on:{click:t.onClick},model:{value:t.cmsId,callback:function(i){t.cmsId=i},expression:"cmsId"}},t._l(t.cmsList,(function(t){return i("van-tab",{key:t.id,attrs:{title:t.title,name:t.id}})})),1),i("pclogin")],1)},s=[],o=e("ade3"),c=(e("a481"),e("6762"),e("2fdb"),e("cacf")),r=e("7dcb"),l=function(){var t=this,i=t._self._c;return i("van-dialog",{attrs:{title:"登录操作",width:"400px"},on:{confirm:t.login},model:{value:t.showPcLogin,callback:function(i){t.showPcLogin=i},expression:"showPcLogin"}},[i("div",{staticClass:"text-center padding"},[i("van-cell-group",{attrs:{inset:""}},[i("van-field",{attrs:{name:"手机号",label:"手机号",required:"",placeholder:"手机号",rules:[{required:!0,message:"请填写手机号"}]},model:{value:t.mobile,callback:function(i){t.mobile=i},expression:"mobile"}}),"1736999159118508033"!=t.activityId?i("van-field",{attrs:{center:"",clearable:"",maxlength:"6",label:"短信验证码",required:"",placeholder:"请输入短信验证码"},scopedSlots:t._u([{key:"button",fn:function(){return[i("van-button",{attrs:{size:"small",type:"primary",disabled:t.waiting},on:{click:function(i){return t.doSendSmsCode()}}},[t.waiting?i("span",[t._v(t._s(t.waitingTime)+"秒后重发")]):i("span",{staticStyle:{"font-size":"13px"}},[t._v("获取验证码")])])]},proxy:!0}],null,!1,850170227),model:{value:t.code,callback:function(i){t.code=i},expression:"code"}}):t._e()],1)],1)])},d=[],u={name:"pclogin",computed:{showPcLogin:{get:function(){return this.$store.state.user.showPcLogin},set:function(t){this.$store.commit("user/changePcLogin",t)}}},data:function(){return{activityId:void 0,waiting:!1,waitingTime:60,mobile:"",code:""}},mounted:function(){this.activityId=this.$route.query.id},methods:{login:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?this.code||0x181b0f883e693000==this.activityId?/^\d{6}$/.test(this.code)||0x181b0f883e693000==this.activityId?void this.$fly.post("/pyp/web/user/pcLogin",{mobile:this.mobile,code:0x181b0f883e693000==this.activityId?"121212":this.code}).then((function(i){i&&200===i.code?(vant.Toast("登录成功"),t.$store.commit("user/update",i.userInfo),t.showPcLogin=!1,"applyIndex"!=t.$route.name&&"livesDetail"!=t.$route.name||location.reload()):vant.Toast(i.msg)})):(vant.Toast("验证码错误"),!1):(vant.Toast("请输入验证码"),!1):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},doSendSmsCode:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.mobile,activityId:this.activityId}).then((function(i){i&&200===i.code?(t.countdown(),vant.Toast("发送验证码成功")):vant.Toast(i.msg)})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var t=this;this.waiting=!0;var i=window.setInterval((function(){t.waitingTime--,t.waitingTime<0&&(window.clearInterval(i),t.waitingTime=60,t.waiting=!1)}),1e3)}}},m=u,v=e("2877"),p=Object(v["a"])(m,l,d,!1,null,null,null),h=p.exports,y={components:{pclogin:h},data:function(){return{activityId:void 0,bannerIndex:0,cmsId:void 0,cmsList:[],cmsInfo:{},activityInfo:{}}},computed:{userInfo:{get:function(){return this.$store.state.user.userInfo},set:function(t){this.$store.commit("user/update",t)}},isPay:{get:function(){return this.$store.state.apply.isPay},set:function(t){this.$store.commit("apply/update",t)}}},mounted:function(){var t=(new Date).getTime();localStorage.setItem("logAddTime",t+36e5),this.openid=this.$cookie.get("openid"),this.activityId=this.$route.query.id,this.cmsId=sessionStorage.getItem("cmsId")||this.$route.query.cmsId,this.checkLogin(),this.getActivityInfo(),this.activityLogCount()},methods:(a={showLogin:function(){this.$store.commit("user/changePcLogin",!0)},checkLogin:function(){var t=this;this.$fly.get("/pyp/web/user/checkLogin").then((function(i){t.userInfo=i.result,t.userInfo?t.checkApply():t.isPay=0}))},bannerIndexChange:function(t){this.bannerIndex=t},checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(i){200==i.code?(t.isPay=i.isPay,1==t.isPay||2==t.isPay&&vant.Dialog.alert({title:"提示",message:"您有一笔注册费待支付，点击跳转"}).then((function(){t.$router.push({name:"applySuccess",query:{orderId:i.result,id:t.activityId}})}))):vant.Toast(i.msg)}))},changeShowType:function(t){this.$emit("changeShowType",t)},turnApply:function(){var t=this.cmsList.filter((function(t){return t.model&&t.model.includes("applyIndex")}));if(t){var i=t[0];sessionStorage.setItem("cmsId",i.id);var e=i.model.replace("${activityId}",i.activityId);this.$router.push(JSON.parse(e))}else vant.Toast("暂未开启报名")},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(i){if(t.loading=!1,200==i.code){t.activityInfo=i.activity,document.title=t.activityInfo.name;var e=t.activityInfo.startTime,a=new Date(e.replace(/-/g,"/")),n=new Date,s=a.getTime()-n.getTime();t.dateCompare=s>0?s:0,t.getCmsList()}}))},activityLogCount:function(){var t=localStorage.getItem("logAddTime"),i=(new Date).getTime();i>t&&this.$fly.post("/pyp/activity/activityviewlog/count",{activityId:this.activityId,device:r["a"].getVersion()}).then((function(t){}))},getCmsList:function(){var t=this;this.$fly.get("/pyp/cms/cms/findByActivityId/".concat(this.activityId)).then((function(i){t.loading=!1,200==i.code?(t.cmsList=i.result,t.cmsId?t.changeShowType(t.cmsId):t.changeShowType(t.cmsList[0].id)):(vant.Toast(i.msg),t.cmsList=[])}))},onClick:function(t){if(console.log(t),sessionStorage.setItem("cmsId",t),this.cmsInfo=this.cmsList.filter((function(i){return i.id==t}))[0],this.cmsInfo.url&&Object(c["d"])(this.cmsInfo.url))location.href=this.cmsInfo.url;else if(this.cmsInfo.model&&this.isJSON(this.cmsInfo.model)){var i=this.cmsInfo.model.replace("${activityId}",this.cmsInfo.activityId);this.$router.push(JSON.parse(i))}else"cmsIndex"==this.$route.name?this.changeShowType(t):this.$router.push({name:"cmsIndex",query:{id:this.activityId,cmsId:t}})}},Object(o["a"])(a,"bannerIndexChange",(function(t){this.bannerIndex=t})),Object(o["a"])(a,"isJSON",(function(t){try{return JSON.parse(t),!0}catch(i){return!1}})),a)},f=y,g=(e("dd7a"),Object(v["a"])(f,n,s,!1,null,"7bd3d808",null));i["default"]=g.exports},"1d8e":function(t,i,e){"use strict";e.r(i);e("7f7f");var a=function(){var t=this,i=t._self._c;return i("div",{class:t.isMobilePhone?"":"pc-container"},[t.isMobilePhone?t._e():i("pcheader"),i("van-search",{attrs:{placeholder:"请输入您要搜索的考试&问卷关键词","show-action":"",shape:"round"},model:{value:t.dataForm.name,callback:function(i){t.$set(t.dataForm,"name",i)},expression:"dataForm.name"}},[i("div",{staticClass:"search-text",attrs:{slot:"action"},on:{click:t.onSearch},slot:"action"},[t._v("搜索")])]),i("van-list",{attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:t.onLoad},model:{value:t.loading,callback:function(i){t.loading=i},expression:"loading"}},[i("div",{staticClass:"nav-title",staticStyle:{"margin-top":"8px"}},[i("div",{staticClass:"color"}),i("div",{staticClass:"text"},[t._v("考试&问卷列表")])]),t._l(t.dataList,(function(e){return i("van-card",{key:e.id,staticStyle:{background:"white"},scopedSlots:t._u([{key:"num",fn:function(){return[e.examStatus||0==e.examStatus?0==e.examStatus?i("van-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"small",round:"",type:"info"},on:{click:function(i){return t.startExam(e.id)}}},[t._v("继续答题")]):i("van-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"small",round:"",type:"info"}},[t._v(t._s(t.examActivityUserStatus[e.examStatus].value+(0==e.type?"("+e.points+"分)":"")))]):i("van-button",{attrs:{size:"small",round:"",type:"primary"},on:{click:function(i){return t.startExam(e.id)}}},[t._v("开始答题")])]},proxy:!0}],null,!0)},[i("div",{staticStyle:{"font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(e.name))]),i("div",{staticStyle:{"padding-top":"5px","font-size":"14px"},attrs:{slot:"price"},slot:"price"},[0==e.type?i("van-tag",{staticStyle:{"margin-right":"10px"},attrs:{size:"medium",plain:"",type:"danger"}},[t._v("考试")]):i("van-tag",{staticStyle:{"margin-right":"10px"},attrs:{size:"medium",plain:"",type:"primary"}},[t._v("问卷")]),0==e.examType?i("van-tag",{attrs:{size:"medium",plain:"",type:"danger"}},[t._v("统一时间答题")]):i("van-tag",{attrs:{size:"medium",plain:"",type:"primary"}},[t._v("随时答题")])],1),i("div",{staticStyle:{"padding-top":"5px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[0==e.examType?i("div",[t._v("\n          "+t._s(e.examStartTime)+" ~ "+t._s(e.examEndTime)+"\n        ")]):i("div",[t._v("答题时长："+t._s(e.examTime)+"分钟")])])])}))],2)],1)},n=[],s=(e("ac6a"),e("02d7")),o=e("cacf"),c=e("1b69"),r={components:{pcheader:c["default"]},data:function(){return{isMobilePhone:Object(o["c"])(),examActivityUserStatus:s["a"],openid:void 0,activityId:void 0,dataForm:{name:""},loading:!1,finished:!1,dataList:[],pageIndex:1,pageSize:10,totalPage:0}},mounted:function(){document.title="考试&问卷列表",this.activityId=this.$route.query.id,this.openid=this.$cookie.get("openid"),this.$wxShare()},methods:{onSearch:function(){this.pageIndex=1,this.dataList=[],this.getActivityList()},onLoad:function(){this.checkApply(),this.getActivityList()},checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(i){200==i.code?(t.$store.commit("apply/update",i.isPay),1==i.isPay&&1==i.verifyStatus||t.$router.push({name:"applyIndex",query:{id:t.activityId}})):vant.Toast(i.msg)}))},getActivityList:function(){var t=this;this.$fly.get("/pyp/web/exam/list",{page:this.pageIndex,limit:this.pageSize,activityId:this.activityId,name:this.dataForm.name}).then((function(i){200==i.code?i.page.list&&i.page.list.length>0?(i.page.list.forEach((function(i){t.dataList.push(i)})),t.totalPage=i.page.totalPage,t.pageIndex++,t.totalPage<t.pageIndex?t.finished=!0:t.finished=!1):t.finished=!0:(vant.Toast(i.msg),t.dataList=[],t.totalPage=0,t.finished=!0)}))},startExam:function(t){var i=this;this.$fly.get("/pyp/web/exam/startExam",{examId:t}).then((function(e){200==e.code?i.$router.push({name:"examDetail",query:{examActivityUserId:e.examActivityUserId,id:i.activityId,examId:t,token:e.token}}):vant.Toast(e.msg)}))}}},l=r,d=e("2877"),u=Object(d["a"])(l,a,n,!1,null,null,null);i["default"]=u.exports},"7dcb":function(t,i,e){"use strict";e("a481"),e("4917");i["a"]={getVersion:function(){var t=navigator.userAgent||window.navigator.userAgent,i=/HUAWEI|HONOR/gi,e=/[^;]+(?= Build)/gi,a=/CPU iPhone OS \d[_\d]*/gi,n=/CPU OS \d[_\d]*/gi,s=/Windows NT \d[\.\d]*/gi,o=/Linux x\d[_\d]*/gi,c=/Mac OS X \d[_|\.\d]*/gi;return/Android/gi.test(t)?i.test(t)?t.match(i)[0]+t.match(e)[0]:t.match(e)[0]:/iPhone/gi.test(t)?t.match(a)[0].replace(/CPU iPhone OS/gi,"iPhone").replace(/_/g,"."):/iPad/gi.test(t)?t.match(n)[0].replace(/CPU OS/gi,"iPad").replace(/_/g,"."):/Windows/gi.test(t)?"Windows "+(Math.min(parseInt(t.match(s)[0].replace(/Windows NT /,""))+1,10)<7?"XP":Math.min(parseInt(t.match(s)[0].replace(/Windows NT /,""))+1,10)):/Linux/gi.test(t)?t.match(o)[0]:/Macintosh/gi.test(t)?t.match(c)[0].replace(/_/g,"."):"unknown"}}},ac6a:function(t,i,e){for(var a=e("cadf"),n=e("0d58"),s=e("2aba"),o=e("7726"),c=e("32e9"),r=e("84f2"),l=e("2b4c"),d=l("iterator"),u=l("toStringTag"),m=r.Array,v={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},p=n(v),h=0;h<p.length;h++){var y,f=p[h],g=v[f],I=o[f],b=I&&I.prototype;if(b&&(b[d]||c(b,d,m),b[u]||c(b,u,f),r[f]=m,g))for(y in a)b[y]||s(b,y,a[y],!0)}},ade3:function(t,i,e){"use strict";e.d(i,"a",(function(){return o}));var a=e("53ca");function n(t,i){if("object"!==Object(a["a"])(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,i||"default");if("object"!==Object(a["a"])(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===i?String:Number)(t)}function s(t){var i=n(t,"string");return"symbol"===Object(a["a"])(i)?i:String(i)}function o(t,i,e){return i=s(i),i in t?Object.defineProperty(t,i,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[i]=e,t}},cad8:function(t,i,e){},dd7a:function(t,i,e){"use strict";e("cad8")}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e1c6455a","chunk-2d0a4b8c","chunk-2d0a4b8c","chunk-58faa667","chunk-79fe9f98"],{"04ef":function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.onSearch()}}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"参数名",clearable:""},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.onSearch()}}},[t._v("查询")]),t.isAuth("client:clientthing:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("client:clientthing:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回上一页")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"客户文件名称"}}),e("el-table-column",{attrs:{prop:"createOn","show-overflow-tooltip":!0,"header-align":"center",align:"center",label:"创建时间"}}),e("el-table-column",{attrs:{prop:"updateOn","show-overflow-tooltip":!0,"header-align":"center",align:"center",label:"更新时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e()],1)},n=[],r=(a("99af"),a("a15b"),a("d81d"),a("a573"),a("df53")),o={data:function(){return{dataForm:{clientId:"",name:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1}},components:{AddOrUpdate:r["default"]},activated:function(){this.dataForm.clientId=this.$route.query.id,this.getDataList()},methods:{onSearch:function(){this.pageIndex=1,this.getDataList()},getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/client/clientthing/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,name:this.dataForm.name,appid:this.$cookie.get("appid"),clientId:this.dataForm.clientId})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(t,e.dataForm.clientId)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/client/clientthing/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))}}},l=o,s=a("2877"),d=Object(s["a"])(l,i,n,!1,null,null,null);e["default"]=d.exports},"083a":function(t,e,a){"use strict";var i=a("0d51"),n=TypeError;t.exports=function(t,e){if(!delete t[e])throw new n("Cannot delete property "+i(e)+" of "+i(t))}},a15b:function(t,e,a){"use strict";var i=a("23e7"),n=a("e330"),r=a("44ad"),o=a("fc6a"),l=a("a640"),s=n([].join),d=r!==Object,c=d||!l("join",",");i({target:"Array",proto:!0,forced:c},{join:function(t){return s(o(this),void 0===t?",":t)}})},a434:function(t,e,a){"use strict";var i=a("23e7"),n=a("7b0b"),r=a("23cb"),o=a("5926"),l=a("07fa"),s=a("3a34"),d=a("3511"),c=a("65f0"),u=a("8418"),p=a("083a"),m=a("1dde"),h=m("splice"),g=Math.max,f=Math.min;i({target:"Array",proto:!0,forced:!h},{splice:function(t,e){var a,i,m,h,b,v,F=n(this),k=l(F),y=r(t,k),$=arguments.length;for(0===$?a=i=0:1===$?(a=0,i=k-y):(a=$-2,i=f(g(o(e),0),k-y)),d(k+a-i),m=c(F,i),h=0;h<i;h++)b=y+h,b in F&&u(m,h,F[b]);if(m.length=i,a<i){for(h=y;h<k-i;h++)b=h+i,v=h+a,b in F?F[v]=F[b]:p(F,v);for(h=k;h>k-i+a;h--)p(F,h-1)}else if(a>i)for(h=k-i;h>y;h--)b=h+i-1,v=h+a-1,b in F?F[v]=F[b]:p(F,v);for(h=0;h<a;h++)F[h+y]=arguments[h+2];return s(F,k-i+a),m}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var i=a("23e7"),n=a("d024"),r=a("c430");i({target:"Iterator",proto:!0,real:!0,forced:r},{map:n})},d024:function(t,e,a){"use strict";var i=a("c65b"),n=a("59ed"),r=a("825a"),o=a("46c4"),l=a("c5cc"),s=a("9bdd"),d=l((function(){var t=this.iterator,e=r(i(this.next,t)),a=this.done=!!e.done;if(!a)return s(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return r(this),n(t),new d(o(this),{mapper:t})}},d81d:function(t,e,a){"use strict";var i=a("23e7"),n=a("b727").map,r=a("1dde"),o=r("map");i({target:"Array",proto:!0,forced:!o},{map:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}})},df53:function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.dataFormSubmit()}}},[e("el-form-item",{attrs:{label:"客户文件名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"客户文件名称"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",{attrs:{label:"文件地址",prop:"url"}},[e("el-upload",{attrs:{"list-type":"picture-card","before-upload":t.checkFileSize,"on-success":t.appSuccessHandle,"file-list":t.appFileList,action:t.url},scopedSlots:t._u([{key:"file",fn:function(a){var i=a.file;return e("div",{},[e("img",{staticClass:"el-upload-list__item-thumbnail",attrs:{src:i.url,alt:""}}),e("span",{staticClass:"el-upload-list__item-actions"},[e("span",{staticClass:"el-upload-list__item-preview",on:{click:function(e){return t.handleAppPictureCardPreview(i)}}},[e("i",{staticClass:"el-icon-zoom-in"})]),e("span",{staticClass:"el-upload-list__item-delete",on:{click:function(e){return t.handleAppRemove(i)}}},[e("i",{staticClass:"el-icon-delete"})])])])}}])},[e("i",{staticClass:"el-icon-plus",attrs:{slot:"default"},slot:"default"})]),e("el-dialog",{attrs:{visible:t.imgAppDialogVisible},on:{"update:visible":function(e){t.imgAppDialogVisible=e}}},[e("img",{attrs:{width:"100%",src:t.dialogAppImageUrl,alt:""}})])],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},n=[],r=(a("a434"),a("ac1f"),a("5319"),a("7c8d"),{data:function(){return{url:"",appFileList:[],imgAppDialogVisible:!1,dialogAppImageUrl:"",visible:!1,dataForm:{repeatToken:"",id:0,name:"",url:"",clientId:"",appid:"",companyRoleId:""},dataRule:{name:[{required:!0,message:"客户文件名称不能为空",trigger:"blur"}],url:[{required:!0,message:"文件地址不能为空",trigger:"blur"}],clientId:[{required:!0,message:"客户ID不能为空",trigger:"blur"}],appid:[{required:!0,message:"公司ID不能为空",trigger:"blur"}],companyRoleId:[{required:!0,message:"公司权限ID不能为空",trigger:"blur"}]}}},methods:{init:function(t,e){var a=this;this.url=this.$http.adornUrl("/sys/oss/upload?token=".concat(this.$cookie.get("token"))),this.getToken(),this.appFileList=[],this.dataForm.id=t||0,this.dataForm.clientId=e,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id?a.$http({url:a.$http.adornUrl("/client/clientthing/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.appFileList=e.clientThing.appFileList,a.dataForm.name=e.clientThing.name,a.dataForm.url=e.clientThing.url,a.dataForm.clientId=e.clientThing.clientId,a.dataForm.appid=e.clientThing.appid,a.dataForm.companyRoleId=e.clientThing.companyRoleId)})):(a.dataForm.appid=a.$cookie.get("appid"),a.dataForm.companyRoleId=a.$cookie.get("companyRoleId"))}))},getToken:function(){var t=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.repeatToken=a.result)}))},checkFileSize:function(t){return!(t.size/1024/1024>6)||(this.$message.error("".concat(t.name,"文件大于6MB，请选择小于6MB大小的图片")),!1)},appSuccessHandle:function(t,e,a){this.appFileList=a,this.successNum++,t&&200===t.code?this.dataForm.url&&0!=this.dataForm.url.length?this.dataForm.url+=","+t.url:this.dataForm.url=t.url:this.$message.error(t.msg)},handleAppPictureCardPreview:function(t){this.dialogAppImageUrl=t.url,this.imgAppDialogVisible=!0},handleAppRemove:function(t){this.dataForm.url=(","+this.dataForm.url+",").replace(","+t.url+",",",").substr(1).replace(/,$/,""),this.appFileList.splice(this.appFileList.indexOf(t),1)},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/client/clientthing/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({repeatToken:t.dataForm.repeatToken,id:t.dataForm.id||void 0,name:t.dataForm.name,url:t.dataForm.url,clientId:t.dataForm.clientId,appid:t.dataForm.appid,companyRoleId:t.dataForm.companyRoleId})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):(t.$message.error(a.msg),"不能重复提交"!=a.msg&&t.getToken())}))}))}}}),o=r,l=a("2877"),s=Object(l["a"])(o,i,n,!1,null,null,null);e["default"]=s.exports}}]);
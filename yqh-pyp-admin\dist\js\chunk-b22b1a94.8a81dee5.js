(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b22b1a94"],{"359e":function(e,t,a){"use strict";a.r(t);a("b0c0");var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"mod-config"},[t("el-form",{attrs:{inline:!0,model:e.dataForm}},[t("el-form-item",[t("el-input",{attrs:{placeholder:"名称",clearable:""},model:{value:e.dataForm.name,callback:function(t){e.$set(e.dataForm,"name",t)},expression:"dataForm.name"}})],1),t("el-form-item",[t("el-select",{attrs:{filterable:""},model:{value:e.dataForm.clientId,callback:function(t){e.$set(e.dataForm,"clientId",t)},expression:"dataForm.clientId"}},[t("el-option",{attrs:{value:"",label:"全部(客户)"}}),e._l(e.client,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1),t("el-form-item",[t("el-select",{attrs:{placeholder:"省份",filterable:""},on:{change:e.provinceChange},model:{value:e.dataForm.provinceId,callback:function(t){e.$set(e.dataForm,"provinceId",t)},expression:"dataForm.provinceId"}},[t("el-option",{attrs:{value:"",label:"全部(省份)"}}),e._l(e.provinces,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1),t("el-form-item",[t("el-select",{attrs:{placeholder:"城市",filterable:""},model:{value:e.dataForm.cityId,callback:function(t){e.$set(e.dataForm,"cityId",t)},expression:"dataForm.cityId"}},[t("el-option",{attrs:{value:"",label:"全部(城市)"}}),e._l(e.cities,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1),t("el-form-item",[t("el-select",{attrs:{filterable:""},model:{value:e.dataForm.clientType,callback:function(t){e.$set(e.dataForm,"clientType",t)},expression:"dataForm.clientType"}},[t("el-option",{attrs:{value:"",label:"全部(客户类型)"}}),e._l(e.clientType,(function(e){return t("el-option",{key:e,attrs:{label:e,value:e}})}))],2)],1),t("el-form-item",[t("el-select",{attrs:{filterable:""},model:{value:e.dataForm.oldOrNew,callback:function(t){e.$set(e.dataForm,"oldOrNew",t)},expression:"dataForm.oldOrNew"}},[t("el-option",{attrs:{value:"",label:"全部(新老客户)"}}),e._l(e.oldOrNew,(function(e){return t("el-option",{key:e,attrs:{label:e,value:e}})}))],2)],1),t("el-form-item",[t("el-select",{attrs:{filterable:""},model:{value:e.dataForm.activityType,callback:function(t){e.$set(e.dataForm,"activityType",t)},expression:"dataForm.activityType"}},[t("el-option",{attrs:{value:"",label:"全部(会议形式)"}}),e._l(e.activityType,(function(e){return t("el-option",{key:e,attrs:{label:e,value:e}})}))],2)],1),t("el-form-item",[t("el-select",{attrs:{filterable:""},model:{value:e.dataForm.userId,callback:function(t){e.$set(e.dataForm,"userId",t)},expression:"dataForm.userId"}},[t("el-option",{attrs:{value:"",label:"全部(员工)"}}),e._l(e.sysuser,(function(e){return t("el-option",{key:e.userId,attrs:{label:e.username,value:e.userId}})}))],2)],1),t("el-form-item",[t("el-date-picker",{attrs:{type:"daterange","value-format":"yyyy/MM/dd","range-separator":"至","start-placeholder":"开始日期(开始时间)","end-placeholder":"结束日期(开始时间)","picker-options":e.pickerOptions},model:{value:e.timeArray,callback:function(t){e.timeArray=t},expression:"timeArray"}})],1),t("el-form-item",[t("el-button",{on:{click:function(t){return e.getDataList()}}},[e._v("查询")]),e.isAuth("activity:activity:save")?t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addOrUpdateHandle()}}},[e._v("新增")]):e._e(),e.isAuth("activity:activity:delete")?t("el-button",{attrs:{type:"danger",disabled:e.dataListSelections.length<=0},on:{click:function(t){return e.deleteHandle()}}},[e._v("批量删除")]):e._e()],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:e.dataList,height:"800px",border:""},on:{"selection-change":e.selectionChangeHandle}},[t("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),t("el-table-column",{attrs:{prop:"clientType","header-align":"center",align:"center",label:"客户类型"}}),t("el-table-column",{attrs:{prop:"oldOrNew","header-align":"center",align:"center",label:"新老客户"}}),t("el-table-column",{attrs:{prop:"clientName","header-align":"center",align:"center",label:"客户名称"}}),t("el-table-column",{attrs:{prop:"duties","header-align":"center",align:"center",label:"科室"}}),t("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"会议名称"}}),t("el-table-column",{attrs:{prop:"code","header-align":"center",align:"center",label:"会议编码"}}),t("el-table-column",{attrs:{prop:"startTime","header-align":"center",align:"center",label:"开始时间"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[e._v(e._s(e._f("dateFilter")(a.row.startTime)))])}}])}),t("el-table-column",{attrs:{prop:"endTime","header-align":"center",align:"center",label:"结束时间"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[e._v(e._s(e._f("dateFilter")(a.row.endTime)))])}}])}),t("el-table-column",{attrs:{prop:"activityType","header-align":"center",align:"center",label:"会议形式"}}),t("el-table-column",{attrs:{prop:"provinceName","header-align":"center",align:"center",label:"区域"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[e._v(e._s(a.row.provinceName+"-"+a.row.cityName))])}}])}),t("el-table-column",{attrs:{prop:"address","header-align":"center",align:"center",label:"会议地址"}}),t("el-table-column",{attrs:{prop:"role","header-align":"center",align:"center",label:"工作组"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},e._l(a.row.activityRoleEntities,(function(a){return t("div",{key:a.id},[t("el-tag",[e._v(e._s(a.username)+"-"+e._s(null==a.roleId?"空":e.activityRole[a.roleId].value))])],1)})),0)}}])}),t("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"250",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.acitivitySettle(a.row.id)}}},[e._v("会议结算表")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.acitivityRole(a.row.id)}}},[e._v("工作人员配置")]),e.isAuth("activity:activity:update")?t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.addOrUpdateHandle(a.row.id)}}},[e._v("修改")]):e._e(),e.isAuth("activity:activity:delete")?t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHandle(a.row.id)}}},[e._v("删除")]):e._e()]}}])})],1),t("el-pagination",{attrs:{"current-page":e.pageIndex,"page-sizes":[10,20,50,100],"page-size":e.pageSize,total:e.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle}})],1)},i=[],r=(a("99af"),a("a15b"),a("d81d"),a("14d9"),a("a573"),a("b2e5")),l=a.n(r),o=a("7de9"),u=a("593c"),c={data:function(){return{timeArray:[],sysuser:[],clientType:[],oldOrNew:[],activityType:[],client:[],provinces:[],cities:[],appid:"",wxAccount:{},yesOrNo:o["g"],activityRole:u["b"],dataForm:{name:"",clientId:"",clientType:"",oldOrNew:"",activityType:"",userId:"",provinceId:"",cityId:""},dataList:[],pageIndex:1,pageSize:20,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]}}},components:{VueQrcode:l.a},filters:{dateFilter:function(e){return e?e.substring(0,10):""}},activated:function(){this.appid=this.$cookie.get("appid"),this.getDataList(),this.getAccountInfo(),this.findClient(),this.getResult(),this.getSysUser(),this.getProvinces()},methods:{getProvinces:function(){var e=this;this.$http({url:this.$http.adornUrl("/sys/region/pid/100000"),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code?e.provinces=a.list:e.provinces=[]}))},provinceChange:function(e){var t=this;if(void 0===e||""===e)return this.cities=[],this.dataForm.cityId="",void this.getDataList();this.$http({url:this.$http.adornUrl("/sys/region/pid/".concat(e)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code?t.cities=a.list:t.cities=[],t.getDataList()}))},getDataList:function(){var e=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/activity/activity/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,clientId:this.dataForm.clientId,clientType:this.dataForm.clientType,oldOrNew:this.dataForm.oldOrNew,userId:this.dataForm.userId,activityType:this.dataForm.activityType,name:this.dataForm.name,provinceId:this.dataForm.provinceId,cityId:this.dataForm.cityId,start:this.timeArray&&this.timeArray.length>0?this.timeArray[0]+" 00:00:00":"",end:this.timeArray&&this.timeArray.length>0?this.timeArray[1]+" 23:59:59":""})}).then((function(t){var a=t.data;a&&200===a.code?(e.dataList=a.page.list,e.totalPage=a.page.totalCount):(e.dataList=[],e.totalPage=0),e.dataListLoading=!1}))},findClient:function(){var e=this;this.$http({url:this.$http.adornUrl("/client/client/findAll"),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.client=a.result)}))},getAccountInfo:function(){var e=this;this.$http({url:this.$http.adornUrl("/manage/wxAccount/info/".concat(this.appid)),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.wxAccount=a.wxAccount)}))},getSysUser:function(){var e=this;this.$http({url:this.$http.adornUrl("/sys/user/findByAppid"),method:"get"}).then((function(t){var a=t.data;a&&200===a.code?e.sysuser=a.result:e.sysuser=[]}))},getResult:function(){var e=this;this.$http({url:this.$http.adornUrl("/sys/config/findOnlyParamKey"),method:"get",params:this.$http.adornParams({paramKey:"clientType"})}).then((function(t){var a=t.data;a&&200===a.code&&(e.clientType=a.result.paramValue?a.result.paramValue.split(","):[])})),this.$http({url:this.$http.adornUrl("/sys/config/findOnlyParamKey"),method:"get",params:this.$http.adornParams({paramKey:"oldOrNew"})}).then((function(t){var a=t.data;a&&200===a.code&&(e.oldOrNew=a.result.paramValue?a.result.paramValue.split(","):[])})),this.$http({url:this.$http.adornUrl("/sys/config/findOnlyParamKey"),method:"get",params:this.$http.adornParams({paramKey:"activityType"})}).then((function(t){var a=t.data;a&&200===a.code&&(e.activityType=a.result.paramValue?a.result.paramValue.split(","):[])}))},sizeChangeHandle:function(e){this.pageSize=e,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(e){this.pageIndex=e,this.getDataList()},selectionChangeHandle:function(e){this.dataListSelections=e},addOrUpdateHandle:function(e){this.addOrUpdateVisible=!0,this.$router.push({name:"activityextraAddOrUpdate",query:{id:e}})},acitivitySettle:function(e){this.$router.push({name:"activitysettle",query:{activityId:e}})},acitivityRole:function(e){this.$router.push({name:"activityrole",query:{activityId:e}})},deleteHandle:function(e){var t=this,a=e?[e]:this.dataListSelections.map((function(e){return e.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(e?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:t.$http.adornUrl("/activity/activity/delete"),method:"post",data:t.$http.adornData(a,!1)}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.getDataList()}}):t.$message.error(a.msg)}))}))},copy:function(e){var t=this;this.$confirm("确定复制会议操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:t.$http.adornUrl("/activity/activity/copy"),method:"get",params:t.$http.adornParams({id:e})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1e3,onClose:function(){t.getDataList()}}):t.$message.error(a.msg)}))}))},acitivityConfig:function(e){this.$router.push({name:"activityConfig",query:{activityId:e}})},openUrl:function(e){window.open(e)}}},s=c,d=a("2877"),y=Object(d["a"])(s,n,i,!1,null,null,null);t["default"]=y.exports},"593c":function(e,t,a){"use strict";a.d(t,"h",(function(){return n})),a.d(t,"i",(function(){return i})),a.d(t,"n",(function(){return r})),a.d(t,"f",(function(){return l})),a.d(t,"o",(function(){return o})),a.d(t,"c",(function(){return u})),a.d(t,"m",(function(){return c})),a.d(t,"b",(function(){return s})),a.d(t,"g",(function(){return d})),a.d(t,"j",(function(){return y})),a.d(t,"d",(function(){return v})),a.d(t,"k",(function(){return p})),a.d(t,"l",(function(){return h})),a.d(t,"a",(function(){return m})),a.d(t,"e",(function(){return f}));var n=[{key:0,value:"后台签到"},{key:1,value:"微信扫码签到"}],i=[{key:0,value:"未签到"},{key:1,value:"已签到"},{key:2,value:"已签退"}],r=[{key:0,value:"未报名"},{key:1,value:"已报名"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],l=[{key:0,value:"无需"},{key:1,value:"统一"},{key:2,value:"随机"},{key:3,value:"统一(多个)"}],o=[{key:0,value:"未审核"},{key:1,value:"审核通过"},{key:2,value:"审核不通过"}],u=[{key:0,value:"飞机"},{key:1,value:"火车"},{key:2,value:"其他"}],c=[{key:0,value:"会务组出票"},{key:1,value:"自行出票"}],s=[{key:0,value:"主控"},{key:1,value:"学员管理"},{key:2,value:"专家管理"},{key:3,value:"技术"},{key:4,value:"业务员"},{key:5,value:"兼职"}],d=[{key:0,value:"项目收支"},{key:1,value:"服务费"}],y=[{key:0,value:"大会出票"},{key:1,value:"自行购买，凭票大会报销"}],v=[{key:0,value:"未出票"},{key:1,value:"已出票"}],p=[{key:0,value:"已提交"},{key:1,value:"已预订"},{key:2,value:"已支付,待出票"},{key:4,value:"已出票"},{key:5,value:"不能出票待退款"},{key:7,value:"不能出票已退款"},{key:13,value:"(退票)审核不通过交易结束"},{key:15,value:"(退票)申请中"},{key:16,value:"(退票)已退款交易结束"},{key:22,value:"(改签)审核不通过交易结束"},{key:23,value:"(改签)需补款待支付"},{key:26,value:"(改签)无法改签已退款交易结束"},{key:27,value:"(改签)改签成功交易结束"},{key:28,value:"(改签)改签已取消交易结束"},{key:29,value:"(改签)改签处理中"},{key:99,value:"已取消"}],h=[{key:0,value:"已提交"},{key:1,value:"已预订"},{key:2,value:"占位成功"},{key:4,value:"占位失败"},{key:5,value:"已取消"},{key:8,value:"出票成功"},{key:9,value:"出票失败"},{key:31,value:"改签占位成功"},{key:32,value:"改签占位失败"},{key:34,value:"改签确认出票成功"},{key:35,value:"改签确认出票失败"},{key:37,value:"改签已取消"},{key:21,value:"退票成功"},{key:22,value:"退票失败"},{key:23,value:"已退票未退款"}],m=[{key:0,value:"专家信息"},{key:1,value:"专家任务确认"},{key:2,value:"专家行程"},{key:3,value:"专家接送"},{key:4,value:"专家劳务费信息"},{key:5,value:"专家劳务费签字"},{key:6,value:"其他"},{key:7,value:"活动即将过期"},{key:8,value:"活动已过期"},{key:9,value:"活动续费成功"}],f=[{key:0,value:"未读"},{key:1,value:"已读"}]},"7de9":function(e,t,a){"use strict";a.d(t,"g",(function(){return n})),a.d(t,"f",(function(){return i})),a.d(t,"e",(function(){return r})),a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return u})),a.d(t,"d",(function(){return c}));var n=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],i=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],r=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],l=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],o=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],u=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],c=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]},a15b:function(e,t,a){"use strict";var n=a("23e7"),i=a("e330"),r=a("44ad"),l=a("fc6a"),o=a("a640"),u=i([].join),c=r!==Object,s=c||!o("join",",");n({target:"Array",proto:!0,forced:s},{join:function(e){return u(l(this),void 0===e?",":e)}})},a573:function(e,t,a){"use strict";a("ab43")},ab43:function(e,t,a){"use strict";var n=a("23e7"),i=a("d024"),r=a("c430");n({target:"Iterator",proto:!0,real:!0,forced:r},{map:i})},d024:function(e,t,a){"use strict";var n=a("c65b"),i=a("59ed"),r=a("825a"),l=a("46c4"),o=a("c5cc"),u=a("9bdd"),c=o((function(){var e=this.iterator,t=r(n(this.next,e)),a=this.done=!!t.done;if(!a)return u(e,this.mapper,[t.value,this.counter++],!0)}));e.exports=function(e){return r(this),i(e),new c(l(this),{mapper:e})}},d81d:function(e,t,a){"use strict";var n=a("23e7"),i=a("b727").map,r=a("1dde"),l=r("map");n({target:"Array",proto:!0,forced:!l},{map:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}})}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-25fea87e"],{"0f86":function(t,o,n){},"11e9":function(t,o,n){var e=n("52a7"),i=n("4630"),c=n("6821"),a=n("6a99"),r=n("69a8"),s=n("c69a"),l=Object.getOwnPropertyDescriptor;o.f=n("9e1e")?l:function(t,o){if(t=c(t),o=a(o,!0),s)try{return l(t,o)}catch(n){}if(r(t,o))return i(!e.f.call(t,o),t[o])}},"1d52":function(t,o,n){"use strict";n.d(o,"a",(function(){return r}));n("386d"),n("4917"),n("3b2b"),n("a481");var e=n("c135"),i="wx6a7f38e0347e6669",c="https://open.weixin.qq.com/connect/oauth2/authorize?appid="+i+"&redirect_uri=REDIRECT_URI&response_type=code&scope=snsapi_userinfo&state=0#wechat_redirect",a=!1;function r(t){return new Promise((function(o,n){if(a)return console.log("微信授权正在进行中，请勿重复操作"),void n(new Error("授权正在进行中"));a=!0;var i=s("code");if(console.log("获取到的code:",i),i)console.log("获取到code，开始换取用户信息:",i),e["a"].post("/pyp/wxAuth/codeToUserInfo",{code:i}).then((function(t){a=!1,200==t.code?(console.log("微信授权完成"),o(t.data)):(console.log("换取openid失败:",t.msg),n(new Error(t.msg||"换取用户信息失败")))})).catch((function(t){a=!1,console.error("微信授权请求失败:",t),n(t)}));else{console.log("未获取到code，跳转微信授权"),a=!1;var r=encodeURIComponent(t||window.location.href);window.location.href=c.replace("REDIRECT_URI",r)}}))}function s(t){var o=new RegExp("(^|&)"+t+"=([^&]*)(&|$)"),n=window.location.search.substring(1).match(o);return n?decodeURIComponent(n[2]):""}},"386d":function(t,o,n){"use strict";var e=n("cb7c"),i=n("83a1"),c=n("5f1b");n("214f")("search",1,(function(t,o,n,a){return[function(n){var e=t(this),i=void 0==n?void 0:n[o];return void 0!==i?i.call(n,e):new RegExp(n)[o](String(e))},function(t){var o=a(n,t,this);if(o.done)return o.value;var r=e(t),s=String(this),l=r.lastIndex;i(l,0)||(r.lastIndex=0);var u=c(r,s);return i(r.lastIndex,l)||(r.lastIndex=l),null===u?-1:u.index}]}))},"3b2b":function(t,o,n){var e=n("7726"),i=n("5dbc"),c=n("86cc").f,a=n("9093").f,r=n("aae3"),s=n("0bfb"),l=e.RegExp,u=l,d=l.prototype,f=/a/g,g=/a/g,v=new l(f)!==f;if(n("9e1e")&&(!v||n("79e5")((function(){return g[n("2b4c")("match")]=!1,l(f)!=f||l(g)==g||"/a/i"!=l(f,"i")})))){l=function(t,o){var n=this instanceof l,e=r(t),c=void 0===o;return!n&&e&&t.constructor===l&&c?t:i(v?new u(e&&!c?t.source:t,o):u((e=t instanceof l)?t.source:t,e&&c?s.call(t):o),n?this:d,l)};for(var p=function(t){t in l||c(l,t,{configurable:!0,get:function(){return u[t]},set:function(o){u[t]=o}})},h=a(u),b=0;h.length>b;)p(h[b++]);d.constructor=l,l.prototype=d,n("2aba")(e,"RegExp",l)}n("7a56")("RegExp")},"5dbc":function(t,o,n){var e=n("d3f4"),i=n("8b97").set;t.exports=function(t,o,n){var c,a=o.constructor;return a!==n&&"function"==typeof a&&(c=a.prototype)!==n.prototype&&e(c)&&i&&i(t,c),t}},"6dc5":function(t,o,n){"use strict";n("0f86")},"83a1":function(t,o){t.exports=Object.is||function(t,o){return t===o?0!==t||1/t===1/o:t!=t&&o!=o}},"8b97":function(t,o,n){var e=n("d3f4"),i=n("cb7c"),c=function(t,o){if(i(t),!e(o)&&null!==o)throw TypeError(o+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,o,e){try{e=n("9b43")(Function.call,n("11e9").f(Object.prototype,"__proto__").set,2),e(t,[]),o=!(t instanceof Array)}catch(i){o=!0}return function(t,n){return c(t,n),o?t.__proto__=n:e(t,n),t}}({},!1):void 0),check:c}},9093:function(t,o,n){var e=n("ce10"),i=n("e11e").concat("length","prototype");o.f=Object.getOwnPropertyNames||function(t){return e(t,i)}},beb0:function(t,o,n){"use strict";n.r(o);var e=function(){var t=this,o=t._self._c;return o("div",{staticClass:"login-container"},[t._m(0),t._m(1),o("div",{staticClass:"login-card"},[o("div",{staticClass:"login-section"},[o("div",{staticClass:"section-header"},[o("van-icon",{attrs:{name:"phone-o",size:"20",color:"#1989fa"}}),o("span",{staticClass:"section-title"},[t._v("手机验证码登录")])],1),o("div",{staticClass:"form-container"},[o("div",{staticClass:"input-group"},[o("van-field",{staticClass:"custom-field",attrs:{name:"手机号",placeholder:"请输入手机号",border:!1},scopedSlots:t._u([{key:"left-icon",fn:function(){return[o("van-icon",{attrs:{name:"phone-o",color:"#969799"}})]},proxy:!0}]),model:{value:t.mobile,callback:function(o){t.mobile=o},expression:"mobile"}})],1),o("div",{staticClass:"input-group"},[o("van-field",{staticClass:"custom-field",attrs:{center:"",clearable:"",maxlength:"6",placeholder:"请输入短信验证码",border:!1},scopedSlots:t._u([{key:"left-icon",fn:function(){return[o("van-icon",{attrs:{name:"shield-o",color:"#969799"}})]},proxy:!0},{key:"button",fn:function(){return[o("van-button",{staticClass:"code-btn",attrs:{size:"small",type:t.waiting?"default":"primary",disabled:t.waiting,round:""},on:{click:function(o){return t.doSendSmsCode()}}},[t.waiting?o("span",[t._v(t._s(t.waitingTime)+"s")]):o("span",[t._v("获取验证码")])])]},proxy:!0}]),model:{value:t.code,callback:function(o){t.code=o},expression:"code"}})],1),o("div",{staticClass:"login-btn-container"},[o("van-button",{staticClass:"login-btn",attrs:{type:"primary",size:"large",loading:t.loginLoading,round:"",block:""},on:{click:t.mobileLogin}},[o("van-icon",{attrs:{name:"arrow"}}),o("span",{staticStyle:{"margin-left":"8px"}},[t._v("立即登录")])],1)],1)])]),o("div",{staticClass:"wx-login-section"},[t._m(2),o("van-button",{staticClass:"wx-login-btn",attrs:{color:"linear-gradient(135deg, #07c160, #1aad19)",size:"large",loading:t.wxLoginLoading,disabled:t.wxLoginLoading,round:"",block:""},on:{click:t.wxLogin}},[o("van-icon",{attrs:{name:"wechat",size:"20"}}),o("span",{staticStyle:{"margin-left":"8px"}},[t._v("\n                    "+t._s(t.wxLoginLoading?"授权中...":"微信授权登录")+"\n                ")])],1)],1)]),o("div",{staticClass:"footer-info"},[o("p",[t._v("登录即表示同意"),o("span",{staticClass:"link",on:{click:t.goToUserAgreement}},[t._v("《用户协议》")]),t._v("和"),o("span",{staticClass:"link",on:{click:t.goToPrivacyPolicy}},[t._v("《隐私政策》")])])])])},i=[function(){var t=this,o=t._self._c;return o("div",{staticClass:"bg-decoration"},[o("div",{staticClass:"circle circle-1"}),o("div",{staticClass:"circle circle-2"}),o("div",{staticClass:"circle circle-3"})])},function(){var t=this,o=t._self._c;return o("div",{staticClass:"login-header"},[o("div",{staticClass:"logo-container"},[o("div",{staticClass:"logo-icon"},[o("img",{staticStyle:{width:"60px"},attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/20250618/fb791c64adc6424883781564ec4de7f9.png",alt:""}})]),o("h1",{staticClass:"app-title"},[t._v("易企化AI爆店码")]),o("p",{staticClass:"app-subtitle"},[t._v("AI助力商家营销获客")])])])},function(){var t=this,o=t._self._c;return o("div",{staticClass:"divider"},[o("span",[t._v("其他登录方式")])])}],c=(n("4917"),n("3b2b"),n("386d"),n("cacf")),a=n("1d52"),r={name:"MobileLogin",data:function(){return{mobile:"",code:"",waiting:!1,waitingTime:60,loginLoading:!1,wxLoginLoading:!1,returnUrl:"",isWeixin:Object(c["e"])(),activityId:void 0}},mounted:function(){document.title="登录";var t=this.$route.query.returnUrl||"/";console.log("=== MobileLogin URL处理开始 ==="),console.log("原始 $route.query.returnUrl:",t),console.log("当前页面完整URL:",window.location.href),console.log("当前页面search:",window.location.search);try{for(var o=0;o<3;o++){var n=decodeURIComponent(t);if(console.log("第".concat(o+1,"次解码:"),n),n===t){console.log("第".concat(o+1,"次解码无变化，停止解码"));break}t=n}}catch(e){console.log("URL解码完成或出错:",e)}this.returnUrl=t,this.activityId=this.$route.query.id,console.log("最终处理后的returnUrl:",this.returnUrl),this.checkWxAuthCode(),console.log("=== MobileLogin URL处理结束 ===")},methods:{mobileLogin:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?this.code?/^\d{6}$/.test(this.code)?(this.loginLoading=!0,void this.$fly.post("/pyp/web/user/pcLogin",{mobile:this.mobile,code:this.code}).then((function(o){t.loginLoading=!1,o&&200===o.code?(vant.Toast("登录成功"),t.$store.commit("user/update",o.userInfo),location.href=t.returnUrl):vant.Toast(o.msg||"登录失败")})).catch((function(){t.loginLoading=!1,vant.Toast("登录失败，请重试")}))):(vant.Toast("验证码格式错误"),!1):(vant.Toast("请输入验证码"),!1):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},wxLogin:function(){var t=this;this.wxLoginLoading||(this.wxLoginLoading=!0,Object(a["a"])(this.returnUrl).then((function(){location.href=t.returnUrl})).catch((function(o){t.wxLoginLoading=!1,console.error("微信授权失败:",o),vant.Toast("微信授权失败，请重试")})))},doSendSmsCode:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.mobile,activityId:this.activityId}).then((function(o){o&&200===o.code?(t.countdown(),vant.Toast("发送验证码成功")):vant.Toast(o.msg||"发送失败")})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var t=this;this.waiting=!0;var o=window.setInterval((function(){t.waitingTime--,t.waitingTime<0&&(window.clearInterval(o),t.waitingTime=60,t.waiting=!1)}),1e3)},getUrlParam:function(t){var o=new RegExp("(^|&)"+t+"=([^&]*)(&|$)"),n=window.location.search.substring(1).match(o);return n?decodeURIComponent(n[2]):null},checkWxAuthCode:function(){var t=this,o=this.getUrlParam("code"),n=this.getUrlParam("state");console.log("检查微信授权参数 - code:",o,"state:",n),console.log("当前search参数:",window.location.search),o&&null!==n?(console.log("检测到微信授权回调，自动处理授权"),this.wxLoginLoading=!0,Object(a["a"])(this.returnUrl).then((function(){console.log("自动微信授权成功，跳转到目标页面"),t.wxLoginLoading=!1,location.href=t.returnUrl})).catch((function(o){t.wxLoginLoading=!1,console.error("自动微信授权失败:",o),vant.Toast("微信授权失败，请重试")}))):console.log("未检测到微信授权回调参数")},goToUserAgreement:function(){this.$router.push({name:"userAgreement"})},goToPrivacyPolicy:function(){this.$router.push({name:"privacyPolicy"})}}},s=r,l=(n("6dc5"),n("2877")),u=Object(l["a"])(s,e,i,!1,null,"724f2b57",null);o["default"]=u.exports}}]);
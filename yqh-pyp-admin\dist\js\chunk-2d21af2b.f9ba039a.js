(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d21af2b"],{be4f:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible,width:"80%"},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.dataFormSubmit()}}},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"类型编码",prop:"typeCode"}},[e("el-input",{attrs:{placeholder:"类型编码（如：douyin）",disabled:!!t.dataForm.id},model:{value:t.dataForm.typeCode,callback:function(e){t.$set(t.dataForm,"typeCode",e)},expression:"dataForm.typeCode"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"类型名称",prop:"typeName"}},[e("el-input",{attrs:{placeholder:"类型名称（如：抖音）"},model:{value:t.dataForm.typeName,callback:function(e){t.$set(t.dataForm,"typeName",e)},expression:"dataForm.typeName"}})],1)],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"平台名称",prop:"platform"}},[e("el-input",{attrs:{placeholder:"平台名称"},model:{value:t.dataForm.platform,callback:function(e){t.$set(t.dataForm,"platform",e)},expression:"dataForm.platform"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"内容类型",prop:"contentType"}},[e("el-input",{attrs:{placeholder:"内容类型（如：短视频）"},model:{value:t.dataForm.contentType,callback:function(e){t.$set(t.dataForm,"contentType",e)},expression:"dataForm.contentType"}})],1)],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"标题长度要求",prop:"titleLength"}},[e("el-input",{attrs:{placeholder:"标题长度要求"},model:{value:t.dataForm.titleLength,callback:function(e){t.$set(t.dataForm,"titleLength",e)},expression:"dataForm.titleLength"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"内容长度要求",prop:"contentLength"}},[e("el-input",{attrs:{placeholder:"内容长度要求"},model:{value:t.dataForm.contentLength,callback:function(e){t.$set(t.dataForm,"contentLength",e)},expression:"dataForm.contentLength"}})],1)],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"话题数量",prop:"topicsCount"}},[e("el-input-number",{attrs:{min:1,max:20,placeholder:"话题数量"},model:{value:t.dataForm.topicsCount,callback:function(e){t.$set(t.dataForm,"topicsCount",e)},expression:"dataForm.topicsCount"}})],1)],1),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[e("el-input-number",{attrs:{min:0,placeholder:"排序"},model:{value:t.dataForm.sortOrder,callback:function(e){t.$set(t.dataForm,"sortOrder",e)},expression:"dataForm.sortOrder"}})],1)],1),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"状态",prop:"status"}},[e("el-radio-group",{model:{value:t.dataForm.status,callback:function(e){t.$set(t.dataForm,"status",e)},expression:"dataForm.status"}},[e("el-radio",{attrs:{label:1}},[t._v("启用")]),e("el-radio",{attrs:{label:0}},[t._v("禁用")])],1)],1)],1)],1),e("el-form-item",{attrs:{label:"话题格式要求",prop:"topicsFormat"}},[e("el-input",{attrs:{placeholder:"话题格式要求（如：不带#号，用逗号分隔）"},model:{value:t.dataForm.topicsFormat,callback:function(e){t.$set(t.dataForm,"topicsFormat",e)},expression:"dataForm.topicsFormat"}})],1),e("el-form-item",{attrs:{label:"风格特点",prop:"style"}},[e("el-input",{attrs:{placeholder:"风格特点"},model:{value:t.dataForm.style,callback:function(e){t.$set(t.dataForm,"style",e)},expression:"dataForm.style"}})],1),e("el-form-item",{attrs:{label:"内容要求",prop:"requirements"}},[e("el-input",{attrs:{type:"textarea",rows:4,placeholder:"内容要求（每行一个要求）"},model:{value:t.dataForm.requirements,callback:function(e){t.$set(t.dataForm,"requirements",e)},expression:"dataForm.requirements"}})],1),e("el-form-item",{attrs:{label:"Prompt模板",prop:"promptTemplate"}},[e("el-input",{attrs:{type:"textarea",rows:8,placeholder:"Prompt模板，支持占位符：{platform}, {content_type}, {keyword}, {title_section}, {title_length}, {content_length}, {topics_count}, {topics_format}, {requirements}, {style}"},model:{value:t.dataForm.promptTemplate,callback:function(e){t.$set(t.dataForm,"promptTemplate",e)},expression:"dataForm.promptTemplate"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},o=[],l={data:function(){return{visible:!1,dataForm:{id:0,typeCode:"",typeName:"",platform:"",contentType:"",titleLength:"",contentLength:"",topicsCount:5,topicsFormat:"",requirements:"",style:"",promptTemplate:"",sortOrder:0,status:1},dataRule:{typeCode:[{required:!0,message:"类型编码不能为空",trigger:"blur"}],typeName:[{required:!0,message:"类型名称不能为空",trigger:"blur"}],platform:[{required:!0,message:"平台名称不能为空",trigger:"blur"}],contentType:[{required:!0,message:"内容类型不能为空",trigger:"blur"}],titleLength:[{required:!0,message:"标题长度要求不能为空",trigger:"blur"}],contentLength:[{required:!0,message:"内容长度要求不能为空",trigger:"blur"}],topicsCount:[{required:!0,message:"话题数量不能为空",trigger:"blur"}],topicsFormat:[{required:!0,message:"话题格式要求不能为空",trigger:"blur"}],requirements:[{required:!0,message:"内容要求不能为空",trigger:"blur"}],style:[{required:!0,message:"风格特点不能为空",trigger:"blur"}],promptTemplate:[{required:!0,message:"Prompt模板不能为空",trigger:"blur"}]}}},methods:{init:function(t){var e=this;this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.dataForm.id?e.$http({url:e.$http.adornUrl("/activity/adtypeconfig/info/".concat(e.dataForm.id)),method:"get",params:e.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm.typeCode=a.adTypeConfig.typeCode,e.dataForm.typeName=a.adTypeConfig.typeName,e.dataForm.platform=a.adTypeConfig.platform,e.dataForm.contentType=a.adTypeConfig.contentType,e.dataForm.titleLength=a.adTypeConfig.titleLength,e.dataForm.contentLength=a.adTypeConfig.contentLength,e.dataForm.topicsCount=a.adTypeConfig.topicsCount,e.dataForm.topicsFormat=a.adTypeConfig.topicsFormat,e.dataForm.requirements=a.adTypeConfig.requirements,e.dataForm.style=a.adTypeConfig.style,e.dataForm.promptTemplate=a.adTypeConfig.promptTemplate,e.dataForm.sortOrder=a.adTypeConfig.sortOrder,e.dataForm.status=a.adTypeConfig.status)})):e.dataForm.promptTemplate="请为{platform}平台生成{content_type}文案。\n\n关键词：{keyword}\n{title_section}\n要求：\n{requirements}\n\n请以JSON格式返回以下内容（键名使用英文）：\n- 标题（title，{title_length}）\n- 内容（content，{content_length}）\n- 话题（topics，{topics_count}个，用逗号分隔，{topics_format}）\n\n风格特点：{style}"}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/activity/adtypeconfig/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,typeCode:t.dataForm.typeCode,typeName:t.dataForm.typeName,platform:t.dataForm.platform,contentType:t.dataForm.contentType,titleLength:t.dataForm.titleLength,contentLength:t.dataForm.contentLength,topicsCount:t.dataForm.topicsCount,topicsFormat:t.dataForm.topicsFormat,requirements:t.dataForm.requirements,style:t.dataForm.style,promptTemplate:t.dataForm.promptTemplate,sortOrder:t.dataForm.sortOrder,status:t.dataForm.status})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))}}},n=l,s=a("2877"),i=Object(s["a"])(n,r,o,!1,null,null,null);e["default"]=i.exports}}]);
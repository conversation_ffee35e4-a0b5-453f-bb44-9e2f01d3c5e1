.plane-select[data-v-1fdf3661]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;height:100vh;background-color:#f5f5f5}.date-nav[data-v-1fdf3661]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-ms-flex-align:center;align-items:center;padding:10px 15px;background-color:#fff;margin-bottom:10px}.current-date[data-v-1fdf3661]{font-size:16px;font-weight:700}.loading-overlay[data-v-1fdf3661]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;height:200px}.loading-text[data-v-1fdf3661]{margin-top:10px;color:#999}.flight-list[data-v-1fdf3661]{padding:0 10px;-webkit-box-flex:1;-ms-flex:1;flex:1;overflow-y:auto}.flight-item[data-v-1fdf3661]{background-color:#fff;border-radius:8px;margin-bottom:10px;padding:15px;-webkit-box-shadow:0 2px 4px rgba(0,0,0,.1);box-shadow:0 2px 4px rgba(0,0,0,.1)}.time-row[data-v-1fdf3661]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-ms-flex-align:center;align-items:center;margin-bottom:15px}.time-info[data-v-1fdf3661]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;width:30%}.text-right[data-v-1fdf3661]{text-align:right}.arrival-time[data-v-1fdf3661],.departure-time[data-v-1fdf3661]{font-size:18px;font-weight:700;color:#333}.airport-name[data-v-1fdf3661]{font-size:12px;color:#666;margin-top:4px}.flight-duration[data-v-1fdf3661]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-ms-flex-align:center;align-items:center;position:relative;width:40%}.duration-line[data-v-1fdf3661]{width:100%;height:1px;background-color:#ddd;position:relative}.duration-line[data-v-1fdf3661]:after,.duration-line[data-v-1fdf3661]:before{content:"";position:absolute;width:6px;height:6px;border-radius:50%;background-color:#ddd;top:-2.5px}.duration-line[data-v-1fdf3661]:before{left:0}.duration-line[data-v-1fdf3661]:after{right:0}.duration-text[data-v-1fdf3661]{font-size:12px;color:#999;margin-top:5px}.flight-detail[data-v-1fdf3661]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-ms-flex-align:center;align-items:center;border-top:1px solid #f5f5f5;padding-top:10px}.flight-number[data-v-1fdf3661]{font-size:14px;font-weight:700;color:#333}.company-name[data-v-1fdf3661]{font-size:12px;color:#999;margin-left:5px}.price-preview[data-v-1fdf3661]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.price-from[data-v-1fdf3661]{font-size:16px;color:#f56c6c;font-weight:700;margin-right:5px}.cabin-header[data-v-1fdf3661]{background-color:#fff;padding:15px;position:relative;margin-bottom:10px}.back-icon[data-v-1fdf3661]{position:absolute;left:15px;top:15px;font-size:20px;color:#333}.selected-flight-info[data-v-1fdf3661]{padding-left:30px}.cabin-list[data-v-1fdf3661]{padding:0 10px;-webkit-box-flex:1;-ms-flex:1;flex:1;overflow-y:auto}.cabin-item[data-v-1fdf3661]{background-color:#fff;border-radius:8px;margin-bottom:10px;padding:15px;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.1);box-shadow:0 1px 3px rgba(0,0,0,.1);-webkit-transition:all .3s ease;transition:all .3s ease}.cabin-item.selected-item[data-v-1fdf3661]{background-color:#e6f7ff;border:1px solid #1989fa}.cabin-info[data-v-1fdf3661]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.cabin-left[data-v-1fdf3661]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column}.cabin-name[data-v-1fdf3661]{font-size:16px;font-weight:700;color:#333;margin-bottom:4px}.cabin-desc[data-v-1fdf3661]{font-size:12px;color:#999}.cabin-right[data-v-1fdf3661]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:end;-ms-flex-align:end;align-items:flex-end}.original-price[data-v-1fdf3661]{font-size:12px;color:#999;text-decoration:line-through;margin-bottom:2px}.sale-price[data-v-1fdf3661]{font-size:16px;color:#f56c6c;font-weight:700}.bottom-buttons[data-v-1fdf3661]{display:-webkit-box;display:-ms-flexbox;display:flex;padding:10px;gap:10px;background-color:#fff;border-top:1px solid #eee;position:sticky;bottom:0;z-index:10}
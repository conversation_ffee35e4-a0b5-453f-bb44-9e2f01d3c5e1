(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-60feecd6"],{"0ccb":function(a,t,e){"use strict";var r=e("e330"),n=e("50c4"),o=e("577e"),s=e("1148"),l=e("1d80"),i=r(s),d=r("".slice),m=Math.ceil,c=function(a){return function(t,e,r){var s,c,p=o(l(t)),u=n(e),h=p.length,f=void 0===r?" ":o(r);return u<=h||""===f?p:(s=u-h,c=i(f,m(s/f.length)),c.length>s&&(c=d(c,0,s)),a?p+c:c+p)}};a.exports={start:c(!1),end:c(!0)}},1148:function(a,t,e){"use strict";var r=e("5926"),n=e("577e"),o=e("1d80"),s=RangeError;a.exports=function(a){var t=n(o(this)),e="",l=r(a);if(l<0||l===1/0)throw new s("Wrong number of repetitions");for(;l>0;(l>>>=1)&&(t+=t))1&l&&(e+=t);return e}},"4d90":function(a,t,e){"use strict";var r=e("23e7"),n=e("0ccb").start,o=e("9a0c");r({target:"String",proto:!0,forced:o},{padStart:function(a){return n(this,a,arguments.length>1?arguments[1]:void 0)}})},"7bfa":function(a,t,e){"use strict";e.r(t);e("b0c0");var r=function(){var a=this,t=a._self._c;return t("el-dialog",{attrs:{title:a.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:a.visible},on:{"update:visible":function(t){a.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:a.dataForm,rules:a.dataRule,"label-width":"80px"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&a._k(t.keyCode,"enter",13,t.key,"Enter")?null:a.dataFormSubmit()}}},[t("el-form-item",{attrs:{label:"姓名",prop:"name"}},[t("el-input",{attrs:{placeholder:"业务员姓名"},model:{value:a.dataForm.name,callback:function(t){a.$set(a.dataForm,"name",t)},expression:"dataForm.name"}})],1),t("el-form-item",{attrs:{label:"编号",prop:"code"}},[t("el-input",{attrs:{placeholder:"业务员编号"},model:{value:a.dataForm.code,callback:function(t){a.$set(a.dataForm,"code",t)},expression:"dataForm.code"}},[t("el-button",{attrs:{slot:"append",loading:a.generating},on:{click:a.generateCode},slot:"append"},[a._v(" "+a._s(a.generating?"生成中":"自动生成")+" ")])],1)],1),t("el-form-item",{attrs:{label:"手机号",prop:"mobile"}},[t("el-input",{attrs:{placeholder:"手机号"},model:{value:a.dataForm.mobile,callback:function(t){a.$set(a.dataForm,"mobile",t)},expression:"dataForm.mobile"}})],1),t("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[t("el-input",{attrs:{placeholder:"邮箱"},model:{value:a.dataForm.email,callback:function(t){a.$set(a.dataForm,"email",t)},expression:"dataForm.email"}})],1),t("el-form-item",{attrs:{label:"所属渠道",prop:"channelId"}},[t("el-select",{attrs:{placeholder:"请选择所属渠道",clearable:""},model:{value:a.dataForm.channelId,callback:function(t){a.$set(a.dataForm,"channelId",t)},expression:"dataForm.channelId"}},a._l(a.channelList,(function(a){return t("el-option",{key:a.id,attrs:{label:a.name,value:a.id}})})),1)],1),t("el-form-item",{attrs:{label:"部门",prop:"department"}},[t("el-input",{attrs:{placeholder:"部门"},model:{value:a.dataForm.department,callback:function(t){a.$set(a.dataForm,"department",t)},expression:"dataForm.department"}})],1),t("el-form-item",{attrs:{label:"职位",prop:"position"}},[t("el-input",{attrs:{placeholder:"职位"},model:{value:a.dataForm.position,callback:function(t){a.$set(a.dataForm,"position",t)},expression:"dataForm.position"}})],1),t("el-form-item",{attrs:{label:"上级业务员",prop:"parentId"}},[t("el-select",{attrs:{placeholder:"请选择上级业务员",clearable:""},model:{value:a.dataForm.parentId,callback:function(t){a.$set(a.dataForm,"parentId",t)},expression:"dataForm.parentId"}},a._l(a.parentSalesmanList,(function(a){return t("el-option",{key:a.id,attrs:{label:a.name,value:a.id}})})),1)],1),t("el-form-item",{attrs:{label:"状态",prop:"status"}},[t("el-radio-group",{model:{value:a.dataForm.status,callback:function(t){a.$set(a.dataForm,"status",t)},expression:"dataForm.status"}},[t("el-radio",{attrs:{label:0}},[a._v("禁用")]),t("el-radio",{attrs:{label:1}},[a._v("启用")])],1)],1),t("el-form-item",{attrs:{label:"标签",prop:"tags"}},[t("el-input",{attrs:{placeholder:"标签，多个用逗号分隔"},model:{value:a.dataForm.tags,callback:function(t){a.$set(a.dataForm,"tags",t)},expression:"dataForm.tags"}})],1),t("el-form-item",{attrs:{label:"备注",prop:"remarks"}},[t("el-input",{attrs:{type:"textarea",placeholder:"备注信息"},model:{value:a.dataForm.remarks,callback:function(t){a.$set(a.dataForm,"remarks",t)},expression:"dataForm.remarks"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){a.visible=!1}}},[a._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return a.dataFormSubmit()}}},[a._v("确定")])],1)],1)},n=[],o=(e("99af"),e("4de4"),e("d3b7"),e("4d90"),e("0643"),e("2382"),{data:function(){return{visible:!1,dataForm:{id:0,name:"",code:"",mobile:"",email:"",channelId:"",department:"",position:"",parentId:"",status:1,tags:"",remarks:""},channelList:[],parentSalesmanList:[],generating:!1,dataRule:{name:[{required:!0,message:"业务员姓名不能为空",trigger:"blur"}],code:[{required:!0,message:"业务员编号不能为空",trigger:"blur"}],mobile:[{required:!0,message:"手机号不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确",trigger:"blur"}],email:[{type:"email",message:"邮箱格式不正确",trigger:"blur"}],channelId:[{required:!0,message:"请选择所属渠道",trigger:"change"}]}}},methods:{init:function(a){var t=this;this.dataForm.id=a||0,this.visible=!0,this.getChannelList(),this.getParentSalesmanList(),this.$nextTick((function(){t.$refs["dataForm"].resetFields(),t.dataForm.id&&t.$http({url:t.$http.adornUrl("/channel/salesman/info/".concat(t.dataForm.id)),method:"get",params:t.$http.adornParams()}).then((function(a){var e=a.data;e&&200===e.code&&(t.dataForm.name=e.salesman.name,t.dataForm.code=e.salesman.code,t.dataForm.mobile=e.salesman.mobile,t.dataForm.email=e.salesman.email,t.dataForm.channelId=e.salesman.channelId,t.dataForm.department=e.salesman.department,t.dataForm.position=e.salesman.position,t.dataForm.parentId=e.salesman.parentId,t.dataForm.status=e.salesman.status,t.dataForm.tags=e.salesman.tags,t.dataForm.remarks=e.salesman.remarks)}))}))},getChannelList:function(){var a=this;this.$http({url:this.$http.adornUrl("/channel/channel/select"),method:"get",params:this.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.channelList=e.channelList||[])}))},getParentSalesmanList:function(){var a=this;this.$http({url:this.$http.adornUrl("/salesman/salesman/findByAppid"),method:"get",params:this.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.parentSalesmanList=e.result||[],a.dataForm.id&&(a.parentSalesmanList=a.parentSalesmanList.filter((function(t){return t.id!==a.dataForm.id}))))}))},generateCode:function(){var a=this;this.generating=!0;var t=new Date,e=t.getFullYear(),r=String(t.getMonth()+1).padStart(2,"0"),n=String(t.getDate()).padStart(2,"0"),o=String(Math.floor(1e4*Math.random())).padStart(4,"0"),s="YW".concat(e).concat(r).concat(n).concat(o);this.$http({url:this.$http.adornUrl("/channel/salesman/checkCode"),method:"get",params:this.$http.adornParams({code:s,excludeId:this.dataForm.id||null})}).then((function(t){var e=t.data;a.generating=!1,e&&200===e.code&&e.exists?a.generateCode():(a.dataForm.code=s,a.$message.success("编号生成成功"))})).catch((function(){a.generating=!1,a.dataForm.code=s,a.$message.success("编号生成成功")}))},dataFormSubmit:function(){var a=this;this.$refs["dataForm"].validate((function(t){t&&a.$http({url:a.$http.adornUrl("/channel/salesman/".concat(a.dataForm.id?"update":"save")),method:"post",data:a.$http.adornData({id:a.dataForm.id||void 0,name:a.dataForm.name,code:a.dataForm.code,mobile:a.dataForm.mobile,email:a.dataForm.email,channelId:a.dataForm.channelId,department:a.dataForm.department,position:a.dataForm.position,parentId:a.dataForm.parentId||null,status:a.dataForm.status,tags:a.dataForm.tags,remarks:a.dataForm.remarks})}).then((function(t){var e=t.data;e&&200===e.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.visible=!1,a.$emit("refreshDataList")}}):a.$message.error(e.msg)}))}))}}}),s=o,l=e("2877"),i=Object(l["a"])(s,r,n,!1,null,null,null);t["default"]=i.exports},"9a0c":function(a,t,e){"use strict";var r=e("b5db");a.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(r)}}]);
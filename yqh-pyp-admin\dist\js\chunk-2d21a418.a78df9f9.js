(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d21a418"],{bb79:function(a,t,e){"use strict";e.r(t);e("b0c0");var o=function(){var a=this,t=a._self._c;return t("el-dialog",{attrs:{title:a.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:a.visible},on:{"update:visible":function(t){a.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:a.dataForm,rules:a.dataRule,"label-width":"120px"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&a._k(t.keyCode,"enter",13,t.key,"Enter")?null:a.dataFormSubmit()}}},[t("el-form-item",{attrs:{label:"业务员",prop:"salesmanId"}},[t("el-select",{attrs:{placeholder:"请选择业务员",filterable:"",disabled:null!==a.presetSalesmanId},model:{value:a.dataForm.salesmanId,callback:function(t){a.$set(a.dataForm,"salesmanId",t)},expression:"dataForm.salesmanId"}},a._l(a.salesmanList,(function(a){return t("el-option",{key:a.id,attrs:{label:a.name+"("+a.code+")",value:a.id}})})),1)],1),t("el-form-item",{attrs:{label:"佣金类型",prop:"commissionType"}},[t("el-select",{attrs:{placeholder:"请选择佣金类型"},on:{change:a.commissionTypeChange},model:{value:a.dataForm.commissionType,callback:function(t){a.$set(a.dataForm,"commissionType",t)},expression:"dataForm.commissionType"}},a._l(a.commissionTypes,(function(a){return t("el-option",{key:a.code,attrs:{label:a.desc,value:a.code}})})),1)],1),t("el-form-item",{attrs:{label:"计算方式",prop:"calculationType"}},[t("el-select",{attrs:{placeholder:"请选择计算方式"},on:{change:a.calculationTypeChange},model:{value:a.dataForm.calculationType,callback:function(t){a.$set(a.dataForm,"calculationType",t)},expression:"dataForm.calculationType"}},a._l(a.availableCalculationTypes,(function(a){return t("el-option",{key:a.code,attrs:{label:a.desc,value:a.code}})})),1)],1),t("el-form-item",{attrs:{label:"佣金值",prop:"commissionValue"}},[t("el-input-number",{attrs:{precision:4,min:0,max:2===a.dataForm.calculationType?1:999999,placeholder:"请输入佣金值"},model:{value:a.dataForm.commissionValue,callback:function(t){a.$set(a.dataForm,"commissionValue",t)},expression:"dataForm.commissionValue"}}),1===a.dataForm.calculationType?t("span",{staticStyle:{"margin-left":"10px"}},[a._v("元")]):a._e(),2===a.dataForm.calculationType?t("span",{staticStyle:{"margin-left":"10px"}},[a._v("（比例，如0.1表示10%）")]):a._e()],1),t("el-form-item",{attrs:{label:"最小金额",prop:"minAmount"}},[t("el-input-number",{attrs:{precision:2,min:0,placeholder:"最小佣金金额（可选）"},model:{value:a.dataForm.minAmount,callback:function(t){a.$set(a.dataForm,"minAmount",t)},expression:"dataForm.minAmount"}}),t("span",{staticStyle:{"margin-left":"10px"}},[a._v("元")])],1),t("el-form-item",{attrs:{label:"最大金额",prop:"maxAmount"}},[t("el-input-number",{attrs:{precision:2,min:0,placeholder:"最大佣金金额（可选）"},model:{value:a.dataForm.maxAmount,callback:function(t){a.$set(a.dataForm,"maxAmount",t)},expression:"dataForm.maxAmount"}}),t("span",{staticStyle:{"margin-left":"10px"}},[a._v("元")])],1),t("el-form-item",{attrs:{label:"状态",prop:"status"}},[t("el-radio-group",{model:{value:a.dataForm.status,callback:function(t){a.$set(a.dataForm,"status",t)},expression:"dataForm.status"}},[t("el-radio",{attrs:{label:1}},[a._v("启用")]),t("el-radio",{attrs:{label:0}},[a._v("禁用")])],1)],1),t("el-form-item",{attrs:{label:"生效时间",prop:"effectiveDate"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择生效时间",format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:a.dataForm.effectiveDate,callback:function(t){a.$set(a.dataForm,"effectiveDate",t)},expression:"dataForm.effectiveDate"}})],1),t("el-form-item",{attrs:{label:"失效时间",prop:"expiryDate"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择失效时间（可选）",format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:a.dataForm.expiryDate,callback:function(t){a.$set(a.dataForm,"expiryDate",t)},expression:"dataForm.expiryDate"}})],1),t("el-form-item",{attrs:{label:"备注",prop:"remarks"}},[t("el-input",{attrs:{type:"textarea",placeholder:"备注信息"},model:{value:a.dataForm.remarks,callback:function(t){a.$set(a.dataForm,"remarks",t)},expression:"dataForm.remarks"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){a.visible=!1}}},[a._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return a.dataFormSubmit()}}},[a._v("确定")])],1)],1)},s=[],i=(e("4de4"),e("d3b7"),e("0643"),e("2382"),{data:function(){return{visible:!1,dataForm:{id:0,salesmanId:"",commissionType:"",calculationType:"",commissionValue:0,minAmount:null,maxAmount:null,status:1,effectiveDate:"",expiryDate:"",remarks:""},salesmanList:[],commissionTypes:[],calculationTypes:[],presetSalesmanId:null,dataRule:{salesmanId:[{required:!0,message:"业务员不能为空",trigger:"change"}],commissionType:[{required:!0,message:"佣金类型不能为空",trigger:"change"}],calculationType:[{required:!0,message:"计算方式不能为空",trigger:"change"}],commissionValue:[{required:!0,message:"佣金值不能为空",trigger:"blur"}]}}},computed:{availableCalculationTypes:function(){return 3===this.dataForm.commissionType?this.calculationTypes.filter((function(a){return 1===a.code})):this.calculationTypes}},methods:{init:function(a,t){var e=this;this.dataForm.id=a||0,this.presetSalesmanId=t||null,this.visible=!0,this.getSalesmanList(),this.getCommissionTypes(),this.getCalculationTypes(),this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.presetSalesmanId&&!e.dataForm.id&&(e.dataForm.salesmanId=e.presetSalesmanId),e.dataForm.id&&e.$http({url:e.$http.adornUrl("/salesman/commission/config/info/".concat(e.dataForm.id)),method:"get",params:e.$http.adornParams()}).then((function(a){var t=a.data;t&&200===t.code&&(e.dataForm.salesmanId=t.config.salesmanId,e.dataForm.commissionType=t.config.commissionType,e.dataForm.calculationType=t.config.calculationType,e.dataForm.commissionValue=t.config.commissionValue,e.dataForm.minAmount=t.config.minAmount,e.dataForm.maxAmount=t.config.maxAmount,e.dataForm.status=t.config.status,e.dataForm.effectiveDate=t.config.effectiveDate,e.dataForm.expiryDate=t.config.expiryDate,e.dataForm.remarks=t.config.remarks)}))}))},getSalesmanList:function(){var a=this;this.$http({url:this.$http.adornUrl("/salesman/salesman/list"),method:"get",params:this.$http.adornParams({page:1,limit:1e3})}).then((function(t){var e=t.data;e&&200===e.code&&(a.salesmanList=e.page.list)}))},getCommissionTypes:function(){var a=this;this.$http({url:this.$http.adornUrl("/salesman/commission/config/getCommissionTypes"),method:"get",params:this.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.commissionTypes=e.commissionTypes)}))},getCalculationTypes:function(){var a=this;this.$http({url:this.$http.adornUrl("/salesman/commission/config/getCalculationTypes"),method:"get",params:this.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.calculationTypes=e.calculationTypes)}))},commissionTypeChange:function(){3===this.dataForm.commissionType&&(this.dataForm.calculationType=1)},calculationTypeChange:function(){this.dataForm.commissionValue=0},dataFormSubmit:function(){var a=this;this.$refs["dataForm"].validate((function(t){if(t){if(a.dataForm.minAmount&&a.dataForm.maxAmount&&a.dataForm.minAmount>a.dataForm.maxAmount)return void a.$message.error("最小金额不能大于最大金额");if(2===a.dataForm.calculationType&&(a.dataForm.commissionValue<0||a.dataForm.commissionValue>1))return void a.$message.error("百分比佣金值应在0-1之间");a.$http({url:a.$http.adornUrl("/salesman/commission/config/".concat(a.dataForm.id?"update":"save")),method:"post",data:a.$http.adornData({id:a.dataForm.id||void 0,salesmanId:a.dataForm.salesmanId,commissionType:a.dataForm.commissionType,calculationType:a.dataForm.calculationType,commissionValue:a.dataForm.commissionValue,minAmount:a.dataForm.minAmount,maxAmount:a.dataForm.maxAmount,status:a.dataForm.status,effectiveDate:a.dataForm.effectiveDate,expiryDate:a.dataForm.expiryDate,remarks:a.dataForm.remarks})}).then((function(t){var e=t.data;e&&200===e.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.visible=!1,a.$emit("refreshDataList")}}):a.$message.error(e.msg)}))}}))}}}),m=i,n=e("2877"),r=Object(n["a"])(m,o,s,!1,null,null,null);t["default"]=r.exports}}]);
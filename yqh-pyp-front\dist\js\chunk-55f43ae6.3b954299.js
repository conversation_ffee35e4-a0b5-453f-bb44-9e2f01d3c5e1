(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-55f43ae6"],{"771a":function(t,a,e){"use strict";e.r(a);e("7f7f");var s=function(){var t=this,a=t._self._c;return a("div",{staticClass:"salesman-qrcode"},[t.loading?a("van-loading",{attrs:{type:"spinner",color:"#1989fa",size:"24px"}},[t._v("\n    正在加载...\n  ")]):t.showNotSalesmanTip?a("div",{staticClass:"not-salesman-tip"},[a("div",{staticClass:"tip-icon"},[a("van-icon",{attrs:{name:"warning-o",size:"60",color:"#ff6b6b"}})],1),a("h3",[t._v("您不是业务员")]),a("p",[t._v("此页面仅供业务员使用，请联系管理员开通业务员权限。")]),a("van-button",{attrs:{type:"primary",size:"large"},on:{click:t.goHome}},[t._v("\n      返回首页\n    ")])],1):a("div",[a("div",{staticClass:"salesman-card"},[a("div",{staticClass:"avatar"},[a("img",{attrs:{src:t.salesman.avatar||"http://mpjoy.oss-cn-beijing.aliyuncs.com/20240917/d9301e81ab674112b3acf4a84aff4e96.png",alt:"业务员头像"},on:{error:t.handleImageError}}),a("div",{staticClass:"default-avatar",staticStyle:{display:"none"}},[a("van-icon",{attrs:{name:"user-o",size:"30"}})],1)]),a("div",{staticClass:"info"},[a("h2",[t._v(t._s(t.salesman.name))]),a("p",{staticClass:"contact"},[t._v(t._s(t.salesman.mobile))])])]),a("div",{staticClass:"packages-section"},[a("h3",[t._v("选择套餐生成二维码")]),t.rechargePackages.length>0?a("div",{staticClass:"package-group"},[a("h4",[t._v("充值套餐")]),a("div",{staticClass:"package-list"},t._l(t.rechargePackages,(function(e){return a("div",{key:"recharge-"+e.id,staticClass:"package-item",class:{active:t.selectedPackageId===e.id&&1===t.selectedPackageType},on:{click:function(a){return t.selectPackage(e.id,1)}}},[a("div",{staticClass:"package-name"},[t._v(t._s(e.name))]),a("div",{staticClass:"package-price"},[t._v("¥"+t._s(e.price))]),a("div",{staticClass:"package-desc"},[t._v(t._s(e.countValue)+"次")])])})),0)]):t._e(),t.activityPackages.length>0?a("div",{staticClass:"package-group"},[a("h4",[t._v("活动套餐")]),a("div",{staticClass:"package-list"},t._l(t.activityPackages,(function(e){return a("div",{key:"activity-"+e.id,staticClass:"package-item",class:{active:t.selectedPackageId===e.id&&2===t.selectedPackageType},on:{click:function(a){return t.selectPackage(e.id,2)}}},[a("div",{staticClass:"package-name"},[t._v(t._s(e.name))]),a("div",{staticClass:"package-price"},[t._v("¥"+t._s(e.price))]),a("div",{staticClass:"package-desc"},[t._v(t._s(e.description||"创建活动"))])])})),0)]):t._e()]),t.qrcodeContent?a("div",{staticClass:"qrcode-section"},[a("div",{staticClass:"qrcode-container"},[a("vue-qrcode",{attrs:{options:{width:280},value:t.qrcodeContent}})],1),a("p",{staticClass:"qrcode-tip"},[t._v("长按二维码，保存或转发给客户")])]):t._e(),t.qrcodeContent?a("div",{staticClass:"action-buttons"},[a("van-button",{attrs:{type:"primary",size:"large",icon:"share-o"},on:{click:t.shareQrcode}},[t._v("\n        分享\n      ")]),a("van-button",{attrs:{size:"large",icon:"link-o"},on:{click:t.copyLink}},[t._v("\n        复制链接\n      ")])],1):t._e(),t.stats?a("div",{staticClass:"stats-section"},[a("h3",[t._v("推广统计")]),a("div",{staticClass:"stats-grid"},[a("div",{staticClass:"stat-item"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.scanCount||0))]),a("div",{staticClass:"stat-label"},[t._v("扫码次数")])]),a("div",{staticClass:"stat-item"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.orderCount||0))]),a("div",{staticClass:"stat-label"},[t._v("订单数量")])]),a("div",{staticClass:"stat-item"},[a("div",{staticClass:"stat-value"},[t._v("¥"+t._s(t.stats.payAmount||0))]),a("div",{staticClass:"stat-label"},[t._v("总金额")])])])]):t._e(),t.salesman.remarks?a("div",{staticClass:"intro-section"},[a("h3",[t._v("个人介绍")]),a("p",[t._v(t._s(t.salesman.remarks))])]):t._e()])],1)},n=[],c=(e("96cf"),e("1da1")),i=e("b2e5"),o=e.n(i),r={name:"SalesmanQrcode",components:{VueQrcode:o.a},data:function(){return{loading:!0,showNotSalesmanTip:!1,salesman:{},rechargePackages:[],activityPackages:[],selectedPackageId:null,selectedPackageType:null,qrcodeContent:"",stats:null,userInfo:null}},mounted:function(){document.title="业务员",this.checkUserAndLoadInfo()},methods:{checkUserAndLoadInfo:function(){var t=Object(c["a"])(regeneratorRuntime.mark((function t(){var a,e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.loading=!0,t.next=4,this.$fly.get("/pyp/web/user/checkLogin");case 4:if(a=t.sent,!a.result){t.next=10;break}return t.next=8,this.checkSalesmanAndLoadInfo();case 8:t.next=19;break;case 10:return t.next=12,this.getUserInfo();case 12:if(e=t.sent,!e){t.next=18;break}return t.next=16,this.checkSalesmanAndLoadInfo();case 16:t.next=19;break;case 18:this.showNotSalesmanTip=!0;case 19:t.next=25;break;case 21:t.prev=21,t.t0=t["catch"](0),console.error("检查用户身份失败:",t.t0),this.showNotSalesmanTip=!0;case 25:return t.prev=25,this.loading=!1,t.finish(25);case 28:case"end":return t.stop()}}),t,this,[[0,21,25,28]])})));function a(){return t.apply(this,arguments)}return a}(),getUserInfo:function(){var t=this;return new Promise((function(a,e){t.$fly.get("/pyp/wxUser/getUserInfo").then((function(s){200==s.code?(t.userInfo=s.data,a(s.data)):(vant.Toast(s.msg),e(new Error(s.msg)))})).catch((function(t){e(t)}))}))},checkSalesmanAndLoadInfo:function(){var t=Object(c["a"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$fly.get("/pyp/web/salesman/scanByAuth");case 3:a=t.sent,200===a.code?(this.salesman=a.salesman,this.rechargePackages=a.rechargePackages||[],this.activityPackages=a.activityPackages||[],this.stats=a.stats||null,this.rechargePackages.length>0?this.selectPackage(this.rechargePackages[0].id,1):this.activityPackages.length>0&&this.selectPackage(this.activityPackages[0].id,2)):this.showNotSalesmanTip=!0,t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("检查业务员身份失败:",t.t0),this.showNotSalesmanTip=!0;case 11:case"end":return t.stop()}}),t,this,[[0,7]])})));function a(){return t.apply(this,arguments)}return a}(),selectPackage:function(t,a){this.selectedPackageId=t,this.selectedPackageType=a,this.qrcodeContent=this.buildQrcodeContent()},buildQrcodeContent:function(){return this.selectedPackageId?"https://yqihua.com/p_front/#/salesman/scan?packageId=".concat(this.selectedPackageId,"&salesmanId=").concat(this.salesman.id,"&packageType=").concat(this.selectedPackageType):""},goHome:function(){this.$router.push({name:"index"})},shareQrcode:function(){this.qrcodeContent?this.$wxShare("易企化AI爆店码","http://mpjoy.oss-cn-beijing.aliyuncs.com/20230209/e90c0d7c2ef2424285fa4ba22976bc20.png","扫描二维码，选择套餐购买",this.qrcodeContent):this.$toast("请先选择套餐")},copyLink:function(){var t=this;this.qrcodeContent?navigator.clipboard?navigator.clipboard.writeText(this.qrcodeContent).then((function(){t.$toast("链接已复制到剪贴板")})).catch((function(){t.fallbackCopyTextToClipboard()})):this.fallbackCopyTextToClipboard():this.$toast("请先选择套餐")},fallbackCopyTextToClipboard:function(){var t=document.createElement("textarea");t.value=this.qrcodeContent,document.body.appendChild(t),t.focus(),t.select();try{document.execCommand("copy"),this.$toast("链接已复制到剪贴板")}catch(a){this.$toast("复制失败，请手动复制")}document.body.removeChild(t)},handleImageError:function(t){var a=t.target,e=a.nextElementSibling;e&&(a.style.display="none",e.style.display="flex")}}},l=r,d=(e("f717"),e("2877")),h=Object(d["a"])(l,s,n,!1,null,"1ac7f0fa",null);a["default"]=h.exports},a1a6b:function(t,a,e){},f717:function(t,a,e){"use strict";e("a1a6b")}}]);
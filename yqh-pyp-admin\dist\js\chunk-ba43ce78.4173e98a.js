(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ba43ce78","chunk-2d20edb7"],{2151:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t._self._c;return e("nav",{staticClass:"site-navbar",class:"site-navbar--"+t.navbarLayoutType},[e("div",{staticClass:"site-navbar__header"},[e("h1",{staticClass:"site-navbar__brand",on:{click:function(e){return t.turnIndex()}}},[t._m(0),t._m(1)])]),e("div",{staticClass:"site-navbar__body clearfix"},[e("el-menu",{staticClass:"site-navbar__menu",attrs:{mode:"horizontal"}},[e("el-menu-item",{staticClass:"site-navbar__switch",attrs:{index:"0"},on:{click:function(e){t.sidebarFold=!t.sidebarFold}}},[e("i",{class:t.sidebarFold?"el-icon-s-unfold":"el-icon-s-fold"})])],1),e("el-menu",{staticClass:"site-navbar__menu site-navbar__menu--right",attrs:{mode:"horizontal"}},[t.isAuth("wx:wxaccount:list")?e("el-menu-item",{attrs:{index:"2"}},[e("template",{slot:"title"},[e("wx-account-selector")],1)],2):t._e(),e("el-menu-item",{staticClass:"site-navbar__avatar",attrs:{index:"3"}},[e("el-dropdown",{attrs:{"show-timeout":0,placement:"bottom"}},[e("span",{staticClass:"el-dropdown-link"},[t._v(" "+t._s(t.userName)+" ")]),e("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[e("el-dropdown-item",{nativeOn:{click:function(e){return t.updatePasswordHandle()}}},[t._v("修改密码")]),e("el-dropdown-item",{nativeOn:{click:function(e){return t.logoutHandle()}}},[t._v("退出")])],1)],1)],1)],1)],1),t.updatePassowrdVisible?e("update-password",{ref:"updatePassowrd"}):t._e()],1)},n=[function(){var t=this,e=t._self._c;return e("a",{staticClass:"site-navbar__brand-lg",attrs:{href:"javascript:;"}},[e("img",{staticStyle:{width:"30px",height:"30px"},attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/565465b88b2d453f9e4731e29138bd8e.png",alt:""}}),e("div",{staticStyle:{"margin-left":"10px"}},[t._v("AI爆店码")])])},function(){var t=this,e=t._self._c;return e("a",{staticClass:"site-navbar__brand-mini",attrs:{href:"javascript:;"}},[e("img",{staticStyle:{width:"30px",height:"30px"},attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/565465b88b2d453f9e4731e29138bd8e.png",alt:""}})])}],o=(a("14d9"),a("b0c0"),a("b0c6")),i=function(){var t=this,e=t._self._c;return e("el-select",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],attrs:{size:"small",filterable:""},on:{change:t.selectAccount},model:{value:t.selectedAppid,callback:function(e){t.selectedAppid=e},expression:"selectedAppid"}},t._l(t.accountList,(function(a){return e("el-option",{key:a.appid,attrs:{label:a.name+"（"+t.ACCOUNT_TYPES[a.type]+"）",value:a.appid}})})),1)},r=[],c=a("2f62"),d={data:function(){return{dataListLoading:!1}},computed:Object(c["b"])({accountList:function(t){return t.wxAccount.accountList},ACCOUNT_TYPES:function(t){return t.wxAccount.ACCOUNT_TYPES},selectedAppid:function(t){return t.wxAccount.selectedAppid}}),mounted:function(){this.getDataList()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/manage/wxAccount/list"),method:"get"}).then((function(e){var a=e.data;a&&200===a.code&&(t.$store.commit("wxAccount/updateAccountList",a.list),a.list.length||t.$message.info("公众号列表为空，请先添加")),t.dataListLoading=!1}))},selectAccount:function(t){this.selectedAppid!=t&&this.$store.commit("wxAccount/selectAccount",t)}}},l=d,u=a("2877"),m=Object(u["a"])(l,i,r,!1,null,null,null),p=m.exports,f=a("ed08"),b={data:function(){return{updatePassowrdVisible:!1}},components:{UpdatePassword:o["default"],WxAccountSelector:p},computed:{navbarLayoutType:{get:function(){return this.$store.state.common.navbarLayoutType}},sidebarFold:{get:function(){return this.$store.state.common.sidebarFold},set:function(t){this.$store.commit("common/updateSidebarFold",t)}},mainTabs:{get:function(){return this.$store.state.common.mainTabs},set:function(t){this.$store.commit("common/updateMainTabs",t)}},userName:{get:function(){return this.$store.state.user.name}}},methods:{turnIndex:function(){window.open("http://huiyi.zhaoshengniuren.com")},updatePasswordHandle:function(){var t=this;this.updatePassowrdVisible=!0,this.$nextTick((function(){t.$refs.updatePassowrd.init()}))},logoutHandle:function(){var t=this;this.$confirm("确定进行[退出]操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:t.$http.adornUrl("/sys/logout"),method:"post",data:t.$http.adornData()}).then((function(e){var a=e.data;a&&200===a.code&&(Object(f["a"])(),t.$router.push({name:"login"}))}))})).catch((function(){}))}}},h=b,w=(a("dad0"),Object(u["a"])(h,s,n,!1,null,"09b6506c",null));e["default"]=w.exports},"41c7":function(t,e,a){},b0c6:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"修改密码",visible:t.visible,"append-to-body":!0},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"}},[e("el-form-item",{attrs:{label:"账号"}},[e("span",[t._v(t._s(t.userName))])]),e("el-form-item",{attrs:{label:"原密码",prop:"password"}},[e("el-input",{attrs:{type:"password"},model:{value:t.dataForm.password,callback:function(e){t.$set(t.dataForm,"password",e)},expression:"dataForm.password"}})],1),e("el-form-item",{attrs:{label:"新密码",prop:"newPassword"}},[e("el-input",{attrs:{type:"password"},model:{value:t.dataForm.newPassword,callback:function(e){t.$set(t.dataForm,"newPassword",e)},expression:"dataForm.newPassword"}})],1),e("el-form-item",{attrs:{label:"确认密码",prop:"confirmPassword"}},[e("el-input",{attrs:{type:"password"},model:{value:t.dataForm.confirmPassword,callback:function(e){t.$set(t.dataForm,"confirmPassword",e)},expression:"dataForm.confirmPassword"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},n=[],o=(a("d9e2"),a("b0c0"),a("ac1f"),a("5319"),a("ed08")),i={data:function(){var t=this,e=function(e,a,s){t.dataForm.newPassword!==a?s(new Error("确认密码与新密码不一致")):s()};return{visible:!1,dataForm:{password:"",newPassword:"",confirmPassword:""},dataRule:{password:[{required:!0,message:"原密码不能为空",trigger:"blur"}],newPassword:[{required:!0,message:"新密码不能为空",trigger:"blur"}],confirmPassword:[{required:!0,message:"确认密码不能为空",trigger:"blur"},{validator:e,trigger:"blur"}]}}},computed:{userName:{get:function(){return this.$store.state.user.name}},mainTabs:{get:function(){return this.$store.state.common.mainTabs},set:function(t){this.$store.commit("common/updateMainTabs",t)}}},methods:{init:function(){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs["dataForm"].resetFields()}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/sys/user/password"),method:"post",data:t.$http.adornData({password:t.dataForm.password,newPassword:t.dataForm.newPassword})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$nextTick((function(){t.mainTabs=[],Object(o["a"])(),t.$router.replace({name:"login"})}))}}):t.$message.error(a.msg)}))}))}}},r=i,c=a("2877"),d=Object(c["a"])(r,s,n,!1,null,null,null);e["default"]=d.exports},dad0:function(t,e,a){"use strict";a("41c7")}}]);
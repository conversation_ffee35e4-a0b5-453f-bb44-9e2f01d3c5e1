(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d21445b"],{afdd:function(t,a,e){"use strict";e.r(a);e("b0c0");var i=function(){var t=this,a=t._self._c;return a("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(a){t.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&t._k(a.keyCode,"enter",13,a.key,"Enter")?null:t.dataFormSubmit()}}},[a("el-form-item",{attrs:{label:"标签名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"标签名称"},model:{value:t.dataForm.name,callback:function(a){t.$set(t.dataForm,"name",a)},expression:"dataForm.name"}})],1),a("el-form-item",{attrs:{label:"排序",prop:"paixu"}},[a("el-input",{attrs:{placeholder:"排序"},model:{value:t.dataForm.paixu,callback:function(a){t.$set(t.dataForm,"paixu",a)},expression:"dataForm.paixu"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){t.visible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],o={data:function(){return{visible:!1,dataForm:{id:0,activityId:"",name:"",createOn:"",createBy:"",updateOn:"",updateBy:"",paixu:0},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],name:[{required:!0,message:"不能为空",trigger:"blur"}],paixu:[{required:!0,message:"不能为空",trigger:"blur"}]}}},methods:{init:function(t,a){var e=this;this.dataForm.id=t||0,this.dataForm.activityId=a,this.visible=!0,this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.dataForm.id&&e.$http({url:e.$http.adornUrl("/hotel/hotelassigntag/info/".concat(e.dataForm.id)),method:"get",params:e.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm.activityId=a.hotelAssignTag.activityId,e.dataForm.name=a.hotelAssignTag.name,e.dataForm.paixu=a.hotelAssignTag.paixu)}))}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(a){a&&t.$http({url:t.$http.adornUrl("/hotel/hotelassigntag/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,activityId:t.dataForm.activityId,name:t.dataForm.name,paixu:t.dataForm.paixu})}).then((function(a){var e=a.data;e&&200===e.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(e.msg)}))}))}}},n=o,d=e("2877"),s=Object(d["a"])(n,i,r,!1,null,null,null);a["default"]=s.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d22dbb5"],{f96b:function(t,a,e){"use strict";e.r(a);e("b0c0");var r=function(){var t=this,a=t._self._c;return a("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(a){t.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&t._k(a.keyCode,"enter",13,a.key,"Enter")?null:t.dataFormSubmit()}}},[a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"名称"},model:{value:t.dataForm.name,callback:function(a){t.$set(t.dataForm,"name",a)},expression:"dataForm.name"}})],1),a("el-form-item",{attrs:{label:"地址",prop:"url"}},[a("el-upload",{staticClass:"avatar-uploader",attrs:{"list-type":"picture-card","before-upload":t.checkFileSize,"show-file-list":!1,"on-success":t.backgroundSuccessHandle,action:t.url}},[t.dataForm.url?a("img",{staticClass:"avatar",attrs:{width:"100px",src:t.dataForm.url}}):a("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){t.visible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},s=[],o={data:function(){return{url:"",visible:!1,dataForm:{id:0,name:"",url:""},dataRule:{name:[{required:!0,message:"名称不能为空",trigger:"blur"}],url:[{required:!0,message:"地址不能为空",trigger:"blur"}]}}},methods:{init:function(t){var a=this;this.url=this.$http.adornUrl("/sys/oss/upload?token=".concat(this.$cookie.get("token"))),this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/sys/sysmusic/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.dataForm.url=e.sysMusic.url,a.dataForm.name=e.sysMusic.name)}))}))},checkFileSize:function(t){return!(t.size/1024/1024>1)||(this.$message.error("".concat(t.name,"文件大于1MB，请选择小于1MB大小的MP3")),!1)},backgroundSuccessHandle:function(t,a,e){t&&200===t.code?(this.dataForm.url=t.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(t.msg)},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(a){a&&t.$http({url:t.$http.adornUrl("/sys/sysmusic/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,url:t.dataForm.url,name:t.dataForm.name})}).then((function(a){var e=a.data;e&&200===e.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(e.msg)}))}))}}},i=o,n=e("2877"),l=Object(n["a"])(i,r,s,!1,null,null,null);a["default"]=l.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-31d07a78"],{"7de9":function(e,t,a){"use strict";a.d(t,"g",(function(){return i})),a.d(t,"f",(function(){return n})),a.d(t,"e",(function(){return r})),a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return s})),a.d(t,"d",(function(){return u}));var i=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],n=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],r=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],l=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],o=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],s=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],u=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]},c9c3:function(e,t,a){"use strict";a.r(t);a("b0c0");var i=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"姓名",prop:"contact"}},[t("el-input",{attrs:{placeholder:"姓名",clearable:""},model:{value:e.dataForm.contact,callback:function(t){e.$set(e.dataForm,"contact",t)},expression:"dataForm.contact"}})],1),t("el-form-item",{attrs:{label:"手机",prop:"mobile"}},[t("el-input",{attrs:{placeholder:"手机",clearable:""},model:{value:e.dataForm.mobile,callback:function(t){e.$set(e.dataForm,"mobile",t)},expression:"dataForm.mobile"}})],1),e.channelList.length>0?t("el-form-item",{attrs:{label:"报名通道",prop:"applyActivityChannelConfigId",rules:[{required:!0,message:"请选择报名通道",trigger:"blur"}]}},[t("el-select",{attrs:{placeholder:"报名通道",filterable:""},model:{value:e.dataForm.applyActivityChannelConfigId,callback:function(t){e.$set(e.dataForm,"applyActivityChannelConfigId",t)},expression:"dataForm.applyActivityChannelConfigId"}},e._l(e.channelList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1):e._e(),t("el-form-item",{attrs:{label:"订单状态",prop:"status"}},[t("el-select",{attrs:{placeholder:"订单状态",filterable:""},model:{value:e.dataForm.status,callback:function(t){e.$set(e.dataForm,"status",t)},expression:"dataForm.status"}},e._l(e.orderStatus,(function(e){return t("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"支付来源",prop:"source"}},[t("el-select",{attrs:{placeholder:"支付来源",filterable:""},model:{value:e.dataForm.source,callback:function(t){e.$set(e.dataForm,"source",t)},expression:"dataForm.source"}},e._l(e.sources,(function(e){return t("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"备注",prop:"remarks"}},[t("el-input",{attrs:{placeholder:"备注",filterable:""},model:{value:e.dataForm.remarks,callback:function(t){e.$set(e.dataForm,"remarks",t)},expression:"dataForm.remarks"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},n=[],r=a("7de9"),l={data:function(){return{visible:!1,dataForm:{id:0,activityId:"",applyActivityChannelConfigId:"",contact:"",mobile:"",status:1,source:0,remarks:""},sources:r["f"],orderStatus:r["e"],channelList:[],dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],status:[{required:!0,message:"订单状态不能为空",trigger:"blur"}],contact:[{required:!0,message:"联系人不能为空",trigger:"blur"}],mobile:[{required:!0,message:"手机不能为空",trigger:"blur"}],source:[{required:!0,message:"支付来源不能为空",trigger:"blur"}]}}},methods:{init:function(e){var t=this;this.dataForm.activityId=e,this.visible=!0,this.$nextTick((function(){t.$refs["dataForm"].resetFields(),t.getChannelByActivityId()}))},getChannelByActivityId:function(){var e=this;this.$http({url:this.$http.adornUrl("/apply/applyactivitychannelconfig/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.channelList=a.result,console.log(e.channelList))}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$http({url:e.$http.adornUrl("/activity/activityuserapplyorder/createOrderAdmin"),method:"post",data:e.$http.adornData({activityId:e.dataForm.activityId,contact:e.dataForm.contact,applyActivityChannelConfigId:e.dataForm.applyActivityChannelConfigId,status:e.dataForm.status,mobile:e.dataForm.mobile,source:e.dataForm.source,remarks:e.dataForm.remarks})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(a.msg)}))}))}}},o=l,s=a("2877"),u=Object(s["a"])(o,i,n,!1,null,null,null);t["default"]=u.exports}}]);
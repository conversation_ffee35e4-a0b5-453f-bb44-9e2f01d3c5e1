(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6c1dbe2a"],{"28a5":function(t,e,i){"use strict";var a=i("aae3"),n=i("cb7c"),r=i("ebd6"),o=i("0390"),s=i("9def"),c=i("5f1b"),l=i("520a"),p=i("79e5"),d=Math.min,u=[].push,m="split",h="length",f="lastIndex",v=4294967295,y=!p((function(){RegExp(v,"y")}));i("214f")("split",2,(function(t,e,i,p){var g;return g="c"=="abbc"[m](/(b)*/)[1]||4!="test"[m](/(?:)/,-1)[h]||2!="ab"[m](/(?:ab)*/)[h]||4!="."[m](/(.?)(.?)/)[h]||"."[m](/()()/)[h]>1||""[m](/.?/)[h]?function(t,e){var n=String(this);if(void 0===t&&0===e)return[];if(!a(t))return i.call(n,t,e);var r,o,s,c=[],p=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),d=0,m=void 0===e?v:e>>>0,y=new RegExp(t.source,p+"g");while(r=l.call(y,n)){if(o=y[f],o>d&&(c.push(n.slice(d,r.index)),r[h]>1&&r.index<n[h]&&u.apply(c,r.slice(1)),s=r[0][h],d=o,c[h]>=m))break;y[f]===r.index&&y[f]++}return d===n[h]?!s&&y.test("")||c.push(""):c.push(n.slice(d)),c[h]>m?c.slice(0,m):c}:"0"[m](void 0,0)[h]?function(t,e){return void 0===t&&0===e?[]:i.call(this,t,e)}:i,[function(i,a){var n=t(this),r=void 0==i?void 0:i[e];return void 0!==r?r.call(i,n,a):g.call(String(n),i,a)},function(t,e){var a=p(g,t,this,e,g!==i);if(a.done)return a.value;var l=n(t),u=String(this),m=r(l,RegExp),h=l.unicode,f=(l.ignoreCase?"i":"")+(l.multiline?"m":"")+(l.unicode?"u":"")+(y?"y":"g"),T=new m(y?l:"^(?:"+l.source+")",f),S=void 0===e?v:e>>>0;if(0===S)return[];if(0===u.length)return null===c(T,u)?[u]:[];var b=0,F=0,w=[];while(F<u.length){T.lastIndex=y?F:0;var C,k=c(T,y?u:u.slice(F));if(null===k||(C=d(s(T.lastIndex+(y?0:F)),u.length))===b)F=o(u,F,h);else{if(w.push(u.slice(b,F)),w.length===S)return w;for(var I=1;I<=k.length-1;I++)if(w.push(k[I]),w.length===S)return w;F=b=C}}return w.push(u.slice(b)),w}]}))},"2e08":function(t,e,i){var a=i("9def"),n=i("9744"),r=i("be13");t.exports=function(t,e,i,o){var s=String(r(t)),c=s.length,l=void 0===i?" ":String(i),p=a(e);if(p<=c||""==l)return s;var d=p-c,u=n.call(l,Math.ceil(d/l.length));return u.length>d&&(u=u.slice(0,d)),o?u+s:s+u}},9744:function(t,e,i){"use strict";var a=i("4588"),n=i("be13");t.exports=function(t){var e=String(n(this)),i="",r=a(t);if(r<0||r==1/0)throw RangeError("Count can't be negative");for(;r>0;(r>>>=1)&&(e+=e))1&r&&(i+=e);return i}},b715:function(t,e,i){"use strict";i("c15a")},c15a:function(t,e,i){},d7b7:function(t,e,i){"use strict";i.r(e);i("7f7f");var a,n=function(){var t=this,e=t._self._c;return e("div",{class:t.isMobilePhone?"":"pc-container"},[t.isMobilePhone?t._e():e("pcheader"),e("div",{staticClass:"user-info-confirm"},[e("div",{staticClass:"info-header"},[e("van-icon",{attrs:{name:"user-circle-o",size:"22",color:"#1989fa"}}),e("span",[t._v("个人信息确认")])],1),e("div",{staticClass:"info-content"},[e("div",{staticClass:"info-item"},[e("div",{staticClass:"info-label"},[t._v("姓名")]),e("div",{staticClass:"info-value"},[t._v(t._s(t.guestInfo.name))])]),e("div",{staticClass:"info-item"},[e("div",{staticClass:"info-label"},[t._v("联系方式")]),e("van-field",{staticClass:"custom-field",attrs:{placeholder:"请输入联系方式",border:!1,required:"",rules:[{required:!0,message:"请填写联系方式"}]},model:{value:t.guestInfo.mobile,callback:function(e){t.$set(t.guestInfo,"mobile",e)},expression:"guestInfo.mobile"}})],1),e("div",{staticClass:"info-item"},[e("div",{staticClass:"info-label"},[t._v("身份证类型")]),e("div",{staticClass:"info-value clickable",on:{click:function(e){t.idCardTypeShow=!0}}},[t._v("\n                    "+t._s(t.guestInfo.idCardType||"请选择身份证类型")+"\n                    "),e("van-icon",{attrs:{name:"arrow",size:"16",color:"#999"}})],1)]),e("div",{staticClass:"info-item"},[e("div",{staticClass:"info-label"},[t._v("身份证号")]),e("van-field",{staticClass:"custom-field",attrs:{placeholder:"请输入身份证号",border:!1,required:"",rules:[{required:!0,message:"请填写身份证号"}]},model:{value:t.guestInfo.idCard,callback:function(e){t.$set(t.guestInfo,"idCard",e)},expression:"guestInfo.idCard"}})],1)]),t.isInfoConfirm?t._e():e("div",{staticClass:"info-footer"},[e("van-button",{attrs:{type:"primary",block:"",round:"",loading:t.confirmLoading},on:{click:t.confirmUserInfo}},[t._v("确认信息")])],1),e("van-action-sheet",{attrs:{actions:t.idCardType},on:{select:t.idCardTypeSelect},model:{value:t.idCardTypeShow,callback:function(e){t.idCardTypeShow=e},expression:"idCardTypeShow"}})],1),e("div",{staticClass:"travel-type-selector"},[e("div",{staticClass:"tool-selector"},[e("span",[t._v("您选择的出行工具")]),e("div",{staticClass:"tool-buttons"},[e("button",{class:["tool-btn",0==t.tripForm.inType?"active":""],on:{click:function(e){return t.selectTravelType(0)}}},[t._v("飞机")]),e("button",{class:["tool-btn",1==t.tripForm.inType?"active":""],on:{click:function(e){return t.selectTravelType(1)}}},[t._v("火车")])])]),e("div",{staticClass:"travel-info-input"},[e("div",{staticClass:"input-row"},[e("button",{class:["input-btn",t.showNewUI?"active":""],on:{click:function(e){return t.toggleInputMode(2)}}},[t._v("查询")]),e("button",{class:["input-btn",t.showNewUI?"":"active"],on:{click:function(e){return t.toggleInputMode(0)}}},[t._v("录入")])])]),t.showNewUI?e("div",[e("div",{staticClass:"location-selector"},[e("div",{staticClass:"location from"},[e("van-field",{attrs:{placeholder:"请输入出发地",readonly:""},on:{focus:function(e){t.showStartSearch=!0}},model:{value:t.tripForm.inStartPlace,callback:function(e){t.$set(t.tripForm,"inStartPlace",e)},expression:"tripForm.inStartPlace"}})],1),e("div",{staticClass:"location-arrow"},[e("van-icon",{attrs:{name:"arrow"}})],1),e("div",{staticClass:"location to"},[e("van-field",{attrs:{placeholder:"请输入目的地",readonly:""},on:{focus:function(e){t.showEndSearch=!0}},model:{value:t.tripForm.inEndPlace,callback:function(e){t.$set(t.tripForm,"inEndPlace",e)},expression:"tripForm.inEndPlace"}})],1)]),e("div",{staticClass:"date-selector",on:{click:function(e){t.dateShow=!0}}},[e("div",{staticClass:"date"},[t._v("\n                    "+t._s(t.formatDisplayDate(t.tripForm.inDate)||"01月17日 周五")+"\n                ")]),e("van-icon",{attrs:{name:"calendar-o"}})],1),e("div",{staticClass:"search-button",on:{click:t.showDetailForm}},[e("span",[t._v(t._s(0==t.tripForm.inType?"搜索机票":"搜索火车票"))])]),e("div",{staticClass:"promotion-info"},[t._v("\n                * 出票规则："+t._s(0==t.tripForm.inType?t.ticketType[t.activityConfig.planeTicketType].value:1==t.tripForm.inType?t.ticketType[t.activityConfig.trainTicketType].value:"")+"\n            ")]),e("div",{staticClass:"go-back-button",on:{click:t.goBack}},[e("span",[t._v("返回上一页面")])])]):e("van-cell-group",[e("van-field",{attrs:{name:"radio",label:"填写方式"},scopedSlots:t._u([{key:"input",fn:function(){return[e("van-radio-group",{attrs:{direction:"horizontal"},model:{value:t.tripForm.doType,callback:function(e){t.$set(t.tripForm,"doType",e)},expression:"tripForm.doType"}},t._l(t.doType,(function(i){return e("van-radio",{directives:[{name:"show",rawName:"v-show",value:i.show,expression:"item.show"}],key:i.key,attrs:{name:i.key}},[t._v(t._s(i.name))])})),1)]},proxy:!0}])}),e("van-cell",{attrs:{required:!0,title:"行程类型","is-link":""},on:{click:function(e){t.guestGoTypeShow=!0}},model:{value:t.tripForm.inTypeName,callback:function(e){t.$set(t.tripForm,"inTypeName",e)},expression:"tripForm.inTypeName"}}),0==t.tripForm.doType?e("div",[e("van-cell",{attrs:{title:"上传行程图片"}},[t.tripForm.image?t._e():e("van-uploader",{attrs:{"after-read":t.afterRead,name:"image","before-read":t.beforeRead,accept:"*"}},[[e("van-icon",{staticStyle:{"margin-left":"5px"},attrs:{name:"description",size:"40px"}}),e("div",{staticStyle:{"font-size":"12px"}},[t._v("点击上传文件")])]],2),t.tripForm.image?e("div",{staticClass:"file-info"},[t.isImage(t.tripForm.image)?[e("van-image",{attrs:{height:"40px",src:t.tripForm.image,fit:"contain"},on:{click:function(e){return e.stopPropagation(),t.preImage(t.tripForm.image)}}})]:[e("van-icon",{attrs:{name:"description",size:"20px"}}),e("span",{staticClass:"file-text"},[t._v("已上传文件")])],e("van-icon",{staticClass:"remove-icon",attrs:{name:"cross"},on:{click:function(e){return e.stopPropagation(),t.removeFile.apply(null,arguments)}}})],2):t._e()],1)],1):e("div",[e("van-field",{attrs:{name:"行程航班/火车号",label:"行程航班/火车号",placeholder:"行程航班/火车号",required:"",rules:[{required:!0,message:"行程航班/火车号"}]},model:{value:t.tripForm.inNumber,callback:function(e){t.$set(t.tripForm,"inNumber",e)},expression:"tripForm.inNumber"}}),e("van-cell",{attrs:{required:!0,title:"行程日期","is-link":""},on:{click:function(e){t.dateShow=!0}},model:{value:t.tripForm.inDate,callback:function(e){t.$set(t.tripForm,"inDate",e)},expression:"tripForm.inDate"}}),e("van-cell",{directives:[{name:"show",rawName:"v-show",value:t.tripForm.inDate,expression:"tripForm.inDate"}],attrs:{required:!0,title:"出发时间","is-link":""},on:{click:function(e){t.startShow=!0}},model:{value:t.tripForm.inStartDate,callback:function(e){t.$set(t.tripForm,"inStartDate",e)},expression:"tripForm.inStartDate"}}),e("van-cell",{directives:[{name:"show",rawName:"v-show",value:t.tripForm.inDate,expression:"tripForm.inDate"}],attrs:{required:!0,title:"到达时间","is-link":""},on:{click:function(e){t.endShow=!0}},model:{value:t.tripForm.inEndDate,callback:function(e){t.$set(t.tripForm,"inEndDate",e)},expression:"tripForm.inEndDate"}}),e("div",{staticStyle:{"font-size":"12px",padding:"5px 20px",color:"red"}},[t._v("\n                    站点精确到航站楼或动车站，而非城市\n                ")]),e("van-field",{attrs:{name:"行程出发站点",label:"行程出发站点",placeholder:"行程出发站点"},model:{value:t.tripForm.inStartPlace,callback:function(e){t.$set(t.tripForm,"inStartPlace",e)},expression:"tripForm.inStartPlace"}}),e("van-field",{attrs:{name:"行程到达站点",label:"行程到达站点",placeholder:"行程到达站点"},model:{value:t.tripForm.inEndPlace,callback:function(e){t.$set(t.tripForm,"inEndPlace",e)},expression:"tripForm.inEndPlace"}})],1),t.showForm?e("div",{staticStyle:{margin:"16px",display:"flex",gap:"10px"}},[e("van-button",{attrs:{round:"",block:"",type:"info"},on:{click:t.goBack}},[t._v("取消")]),e("van-button",{attrs:{round:"",color:"#DD5C5F",block:"",type:"info",loading:t.loading,"loading-text":"提交中"},on:{click:t.submit}},[t._v("提交")])],1):t._e()],1),e("van-popup",{style:{height:"45%"},attrs:{position:"bottom"},model:{value:t.dateShow,callback:function(e){t.dateShow=e},expression:"dateShow"}},[e("van-datetime-picker",{attrs:{type:"date",title:"选择日期","min-date":t.minDate,"max-date":t.maxDate,formatter:t.formatter},on:{cancel:function(e){t.dateShow=!1},confirm:t.dateSelect},model:{value:t.currentDate,callback:function(e){t.currentDate=e},expression:"currentDate"}})],1),e("van-popup",{style:{height:"45%"},attrs:{position:"bottom"},model:{value:t.startShow,callback:function(e){t.startShow=e},expression:"startShow"}},[e("van-datetime-picker",{attrs:{type:"time",title:"选择出发时间",formatter:t.formatter},on:{cancel:function(e){t.startShow=!1},confirm:t.startSelect},model:{value:t.start,callback:function(e){t.start=e},expression:"start"}})],1),e("van-popup",{style:{height:"45%"},attrs:{position:"bottom"},model:{value:t.endShow,callback:function(e){t.endShow=e},expression:"endShow"}},[e("van-datetime-picker",{attrs:{type:"time",title:"选择到达时间",formatter:t.formatter,"min-hour":t.minHour},on:{cancel:function(e){t.endShow=!1},confirm:t.endSelect},model:{value:t.end,callback:function(e){t.end=e},expression:"end"}})],1),e("van-action-sheet",{attrs:{actions:t.guestGoType},on:{select:t.guestGoTypeSelect},model:{value:t.guestGoTypeShow,callback:function(e){t.guestGoTypeShow=e},expression:"guestGoTypeShow"}}),e("van-action-sheet",{attrs:{actions:t.tripType},on:{select:t.tripTypeSelect},model:{value:t.tripTypeShow,callback:function(e){t.tripTypeShow=e},expression:"tripTypeShow"}}),e("van-popup",{style:{height:"70%"},attrs:{position:"bottom",round:""},model:{value:t.showStartSearch,callback:function(e){t.showStartSearch=e},expression:"showStartSearch"}},[e("div",{staticClass:"search-header"},[e("van-search",{attrs:{placeholder:"请输入城市名称或机场/车站名称",shape:"round",background:"#f7f8fa",autofocus:""},on:{input:t.searchStartPlaces},model:{value:t.startSearchText,callback:function(e){t.startSearchText=e},expression:"startSearchText"}}),e("van-icon",{staticClass:"close-icon",attrs:{name:"cross"},on:{click:function(e){t.showStartSearch=!1}}})],1),e("div",{staticClass:"search-results"},[0===t.startSearchResults.length&&t.startSearchText&&!t.startLoading?e("div",{staticClass:"empty-result"},[e("van-empty",{attrs:{description:"暂无搜索结果"}})],1):t._e(),t._l(t.startSearchResults,(function(i,a){return e("div",{key:a,staticClass:"search-item",on:{click:function(e){return t.selectStartPlace(i)}}},[e("div",{staticClass:"item-main"},[t._v(t._s(0==t.tripForm.inType?i.airportName:i.stationName))]),e("div",{staticClass:"item-sub"},[t._v(t._s(i.cityName))])])})),t.startLoading?e("div",{staticClass:"loading-more"},[e("van-loading",{attrs:{type:"spinner",size:"24px",color:"#1989fa"}},[t._v("搜索中...")])],1):t._e()],2)]),e("van-popup",{style:{height:"70%"},attrs:{position:"bottom",round:""},model:{value:t.showEndSearch,callback:function(e){t.showEndSearch=e},expression:"showEndSearch"}},[e("div",{staticClass:"search-header"},[e("van-search",{attrs:{placeholder:"请输入城市名称或机场/车站名称",shape:"round",background:"#f7f8fa",autofocus:""},on:{input:t.searchEndPlaces},model:{value:t.endSearchText,callback:function(e){t.endSearchText=e},expression:"endSearchText"}}),e("van-icon",{staticClass:"close-icon",attrs:{name:"cross"},on:{click:function(e){t.showEndSearch=!1}}})],1),e("div",{staticClass:"search-results"},[0===t.endSearchResults.length&&t.endSearchText&&!t.endLoading?e("div",{staticClass:"empty-result"},[e("van-empty",{attrs:{description:"暂无搜索结果"}})],1):t._e(),t._l(t.endSearchResults,(function(i,a){return e("div",{key:a,staticClass:"search-item",on:{click:function(e){return t.selectEndPlace(i)}}},[e("div",{staticClass:"item-main"},[t._v(t._s(0==t.tripForm.inType?i.airportName:i.stationName))]),e("div",{staticClass:"item-sub"},[t._v(t._s(i.cityName))])])})),t.endLoading?e("div",{staticClass:"loading-more"},[e("van-loading",{attrs:{type:"spinner",size:"24px",color:"#1989fa"}},[t._v("搜索中...")])],1):t._e()],2)])],1)],1)},r=[],o=i("ade3"),s=(i("6762"),i("28a5"),i("6b54"),i("f576"),i("66c7")),c=i("cacf"),l=i("1b69"),p=i("7de9"),d=i("7c8d"),u=i.n(d),m={components:{pcheader:l["default"]},data:function(){return{activityConfig:{},isInfoConfirm:!1,idCardType:p["c"],ticketType:p["e"],idCardTypeShow:!1,guestGoType:p["b"],doType:p["a"],tripType:p["i"],loading:!1,tripTypeShow:!1,dateShow:!1,guestGoTypeShow:!1,selectingStartPlace:!0,showForm:!1,showNewUI:!0,isMobilePhone:Object(c["c"])(),openid:void 0,activityId:void 0,id:void 0,guestInfo:{},tripForm:{id:"",inType:0,inTypeName:"飞机",type:0,typeName:"",inDate:"",inNumber:"",inEndPlace:"",inStartPlace:"",inEndDate:"",inStartDate:"",activityGuestId:"",activityId:"",isBuy:0,price:0,doType:0,image:""},minDate:new Date,maxDate:new Date(2030,10,1),currentDate:new Date,startShow:!1,start:"",endShow:!1,end:"",minHour:"",showStartSearch:!1,showEndSearch:!1,startSearchText:"",endSearchText:"",startSearchResults:[],endSearchResults:[],startLoading:!1,endLoading:!1,startTimeout:null,endTimeout:null}},mounted:function(){this.id=this.$route.query.detailId,this.tripForm.activityGuestId=this.$route.query.detailId,this.openid=this.$cookie.get("openid"),this.tripForm.inDate=s["a"].formatDate.format(new Date,"yyyy/MM/dd hh:mm:ss"),this.$route.query.tripId?this.getTripInfo(this.$route.query.tripId):(this.getActivityList(),this.tripForm.inTypeName=this.guestGoType[0].name,this.tripForm.typeName=this.tripType[0].name)},methods:(a={getActivityConfig:function(){var t=this;this.$fly.get("/pyp/web/activity/activityConfig/check",{activityId:this.activityId,guestId:this.guestInfo.id}).then((function(e){200==e.code?t.activityConfig=e.result:(vant.Toast(e.msg),t.activityConfig={})}))},confirmUserInfo:function(){var t=this;if(this.guestInfo.mobile)return Object(c["b"])(this.guestInfo.mobile)?void(this.guestInfo.idCardType?this.guestInfo.idCard?Object(c["a"])(this.guestInfo.idCard)?(this.confirmLoading=!0,this.$fly.post("/pyp/web/activity/activityguest/updateInfo",{id:this.guestInfo.id,mobile:this.guestInfo.mobile,idCardType:this.guestInfo.idCardType,idCard:this.guestInfo.idCard}).then((function(e){t.confirmLoading=!1,e&&200===e.code?(vant.Toast("信息确认成功"),t.isInfoConfirm=!0):vant.Toast(e.msg||"确认失败，请重试")})).catch((function(){t.confirmLoading=!1,vant.Toast("网络错误，请重试")}))):vant.Toast("请输入正确的身份证"):vant.Toast("请填写身份证"):vant.Toast("请选择身份证类型")):(vant.Toast("请输入正确的手机号"),!1);vant.Toast("请填写联系方式")},idCardTypeSelect:function(t){this.idCardTypeShow=!1,this.guestInfo.idCardType=t.name},toggleInputMode:function(t){this.tripForm.doType=t,this.showNewUI=t,t||(this.showForm=!0)},formatDisplayDate:function(t){if(!t)return"";var e=new Date(t),i=e.getMonth()+1,a=e.getDate(),n=["周日","周一","周二","周三","周四","周五","周六"][e.getDay()];return"".concat(i.toString().padStart(2,"0"),"月").concat(a.toString().padStart(2,"0"),"日 ").concat(n)},selectTravelType:function(t){this.tripForm.inType=t,this.tripForm.inTypeName=this.guestGoType[t].name},selectTripType:function(t){this.tripForm.type=t,this.tripForm.typeName=this.tripType[t].name},showDetailForm:function(){if(!this.guestInfo.mobile)return vant.Toast("请输入联系方式"),!1;if(!Object(c["b"])(this.guestInfo.mobile))return vant.Toast("请输入正确的手机号"),!1;if(!this.guestInfo.idCardType)return vant.Toast("请选择身份证类型"),!1;if(!this.guestInfo.idCard)return vant.Toast("请输入身份证号"),!1;if(this.tripForm.inStartPlace)if(this.tripForm.inEndPlace)if(this.tripForm.inDate){this.tripForm.activityId=this.activityId,this.tripForm.activityGuestId=this.id;var t=JSON.stringify(this.tripForm);0==this.tripForm.inType?this.$router.push({path:"/schedules/expert/components/plane-select",query:{tripInfo:t}}):this.$router.push({path:"/schedules/expert/components/train-select",query:{tripInfo:t}})}else vant.Toast("请选择日期");else vant.Toast("请选择目的地");else vant.Toast("请选择出发地")},goBack:function(){this.$router.back()},getTripInfo:function(t){var e=this;this.$fly.get("/pyp/web/activity/activityguest/getTripById/".concat(t)).then((function(t){200==t.code?(e.tripForm=t.result,e.tripForm.inTypeName=e.guestGoType[e.tripForm.inType].name,e.tripForm.typeName=e.tripType[e.tripForm.type].name,e.getActivityList()):vant.Toast(t.msg)}))},isImage:function(t){var e=["jpg","jpeg","png","gif","bmp","webp"],i=t.split(".").pop().toLowerCase();return e.includes(i)},preImage:function(t){vant.ImagePreview({images:[t],closeable:!0})},startSelect:function(t){this.startShow=!1,this.minHour=this.start.substring(0,2),this.tripForm.inStartDate=this.tripForm.inDate.substring(0,10)+" "+t+":00"},endSelect:function(t){this.endShow=!1,this.tripForm.inEndDate=this.tripForm.inDate.substring(0,10)+" "+t+":00"},dateSelect:function(t){this.dateShow=!1,this.tripForm.inDate=s["a"].formatDate.format(t,"yyyy/MM/dd hh:mm:ss"),this.tripForm.inStartDate||(this.tripForm.inStartDate=this.tripForm.inDate.substring(0,10)+" 08:00:00"),this.tripForm.inEndDate||(this.tripForm.inEndDate=this.tripForm.inDate.substring(0,10)+" 10:00:00")},formatter:function(t,e){return"year"===t?"".concat(e,"年"):"month"===t?"".concat(e,"月"):"day"===t?"".concat(e,"日"):"hour"===t?"".concat(e,"时"):"minute"===t?"".concat(e,"分"):e},getActivityList:function(){var t=this;this.$fly.get("/pyp/web/activity/activityguest/getById/".concat(this.id)).then((function(e){200==e.code?(t.guestInfo=e.result,t.activityId=e.result.activityId,t.tripForm.activityId=e.result.activityId,t.getActivityConfig()):(vant.Toast(e.msg),t.guestInfo={})}))},guestGoTypeSelect:function(t){this.guestGoTypeShow=!1,this.tripForm.inType=t.key,this.tripForm.inTypeName=t.name},tripTypeSelect:function(t){this.tripTypeShow=!1,this.tripForm.type=t.key,this.tripForm.typeName=t.name},afterRead:function(t,e){var i=e.name;t.status="uploading",t.message="上传中...";var a=new FormData,n=t.file,r=this;"image/jpeg"!==n.type&&"image/png"!==n.type&&"image/jpg"!==n.type?(a.append("file",n),r.$fly.post("/pyp/web/upload",a).then((function(t){t&&200===t.code&&r.$set(r.tripForm,i,t.result)}))):new u.a(n,{quality:.7,success:function(t){a.append("file",new window.File([t],n.name,{type:n.type})),r.$fly.post("/pyp/web/upload",a).then((function(t){t&&200===t.code&&r.$set(r.tripForm,i,t.result)}))}})},beforeRead:function(t){return!0},removeFile:function(){this.tripForm.image=""},searchStartPlaces:function(){var t=this;""!==this.startSearchText?(this.startLoading=!0,clearTimeout(this.startTimeout),this.startTimeout=setTimeout((function(){var e=0==t.tripForm.inType?"/pyp/web/config/configairport/findByName":"/pyp/web/config/configtrainstation/findByName";t.$fly.get(e,{name:t.startSearchText}).then((function(e){t.startLoading=!1,e&&200===e.code?t.startSearchResults=e.result:(vant.Toast(e.msg||"搜索失败"),t.startSearchResults=[])})).catch((function(){t.startLoading=!1,vant.Toast("网络错误，请重试")}))}),300)):this.startSearchResults=[]},searchEndPlaces:function(){var t=this;""!==this.endSearchText?(this.endLoading=!0,clearTimeout(this.endTimeout),this.endTimeout=setTimeout((function(){var e=0==t.tripForm.inType?"/pyp/web/config/configairport/findByName":"/pyp/web/config/configtrainstation/findByName";t.$fly.get(e,{name:t.endSearchText}).then((function(e){t.endLoading=!1,e&&200===e.code?t.endSearchResults=e.result:(vant.Toast(e.msg||"搜索失败"),t.endSearchResults=[])})).catch((function(){t.endLoading=!1,vant.Toast("网络错误，请重试")}))}),300)):this.endSearchResults=[]},selectStartPlace:function(t){0==this.tripForm.inType?(this.tripForm.inStartPlace=t.airportName,this.tripForm.inStartCity=t.cityName,this.tripForm.startCityCode=t.cityCode):(this.tripForm.inStartPlace=t.stationName,this.tripForm.inStartCity=t.cityName,this.tripForm.startCityCode=t.stationCode),this.showStartSearch=!1},selectEndPlace:function(t){0==this.tripForm.inType?(this.tripForm.inEndPlace=t.airportName,this.tripForm.inEndCity=t.cityName,this.tripForm.endCityCode=t.cityCode):(this.tripForm.inEndPlace=t.stationName,this.tripForm.inEndCity=t.cityName,this.tripForm.endCityCode=t.stationCode),this.showEndSearch=!1}},Object(o["a"])(a,"selectTravelType",(function(t){this.tripForm.inType=t,this.tripForm.inTypeName=this.guestGoType[t].name,this.tripForm.inStartPlace="",this.tripForm.inEndPlace="",this.tripForm.inStartCity="",this.tripForm.inEndCity="",this.tripForm.startCityCode="",this.tripForm.endCityCode=""})),Object(o["a"])(a,"submit",(function(){var t=this;if(!this.guestInfo.mobile)return vant.Toast("请输入联系方式"),!1;if(!Object(c["b"])(this.guestInfo.mobile))return vant.Toast("请输入正确的手机号"),!1;if(!this.guestInfo.idCardType)return vant.Toast("请选择身份证类型"),!1;if(!this.guestInfo.idCard)return vant.Toast("请输入身份证号"),!1;if(0==this.tripForm.doType){if(!this.tripForm.image)return vant.Toast("请上传行程图片"),!1}else{if(""===this.tripForm.inType)return vant.Toast("选择类型"),!1;if(""===this.tripForm.type)return vant.Toast("选择类型"),!1;if(!this.tripForm.inDate||!this.tripForm.inStartDate||!this.tripForm.inEndDate)return vant.Toast("选择日期"),!1;if(!this.tripForm.inNumber)return vant.Toast("请输入航班号/火车号"),!1}this.loading=!0;var e=this.tripForm.id?"/pyp/web/activity/activityguest/updateTrip":"/pyp/web/activity/activityguest/saveTrip";this.$fly.post(e,this.tripForm).then((function(e){t.loading=!1,e&&200===e.code?(vant.Toast("提交成功"),t.$router.back()):vant.Toast(e.msg)}))})),a)},h=m,f=(i("b715"),i("2877")),v=Object(f["a"])(h,n,r,!1,null,"793c851c",null);e["default"]=v.exports},f576:function(t,e,i){"use strict";var a=i("5ca1"),n=i("2e08"),r=i("a25f"),o=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(r);a(a.P+a.F*o,"String",{padStart:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0,!0)}})}}]);
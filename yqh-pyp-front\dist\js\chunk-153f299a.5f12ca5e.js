(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-153f299a"],{"196b":function(a,t,s){"use strict";s.r(t);s("7f7f");var e=function(){var a=this,t=a._self._c;return t("div",{staticClass:"salesman-scan"},[t("div",{staticClass:"header-decoration"}),t("div",{staticClass:"salesman-info"},[t("div",{staticClass:"card-glow"}),t("div",{staticClass:"avatar-container"},[t("div",{staticClass:"avatar"},[t("img",{attrs:{src:a.salesman.avatar||"http://mpjoy.oss-cn-beijing.aliyuncs.com/20240917/d9301e81ab674112b3acf4a84aff4e96.png",alt:"业务员头像"},on:{error:a.handleImageError}})]),t("div",{staticClass:"online-status"})]),t("div",{staticClass:"info"},[t("h3",[a._v(a._s(a.salesman.name))]),t("div",{staticClass:"contact-info"},[t("i",{staticClass:"icon-phone"}),t("span",[a._v(a._s(a.salesman.mobile))])])]),a._m(0)]),a.rechargePackages.length>0?t("div",{staticClass:"package-section"},[a._m(1),t("div",{staticClass:"package-list"},a._l(a.rechargePackages,(function(s){return t("div",{key:s.id,staticClass:"package-item",class:{active:a.selectedPackageId==s.id&&1==a.selectedPackageType},on:{click:function(t){return a.selectPackage(s.id,1)}}},[t("div",{staticClass:"package-glow"}),t("div",{staticClass:"package-header"},[t("div",{staticClass:"package-title"},[t("span",{staticClass:"package-name"},[a._v(a._s(s.name))]),t("div",{staticClass:"package-tags"},[s.isHot?t("span",{staticClass:"tag hot"},[t("i",{staticClass:"icon-fire"}),a._v("热门\n              ")]):a._e(),s.isRecommended?t("span",{staticClass:"tag recommend"},[t("i",{staticClass:"icon-star"}),a._v("推荐\n              ")]):a._e()])]),t("div",{staticClass:"package-price-container"},[t("span",{staticClass:"currency"},[a._v("¥")]),t("span",{staticClass:"package-price"},[a._v(a._s(s.price))])])]),t("div",{staticClass:"package-desc"},[a._v(a._s(s.description))]),t("div",{staticClass:"package-benefits"},[t("div",{staticClass:"benefit-item"},[t("i",{staticClass:"icon-count"}),t("span",{staticClass:"package-count"},[a._v(a._s(s.countValue)+"次使用")])])])])})),0)]):a._e(),a.activityPackages.length>0?t("div",{staticClass:"package-section"},[a._m(2),t("div",{staticClass:"package-list"},a._l(a.activityPackages,(function(s){return t("div",{key:s.id,staticClass:"package-item",class:{active:a.selectedPackageId==s.id&&2==a.selectedPackageType},on:{click:function(t){return a.selectPackage(s.id,2)}}},[t("div",{staticClass:"package-glow"}),t("div",{staticClass:"package-header"},[t("div",{staticClass:"package-title"},[t("span",{staticClass:"package-name"},[a._v(a._s(s.name))]),t("div",{staticClass:"package-tags"},[s.isHot?t("span",{staticClass:"tag hot"},[t("i",{staticClass:"icon-fire"}),a._v("热门\n              ")]):a._e(),s.isRecommended?t("span",{staticClass:"tag recommend"},[t("i",{staticClass:"icon-star"}),a._v("推荐\n              ")]):a._e()])]),t("div",{staticClass:"package-price-container"},[t("span",{staticClass:"currency"},[a._v("¥")]),t("span",{staticClass:"package-price"},[a._v(a._s(s.price))])])]),t("div",{staticClass:"package-desc"},[a._v(a._s(s.description))]),t("div",{staticClass:"package-benefits"},[t("div",{staticClass:"benefit-item"},[t("i",{staticClass:"icon-activity"}),t("span",{staticClass:"package-count"},[a._v("可转发分享"+a._s(s.countValue)+"次")])])])])})),0)]):a._e(),2==a.selectedPackageType?t("div",{staticClass:"activity-name-section"},[a._m(3),t("div",{staticClass:"input-container"},[t("van-field",{staticClass:"custom-field",attrs:{placeholder:"请输入活动名称",maxlength:"50","show-word-limit":"",required:""},model:{value:a.activityName,callback:function(t){a.activityName=t},expression:"activityName"}})],1)]):a._e(),t("div",{staticClass:"action-buttons"},[t("van-button",{staticClass:"purchase-button",attrs:{type:"primary",size:"large",disabled:!a.canSubmit,loading:a.submitting},on:{click:a.createOrder}},[t("i",{staticClass:"icon-cart"}),a._v("\n      立即购买\n    ")])],1),a.salesman.remarks?t("div",{staticClass:"salesman-intro"},[a._m(4),t("div",{staticClass:"intro-content"},[t("p",[a._v(a._s(a.salesman.remarks))])])]):a._e()])},i=[function(){var a=this,t=a._self._c;return t("div",{staticClass:"trust-badge"},[t("i",{staticClass:"icon-verified"}),t("span",[a._v("认证业务员")])])},function(){var a=this,t=a._self._c;return t("div",{staticClass:"section-header"},[t("div",{staticClass:"section-icon recharge-icon"}),t("h4",[a._v("充值套餐")]),t("div",{staticClass:"section-subtitle"},[a._v("选择适合的充值方案")])])},function(){var a=this,t=a._self._c;return t("div",{staticClass:"section-header"},[t("div",{staticClass:"section-icon activity-icon"}),t("h4",[a._v("活动套餐")]),t("div",{staticClass:"section-subtitle"},[a._v("创建营销活动方案")])])},function(){var a=this,t=a._self._c;return t("div",{staticClass:"section-header"},[t("div",{staticClass:"section-icon name-icon"}),t("h4",[a._v("活动信息")]),t("div",{staticClass:"section-subtitle"},[a._v("为您的活动起个好名字")])])},function(){var a=this,t=a._self._c;return t("div",{staticClass:"section-header"},[t("div",{staticClass:"section-icon intro-icon"}),t("h4",[a._v("业务员介绍")]),t("div",{staticClass:"section-subtitle"},[a._v("了解您的专属服务顾问")])])}],c=s("2909"),n={name:"SalesmanScan",data:function(){return{salesman:{},rechargePackages:[],activityPackages:[],selectedPackageId:null,selectedPackageType:null,activityName:"",submitting:!1}},computed:{canSubmit:function(){return 1==this.selectedPackageType?null!==this.selectedPackageId:2==this.selectedPackageType&&(null!==this.selectedPackageId&&""!==this.activityName.trim())}},mounted:function(){document.title="购买活动套餐",this.loadSalesmanInfo()},methods:{loadSalesmanInfo:function(){var a=this,t=this.$route.query.salesmanId;if(t){var s=this.$route.query.packageId||"",e=this.$route.query.packageType||"";this.$fly.get("/pyp/web/salesman/scan",{salesmanId:t}).then((function(t){200==t.code?(a.salesman=t.salesman,a.rechargePackages=t.rechargePackages||[],a.activityPackages=t.activityPackages||[],s&&e&&a.filterAndSelectPackage(s,e)):a.$toast(t.msg||"加载失败")})).catch((function(t){a.$toast("加载失败")}))}else this.$toast("参数错误")},filterAndSelectPackage:function(a,t){var s=parseInt(a),e=parseInt(t);if(1==e){Object(c["a"])(this.rechargePackages);this.rechargePackages=this.rechargePackages.filter((function(a){return a.id==s})),this.activityPackages=[],this.rechargePackages.length>0?this.selectPackage(s,1):this.$toast("未找到指定的充值套餐")}else if(2==e){Object(c["a"])(this.activityPackages);this.activityPackages=this.activityPackages.filter((function(a){return a.id==s})),this.rechargePackages=[],this.activityPackages.length>0?this.selectPackage(s,2):this.$toast("未找到指定的活动套餐")}},selectPackage:function(a,t){this.selectedPackageId=a,this.selectedPackageType=t,2!==t&&(this.activityName="")},handleImageError:function(a){a.target.style.display="none",a.target.nextElementSibling.style.display="flex"},createOrder:function(){var a=this;if(this.canSubmit){this.submitting=!0;var t={salesmanId:this.$route.query.salesmanId,packageId:this.selectedPackageId};2==this.selectedPackageType&&(t.activityName=this.activityName.trim()),this.$fly.get("/pyp/web/salesman/createOrder",t).then((function(t){200==t.code?2==t.orderType?a.$router.push({name:"rechargePayment",query:{orderId:t.orderId,from:"salesman"}}):3==t.orderType&&a.$router.push({name:"rechargePayment",query:{orderId:t.orderId,from:"salesman",type:"activity"}}):a.$toast(t.msg||"创建订单失败")})).catch((function(t){console.error("创建订单失败:",t),a.$toast("创建订单失败")})).finally((function(){a.submitting=!1}))}}}},r=n,l=(s("1f8f"),s("2877")),o=Object(l["a"])(r,e,i,!1,null,"136f3dc7",null);t["default"]=o.exports},"1f8f":function(a,t,s){"use strict";s("ed49")},2909:function(a,t,s){"use strict";function e(a,t){(null==t||t>a.length)&&(t=a.length);for(var s=0,e=new Array(t);s<t;s++)e[s]=a[s];return e}function i(a){if(Array.isArray(a))return e(a)}function c(a){if("undefined"!==typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}function n(a,t){if(a){if("string"===typeof a)return e(a,t);var s=Object.prototype.toString.call(a).slice(8,-1);return"Object"===s&&a.constructor&&(s=a.constructor.name),"Map"===s||"Set"===s?Array.from(a):"Arguments"===s||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s)?e(a,t):void 0}}function r(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(a){return i(a)||c(a)||n(a)||r()}s.d(t,"a",(function(){return l}))},ed49:function(a,t,s){}}]);
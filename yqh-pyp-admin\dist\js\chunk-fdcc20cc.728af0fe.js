(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-fdcc20cc","chunk-2d0dd7d3"],{"5fc3":function(e,t,a){"use strict";a.r(t);a("b0c0");var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"mod-config"},[t("el-form",{attrs:{inline:!0,model:e.dataForm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.getDataList()}}},[t("el-form-item",[t("el-input",{attrs:{placeholder:"参数名",clearable:""},model:{value:e.dataForm.name,callback:function(t){e.$set(e.dataForm,"name",t)},expression:"dataForm.name"}})],1),t("el-form-item",[t("el-button",{on:{click:function(t){return e.getDataList()}}},[e._v("查询")]),e.isAuth("invoice:invoice:save")?t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addOrUpdateHandle()}}},[e._v("新增")]):e._e(),e.isAuth("invoice:invoice:delete")?t("el-button",{attrs:{type:"danger",disabled:e.dataListSelections.length<=0},on:{click:function(t){return e.deleteHandle()}}},[e._v("批量删除")]):e._e()],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:e.dataList,border:""},on:{"selection-change":e.selectionChangeHandle}},[t("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),t("el-table-column",{attrs:{prop:"invoiceName","header-align":"center",align:"center",label:"发票抬头"}}),t("el-table-column",{attrs:{prop:"invoiceCode","header-align":"center",align:"center",label:"纳税人识别码"}}),t("el-table-column",{attrs:{prop:"invoiceBank","header-align":"center",align:"center",label:"银行"}}),t("el-table-column",{attrs:{prop:"invoiceAccount","header-align":"center",align:"center",label:"开户行"}}),t("el-table-column",{attrs:{prop:"invoiceMobile","header-align":"center",align:"center",label:"联系方式"}}),t("el-table-column",{attrs:{prop:"invoiceAddress","header-align":"center",align:"center",label:"注册地址"}}),t("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.addOrUpdateHandle(a.row.id)}}},[e._v("修改")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHandle(a.row.id)}}},[e._v("删除")])]}}])})],1),t("el-pagination",{attrs:{"current-page":e.pageIndex,"page-sizes":[10,20,50,100],"page-size":e.pageSize,total:e.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle}}),e.addOrUpdateVisible?t("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:e.getDataList}}):e._e()],1)},n=[],o=(a("99af"),a("a15b"),a("d81d"),a("a573"),a("8236")),r={data:function(){return{dataForm:{name:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1}},components:{AddOrUpdate:o["default"]},activated:function(){this.getDataList()},methods:{getDataList:function(){var e=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/invoice/invoice/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,name:this.dataForm.name})}).then((function(t){var a=t.data;a&&200===a.code?(e.dataList=a.page.list,e.totalPage=a.page.totalCount):(e.dataList=[],e.totalPage=0),e.dataListLoading=!1}))},sizeChangeHandle:function(e){this.pageSize=e,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(e){this.pageIndex=e,this.getDataList()},selectionChangeHandle:function(e){this.dataListSelections=e},addOrUpdateHandle:function(e){var t=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){t.$refs.addOrUpdate.init(e)}))},deleteHandle:function(e){var t=this,a=e?[e]:this.dataListSelections.map((function(e){return e.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(e?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:t.$http.adornUrl("/invoice/invoice/delete"),method:"post",data:t.$http.adornData(a,!1)}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.getDataList()}}):t.$message.error(a.msg)}))}))}}},c=r,d=a("2877"),l=Object(d["a"])(c,i,n,!1,null,null,null);t["default"]=l.exports},8236:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"80px"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.dataFormSubmit()}}},[t("el-form-item",{attrs:{label:"发票抬头",prop:"invoiceName"}},[t("el-input",{attrs:{placeholder:"发票抬头"},model:{value:e.dataForm.invoiceName,callback:function(t){e.$set(e.dataForm,"invoiceName",t)},expression:"dataForm.invoiceName"}})],1),t("el-form-item",{attrs:{label:"纳税人识别码",prop:"invoiceCode"}},[t("el-input",{attrs:{placeholder:"纳税人识别码"},model:{value:e.dataForm.invoiceCode,callback:function(t){e.$set(e.dataForm,"invoiceCode",t)},expression:"dataForm.invoiceCode"}})],1),t("el-form-item",{attrs:{label:"银行",prop:"invoiceBank"}},[t("el-input",{attrs:{placeholder:"银行"},model:{value:e.dataForm.invoiceBank,callback:function(t){e.$set(e.dataForm,"invoiceBank",t)},expression:"dataForm.invoiceBank"}})],1),t("el-form-item",{attrs:{label:"开户行",prop:"invoiceAccount"}},[t("el-input",{attrs:{placeholder:"开户行"},model:{value:e.dataForm.invoiceAccount,callback:function(t){e.$set(e.dataForm,"invoiceAccount",t)},expression:"dataForm.invoiceAccount"}})],1),t("el-form-item",{attrs:{label:"联系方式",prop:"invoiceMobile"}},[t("el-input",{attrs:{placeholder:"联系方式"},model:{value:e.dataForm.invoiceMobile,callback:function(t){e.$set(e.dataForm,"invoiceMobile",t)},expression:"dataForm.invoiceMobile"}})],1),t("el-form-item",{attrs:{label:"注册地址",prop:"invoiceAddress"}},[t("el-input",{attrs:{placeholder:"注册地址"},model:{value:e.dataForm.invoiceAddress,callback:function(t){e.$set(e.dataForm,"invoiceAddress",t)},expression:"dataForm.invoiceAddress"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},n=[],o={data:function(){return{visible:!1,dataForm:{repeatToken:"",id:0,invoiceName:"",invoiceCode:"",invoiceBank:"",invoiceAccount:"",invoiceMobile:"",invoiceAddress:""},dataRule:{invoiceName:[{required:!0,message:"发票抬头不能为空",trigger:"blur"}],invoiceCode:[{required:!0,message:"纳税人识别码不能为空",trigger:"blur"}],invoiceBank:[{required:!0,message:"银行不能为空",trigger:"blur"}],invoiceAccount:[{required:!0,message:"开户行不能为空",trigger:"blur"}],invoiceMobile:[{required:!0,message:"注册地址不能为空",trigger:"blur"}],invoiceAddress:[{required:!0,message:"联系方式不能为空",trigger:"blur"}]}}},methods:{init:function(e){var t=this;this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){t.$refs["dataForm"].resetFields(),t.dataForm.id&&t.$http({url:t.$http.adornUrl("/invoice/invoice/info/".concat(t.dataForm.id)),method:"get",params:t.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.invoiceName=a.invoice.invoiceName,t.dataForm.invoiceCode=a.invoice.invoiceCode,t.dataForm.invoiceBank=a.invoice.invoiceBank,t.dataForm.invoiceAccount=a.invoice.invoiceAccount,t.dataForm.invoiceMobile=a.invoice.invoiceMobile,t.dataForm.invoiceAddress=a.invoice.invoiceAddress)}))}))},getToken:function(){var e=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm.repeatToken=a.result)}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$http({url:e.$http.adornUrl("/invoice/invoice/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({repeatToken:e.dataForm.repeatToken,id:e.dataForm.id||void 0,invoiceName:e.dataForm.invoiceName,invoiceCode:e.dataForm.invoiceCode,invoiceBank:e.dataForm.invoiceBank,invoiceAccount:e.dataForm.invoiceAccount,invoiceMobile:e.dataForm.invoiceMobile,invoiceAddress:e.dataForm.invoiceAddress})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(a.msg)}))}))}}},r=o,c=a("2877"),d=Object(c["a"])(r,i,n,!1,null,null,null);t["default"]=d.exports},a15b:function(e,t,a){"use strict";var i=a("23e7"),n=a("e330"),o=a("44ad"),r=a("fc6a"),c=a("a640"),d=n([].join),l=o!==Object,s=l||!c("join",",");i({target:"Array",proto:!0,forced:s},{join:function(e){return d(r(this),void 0===e?",":e)}})},a573:function(e,t,a){"use strict";a("ab43")},ab43:function(e,t,a){"use strict";var i=a("23e7"),n=a("d024"),o=a("c430");i({target:"Iterator",proto:!0,real:!0,forced:o},{map:n})},d024:function(e,t,a){"use strict";var i=a("c65b"),n=a("59ed"),o=a("825a"),r=a("46c4"),c=a("c5cc"),d=a("9bdd"),l=c((function(){var e=this.iterator,t=o(i(this.next,e)),a=this.done=!!t.done;if(!a)return d(e,this.mapper,[t.value,this.counter++],!0)}));e.exports=function(e){return o(this),n(e),new l(r(this),{mapper:e})}},d81d:function(e,t,a){"use strict";var i=a("23e7"),n=a("b727").map,o=a("1dde"),r=o("map");i({target:"Array",proto:!0,forced:!r},{map:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})}}]);
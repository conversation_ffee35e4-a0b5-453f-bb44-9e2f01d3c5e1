(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a808e0b6","chunk-5f0521d1","chunk-b68ec612","chunk-23ec7528","chunk-2d22c4f3"],{"40a7":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"日程讲者嘉宾修改","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"}},[t.placeActivityTopicGuest.length>0?e("el-table",{staticStyle:{margin:"20px 0"},attrs:{data:t.placeActivityTopicGuest,border:""}},[e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"嘉宾名称"}}),e("el-table-column",{attrs:{prop:"orderBy","show-overflow-tooltip":"true","header-align":"center",align:"center",label:"排序"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-input",{attrs:{placeholder:"排序"},model:{value:a.row.orderBy,callback:function(e){t.$set(a.row,"orderBy",e)},expression:"scope.row.orderBy"}})],1)}}],null,!1,525224533)}),e("el-table-column",{attrs:{prop:"confirmStatus","show-overflow-tooltip":"true","header-align":"center",align:"center",label:"确认状态"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-select",{attrs:{placeholder:"确认状态",filterable:""},model:{value:a.row.confirmStatus,callback:function(e){t.$set(a.row,"confirmStatus",e)},expression:"scope.row.confirmStatus"}},t._l(t.confirmStatus,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1)}}],null,!1,1300827402)}),e("el-table-column",{attrs:{prop:"confirmTime","header-align":"center",align:"center",label:"确认时间"}}),e("el-table-column",{attrs:{prop:"confirmReason","header-align":"center",align:"center",label:"拒绝理由"}})],1):t._e()],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{disabled:0==t.placeActivityTopicGuest.length,type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],l=a("ed56"),s={data:function(){return{confirmStatus:l["a"],visible:!1,placeActivityTopicGuest:[],guestList:[],dataForm:{id:0,activityId:"",name:"",placeId:"",topicGuestIds:[],orderBy:0},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],name:[{required:!0,message:"会场名称不能为空",trigger:"blur"}],placeId:[{required:!0,message:"场地不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序，数值越小越靠前不能为空",trigger:"blur"}]}}},methods:{init:function(t,e){var a=this;this.dataForm.activityId=t,this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/place/placeactivitytopicscheduleguest/findBySchedulesId/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.placeActivityTopicGuest=e.result)}))}))},getGuest:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activityguest/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.guestList=a.result)}))},dataFormSubmit:function(){var t=this;this.$http({url:this.$http.adornUrl("/place/placeactivitytopicscheduleguest/updateBatch"),method:"post",data:this.placeActivityTopicGuest}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}}},c=s,o=a("2877"),n=Object(o["a"])(c,i,r,!1,null,null,null);e["default"]=n.exports},4533:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"日程主持嘉宾修改","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"}},[t.placeActivityTopicGuest.length>0?e("el-table",{staticStyle:{margin:"20px 0"},attrs:{data:t.placeActivityTopicGuest,border:""}},[e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"嘉宾名称"}}),e("el-table-column",{attrs:{prop:"orderBy","show-overflow-tooltip":"true","header-align":"center",align:"center",label:"排序"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-input",{attrs:{placeholder:"排序"},model:{value:a.row.orderBy,callback:function(e){t.$set(a.row,"orderBy",e)},expression:"scope.row.orderBy"}})],1)}}],null,!1,525224533)}),e("el-table-column",{attrs:{prop:"confirmStatus","show-overflow-tooltip":"true","header-align":"center",align:"center",label:"确认状态"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-select",{attrs:{placeholder:"确认状态",filterable:""},model:{value:a.row.confirmStatus,callback:function(e){t.$set(a.row,"confirmStatus",e)},expression:"scope.row.confirmStatus"}},t._l(t.confirmStatus,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1)}}],null,!1,1300827402)}),e("el-table-column",{attrs:{prop:"confirmTime","header-align":"center",align:"center",label:"确认时间"}}),e("el-table-column",{attrs:{prop:"confirmReason","header-align":"center",align:"center",label:"拒绝理由"}})],1):t._e()],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{disabled:0==t.placeActivityTopicGuest.length,type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],l=a("ed56"),s={data:function(){return{confirmStatus:l["a"],visible:!1,placeActivityTopicGuest:[],guestList:[],dataForm:{id:0,activityId:"",name:"",placeId:"",topicGuestIds:[],orderBy:0},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],name:[{required:!0,message:"会场名称不能为空",trigger:"blur"}],placeId:[{required:!0,message:"场地不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序，数值越小越靠前不能为空",trigger:"blur"}]}}},methods:{init:function(t,e){var a=this;this.dataForm.activityId=t,this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/place/placeactivitytopicschedulespeaker/findBySchedulesId/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.placeActivityTopicGuest=e.result)}))}))},getGuest:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activityguest/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.guestList=a.result)}))},dataFormSubmit:function(){var t=this;this.$http({url:this.$http.adornUrl("/place/placeactivitytopicschedulespeaker/updateBatch"),method:"post",data:this.placeActivityTopicGuest}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}}},c=s,o=a("2877"),n=Object(o["a"])(c,i,r,!1,null,null,null);e["default"]=n.exports},a15b:function(t,e,a){"use strict";var i=a("23e7"),r=a("e330"),l=a("44ad"),s=a("fc6a"),c=a("a640"),o=r([].join),n=l!==Object,d=n||!c("join",",");i({target:"Array",proto:!0,forced:d},{join:function(t){return o(s(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var i=a("23e7"),r=a("d024"),l=a("c430");i({target:"Iterator",proto:!0,real:!0,forced:l},{map:r})},d024:function(t,e,a){"use strict";var i=a("c65b"),r=a("59ed"),l=a("825a"),s=a("46c4"),c=a("c5cc"),o=a("9bdd"),n=c((function(){var t=this.iterator,e=l(i(this.next,t)),a=this.done=!!e.done;if(!a)return o(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return l(this),r(t),new n(s(this),{mapper:t})}},d81d:function(t,e,a){"use strict";var i=a("23e7"),r=a("b727").map,l=a("1dde"),s=l("map");i({target:"Array",proto:!0,forced:!s},{map:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})},e739:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"日程讨论嘉宾修改","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"}},[t.placeActivityTopicGuest.length>0?e("el-table",{staticStyle:{margin:"20px 0"},attrs:{data:t.placeActivityTopicGuest,border:""}},[e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"嘉宾名称"}}),e("el-table-column",{attrs:{prop:"orderBy","show-overflow-tooltip":"true","header-align":"center",align:"center",label:"排序"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-input",{attrs:{placeholder:"排序"},model:{value:a.row.orderBy,callback:function(e){t.$set(a.row,"orderBy",e)},expression:"scope.row.orderBy"}})],1)}}],null,!1,525224533)}),e("el-table-column",{attrs:{prop:"confirmStatus","show-overflow-tooltip":"true","header-align":"center",align:"center",label:"确认状态"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-select",{attrs:{placeholder:"确认状态",filterable:""},model:{value:a.row.confirmStatus,callback:function(e){t.$set(a.row,"confirmStatus",e)},expression:"scope.row.confirmStatus"}},t._l(t.confirmStatus,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1)}}],null,!1,1300827402)}),e("el-table-column",{attrs:{prop:"confirmTime","header-align":"center",align:"center",label:"确认时间"}}),e("el-table-column",{attrs:{prop:"confirmReason","header-align":"center",align:"center",label:"拒绝理由"}})],1):t._e()],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{disabled:0==t.placeActivityTopicGuest.length,type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],l=a("ed56"),s={data:function(){return{confirmStatus:l["a"],visible:!1,placeActivityTopicGuest:[],guestList:[],dataForm:{id:0,activityId:"",name:"",placeId:"",topicGuestIds:[],orderBy:0},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],name:[{required:!0,message:"会场名称不能为空",trigger:"blur"}],placeId:[{required:!0,message:"场地不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序，数值越小越靠前不能为空",trigger:"blur"}]}}},methods:{init:function(t,e){var a=this;this.dataForm.activityId=t,this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/place/placeactivitytopicschedulediscuss/findBySchedulesId/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.placeActivityTopicGuest=e.result)}))}))},getGuest:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activityguest/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.guestList=a.result)}))},dataFormSubmit:function(){var t=this;this.$http({url:this.$http.adornUrl("/place/placeactivitytopicschedulediscuss/updateBatch"),method:"post",data:this.placeActivityTopicGuest}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}}},c=s,o=a("2877"),n=Object(o["a"])(c,i,r,!1,null,null,null);e["default"]=n.exports},ebff:function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-select",{attrs:{placeholder:"全部场地",filterable:""},model:{value:t.dataForm.placeId,callback:function(e){t.$set(t.dataForm,"placeId",e)},expression:"dataForm.placeId"}},[e("el-option",{attrs:{label:"全部场地",value:""}}),t._l(t.placeList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}))],2)],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"全部主题",filterable:""},model:{value:t.dataForm.topicId,callback:function(e){t.$set(t.dataForm,"topicId",e)},expression:"dataForm.topicId"}},[e("el-option",{attrs:{label:"全部主题",value:""}}),t._l(t.placeTopicList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}))],2)],1),e("el-form-item",[e("el-input",{attrs:{placeholder:"日程名称",clearable:""},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),t.isAuth("place:placeactivitytopicschedule:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.downloadDemo()}}},[t._v("导入模板")]),e("el-button",{attrs:{type:"primary"}},[e("Upload",{attrs:{url:"/place/placeactivitytopicschedule/importExcel?appid="+t.appid+"&activityId="+t.dataForm.activityId,name:"日程导入"},on:{uploaded:t.getDataList}})],1),t.isAuth("place:placeactivitytopicschedule:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"placeName","header-align":"center",align:"center",label:"场地名称"}}),e("el-table-column",{attrs:{prop:"placeTopicName","header-align":"center",align:"center",label:"主题名称"}}),e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"日程名称"}}),e("el-table-column",{attrs:{prop:"startTime","header-align":"center",align:"center",label:"开始时间"}}),e("el-table-column",{attrs:{prop:"endTime","header-align":"center",align:"center",label:"结束时间"}}),e("el-table-column",{attrs:{prop:"activityGuests","header-align":"center",align:"center",label:"主持"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{on:{click:function(e){return t.showSpeaker(a.row.id)}}},t._l(a.row.activitySpeakers,(function(a,i){return e("el-tag",{key:i,class:"tag-color tag-color-"+a.confirmStatus,attrs:{type:"primary"}},[t._v(t._s(a.name))])})),1)}}])}),e("el-table-column",{attrs:{prop:"activityGuests","header-align":"center",align:"center",label:"讲者"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{on:{click:function(e){return t.showGuest(a.row.id)}}},t._l(a.row.activityGuests,(function(a,i){return e("el-tag",{key:i,class:"tag-color tag-color-"+a.confirmStatus,attrs:{type:"primary"}},[t._v(t._s(a.name))])})),1)}}])}),e("el-table-column",{attrs:{prop:"activityGuests","header-align":"center",align:"center",label:"讨论"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{on:{click:function(e){return t.showDiscuss(a.row.id)}}},t._l(a.row.activityDiscuss,(function(a,i){return e("el-tag",{key:i,class:"tag-color tag-color-"+a.confirmStatus,attrs:{type:"primary"}},[t._v(t._s(a.name))])})),1)}}])}),e("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center","show-overflow-tooltip":"",label:"创建时间"}}),e("el-table-column",{attrs:{prop:"updateOn","header-align":"center","show-overflow-tooltip":"",align:"center",label:"更新时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e(),t.placeactivitytopicschedulediscussVisible?e("placeactivitytopicschedulediscuss",{ref:"placeactivitytopicschedulediscuss",on:{refreshDataList:t.getDataList}}):t._e(),t.placeactivitytopicscheduleguestVisible?e("placeactivitytopicscheduleguest",{ref:"placeactivitytopicscheduleguest",on:{refreshDataList:t.getDataList}}):t._e(),t.placeactivitytopicschedulspeakerVisible?e("placeactivitytopicschedulspeaker",{ref:"placeactivitytopicschedulspeaker",on:{refreshDataList:t.getDataList}}):t._e()],1)},r=[],l=(a("99af"),a("a15b"),a("d81d"),a("d3b7"),a("3ca3"),a("a573"),a("ddb0"),a("f34d")),s=a("e739"),c=a("40a7"),o=a("4533"),n={data:function(){return{dataForm:{name:"",activityId:void 0,placeId:void 0,topicId:void 0},dataList:[],pageIndex:1,pageSize:10,totalPage:0,placeList:[],placeTopicList:[],dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,placeactivitytopicschedulediscussVisible:!1,placeactivitytopicscheduleguestVisible:!1,placeactivitytopicschedulspeakerVisible:!1}},components:{AddOrUpdate:l["default"],placeactivitytopicschedulediscuss:s["default"],placeactivitytopicschedulspeaker:o["default"],Upload:function(){return a.e("chunk-043b0b7f").then(a.bind(null,"9dac"))},placeactivitytopicscheduleguest:c["default"]},activated:function(){this.dataForm.activityId=this.$route.query.activityId,this.dataForm.placeId=this.$route.query.placeId||void 0,this.dataForm.topicId=this.$route.query.topicId||void 0,this.getDataList(),this.getPlace(),this.getPlaceTopic()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/place/placeactivitytopicschedule/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,activityId:this.dataForm.activityId,placeId:this.dataForm.placeId,topicId:this.dataForm.topicId,name:this.dataForm.name})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(e.dataForm.activityId,e.dataForm.topicId,t)}))},showDiscuss:function(t){var e=this;this.placeactivitytopicschedulediscussVisible=!0,this.$nextTick((function(){e.$refs.placeactivitytopicschedulediscuss.init(e.dataForm.activityId,t)}))},showGuest:function(t){var e=this;this.placeactivitytopicscheduleguestVisible=!0,this.$nextTick((function(){e.$refs.placeactivitytopicscheduleguest.init(e.dataForm.activityId,t)}))},showSpeaker:function(t){var e=this;this.placeactivitytopicschedulspeakerVisible=!0,this.$nextTick((function(){e.$refs.placeactivitytopicschedulspeaker.init(e.dataForm.activityId,t)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/place/placeactivitytopicschedule/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},getPlace:function(){var t=this;this.$http({url:this.$http.adornUrl("/place/placeactivity/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.placeList=a.result)}))},getPlaceTopic:function(){var t=this;this.$http({url:this.$http.adornUrl("/place/placeactivitytopic/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.placeTopicList=a.result)}))},downloadDemo:function(){var t=this.$http.adornUrl("/place/placeactivitytopicschedule/downloadDemo?"+["token="+this.$cookie.get("token")].join("&"));window.open(t)}}},d=n,u=a("2877"),p=Object(u["a"])(d,i,r,!1,null,null,null);e["default"]=p.exports},ed56:function(t,e,a){"use strict";a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return l}));var i=[{key:0,value:"预告"},{key:1,value:"直播"},{key:2,value:"录播"}],r=[{key:0,value:"未确认"},{key:1,value:"确认通过"},{key:2,value:"确认不通过"}],l=[{key:0,value:"自定义内容"},{key:1,value:"自定义链接"},{key:2,value:"会议日程"},{key:3,value:"会议嘉宾"},{key:4,value:"聊天室"},{key:5,value:"考试&问卷"},{key:6,value:"展商列表"},{key:7,value:"录播视频列表"}]},f34d:function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"日程名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"日程名称"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",{attrs:{label:"会议日期",prop:"times"}},[e("el-date-picker",{staticStyle:{windth:"100%"},attrs:{"picker-options":t.pickerOptions,"default-value":t.activityInfo.startTime,type:"datetimerange","value-format":"yyyy/MM/dd HH:mm:ss","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.dateChange},model:{value:t.dataForm.times,callback:function(e){t.$set(t.dataForm,"times",e)},expression:"dataForm.times"}})],1),e("el-form-item",{attrs:{label:"会议主题",prop:"placeActivityTopicId"}},[e("el-select",{attrs:{placeholder:"会议主题",filterable:""},model:{value:t.dataForm.placeActivityTopicId,callback:function(e){t.$set(t.dataForm,"placeActivityTopicId",e)},expression:"dataForm.placeActivityTopicId"}},t._l(t.placeTopicList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"讲者",prop:"scheduleGuestIds"}},[e("el-select",{attrs:{multiple:"",placeholder:"讲者",filterable:""},model:{value:t.dataForm.scheduleGuestIds,callback:function(e){t.$set(t.dataForm,"scheduleGuestIds",e)},expression:"dataForm.scheduleGuestIds"}},t._l(t.guestList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"主持",prop:"scheduleSpeakerIds"}},[e("el-select",{attrs:{multiple:"",placeholder:"主持",filterable:""},model:{value:t.dataForm.scheduleSpeakerIds,callback:function(e){t.$set(t.dataForm,"scheduleSpeakerIds",e)},expression:"dataForm.scheduleSpeakerIds"}},t._l(t.guestList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"讨论",prop:"scheduleDiscussIds"}},[e("el-select",{attrs:{multiple:"",placeholder:"讨论",filterable:""},model:{value:t.dataForm.scheduleDiscussIds,callback:function(e){t.$set(t.dataForm,"scheduleDiscussIds",e)},expression:"dataForm.scheduleDiscussIds"}},t._l(t.guestList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"讲者别名",prop:"aliasGuestName"}},[e("el-input",{attrs:{placeholder:"讲者别名"},model:{value:t.dataForm.aliasGuestName,callback:function(e){t.$set(t.dataForm,"aliasGuestName",e)},expression:"dataForm.aliasGuestName"}})],1),e("el-form-item",{attrs:{label:"主持别名",prop:"aliasSpeakerName"}},[e("el-input",{attrs:{placeholder:"主持别名"},model:{value:t.dataForm.aliasSpeakerName,callback:function(e){t.$set(t.dataForm,"aliasSpeakerName",e)},expression:"dataForm.aliasSpeakerName"}})],1),e("el-form-item",{attrs:{label:"讨论别名",prop:"aliasDiscussName"}},[e("el-input",{attrs:{placeholder:"讨论别名"},model:{value:t.dataForm.aliasDiscussName,callback:function(e){t.$set(t.dataForm,"aliasDiscussName",e)},expression:"dataForm.aliasDiscussName"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],l={data:function(){var t=this;return{visible:!1,placeTopicList:[],guestList:[],activityInfo:{},dataForm:{id:0,activityId:"",orderBy:0,placeActivityTopicId:"",name:"",startTime:"",endTime:"",aliasGuestName:"",aliasSpeakerName:"",aliasDiscussName:"",scheduleGuestIds:[],scheduleSpeakerIds:[],scheduleDiscussIds:[],times:[],videoUrl:""},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],times:[{required:!0,message:"会议时间不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序，数值越小越靠前不能为空",trigger:"blur"}],placeActivityTopicId:[{required:!0,message:"主题ID不能为空",trigger:"blur"}],name:[{required:!0,message:"日程名称不能为空",trigger:"blur"}],startTime:[{required:!0,message:"开始时间不能为空",trigger:"blur"}],endTime:[{required:!0,message:"结束时间不能为空",trigger:"blur"}]},pickerOptions:{disabledDate:function(e){return t.dealDisabledDate(e)}}}},methods:{init:function(t,e,a){var i=this;this.dataForm.activityId=t,this.dataForm.placeActivityTopicId=e||void 0,this.dataForm.id=a||0,this.visible=!0,this.$nextTick((function(){i.$refs["dataForm"].resetFields(),i.dataForm.id&&i.$http({url:i.$http.adornUrl("/place/placeactivitytopicschedule/info/".concat(i.dataForm.id)),method:"get",params:i.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(i.$set(i.dataForm,"times",[e.placeActivityTopicSchedule.startTime,e.placeActivityTopicSchedule.endTime]),i.dataForm.activityId=e.placeActivityTopicSchedule.activityId,i.dataForm.placeId=e.placeActivityTopicSchedule.placeId,i.dataForm.orderBy=e.placeActivityTopicSchedule.orderBy,i.dataForm.placeActivityTopicId=e.placeActivityTopicSchedule.placeActivityTopicId,i.dataForm.name=e.placeActivityTopicSchedule.name,i.dataForm.scheduleGuestIds=e.placeActivityTopicSchedule.scheduleGuestIds,i.dataForm.scheduleSpeakerIds=e.placeActivityTopicSchedule.scheduleSpeakerIds,i.dataForm.scheduleDiscussIds=e.placeActivityTopicSchedule.scheduleDiscussIds,i.dataForm.startTime=e.placeActivityTopicSchedule.startTime,i.dataForm.endTime=e.placeActivityTopicSchedule.endTime,i.dataForm.videoUrl=e.placeActivityTopicSchedule.videoUrl,i.dataForm.aliasGuestName=e.placeActivityTopicSchedule.aliasGuestName,i.dataForm.aliasSpeakerName=e.placeActivityTopicSchedule.aliasSpeakerName,i.dataForm.aliasDiscussName=e.placeActivityTopicSchedule.aliasDiscussName)}))})),this.getPlaceTopic(),this.getGuest(),this.getActivity()},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/place/placeactivitytopicschedule/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,activityId:t.dataForm.activityId,placeId:t.dataForm.placeId,orderBy:t.dataForm.orderBy,placeActivityTopicId:t.dataForm.placeActivityTopicId,name:t.dataForm.name,startTime:t.dataForm.startTime,endTime:t.dataForm.endTime,scheduleGuestIds:t.dataForm.scheduleGuestIds,scheduleSpeakerIds:t.dataForm.scheduleSpeakerIds,scheduleDiscussIds:t.dataForm.scheduleDiscussIds,aliasGuestName:t.dataForm.aliasGuestName,aliasSpeakerName:t.dataForm.aliasSpeakerName,aliasDiscussName:t.dataForm.aliasDiscussName,videoUrl:t.dataForm.videoUrl})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))},getPlace:function(){var t=this;this.$http({url:this.$http.adornUrl("/place/placeactivity/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.placeList=a.result)}))},getPlaceTopic:function(){var t=this;this.$http({url:this.$http.adornUrl("/place/placeactivitytopic/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.placeTopicList=a.result)}))},getGuest:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activityguest/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.guestList=a.result)}))},getActivity:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activity/info/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.activityInfo=a.activity)}))},dealDisabledDate:function(t){if(null!=this.activityInfo.endTime&&null!=this.activityInfo.startTime)return t.getTime()+864e5<new Date(this.activityInfo.startTime).getTime()||t.getTime()>=new Date(this.activityInfo.endTime).getTime()},dateChange:function(t){this.dataForm.startTime=t[0],this.dataForm.endTime=t[1],console.log(t)}}},s=l,c=a("2877"),o=Object(c["a"])(s,i,r,!1,null,null,null);e["default"]=o.exports}}]);
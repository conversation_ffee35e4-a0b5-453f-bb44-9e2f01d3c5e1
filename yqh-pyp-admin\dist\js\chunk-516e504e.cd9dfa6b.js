(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-516e504e","chunk-0603ab50","chunk-0506e191","chunk-0506e191"],{"0ccb":function(t,e,a){"use strict";var i=a("e330"),r=a("50c4"),n=a("577e"),o=a("1148"),s=a("1d80"),l=i(o),c=i("".slice),d=Math.ceil,u=function(t){return function(e,a,i){var o,u,m=n(s(e)),p=r(a),f=m.length,v=void 0===i?" ":n(i);return p<=f||""===v?m:(o=p-f,u=l(v,d(o/v.length)),u.length>o&&(u=c(u,0,o)),t?m+u:u+m)}};t.exports={start:u(!1),end:u(!0)}},1148:function(t,e,a){"use strict";var i=a("5926"),r=a("577e"),n=a("1d80"),o=RangeError;t.exports=function(t){var e=r(n(this)),a="",s=i(t);if(s<0||s===1/0)throw new o("Wrong number of repetitions");for(;s>0;(s>>>=1)&&(e+=e))1&s&&(a+=e);return a}},2532:function(t,e,a){"use strict";var i=a("23e7"),r=a("e330"),n=a("5a34"),o=a("1d80"),s=a("577e"),l=a("ab13"),c=r("".indexOf);i({target:"String",proto:!0,forced:!l("includes")},{includes:function(t){return!!~c(s(o(this)),s(n(t)),arguments.length>1?arguments[1]:void 0)}})},"44e7":function(t,e,a){"use strict";var i=a("861d"),r=a("c6b6"),n=a("b622"),o=n("match");t.exports=function(t){var e;return i(t)&&(void 0!==(e=t[o])?!!e:"RegExp"===r(t))}},"48d8":function(t,e,a){"use strict";a("d4b0")},"498a":function(t,e,a){"use strict";var i=a("23e7"),r=a("58a8").trim,n=a("c8d2");i({target:"String",proto:!0,forced:n("trim")},{trim:function(){return r(this)}})},"4d90":function(t,e,a){"use strict";var i=a("23e7"),r=a("0ccb").start,n=a("9a0c");i({target:"String",proto:!0,forced:n},{padStart:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})},"5a34":function(t,e,a){"use strict";var i=a("44e7"),r=TypeError;t.exports=function(t){if(i(t))throw new r("The method doesn't accept regular expressions");return t}},"647d":function(t,e,a){},"7db0":function(t,e,a){"use strict";var i=a("23e7"),r=a("b727").find,n=a("44d2"),o="find",s=!0;o in[]&&Array(1)[o]((function(){s=!1})),i({target:"Array",proto:!0,forced:s},{find:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}}),n(o)},"9a0c":function(t,e,a){"use strict";var i=a("b5db");t.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(i)},a15b:function(t,e,a){"use strict";var i=a("23e7"),r=a("e330"),n=a("44ad"),o=a("fc6a"),s=a("a640"),l=r([].join),c=n!==Object,d=c||!s("join",",");i({target:"Array",proto:!0,forced:d},{join:function(t){return l(o(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab13:function(t,e,a){"use strict";var i=a("b622"),r=i("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(a){try{return e[r]=!1,"/./"[t](e)}catch(i){}}return!1}},ab43:function(t,e,a){"use strict";var i=a("23e7"),r=a("d024"),n=a("c430");i({target:"Iterator",proto:!0,real:!0,forced:n},{map:r})},b680:function(t,e,a){"use strict";var i=a("23e7"),r=a("e330"),n=a("5926"),o=a("408a"),s=a("1148"),l=a("d039"),c=RangeError,d=String,u=Math.floor,m=r(s),p=r("".slice),f=r(1..toFixed),v=function(t,e,a){return 0===e?a:e%2===1?v(t,e-1,a*t):v(t*t,e/2,a)},h=function(t){var e=0,a=t;while(a>=4096)e+=12,a/=4096;while(a>=2)e+=1,a/=2;return e},g=function(t,e,a){var i=-1,r=a;while(++i<6)r+=e*t[i],t[i]=r%1e7,r=u(r/1e7)},y=function(t,e){var a=6,i=0;while(--a>=0)i+=t[a],t[a]=u(i/e),i=i%e*1e7},b=function(t){var e=6,a="";while(--e>=0)if(""!==a||0===e||0!==t[e]){var i=d(t[e]);a=""===a?i:a+m("0",7-i.length)+i}return a},x=l((function(){return"0.000"!==f(8e-5,3)||"1"!==f(.9,0)||"1.25"!==f(1.255,2)||"1000000000000000128"!==f(0xde0b6b3a7640080,0)}))||!l((function(){f({})}));i({target:"Number",proto:!0,forced:x},{toFixed:function(t){var e,a,i,r,s=o(this),l=n(t),u=[0,0,0,0,0,0],f="",x="0";if(l<0||l>20)throw new c("Incorrect fraction digits");if(s!==s)return"NaN";if(s<=-1e21||s>=1e21)return d(s);if(s<0&&(f="-",s=-s),s>1e-21)if(e=h(s*v(2,69,1))-69,a=e<0?s*v(2,-e,1):s/v(2,e,1),a*=4503599627370496,e=52-e,e>0){g(u,0,a),i=l;while(i>=7)g(u,1e7,0),i-=7;g(u,v(10,i,1),0),i=e-1;while(i>=23)y(u,1<<23),i-=23;y(u,1<<i),g(u,1,1),y(u,2),x=b(u)}else g(u,0,a),g(u,1<<-e,0),x=b(u)+m("0",l);return l>0?(r=x.length,x=f+(r<=l?"0."+m("0",l-r)+x:p(x,0,r-l)+"."+p(x,r-l))):x=f+x,x}})},c7fd:function(t,e,a){"use strict";a("647d")},c8d2:function(t,e,a){"use strict";var i=a("5e77").PROPER,r=a("d039"),n=a("5899"),o="​᠎";t.exports=function(t){return r((function(){return!!n[t]()||o[t]()!==o||i&&n[t].name!==t}))}},caad:function(t,e,a){"use strict";var i=a("23e7"),r=a("4d64").includes,n=a("d039"),o=a("44d2"),s=n((function(){return!Array(1).includes()}));i({target:"Array",proto:!0,forced:s},{includes:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}}),o("includes")},d024:function(t,e,a){"use strict";var i=a("c65b"),r=a("59ed"),n=a("825a"),o=a("46c4"),s=a("c5cc"),l=a("9bdd"),c=s((function(){var t=this.iterator,e=n(i(this.next,t)),a=this.done=!!e.done;if(!a)return l(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return n(this),r(t),new c(o(this),{mapper:t})}},d4b0:function(t,e,a){},d81d:function(t,e,a){"use strict";var i=a("23e7"),r=a("b727").map,n=a("1dde"),o=n("map");i({target:"Array",proto:!0,forced:!o},{map:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})},f623:function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改活动视频":"新增活动视频","close-on-click-modal":!1,visible:t.visible,width:"800px"},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.dataFormSubmit()}}},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"视频名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"请输入视频名称"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1)],1)],1),e("el-form-item",{attrs:{label:"视频文件",prop:"mediaUrl"}},[e("div",{staticClass:"video-upload-section"},[e("el-button",{attrs:{type:"primary",disabled:t.uploading},on:{click:t.openVideoModal}},[e("i",{staticClass:"el-icon-video-camera"}),t._v(" "+t._s(t.selectedVideo?"重新选择视频":"选择视频")+" ")]),t.selectedVideo?e("div",{staticClass:"selected-video-preview"},[e("div",{staticClass:"video-item"},[e("video",{attrs:{src:t.selectedVideo.url,width:"200",height:"150",controls:"",preload:"metadata"}}),e("div",{staticClass:"video-info"},[e("p",{staticClass:"video-name"},[t._v(t._s(t.dataForm.name||"未命名视频"))]),e("p",{staticClass:"video-url"},[t._v(t._s(t.selectedVideo.url))]),t.dataForm.fileSize?e("p",{staticClass:"video-size"},[t._v("文件大小: "+t._s(t.formatFileSize(t.dataForm.fileSize)))]):t._e(),t.dataForm.duration?e("p",{staticClass:"video-duration"},[t._v("时长: "+t._s(t.formatDuration(t.dataForm.duration)))]):t._e()]),e("div",{staticClass:"video-actions"},[e("el-button",{attrs:{size:"mini"},on:{click:function(e){return t.previewVideo(t.selectedVideo.url)}}},[e("i",{staticClass:"el-icon-zoom-in"}),t._v(" 预览 ")]),e("el-button",{attrs:{size:"mini",type:"danger"},on:{click:t.removeVideo}},[e("i",{staticClass:"el-icon-delete"}),t._v(" 删除 ")])],1)])]):t._e(),t.selectedVideo||t.dataForm.mediaUrl?t._e():e("div",{staticClass:"upload-tip"},[e("i",{staticClass:"el-icon-video-camera-solid"}),e("p",[t._v("请选择视频文件")])])],1)]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"视频类型",prop:"type"}},[e("el-radio-group",{model:{value:t.dataForm.type,callback:function(e){t.$set(t.dataForm,"type",e)},expression:"dataForm.type"}},[e("el-radio",{attrs:{label:0}},[e("span",{staticClass:"radio-label"},[e("i",{staticClass:"el-icon-files"}),t._v(" 素材 ")])]),e("el-radio",{attrs:{label:1}},[e("span",{staticClass:"radio-label"},[e("i",{staticClass:"el-icon-video-camera"}),t._v(" 成品 ")])])],1),e("div",{staticClass:"type-description"},[0===t.dataForm.type?e("p",{staticClass:"type-desc material"},[t._v("素材：原始视频文件，可用于后期编辑制作")]):t._e(),1===t.dataForm.type?e("p",{staticClass:"type-desc product"},[t._v("成品：最终制作完成的视频，可直接使用")]):t._e()])],1)],1),t.dataForm.id?e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"关联文案",prop:"activityTextId"}},[e("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择关联文案",clearable:"",filterable:""},model:{value:t.dataForm.activityTextId,callback:function(e){t.$set(t.dataForm,"activityTextId",e)},expression:"dataForm.activityTextId"}},t._l(t.activityTexts,(function(a){return e("el-option",{key:a.id,attrs:{label:a.title||a.content,value:a.id}},[e("span",{staticStyle:{float:"left"}},[t._v(t._s(a.title||a.content))]),e("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v("ID: "+t._s(a.id))])])})),1)],1)],1):t._e(),t.dataForm.id?e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"使用次数"}},[e("el-input",{attrs:{readonly:""},model:{value:t.dataForm.useCount,callback:function(e){t.$set(t.dataForm,"useCount",e)},expression:"dataForm.useCount"}},[e("template",{slot:"append"},[t._v("次")])],2)],1)],1):t._e()],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary",loading:t.submitting},on:{click:function(e){return t.dataFormSubmit()}}},[t._v(" "+t._s(t.submitting?"保存中...":"确定")+" ")])],1),e("VideoUploadModal",{attrs:{visible:t.videoModalVisible,"default-video":t.selectedVideo},on:{"update:visible":function(e){t.videoModalVisible=e},confirm:t.handleVideoConfirm}}),e("el-dialog",{attrs:{visible:t.previewVisible,width:"70%","append-to-body":"","z-index":3100},on:{"update:visible":function(e){t.previewVisible=e}}},[e("video",{attrs:{width:"100%",src:t.previewVideoUrl,controls:"",autoplay:""}},[t._v(" 您的浏览器不支持视频播放 ")])])],1)},r=[],n=(a("99af"),a("b680"),a("d3b7"),a("25f0"),a("3ca3"),a("4d90"),a("ddb0"),{components:{VideoUploadModal:function(){return a.e("chunk-6481fdba").then(a.bind(null,"c923"))}},data:function(){return{visible:!1,submitting:!1,uploading:!1,videoModalVisible:!1,previewVisible:!1,previewVideoUrl:"",selectedVideo:null,activityTexts:[],dataForm:{repeatToken:"",id:0,fileId:"",name:"",fileSize:0,duration:0,mediaUrl:"",expireTime:"",frameRate:25,activityId:"",paixu:0,type:0,useCount:0,activityTextId:""},dataRule:{name:[{required:!0,message:"视频名称不能为空",trigger:"blur"},{min:2,max:50,message:"视频名称长度在 2 到 50 个字符",trigger:"blur"}],mediaUrl:[{required:!0,message:"请选择视频文件",trigger:"change"}],type:[{required:!0,message:"请选择视频类型",trigger:"change"}],paixu:[{type:"number",message:"排序必须为数字值",trigger:"blur"}],frameRate:[{type:"number",message:"帧率必须为数字值",trigger:"blur"}]}}},methods:{init:function(t,e){var a=this,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;this.getToken(),this.loadActivityTexts(e),this.dataForm.id=t||0,this.dataForm.activityId=e,this.visible=!0,this.selectedVideo=null,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id?a.loadActivityVideoInfo():(a.dataForm.type=i,a.dataForm.paixu=0,a.dataForm.frameRate=25,a.dataForm.useCount=0)}))},loadActivityVideoInfo:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activityvideo/info/".concat(this.dataForm.id)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;if(a&&200===a.code){var i=a.activityVideo;t.dataForm.fileId=i.fileId||"",t.dataForm.name=i.name,t.dataForm.fileSize=i.fileSize||0,t.dataForm.duration=i.duration||0,t.dataForm.mediaUrl=i.mediaUrl,t.dataForm.expireTime=i.expireTime||"",t.dataForm.frameRate=i.frameRate||25,t.dataForm.activityId=i.activityId,t.dataForm.paixu=i.paixu||0,t.dataForm.type=i.type||0,t.dataForm.useCount=i.useCount||0,t.dataForm.activityTextId=i.activityTextId||"",i.mediaUrl&&(t.selectedVideo={id:"existing_".concat(t.dataForm.id),url:i.mediaUrl,name:i.name,fileSize:i.fileSize,duration:i.duration,fileId:i.fileId,frameRate:i.frameRate,createDate:(new Date).toISOString()})}})).catch((function(e){t.$message.error("加载视频信息失败"),console.error(e)}))},loadActivityTexts:function(t){var e=this;this.$http({url:this.$http.adornUrl("/activity/activitytext/list"),method:"get",params:this.$http.adornParams({activityId:t,page:1,limit:1e3})}).then((function(t){var a,i=t.data;i&&200===i.code&&(e.activityTexts=(null===(a=i.page)||void 0===a?void 0:a.list)||[])})).catch((function(t){console.error("加载文案列表失败:",t),e.activityTexts=[]}))},getToken:function(){var t=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.repeatToken=a.result)})).catch((function(t){console.error("获取token失败:",t)}))},openVideoModal:function(){this.videoModalVisible=!0},handleVideoConfirm:function(t){t&&(this.selectedVideo=t,this.dataForm.mediaUrl=t.url,this.dataForm.fileId=t.fileId||"",this.dataForm.fileSize=t.fileSize||0,this.dataForm.duration=t.duration||0,this.dataForm.frameRate=t.frameRate||25,!this.dataForm.name&&t.name&&(this.dataForm.name=t.name.split(".")[0]))},removeVideo:function(){this.selectedVideo=null,this.dataForm.mediaUrl="",this.dataForm.fileId="",this.dataForm.fileSize=0,this.dataForm.duration=0},previewVideo:function(t){this.previewVideoUrl=t,this.previewVisible=!0},formatFileSize:function(t){if(!t||0===t)return"0.00 MB";var e=t/1048576;return e.toFixed(2)+" MB"},formatDuration:function(t){if(!t||0===t)return"0秒";var e=Math.floor(t/3600),a=Math.floor(t%3600/60),i=Math.floor(t%60);return e>0?"".concat(e,":").concat(a.toString().padStart(2,"0"),":").concat(i.toString().padStart(2,"0")):a>0?"".concat(a,":").concat(i.toString().padStart(2,"0")):"".concat(i,"秒")},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){if(e){if(!t.dataForm.mediaUrl)return void t.$message.error("请选择视频文件");t.submitting=!0;var a={repeatToken:t.dataForm.repeatToken,id:t.dataForm.id||void 0,fileId:t.dataForm.fileId||"",name:t.dataForm.name,fileSize:t.dataForm.fileSize||0,duration:t.dataForm.duration||0,mediaUrl:t.dataForm.mediaUrl,expireTime:t.dataForm.expireTime||"",frameRate:t.dataForm.frameRate||25,activityId:t.dataForm.activityId,paixu:t.dataForm.paixu||0,type:t.dataForm.type,useCount:t.dataForm.useCount||0,activityTextId:t.dataForm.activityTextId||""};t.$http({url:t.$http.adornUrl("/activity/activityvideo/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData(a)}).then((function(e){var a=e.data;t.submitting=!1,a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):(t.$message.error(a.msg||"操作失败"),"不能重复提交"!==a.msg&&t.getToken())})).catch((function(e){t.submitting=!1,t.$message.error("提交失败，请重试"),console.error("提交失败:",e)}))}}))}}}),o=n,s=(a("c7fd"),a("2877")),l=Object(s["a"])(o,i,r,!1,null,"9e151e06",null);e["default"]=l.exports},f665:function(t,e,a){"use strict";var i=a("23e7"),r=a("2266"),n=a("59ed"),o=a("825a"),s=a("46c4");i({target:"Iterator",proto:!0,real:!0},{find:function(t){o(this),n(t);var e=s(this),a=0;return r(e,(function(e,i){if(t(e,a++))return i(e)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},fb3c:function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.onSearch()}}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"视频名称",clearable:""},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"选择平台",clearable:""},model:{value:t.dataForm.platform,callback:function(e){t.$set(t.dataForm,"platform",e)},expression:"dataForm.platform"}},t._l(t.platformOptions,(function(t){return e("el-option",{key:t.code,attrs:{label:t.name,value:t.code}})})),1)],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"媒体类型",clearable:""},model:{value:t.dataForm.mediaType,callback:function(e){t.$set(t.dataForm,"mediaType",e)},expression:"dataForm.mediaType"}},[e("el-option",{attrs:{label:"视频",value:"video"}}),e("el-option",{attrs:{label:"图片",value:"image"}})],1)],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.onSearch()}}},[t._v("查询")]),t.isAuth("activity:activityvideo:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("activity:activityvideo:save")?e("el-button",{attrs:{type:"success"},on:{click:function(e){t.showGenerateDialog=!0}}},[t._v("智能生成")]):t._e(),t.isAuth("activity:activityvideo:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",width:"200",label:"视频名称"}}),e("el-table-column",{attrs:{prop:"platform","header-align":"center",align:"center",width:"100",label:"平台"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.getPlatformName(e.row.platform))+" ")]}}])}),e("el-table-column",{attrs:{prop:"mediaType","header-align":"center",align:"center",width:"100",label:"类型"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:"video"===a.row.mediaType?"primary":"success"}},[t._v(" "+t._s("video"===a.row.mediaType?"视频":"图片")+" ")])]}}])}),e("el-table-column",{attrs:{prop:"imageCount","header-align":"center",align:"center",width:"80",label:"数量"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s("image"===e.row.mediaType?(e.row.imageCount||1)+"张":"-")+" ")]}}])}),e("el-table-column",{attrs:{prop:"fileSize","header-align":"center",align:"center",label:"文件大小"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatFileSize(e.row.fileSize))+" ")]}}])}),e("el-table-column",{attrs:{prop:"duration","header-align":"center",align:"center",label:"时长"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatDuration(e.row.duration))+" ")]}}])}),e("el-table-column",{attrs:{prop:"mediaUrl","header-align":"center",align:"center",width:"120",label:"内容预览"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.mediaUrl?e("div",{staticClass:"media-preview"},[e("el-button",{staticClass:"preview-btn",attrs:{type:"text"},on:{click:function(e){return t.previewContent(a.row)}}},[e("i",{class:"video"===a.row.mediaType?"el-icon-video-play":"el-icon-picture"}),t._v(" 预览 ")])],1):e("span",{staticClass:"no-media"},[t._v(" "+t._s("video"===a.row.mediaType?"无视频":"无图片")+" ")])]}}])}),e("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",width:"180",label:"创建时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e(),e("el-dialog",{attrs:{title:"智能生成成品",visible:t.showGenerateDialog,width:"50%",center:""},on:{"update:visible":function(e){t.showGenerateDialog=e}}},[e("el-form",{attrs:{model:t.generateForm,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"选择平台",required:""}},[e("el-select",{attrs:{placeholder:"请选择平台"},on:{change:t.onPlatformChange},model:{value:t.generateForm.platform,callback:function(e){t.$set(t.generateForm,"platform",e)},expression:"generateForm.platform"}},t._l(t.platformOptions,(function(t){return e("el-option",{key:t.code,attrs:{label:t.name,value:t.code}})})),1)],1),e("el-form-item",{attrs:{label:"媒体类型",required:""}},[e("el-radio-group",{on:{change:t.onMediaTypeChange},model:{value:t.generateForm.mediaType,callback:function(e){t.$set(t.generateForm,"mediaType",e)},expression:"generateForm.mediaType"}},t._l(t.availableMediaTypes,(function(a){return e("el-radio",{key:a.code,attrs:{label:a.code}},[e("i",{class:"el-icon-"+a.icon}),t._v(" "+t._s(a.name)+" ")])})),1)],1),"image"===t.generateForm.mediaType?e("el-form-item",{attrs:{label:"图片数量"}},[e("el-input-number",{attrs:{min:1,max:9,step:1},model:{value:t.generateForm.imageCount,callback:function(e){t.$set(t.generateForm,"imageCount",e)},expression:"generateForm.imageCount"}}),e("span",{staticStyle:{"margin-left":"10px",color:"#999"}},[t._v("张")])],1):t._e()],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.showGenerateDialog=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary",loading:t.generating},on:{click:t.handleGenerate}},[t._v(" "+t._s(t.generating?"生成中...":"开始生成")+" ")])],1)],1),e("el-dialog",{attrs:{title:t.currentPreviewItem?"video"===t.currentPreviewItem.mediaType?"视频预览":"图片预览":"内容预览",visible:t.previewVisible,width:"80%",center:"","before-close":t.closePreview},on:{"update:visible":function(e){t.previewVisible=e}}},[t.currentPreviewItem?e("div",{staticClass:"preview-content"},[e("div",{staticClass:"preview-header"},[e("h3",[t._v(t._s(t.currentPreviewItem.name))]),e("div",{staticClass:"preview-tags"},[e("el-tag",{attrs:{type:"video"===t.currentPreviewItem.mediaType?"primary":"success",size:"small"}},[t._v(" "+t._s("video"===t.currentPreviewItem.mediaType?"视频":"图片")+" ")]),t.currentPreviewItem.platform?e("el-tag",{attrs:{type:"info",size:"small"}},[t._v(" "+t._s(t.getPlatformName(t.currentPreviewItem.platform))+" ")]):t._e(),"image"===t.currentPreviewItem.mediaType&&t.currentPreviewItem.imageCount?e("el-tag",{attrs:{type:"warning",size:"small"}},[t._v(" "+t._s(t.currentPreviewItem.imageCount)+"张 ")]):t._e()],1)]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:14}},["video"===t.currentPreviewItem.mediaType?e("div",{staticClass:"video-preview-container"},[t.currentPreviewItem.mediaUrl?e("video",{attrs:{src:t.currentPreviewItem.mediaUrl,controls:"",width:"100%",height:"400"}},[t._v(" 您的浏览器不支持视频播放 ")]):e("div",{staticClass:"no-media-placeholder"},[e("i",{staticClass:"el-icon-video-play",staticStyle:{"font-size":"48px",color:"#ccc"}}),e("p",[t._v("视频处理中...")])])]):e("div",{staticClass:"image-preview-container"},[t.previewImages.length>0?e("div",{staticClass:"image-carousel"},[e("el-carousel",{attrs:{height:"400px","indicator-position":"outside",autoplay:!1},on:{change:t.onImageChange}},t._l(t.previewImages,(function(t,a){return e("el-carousel-item",{key:a},[e("div",{staticClass:"image-item"},[e("img",{staticClass:"preview-image",attrs:{src:t.mediaUrl,alt:"图片 ".concat(a+1)}})])])})),1),t.previewImages.length>1?e("div",{staticClass:"image-counter"},[t._v(" "+t._s(t.currentImageIndex+1)+" / "+t._s(t.previewImages.length)+" ")]):t._e()],1):t.currentPreviewItem.mediaUrl?e("div",{staticClass:"single-image"},[e("img",{staticClass:"preview-image",attrs:{src:t.currentPreviewItem.mediaUrl,alt:"预览图片"}})]):e("div",{staticClass:"no-media-placeholder"},[e("i",{staticClass:"el-icon-picture",staticStyle:{"font-size":"48px",color:"#ccc"}}),e("p",[t._v("图片处理中...")])])])]),e("el-col",{attrs:{span:10}},[e("div",{staticClass:"text-info-panel"},[e("div",{staticClass:"panel-header"},[e("h4",[e("i",{staticClass:"el-icon-edit"}),t._v(" 关联文案")])]),t.loadingTextInfo?e("div",{directives:[{name:"loading",rawName:"v-loading",value:!0,expression:"true"}],staticClass:"loading-text",attrs:{"element-loading-text":"加载文案信息中..."}},[e("div",{staticStyle:{height:"100px"}})]):t.currentTextInfo?e("div",{staticClass:"text-content"},[t.currentTextInfo.name?e("div",{staticClass:"text-item"},[e("div",{staticClass:"text-label"},[e("span",[t._v("标题")]),e("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.copyText(t.currentTextInfo.name)}}},[t._v(" 复制 ")])],1),e("div",{staticClass:"text-value"},[t._v(t._s(t.currentTextInfo.name))])]):t._e(),t.currentTextInfo.promptKeyword?e("div",{staticClass:"text-item"},[e("div",{staticClass:"text-label"},[e("span",[t._v("提示词")]),e("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.copyText(t.currentTextInfo.promptKeyword)}}},[t._v(" 复制 ")])],1),e("div",{staticClass:"text-value"},[t._v(t._s(t.currentTextInfo.promptKeyword))])]):t._e(),t.currentTextInfo.content?e("div",{staticClass:"text-item"},[e("div",{staticClass:"text-label"},[e("span",[t._v("文案内容")]),e("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.copyText(t.currentTextInfo.content)}}},[t._v(" 复制 ")])],1),e("div",{staticClass:"text-value content-text"},[t._v(t._s(t.currentTextInfo.content))])]):t._e(),t.currentTextInfo.topics?e("div",{staticClass:"text-item"},[e("div",{staticClass:"text-label"},[e("span",[t._v("话题标签")]),e("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.copyText(t.currentTextInfo.topics)}}},[t._v(" 复制 ")])],1),e("div",{staticClass:"text-value topics-text"},[t._v(t._s(t.currentTextInfo.topics))])]):t._e(),e("div",{staticClass:"copy-all-section"},[e("el-button",{attrs:{type:"info",size:"small",icon:"el-icon-document-copy"},on:{click:t.copyAllText}},[t._v(" 一键复制全部文案 ")])],1)]):e("div",{staticClass:"no-text-info"},[e("i",{staticClass:"el-icon-warning",staticStyle:{"font-size":"24px",color:"#ccc"}}),e("p",[t._v("暂无关联文案")])])])])],1)],1):t._e()])],1)},r=[],n=(a("99af"),a("7db0"),a("caad"),a("a15b"),a("d81d"),a("b680"),a("d3b7"),a("25f0"),a("2532"),a("4d90"),a("498a"),a("0643"),a("fffc"),a("a573"),a("f623")),o={data:function(){return{dataForm:{name:"",platform:"",mediaType:"",appid:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,activityId:"",previewVisible:!1,previewVideoUrl:"",currentPreviewItem:null,previewImages:[],currentImageIndex:0,currentTextInfo:null,loadingTextInfo:!1,platformOptions:[],availableMediaTypes:[],showGenerateDialog:!1,generating:!1,generateForm:{platform:"douyin",mediaType:"video",imageCount:3}}},components:{AddOrUpdate:n["default"]},activated:function(){this.activityId=this.$route.query.activityId||"",this.loadPlatformConfigs(),this.getDataList()},methods:{loadPlatformConfigs:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activityvideo/platforms"),method:"get"}).then((function(e){var a=e.data;a&&200===a.code&&(t.platformOptions=a.platforms||[],t.onPlatformChange(t.generateForm.platform))}))},onPlatformChange:function(t){var e=this;t?this.$http({url:this.$http.adornUrl("/activity/activityvideo/platforms/".concat(t,"/mediaTypes")),method:"get"}).then((function(t){var a=t.data;if(a&&200===a.code&&(e.availableMediaTypes=a.mediaTypes||[],e.availableMediaTypes.length>0)){var i=e.availableMediaTypes.map((function(t){return t.code}));i.includes(e.generateForm.mediaType)||(e.generateForm.mediaType=e.availableMediaTypes[0].code)}})):this.availableMediaTypes=[]},onMediaTypeChange:function(t){var e=this;if("image"===t){var a=this.platformOptions.find((function(t){return t.code===e.generateForm.platform}));a&&a.defaultImageCount&&(this.generateForm.imageCount=a.defaultImageCount)}},getPlatformName:function(t){var e=this.platformOptions.find((function(e){return e.code===t}));return e?e.name:t},handleGenerate:function(){this.generateForm.platform?this.generateForm.mediaType?(this.generating=!0,"video"===this.generateForm.mediaType?this.generateVideo():this.generateImages()):this.$message.error("请选择媒体类型"):this.$message.error("请选择平台")},generateVideo:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activityvideo/submitVideoEdit"),method:"get",params:this.$http.adornParams({activityId:this.activityId,platform:this.generateForm.platform,mediaType:"video"})}).then((function(e){var a=e.data;t.generating=!1,a&&200===a.code?(t.$message.success("视频生成任务已提交"),t.showGenerateDialog=!1,t.getDataList()):t.$message.error(a.msg||"生成失败")})).catch((function(){t.generating=!1,t.$message.error("生成失败")}))},generateImages:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activityvideo/generateImages"),method:"post",data:this.$http.adornData({activityId:this.activityId,platform:this.generateForm.platform,mediaType:"image",imageCount:this.generateForm.imageCount})}).then((function(e){var a=e.data;t.generating=!1,a&&200===a.code?(t.$message.success("图片生成任务已提交"),t.showGenerateDialog=!1,t.getDataList()):t.$message.error(a.msg||"生成失败")})).catch((function(){t.generating=!1,t.$message.error("生成失败")}))},generateVideoHandle:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activityvideo/submitVideoEdit"),method:"get",params:this.$http.adornParams({activityId:this.activityId})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.getDataList()}}):t.$message.error(a.msg)}))},onSearch:function(){this.pageIndex=1,this.getDataList()},getDataList:function(){var t=this;this.dataListLoading=!0;var e=1;this.$http({url:this.$http.adornUrl("/activity/activityvideo/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,name:this.dataForm.name,activityId:this.activityId,type:e,platform:this.dataForm.platform,mediaType:this.dataForm.mediaType})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){var a=1;e.$refs.addOrUpdate.init(t,e.activityId,a)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/activity/activityvideo/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},previewContent:function(t){this.currentPreviewItem=t,this.currentTextInfo=null,this.loadingTextInfo=!1,this.previewImages=[],this.currentImageIndex=0,"video"===t.mediaType?this.previewVideoUrl=t.mediaUrl:this.loadPreviewImages(t.id),this.loadTextInfo(t.activityTextId),this.previewVisible=!0},previewVideo:function(t){this.previewVideoUrl=t,this.previewVisible=!0},closePreview:function(){this.previewVisible=!1,this.currentPreviewItem=null,this.currentTextInfo=null,this.previewImages=[],this.currentImageIndex=0},loadPreviewImages:function(t){var e=this;this.$http({url:this.$http.adornUrl("/activity/activityvideo/images"),method:"get",params:this.$http.adornParams({videoId:t})}).then((function(t){var a=t.data;a&&200===a.code?(e.previewImages=a.images||[],e.currentImageIndex=0,0===e.previewImages.length&&e.currentPreviewItem.mediaUrl&&(e.previewImages=[{mediaUrl:e.currentPreviewItem.mediaUrl}])):(e.$message.error("加载图片失败"),e.previewImages=[])})).catch((function(){e.currentPreviewItem.mediaUrl?e.previewImages=[{mediaUrl:e.currentPreviewItem.mediaUrl}]:e.previewImages=[]}))},onImageChange:function(t){this.currentImageIndex=t},loadTextInfo:function(t){var e=this;t?(this.loadingTextInfo=!0,this.$http({url:this.$http.adornUrl("/activity/text/info"),method:"get",params:this.$http.adornParams({textId:t})}).then((function(t){var a=t.data;e.loadingTextInfo=!1,a&&200===a.code?e.currentTextInfo=a.textInfo||null:(e.currentTextInfo=null,console.error("加载文案信息失败:",a.msg))})).catch((function(t){e.loadingTextInfo=!1,e.currentTextInfo=null,console.error("加载文案信息失败:",t)}))):this.currentTextInfo=null},copyText:function(t){var e=this;t?navigator.clipboard&&window.isSecureContext?navigator.clipboard.writeText(t).then((function(){e.$message.success("复制成功")})).catch((function(){e.fallbackCopyText(t)})):this.fallbackCopyText(t):this.$message.warning("没有可复制的内容")},fallbackCopyText:function(t){try{var e=document.createElement("textarea");e.value=t,e.style.position="fixed",e.style.left="-999999px",e.style.top="-999999px",document.body.appendChild(e),e.focus(),e.select();var a=document.execCommand("copy");document.body.removeChild(e),a?this.$message.success("复制成功"):this.$message.error("复制失败，请手动复制")}catch(i){this.$message.error("复制失败，请手动复制")}},copyAllText:function(){if(this.currentTextInfo){var t="";this.currentTextInfo.name&&(t+="标题：".concat(this.currentTextInfo.name,"\n\n")),this.currentTextInfo.promptKeyword&&(t+="提示词：".concat(this.currentTextInfo.promptKeyword,"\n\n")),this.currentTextInfo.content&&(t+="文案内容：".concat(this.currentTextInfo.content,"\n\n")),this.currentTextInfo.topics&&(t+="话题标签：".concat(this.currentTextInfo.topics,"\n\n")),t.trim()?this.copyText(t.trim()):this.$message.warning("没有可复制的文案内容")}else this.$message.warning("没有可复制的文案")},formatFileSize:function(t){if(!t)return"-";var e=["B","KB","MB","GB"],a=0;while(t>=1024&&a<e.length-1)t/=1024,a++;return"".concat(t.toFixed(2)," ").concat(e[a])},formatDuration:function(t){if(!t)return"-";var e=Math.floor(t/60),a=Math.floor(t%60);return"".concat(e,":").concat(a.toString().padStart(2,"0"))}}},s=o,l=(a("48d8"),a("2877")),c=Object(l["a"])(s,i,r,!1,null,null,null);e["default"]=c.exports},fffc:function(t,e,a){"use strict";a("f665")}}]);
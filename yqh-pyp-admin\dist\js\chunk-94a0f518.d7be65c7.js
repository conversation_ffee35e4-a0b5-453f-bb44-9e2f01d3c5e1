(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-94a0f518"],{"27e5":function(e,t,a){},"305b":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"类型",prop:"type"}},[t("el-radio-group",{model:{value:e.dataForm.type,callback:function(t){e.$set(e.dataForm,"type",t)},expression:"dataForm.type"}},e._l(e.dataForm.typeList,(function(a,r){return t("el-radio",{key:r,attrs:{label:r}},[e._v(e._s(a))])})),1)],1),t("el-form-item",{attrs:{label:e.dataForm.typeList[e.dataForm.type]+"名称",prop:"name"}},[t("el-input",{attrs:{placeholder:e.dataForm.typeList[e.dataForm.type]+"名称"},model:{value:e.dataForm.name,callback:function(t){e.$set(e.dataForm,"name",t)},expression:"dataForm.name"}})],1),t("el-form-item",{attrs:{label:"上级菜单",prop:"parentName"}},[t("el-popover",{ref:"menuListPopover",attrs:{placement:"bottom-start",trigger:"click"}},[t("el-tree",{ref:"menuListTree",staticStyle:{height:"400px","overflow-x":"scroll"},attrs:{data:e.menuList,props:e.menuListTreeProps,"node-key":"menuId","default-expand-all":!0,"highlight-current":!0,"expand-on-click-node":!1},on:{"current-change":e.menuListTreeCurrentChangeHandle}})],1),t("el-input",{directives:[{name:"popover",rawName:"v-popover:menuListPopover",arg:"menuListPopover"}],staticClass:"menu-list__input",attrs:{readonly:!0,placeholder:"点击选择上级菜单"},model:{value:e.dataForm.parentName,callback:function(t){e.$set(e.dataForm,"parentName",t)},expression:"dataForm.parentName"}})],1),1===e.dataForm.type?t("el-form-item",{attrs:{label:"菜单路由",prop:"url"}},[t("el-input",{attrs:{placeholder:"菜单路由"},model:{value:e.dataForm.url,callback:function(t){e.$set(e.dataForm,"url",t)},expression:"dataForm.url"}})],1):e._e(),0!==e.dataForm.type?t("el-form-item",{attrs:{label:"授权标识",prop:"perms"}},[t("el-input",{attrs:{placeholder:"多个用逗号分隔, 如: user:list,user:create"},model:{value:e.dataForm.perms,callback:function(t){e.$set(e.dataForm,"perms",t)},expression:"dataForm.perms"}})],1):e._e(),t("el-form-item",{attrs:{label:"是否显示",prop:"isShow"}},[t("el-radio-group",{model:{value:e.dataForm.isShow,callback:function(t){e.$set(e.dataForm,"isShow",t)},expression:"dataForm.isShow"}},e._l(e.isShowList,(function(a,r){return t("el-radio",{key:r,attrs:{label:r}},[e._v(e._s(a))])})),1)],1),2!==e.dataForm.type?t("el-form-item",{attrs:{label:"菜单图标",prop:"icon"}},[t("el-row",[t("el-col",{attrs:{span:12}},[t("el-input",{staticClass:"icon-list__input",attrs:{placeholder:"菜单图标名称"},model:{value:e.dataForm.icon,callback:function(t){e.$set(e.dataForm,"icon",t)},expression:"dataForm.icon"}})],1),t("el-col",{staticClass:"icon-list__tips",attrs:{span:12}},[2!==e.dataForm.type?t("el-form-item",{attrs:{label:"排序号",prop:"orderNum"}},[t("el-input-number",{attrs:{"controls-position":"right",min:0,label:"排序号"},model:{value:e.dataForm.orderNum,callback:function(t){e.$set(e.dataForm,"orderNum",t)},expression:"dataForm.orderNum"}})],1):e._e()],1)],1)],1):e._e(),t("div",[e._v("参考ElementUI图标库, "),t("a",{attrs:{href:"https://element.eleme.cn/#/zh-CN/component/icon",target:"_blank"}},[e._v("找图标")])])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},o=[],n=(a("d9e2"),a("ac1f"),a("00b4"),a("ed08")),s={data:function(){var e=this,t=function(t,a,r){1!==e.dataForm.type||/\S/.test(a)?r():r(new Error("菜单URL不能为空"))};return{visible:!1,dataForm:{id:0,type:1,typeList:["目录","菜单","按钮"],name:"",parentId:0,parentName:"",url:"",perms:"",orderNum:0,icon:"",isShow:1},isShowList:["否","是"],dataRule:{name:[{required:!0,message:"菜单名称不能为空",trigger:"blur"}],parentName:[{required:!0,message:"上级菜单不能为空",trigger:"change"}],url:[{validator:t,trigger:"blur"}]},menuList:[],menuListTreeProps:{label:"name",children:"children"}}},methods:{init:function(e){var t=this;this.dataForm.id=e||0,this.$http({url:this.$http.adornUrl("/sys/menu/select"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;t.menuList=Object(n["d"])(a.menuList,"menuId")})).then((function(){t.visible=!0,t.$nextTick((function(){t.$refs["dataForm"].resetFields()}))})).then((function(){t.dataForm.id?t.$http({url:t.$http.adornUrl("/sys/menu/info/".concat(t.dataForm.id)),method:"get",params:t.$http.adornParams()}).then((function(e){var a=e.data;t.dataForm.id=a.menu.menuId,t.dataForm.type=a.menu.type,t.dataForm.name=a.menu.name,t.dataForm.parentId=a.menu.parentId,t.dataForm.url=a.menu.url,t.dataForm.perms=a.menu.perms,t.dataForm.orderNum=a.menu.orderNum,t.dataForm.icon=a.menu.icon,t.dataForm.isShow=a.menu.isShow,t.menuListTreeSetCurrentNode()})):t.menuListTreeSetCurrentNode()}))},menuListTreeCurrentChangeHandle:function(e,t){this.dataForm.parentId=e.menuId,this.dataForm.parentName=e.name},menuListTreeSetCurrentNode:function(){this.$refs.menuListTree.setCurrentKey(this.dataForm.parentId),this.dataForm.parentName=(this.$refs.menuListTree.getCurrentNode()||{})["name"]},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$http({url:e.$http.adornUrl("/sys/menu/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({menuId:e.dataForm.id||void 0,type:e.dataForm.type,name:e.dataForm.name,parentId:e.dataForm.parentId,url:e.dataForm.url,perms:e.dataForm.perms,orderNum:e.dataForm.orderNum,icon:e.dataForm.icon,isShow:e.dataForm.isShow})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(a.msg)}))}))}}},i=s,m=(a("b33b"),a("2877")),l=Object(m["a"])(i,r,o,!1,null,null,null);t["default"]=l.exports},b33b:function(e,t,a){"use strict";a("27e5")}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d216825"],{c387:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.dataFormSubmit()}}},[e("el-form-item",{attrs:{label:"文件地址",prop:"url"}},[e("el-input",{attrs:{placeholder:"文件地址"},model:{value:t.dataForm.url,callback:function(e){t.$set(t.dataForm,"url",e)},expression:"dataForm.url"}})],1),e("el-form-item",{attrs:{label:"专家ID",prop:"activityGuestId"}},[e("el-input",{attrs:{placeholder:"专家ID"},model:{value:t.dataForm.activityGuestId,callback:function(e){t.$set(t.dataForm,"activityGuestId",e)},expression:"dataForm.activityGuestId"}})],1),e("el-form-item",{attrs:{label:"活动ID",prop:"activityId"}},[e("el-input",{attrs:{placeholder:"活动ID"},model:{value:t.dataForm.activityId,callback:function(e){t.$set(t.dataForm,"activityId",e)},expression:"dataForm.activityId"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},i=[],o={data:function(){return{visible:!1,dataForm:{repeatToken:"",id:0,url:"",activityGuestId:"",activityId:""},dataRule:{url:[{required:!0,message:"文件地址不能为空",trigger:"blur"}],activityGuestId:[{required:!0,message:"专家ID不能为空",trigger:"blur"}],activityId:[{required:!0,message:"活动ID不能为空",trigger:"blur"}]}}},methods:{init:function(t){var e=this;this.getToken(),this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.dataForm.id&&e.$http({url:e.$http.adornUrl("/log/logpdfservicefee/info/".concat(e.dataForm.id)),method:"get",params:e.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm.url=a.logPdfServiceFee.url,e.dataForm.activityGuestId=a.logPdfServiceFee.activityGuestId,e.dataForm.activityId=a.logPdfServiceFee.activityId)}))}))},getToken:function(){var t=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.repeatToken=a.result)}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/log/logpdfservicefee/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({repeatToken:t.dataForm.repeatToken,id:t.dataForm.id||void 0,url:t.dataForm.url,activityGuestId:t.dataForm.activityGuestId,activityId:t.dataForm.activityId})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):(t.$message.error(a.msg),"不能重复提交"!=a.msg&&t.getToken())}))}))}}},d=o,l=a("2877"),s=Object(l["a"])(d,r,i,!1,null,null,null);e["default"]=s.exports}}]);
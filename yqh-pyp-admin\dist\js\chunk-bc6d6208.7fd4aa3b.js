(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-bc6d6208"],{"34ae":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return r})),a.d(t,"d",(function(){return l}));var n=[{key:0,value:"整间"},{key:1,value:"男床位"},{key:2,value:"女床位"}],i=[{key:0,value:"整间"},{key:1,value:"拼住"},{key:2,value:"拼住"}],r=[{key:0,value:"已取消"},{key:1,value:"已入住"}],l=[{key:0,value:"未开启"},{key:1,value:"已开启"}]},"7de9":function(e,t,a){"use strict";a.d(t,"g",(function(){return n})),a.d(t,"f",(function(){return i})),a.d(t,"e",(function(){return r})),a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return s})),a.d(t,"d",(function(){return d}));var n=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],i=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],r=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],l=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],o=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],s=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],d=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]},b42b:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e._self._c;return t("div",[t("el-dialog",{attrs:{title:"选择房间号","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{attrs:{inline:!0,model:e.dataForm}},[t("el-form-item",[t("el-input",{attrs:{placeholder:"房间号",clearable:""},model:{value:e.dataForm.number,callback:function(t){e.$set(e.dataForm,"number",t)},expression:"dataForm.number"}})],1),t("el-form-item",[t("el-button",{on:{click:function(t){return e.getDataList()}}},[e._v("查询")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{size:"mini",data:e.dataList,border:""}},[t("el-table-column",{attrs:{prop:"roomName","header-align":"center",align:"center",label:"房型名称"}}),t("el-table-column",{attrs:{prop:"number","header-align":"center",align:"center",label:"房号"}}),t("el-table-column",{attrs:{prop:"isAssign","header-align":"center",align:"center",label:"是否分配"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[t("el-tag",{class:"tag-color tag-color-"+a.row.isAssign,attrs:{type:"primary"}},[e._v(e._s(e.yesOrNo[a.row.isAssign].value))])],1)}}])}),e.dataList.length>0?t("div",e._l(e.dataList[0].assignVos,(function(a,n){return t("el-table-column",{key:n,attrs:{"header-align":"center",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v(e._s(a.row.assignVos[n].number)+" "),a.row.assignVos[n].number>0?t("span",[e._v("("+e._s(a.row.assignVos[n].number<1?e.roomType[a.row.assignVos[n].roomType].value:"满")+")")]):e._e()]}}],null,!0)},[t("template",{slot:"header"},[e._v(" "+e._s(a.date)+" ")])],2)})),1):e._e(),t("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"180",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{staticStyle:{color:"red"},attrs:{type:"text",size:"small"},on:{click:function(t){return e.assignHandle(a.row)}}},[e._v("选择")])]}}])})],1),t("el-pagination",{attrs:{"current-page":e.pageIndex,"page-sizes":[10,20,50,100],"page-size":e.pageSize,total:e.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle}})],1)],1)},i=[],r=(a("d3b7"),a("3ca3"),a("ddb0"),a("34ae")),l=a("7de9"),o={data:function(){return{visible:!1,appid:"",roomType:r["b"],hotels:[],rooms:[],yesOrNo:l["g"],dataForm:{activityId:"",hotelActivityId:"",hotelActivityRoomId:"",number:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,addnumberVisible:!1,assignVisible:!1,assignpeopleVisible:!1}},components:{tagsEditor:function(){return a.e("chunk-4dba3ada").then(a.bind(null,"a55c"))}},methods:{init:function(e,t,a){this.dataForm.activityId=e,this.dataForm.hotelActivityId=t,this.dataForm.hotelActivityRoomId=a,this.visible=!0,this.getDataList()},sizeChangeHandle:function(e){this.pageSize=e,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(e){this.pageIndex=e,this.getDataList()},getDataList:function(){var e=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/hotel/hotelactivityroomnumber/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,number:this.dataForm.number,activityId:this.dataForm.activityId,hotelActivityId:this.dataForm.hotelActivityId,hotelActivityRoomId:this.dataForm.hotelActivityRoomId})}).then((function(t){var a=t.data;a&&200===a.code?(e.dataList=a.page.list,e.totalPage=a.page.totalCount):(e.dataList=[],e.totalPage=0),e.dataListLoading=!1}))},assignHandle:function(e){this.$emit("select",e.number),this.visible=!1}}},s=o,d=a("2877"),u=Object(d["a"])(s,n,i,!1,null,null,null);t["default"]=u.exports}}]);
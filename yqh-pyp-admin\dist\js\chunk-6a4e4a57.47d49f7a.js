(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6a4e4a57","chunk-759514ee"],{"0686":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"mod-config"},[t("el-form",{attrs:{inline:!0,model:e.dataForm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.onSearch()}}},[t("el-form-item",[t("el-input",{attrs:{placeholder:"客户名称",clearable:""},model:{value:e.dataForm.name,callback:function(t){e.$set(e.dataForm,"name",t)},expression:"dataForm.name"}})],1),t("el-form-item",[t("el-input",{attrs:{placeholder:"联系人",clearable:""},model:{value:e.dataForm.username,callback:function(t){e.$set(e.dataForm,"username",t)},expression:"dataForm.username"}})],1),t("el-form-item",[t("el-input",{attrs:{placeholder:"联系方式",clearable:""},model:{value:e.dataForm.mobile,callback:function(t){e.$set(e.dataForm,"mobile",t)},expression:"dataForm.mobile"}})],1),t("el-form-item",[t("el-date-picker",{attrs:{type:"daterange","value-format":"yyyy/MM/dd","range-separator":"至","start-placeholder":"开始日期(新增时间)","end-placeholder":"结束日期(新增时间)","picker-options":e.pickerOptions},model:{value:e.timeArray,callback:function(t){e.timeArray=t},expression:"timeArray"}})],1),t("el-form-item",[t("el-button",{on:{click:function(t){return e.onSearch()}}},[e._v("查询")]),e.isAuth("client:client:save")?t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addOrUpdateHandle()}}},[e._v("新增")]):e._e(),e.isAuth("client:client:delete")?t("el-button",{attrs:{type:"danger",disabled:e.dataListSelections.length<=0},on:{click:function(t){return e.deleteHandle()}}},[e._v("批量删除")]):e._e(),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.exportHandle()}}},[e._v("导出")]),t("el-button",{attrs:{type:"success"},on:{click:function(t){return e.downloadExampleHandle()}}},[e._v("模板下载")]),t("el-button",{attrs:{type:"primary"}},[t("Upload",{attrs:{url:"/client/client/importExcel?appid="+e.$cookie.get("appid"),name:"客户数据导入"},on:{uploaded:e.getDataList}})],1)],1)],1),t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.dataList,border:"",size:"mini"},on:{"selection-change":e.selectionChangeHandle}},[t("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),t("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",width:"250px",label:"客户名称"}}),t("el-table-column",{attrs:{prop:"code","header-align":"center",align:"center",label:"统一社会信用代码"}}),t("el-table-column",{attrs:{prop:"type","header-align":"center",align:"center",label:"类型"}}),t("el-table-column",{attrs:{prop:"username","header-align":"center",align:"center",label:"联系人"}}),t("el-table-column",{attrs:{prop:"bankAccount","header-align":"center",align:"center",label:"银行账户"}}),t("el-table-column",{attrs:{prop:"bankAddress","header-align":"center",align:"center",label:"开户行"}}),t("el-table-column",{attrs:{prop:"mobile","header-align":"center",align:"center",label:"联系方式"}}),t("el-table-column",{attrs:{prop:"email","header-align":"center",align:"center",label:"邮箱"}}),t("el-table-column",{attrs:{prop:"areaName","header-align":"center",align:"center",label:"区域"}}),t("el-table-column",{attrs:{prop:"address","header-align":"center",align:"center",label:"地址"}}),t("el-table-column",{attrs:{prop:"createOn","show-overflow-tooltip":!0,"header-align":"center",align:"center",label:"创建时间"}}),t("el-table-column",{attrs:{prop:"updateOn","show-overflow-tooltip":!0,"header-align":"center",align:"center",label:"更新时间"}}),t("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.thingHandle(a.row.id)}}},[e._v("客户文件")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.addOrUpdateHandle(a.row.id)}}},[e._v("修改")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHandle(a.row.id)}}},[e._v("删除")])]}}])})],1),t("el-pagination",{attrs:{"current-page":e.pageIndex,"page-sizes":[10,20,50,100],"page-size":e.pageSize,total:e.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle}}),e.addOrUpdateVisible?t("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:e.getDataList}}):e._e()],1)},n=[],o=(a("99af"),a("a15b"),a("d81d"),a("14d9"),a("d3b7"),a("3ca3"),a("a573"),a("ddb0"),a("1a4a")),i=[{key:0,value:"医院"},{key:1,value:"企业"}],l={data:function(){return{clientType:i,dataForm:{name:"",username:"",mobile:""},timeArray:[],dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListSelections:[],addOrUpdateVisible:!1,pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]}}},components:{AddOrUpdate:o["default"],Upload:function(){return a.e("chunk-043b0b7f").then(a.bind(null,"9dac"))}},activated:function(){this.getDataList()},methods:{onSearch:function(){this.pageIndex=1,this.getDataList()},getDataList:function(){var e=this;this.$http({url:this.$http.adornUrl("/client/client/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,name:this.dataForm.name,mobile:this.dataForm.mobile,username:this.dataForm.username,appid:this.$cookie.get("appid"),start:this.timeArray&&this.timeArray.length>0?this.timeArray[0]+" 00:00:00":"",end:this.timeArray&&this.timeArray.length>0?this.timeArray[1]+" 23:59:59":""})}).then((function(t){var a=t.data;a&&200===a.code?(e.dataList=a.page.list,e.totalPage=a.page.totalCount):(e.dataList=[],e.totalPage=0)}))},exportHandle:function(){var e=this.$http.adornUrl("/client/client/export?"+["page=1","limit=100000","name="+this.dataForm.name,"mobile="+this.dataForm.mobile,"username="+this.dataForm.username,"appid="+this.$cookie.get("appid"),"token="+this.$cookie.get("token")].join("&"));window.open(e)},sizeChangeHandle:function(e){this.pageSize=e,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(e){this.pageIndex=e,this.getDataList()},selectionChangeHandle:function(e){this.dataListSelections=e},addOrUpdateHandle:function(e){var t=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){t.$refs.addOrUpdate.init(e)}))},bankHandle:function(e){this.$router.push({name:"clientbank",query:{id:e}})},thingHandle:function(e){this.$router.push({name:"clientthing",query:{id:e}})},downloadExampleHandle:function(){var e=this.$http.adornUrl("/client/client/exportExcelExample?"+["token="+this.$cookie.get("token")].join("&"));window.open(e)},deleteHandle:function(e){var t=this,a=e?[e]:this.dataListSelections.map((function(e){return e.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(e?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:t.$http.adornUrl("/client/client/delete"),method:"post",data:t.$http.adornData(a,!1)}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.getDataList()}}):t.$message.error(a.msg)}))}))}}},s=l,d=a("2877"),c=Object(d["a"])(s,r,n,!1,null,null,null);t["default"]=c.exports},"1a4a":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"150px"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.dataFormSubmit()}}},[t("el-form-item",{attrs:{label:"客户名称",prop:"name"}},[t("el-input",{attrs:{placeholder:"客户名称"},model:{value:e.dataForm.name,callback:function(t){e.$set(e.dataForm,"name",t)},expression:"dataForm.name"}})],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"联系人",prop:"username"}},[t("el-input",{attrs:{placeholder:"联系人"},model:{value:e.dataForm.username,callback:function(t){e.$set(e.dataForm,"username",t)},expression:"dataForm.username"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"联系方式",prop:"mobile"}},[t("el-input",{attrs:{placeholder:"联系方式"},model:{value:e.dataForm.mobile,callback:function(t){e.$set(e.dataForm,"mobile",t)},expression:"dataForm.mobile"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"类型",prop:"type"}},[t("el-select",{attrs:{placeholder:"类型",filterable:""},model:{value:e.dataForm.type,callback:function(t){e.$set(e.dataForm,"type",t)},expression:"dataForm.type"}},e._l(e.clientType,(function(e){return t("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"区域",prop:"area"}},[t("el-cascader",{staticStyle:{width:"100%"},attrs:{size:"large",options:e.options},on:{change:e.handleChange},model:{value:e.dataForm.area,callback:function(t){e.$set(e.dataForm,"area",t)},expression:"dataForm.area"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"地址",prop:"address"}},[t("el-input",{attrs:{placeholder:"地址"},model:{value:e.dataForm.address,callback:function(t){e.$set(e.dataForm,"address",t)},expression:"dataForm.address"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[t("el-input",{attrs:{placeholder:"邮箱"},model:{value:e.dataForm.email,callback:function(t){e.$set(e.dataForm,"email",t)},expression:"dataForm.email"}})],1)],1)],1),t("el-form-item",{attrs:{label:"银行账户信息",prop:"isBank"}},[t("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#6f6f6f"},model:{value:e.dataForm.isBank,callback:function(t){e.$set(e.dataForm,"isBank",t)},expression:"dataForm.isBank"}})],1),e.dataForm.isBank?t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"银行卡号",prop:"bankAccount"}},[t("el-input",{attrs:{placeholder:"银行卡号"},model:{value:e.dataForm.bankAccount,callback:function(t){e.$set(e.dataForm,"bankAccount",t)},expression:"dataForm.bankAccount"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"开户行",prop:"bankAddress"}},[t("el-input",{attrs:{placeholder:"开户行"},model:{value:e.dataForm.bankAddress,callback:function(t){e.$set(e.dataForm,"bankAddress",t)},expression:"dataForm.bankAddress"}})],1)],1)],1):e._e(),t("el-form-item",{attrs:{label:"发票信息",prop:"isInvoice"}},[t("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#6f6f6f"},model:{value:e.dataForm.isInvoice,callback:function(t){e.$set(e.dataForm,"isInvoice",t)},expression:"dataForm.isInvoice"}})],1),e.dataForm.isInvoice?t("el-form-item",{attrs:{label:"统一社会编码",prop:"code"}},[t("el-input",{attrs:{placeholder:"统一社会编码"},model:{value:e.dataForm.code,callback:function(t){e.$set(e.dataForm,"code",t)},expression:"dataForm.code"}})],1):e._e(),e.dataForm.isInvoice?t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"注册地址（专票）",prop:"registerAddress"}},[t("el-input",{attrs:{placeholder:"注册地址（专票）"},model:{value:e.dataForm.registerAddress,callback:function(t){e.$set(e.dataForm,"registerAddress",t)},expression:"dataForm.registerAddress"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"注册电话（专票）",prop:"registerTelephone"}},[t("el-input",{attrs:{placeholder:"注册电话（专票）"},model:{value:e.dataForm.registerTelephone,callback:function(t){e.$set(e.dataForm,"registerTelephone",t)},expression:"dataForm.registerTelephone"}})],1)],1)],1):e._e(),e.dataForm.isInvoice?t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"注册银行（专票）",prop:"registerBank"}},[t("el-input",{attrs:{placeholder:"注册银行（专票）"},model:{value:e.dataForm.registerBank,callback:function(t){e.$set(e.dataForm,"registerBank",t)},expression:"dataForm.registerBank"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"注册账户（专票）",prop:"registerAccount"}},[t("el-input",{attrs:{placeholder:"注册账户（专票）"},model:{value:e.dataForm.registerAccount,callback:function(t){e.$set(e.dataForm,"registerAccount",t)},expression:"dataForm.registerAccount"}})],1)],1)],1):e._e()],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},n=[],o=(a("a15b"),a("ef6c")),i={data:function(){return{options:o["regionData"],clientType:[],loading:!1,visible:!1,dataForm:{id:0,isBank:!0,isInvoice:!1,name:"",username:"",code:"",address:"",email:"",mobile:"",registerAddress:"",registerTelephone:"",registerBank:"",repeatToken:"",bankAccount:"",bankAddress:"",registerAccount:"",type:0,area:""},dataRule:{name:[{required:!0,message:"客户名称不能为空",trigger:"blur"}]}}},methods:{init:function(e){var t=this;this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){t.$refs["dataForm"].resetFields(),t.dataForm.id?t.$http({url:t.$http.adornUrl("/client/client/info/".concat(t.dataForm.id)),method:"get",params:t.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.name=a.client.name,t.dataForm.username=a.client.username,t.dataForm.code=a.client.code,t.dataForm.address=a.client.address,t.dataForm.email=a.client.email,t.dataForm.mobile=a.client.mobile,t.dataForm.registerAddress=a.client.registerAddress,t.dataForm.registerTelephone=a.client.registerTelephone,t.dataForm.registerBank=a.client.registerBank,t.dataForm.registerAccount=a.client.registerAccount,t.dataForm.bankAccount=a.client.bankAccount,t.dataForm.bankAddress=a.client.bankAddress,t.dataForm.appid=a.client.appid,t.dataForm.type=a.client.type,t.dataForm.area=a.client.area?a.client.area.split(","):[],t.dataForm.bankAccount&&(t.dataForm.isBank=!0),t.dataForm.code&&(t.dataForm.isInvoice=!0))})):(t.dataForm.appid=t.$cookie.get("appid"),t.dataForm.companyRoleId=t.$cookie.get("companyRoleId"))})),this.getToken(),this.getResult()},getToken:function(){var e=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm.repeatToken=a.result)}))},getResult:function(){var e=this;this.$http({url:this.$http.adornUrl("/sys/config/findOnlyParamKey"),method:"get",params:this.$http.adornParams({paramKey:"clientType"})}).then((function(t){var a=t.data;a&&200===a.code&&(e.clientType=a.result.paramValue?a.result.paramValue.split(","):[])}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.loading=!0,e.$http({url:e.$http.adornUrl("/client/client/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,name:e.dataForm.name,username:e.dataForm.username,code:e.dataForm.code,address:e.dataForm.address,email:e.dataForm.email,mobile:e.dataForm.mobile,registerAddress:e.dataForm.registerAddress,registerTelephone:e.dataForm.registerTelephone,registerBank:e.dataForm.registerBank,bankAddress:e.dataForm.bankAddress,bankAccount:e.dataForm.bankAccount,repeatToken:e.dataForm.repeatToken,appid:e.dataForm.appid,type:e.dataForm.type,area:e.dataForm.area?e.dataForm.area.join(","):"",registerAccount:e.dataForm.registerAccount})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList",a.result)}}):(e.$message.error(a.msg),"不能重复提交"!=a.msg&&e.getToken()),e.loading=!1})))}))}}},l=i,s=a("2877"),d=Object(s["a"])(l,r,n,!1,null,null,null);t["default"]=d.exports},a15b:function(e,t,a){"use strict";var r=a("23e7"),n=a("e330"),o=a("44ad"),i=a("fc6a"),l=a("a640"),s=n([].join),d=o!==Object,c=d||!l("join",",");r({target:"Array",proto:!0,forced:c},{join:function(e){return s(i(this),void 0===e?",":e)}})},a573:function(e,t,a){"use strict";a("ab43")},ab43:function(e,t,a){"use strict";var r=a("23e7"),n=a("d024"),o=a("c430");r({target:"Iterator",proto:!0,real:!0,forced:o},{map:n})},d024:function(e,t,a){"use strict";var r=a("c65b"),n=a("59ed"),o=a("825a"),i=a("46c4"),l=a("c5cc"),s=a("9bdd"),d=l((function(){var e=this.iterator,t=o(r(this.next,e)),a=this.done=!!t.done;if(!a)return s(e,this.mapper,[t.value,this.counter++],!0)}));e.exports=function(e){return o(this),n(e),new d(i(this),{mapper:e})}},d81d:function(e,t,a){"use strict";var r=a("23e7"),n=a("b727").map,o=a("1dde"),i=o("map");r({target:"Array",proto:!0,forced:!i},{map:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b99b6a66","chunk-15dcc354","chunk-d1174e1c","chunk-2235ec61","chunk-2d0e275f"],{"7f93":function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"}},[e("el-form-item",{attrs:{label:"主题名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"主题名称"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",{attrs:{label:"会议场地",prop:"placeId"}},[e("el-select",{attrs:{placeholder:"会议场地",filterable:""},model:{value:t.dataForm.placeId,callback:function(e){t.$set(t.dataForm,"placeId",e)},expression:"dataForm.placeId"}},t._l(t.placeList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"会议日期",prop:"startTime"}},[e("el-date-picker",{staticStyle:{windth:"100%"},attrs:{"picker-options":t.pickerOptions,"default-value":t.activityInfo.startTime,type:"datetimerange","value-format":"yyyy/MM/dd HH:mm:ss","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.dateChange},model:{value:t.dataForm.times,callback:function(e){t.$set(t.dataForm,"times",e)},expression:"dataForm.times"}})],1),e("el-form-item",{attrs:{label:"主持人",prop:"topicGuestIds"}},[e("el-select",{attrs:{multiple:"",placeholder:"主持人",filterable:""},model:{value:t.dataForm.topicGuestIds,callback:function(e){t.$set(t.dataForm,"topicGuestIds",e)},expression:"dataForm.topicGuestIds"}},t._l(t.guestList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"主席",prop:"topicSpeakerIds"}},[e("el-select",{attrs:{multiple:"",placeholder:"主持人",filterable:""},model:{value:t.dataForm.topicSpeakerIds,callback:function(e){t.$set(t.dataForm,"topicSpeakerIds",e)},expression:"dataForm.topicSpeakerIds"}},t._l(t.guestList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"讨论",prop:"topicDiscussIds"}},[e("el-select",{attrs:{multiple:"",placeholder:"讨论",filterable:""},model:{value:t.dataForm.topicDiscussIds,callback:function(e){t.$set(t.dataForm,"topicDiscussIds",e)},expression:"dataForm.topicDiscussIds"}},t._l(t.guestList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"排序，数值越小越靠前",prop:"orderBy"}},[e("el-input",{attrs:{placeholder:"排序，数值越小越靠前"},model:{value:t.dataForm.orderBy,callback:function(e){t.$set(t.dataForm,"orderBy",e)},expression:"dataForm.orderBy"}})],1),e("el-form-item",{attrs:{label:"主持别名",prop:"aliasGuestName"}},[e("el-input",{attrs:{placeholder:"主持别名"},model:{value:t.dataForm.aliasGuestName,callback:function(e){t.$set(t.dataForm,"aliasGuestName",e)},expression:"dataForm.aliasGuestName"}})],1),e("el-form-item",{attrs:{label:"主席别名",prop:"aliasSpeakerName"}},[e("el-input",{attrs:{placeholder:"主席别名"},model:{value:t.dataForm.aliasSpeakerName,callback:function(e){t.$set(t.dataForm,"aliasSpeakerName",e)},expression:"dataForm.aliasSpeakerName"}})],1),e("el-form-item",{attrs:{label:"讨论别名",prop:"aliasDiscussName"}},[e("el-input",{attrs:{placeholder:"讨论别名"},model:{value:t.dataForm.aliasDiscussName,callback:function(e){t.$set(t.dataForm,"aliasDiscussName",e)},expression:"dataForm.aliasDiscussName"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],o={data:function(){var t=this;return{visible:!1,placeList:[],guestList:[],activityInfo:{},dataForm:{id:0,activityId:"",name:"",placeId:"",startTime:"",endTime:"",aliasGuestName:"",aliasSpeakerName:"",aliasDiscussName:"",topicGuestIds:[],topicSpeakerIds:[],topicDiscussIds:[],times:[],orderBy:0},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],name:[{required:!0,message:"会场名称不能为空",trigger:"blur"}],times:[{required:!0,message:"会议时间不能为空",trigger:"blur"}],startTime:[{required:!0,message:"会议时间不能为空",trigger:"blur"}],endTime:[{required:!0,message:"结束时间不能为空",trigger:"blur"}],placeId:[{required:!0,message:"场地不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序，数值越小越靠前不能为空",trigger:"blur"}]},pickerOptions:{disabledDate:function(e){return t.dealDisabledDate(e)}}}},methods:{init:function(t,e){var a=this;this.dataForm.activityId=t,this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/place/placeactivitytopic/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.$set(a.dataForm,"times",[e.placeActivityTopic.startTime,e.placeActivityTopic.endTime]),a.dataForm.activityId=e.placeActivityTopic.activityId,a.dataForm.createOn=e.placeActivityTopic.createOn,a.dataForm.createBy=e.placeActivityTopic.createBy,a.dataForm.updateOn=e.placeActivityTopic.updateOn,a.dataForm.updateBy=e.placeActivityTopic.updateBy,a.dataForm.name=e.placeActivityTopic.name,a.dataForm.placeId=e.placeActivityTopic.placeId,a.dataForm.topicGuestIds=e.placeActivityTopic.topicGuestIds,a.dataForm.topicSpeakerIds=e.placeActivityTopic.topicSpeakerIds,a.dataForm.topicDiscussIds=e.placeActivityTopic.topicDiscussIds,a.dataForm.orderBy=e.placeActivityTopic.orderBy,a.dataForm.startTime=e.placeActivityTopic.startTime,a.dataForm.endTime=e.placeActivityTopic.endTime,a.dataForm.aliasGuestName=e.placeActivityTopic.aliasGuestName,a.dataForm.aliasSpeakerName=e.placeActivityTopic.aliasSpeakerName,a.dataForm.aliasDiscussName=e.placeActivityTopic.aliasDiscussName)}))})),this.getPlace(),this.getGuest(),this.getActivity()},getPlace:function(){var t=this;this.$http({url:this.$http.adornUrl("/place/placeactivity/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.placeList=a.result)}))},getGuest:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activityguest/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.guestList=a.result)}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/place/placeactivitytopic/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,activityId:t.dataForm.activityId,createOn:t.dataForm.createOn,createBy:t.dataForm.createBy,updateOn:t.dataForm.updateOn,updateBy:t.dataForm.updateBy,name:t.dataForm.name,placeId:t.dataForm.placeId,topicGuestIds:t.dataForm.topicGuestIds,topicSpeakerIds:t.dataForm.topicSpeakerIds,topicDiscussIds:t.dataForm.topicDiscussIds,orderBy:t.dataForm.orderBy,startTime:t.dataForm.startTime,endTime:t.dataForm.endTime,aliasGuestName:t.dataForm.aliasGuestName,aliasSpeakerName:t.dataForm.aliasSpeakerName,aliasDiscussName:t.dataForm.aliasDiscussName})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))},getActivity:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activity/info/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.activityInfo=a.activity)}))},dealDisabledDate:function(t){if(null!=this.activityInfo.endTime&&null!=this.activityInfo.startTime)return t.getTime()+864e5<new Date(this.activityInfo.startTime).getTime()||t.getTime()>=new Date(this.activityInfo.endTime).getTime()},dateChange:function(t){this.dataForm.startTime=t[0],this.dataForm.endTime=t[1],console.log(t)}}},s=o,l=a("2877"),c=Object(l["a"])(s,i,r,!1,null,null,null);e["default"]=c.exports},a15b:function(t,e,a){"use strict";var i=a("23e7"),r=a("e330"),o=a("44ad"),s=a("fc6a"),l=a("a640"),c=r([].join),n=o!==Object,d=n||!l("join",",");i({target:"Array",proto:!0,forced:d},{join:function(t){return c(s(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var i=a("23e7"),r=a("d024"),o=a("c430");i({target:"Iterator",proto:!0,real:!0,forced:o},{map:r})},ae59:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"主题主持修改","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"}},[t.placeactivitytopicspeaker.length>0?e("el-table",{staticStyle:{margin:"20px 0"},attrs:{data:t.placeactivitytopicspeaker,border:""}},[e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"嘉宾名称"}}),e("el-table-column",{attrs:{prop:"orderBy","show-overflow-tooltip":"true","header-align":"center",align:"center",label:"排序"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-input",{attrs:{placeholder:"排序"},model:{value:a.row.orderBy,callback:function(e){t.$set(a.row,"orderBy",e)},expression:"scope.row.orderBy"}})],1)}}],null,!1,525224533)}),e("el-table-column",{attrs:{prop:"confirmStatus","show-overflow-tooltip":"true","header-align":"center",align:"center",label:"确认状态"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-select",{attrs:{placeholder:"确认状态",filterable:""},model:{value:a.row.confirmStatus,callback:function(e){t.$set(a.row,"confirmStatus",e)},expression:"scope.row.confirmStatus"}},t._l(t.confirmStatus,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1)}}],null,!1,1300827402)}),e("el-table-column",{attrs:{prop:"confirmTime","header-align":"center",align:"center",label:"确认时间"}}),e("el-table-column",{attrs:{prop:"confirmReason","header-align":"center",align:"center",label:"拒绝理由"}})],1):t._e()],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{disabled:0==t.placeactivitytopicspeaker.length,type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],o=a("ed56"),s={data:function(){return{confirmStatus:o["a"],visible:!1,placeactivitytopicspeaker:[],guestList:[],dataForm:{id:0,activityId:"",name:"",placeId:"",topicGuestIds:[],orderBy:0},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],name:[{required:!0,message:"会场名称不能为空",trigger:"blur"}],placeId:[{required:!0,message:"场地不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序，数值越小越靠前不能为空",trigger:"blur"}]}}},methods:{init:function(t,e){var a=this;this.dataForm.activityId=t,this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/place/placeactivitytopicspeaker/findByTopicId/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.placeactivitytopicspeaker=e.result)}))}))},getGuest:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activityguest/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.guestList=a.result)}))},dataFormSubmit:function(){var t=this;this.$http({url:this.$http.adornUrl("/place/placeactivitytopicspeaker/updateBatch"),method:"post",data:this.placeactivitytopicspeaker}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}}},l=s,c=a("2877"),n=Object(c["a"])(l,i,r,!1,null,null,null);e["default"]=n.exports},c226:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"主题主持修改","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"}},[t.placeactivitytopicspeaker.length>0?e("el-table",{staticStyle:{margin:"20px 0"},attrs:{data:t.placeactivitytopicspeaker,border:""}},[e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"嘉宾名称"}}),e("el-table-column",{attrs:{prop:"orderBy","show-overflow-tooltip":"true","header-align":"center",align:"center",label:"排序"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-input",{attrs:{placeholder:"排序"},model:{value:a.row.orderBy,callback:function(e){t.$set(a.row,"orderBy",e)},expression:"scope.row.orderBy"}})],1)}}],null,!1,525224533)}),e("el-table-column",{attrs:{prop:"confirmStatus","show-overflow-tooltip":"true","header-align":"center",align:"center",label:"确认状态"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-select",{attrs:{placeholder:"确认状态",filterable:""},model:{value:a.row.confirmStatus,callback:function(e){t.$set(a.row,"confirmStatus",e)},expression:"scope.row.confirmStatus"}},t._l(t.confirmStatus,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1)}}],null,!1,1300827402)}),e("el-table-column",{attrs:{prop:"confirmTime","header-align":"center",align:"center",label:"确认时间"}}),e("el-table-column",{attrs:{prop:"confirmReason","header-align":"center",align:"center",label:"拒绝理由"}})],1):t._e()],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{disabled:0==t.placeactivitytopicspeaker.length,type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],o=a("ed56"),s={data:function(){return{confirmStatus:o["a"],visible:!1,placeactivitytopicspeaker:[],guestList:[],dataForm:{id:0,activityId:"",name:"",placeId:"",topicGuestIds:[],orderBy:0},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],name:[{required:!0,message:"会场名称不能为空",trigger:"blur"}],placeId:[{required:!0,message:"场地不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序，数值越小越靠前不能为空",trigger:"blur"}]}}},methods:{init:function(t,e){var a=this;this.dataForm.activityId=t,this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/place/placeactivitytopicdiscuss/findByTopicId/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.placeactivitytopicspeaker=e.result)}))}))},getGuest:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activityguest/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.guestList=a.result)}))},dataFormSubmit:function(){var t=this;this.$http({url:this.$http.adornUrl("/place/placeactivitytopicdiscuss/updateBatch"),method:"post",data:this.placeactivitytopicspeaker}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}}},l=s,c=a("2877"),n=Object(c["a"])(l,i,r,!1,null,null,null);e["default"]=n.exports},d024:function(t,e,a){"use strict";var i=a("c65b"),r=a("59ed"),o=a("825a"),s=a("46c4"),l=a("c5cc"),c=a("9bdd"),n=l((function(){var t=this.iterator,e=o(i(this.next,t)),a=this.done=!!e.done;if(!a)return c(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return o(this),r(t),new n(s(this),{mapper:t})}},d81d:function(t,e,a){"use strict";var i=a("23e7"),r=a("b727").map,o=a("1dde"),s=o("map");i({target:"Array",proto:!0,forced:!s},{map:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})},ed56:function(t,e,a){"use strict";a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return o}));var i=[{key:0,value:"预告"},{key:1,value:"直播"},{key:2,value:"录播"}],r=[{key:0,value:"未确认"},{key:1,value:"确认通过"},{key:2,value:"确认不通过"}],o=[{key:0,value:"自定义内容"},{key:1,value:"自定义链接"},{key:2,value:"会议日程"},{key:3,value:"会议嘉宾"},{key:4,value:"聊天室"},{key:5,value:"考试&问卷"},{key:6,value:"展商列表"},{key:7,value:"录播视频列表"}]},f341:function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-select",{attrs:{placeholder:"全部场地",filterable:""},model:{value:t.dataForm.placeId,callback:function(e){t.$set(t.dataForm,"placeId",e)},expression:"dataForm.placeId"}},[e("el-option",{attrs:{label:"全部场地",value:""}}),t._l(t.placeList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}))],2)],1),e("el-form-item",[e("el-input",{attrs:{placeholder:"主题名称",clearable:""},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),t.isAuth("place:placeactivitytopic:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("place:placeactivitytopic:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"会场名称"}}),e("el-table-column",{attrs:{prop:"placeName","header-align":"center",align:"center",label:"场地名称"}}),e("el-table-column",{attrs:{prop:"activitySpeakers","header-align":"center",align:"center",label:"主席"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{on:{click:function(e){return t.showTopicSpeaker(a.row.id)}}},t._l(a.row.activitySpeakers,(function(a,i){return e("el-tag",{key:i,class:"tag-color tag-color-"+a.confirmStatus,attrs:{type:"primary"}},[t._v(t._s(a.name))])})),1)}}])}),e("el-table-column",{attrs:{prop:"activityGuests","header-align":"center",align:"center",label:"主持人"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{on:{click:function(e){return t.showTopicGuest(a.row.id)}}},t._l(a.row.activityGuests,(function(a,i){return e("el-tag",{key:i,class:"tag-color tag-color-"+a.confirmStatus,attrs:{type:"primary"}},[t._v(t._s(a.name))])})),1)}}])}),e("el-table-column",{attrs:{prop:"activityDiscuss","header-align":"center",align:"center",label:"讨论"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{on:{click:function(e){return t.showTopicDiscuss(a.row.id)}}},t._l(a.row.activityDiscuss,(function(a,i){return e("el-tag",{key:i,class:"tag-color tag-color-"+a.confirmStatus,attrs:{type:"primary"}},[t._v(t._s(a.name))])})),1)}}])}),e("el-table-column",{attrs:{prop:"startTime","header-align":"center",align:"center",label:"开始时间"}}),e("el-table-column",{attrs:{prop:"endTime","header-align":"center",align:"center",label:"结束时间"}}),e("el-table-column",{attrs:{prop:"orderBy","header-align":"center",align:"center",label:"排序"}}),e("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",label:"创建时间"}}),e("el-table-column",{attrs:{prop:"updateOn","header-align":"center",align:"center",label:"更新时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.placeactivitytopicschedule(a.row.id)}}},[t._v("日程管理")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e(),t.topicguestVisible?e("topicguest",{ref:"topicguest",on:{refreshDataList:t.getDataList}}):t._e(),t.topicspeakerVisible?e("topicspeaker",{ref:"topicspeaker",on:{refreshDataList:t.getDataList}}):t._e(),t.topicdiscussVisible?e("topicdiscuss",{ref:"topicdiscuss",on:{refreshDataList:t.getDataList}}):t._e()],1)},r=[],o=(a("99af"),a("a15b"),a("d81d"),a("14d9"),a("a573"),a("7f93")),s=a("faea"),l=a("ae59"),c=a("c226"),n={data:function(){return{dataForm:{name:"",placeId:"",activityId:void 0},dataList:[],placeList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,topicguestVisible:!1,topicspeakerVisible:!1,topicdiscussVisible:!1}},components:{AddOrUpdate:o["default"],topicguest:s["default"],topicspeaker:l["default"],topicdiscuss:c["default"]},activated:function(){this.dataForm.activityId=this.$route.query.activityId,this.dataForm.placeId=this.$route.query.placeId||void 0,this.getDataList(),this.getPlace()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/place/placeactivitytopic/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,activityId:this.dataForm.activityId,placeId:this.dataForm.placeId,name:this.dataForm.name})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},getPlace:function(){var t=this;this.$http({url:this.$http.adornUrl("/place/placeactivity/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.placeList=a.result)}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(e.dataForm.activityId,t)}))},showTopicGuest:function(t){var e=this;this.topicguestVisible=!0,this.$nextTick((function(){e.$refs.topicguest.init(e.dataForm.activityId,t)}))},showTopicDiscuss:function(t){var e=this;this.topicdiscussVisible=!0,this.$nextTick((function(){e.$refs.topicdiscuss.init(e.dataForm.activityId,t)}))},showTopicSpeaker:function(t){var e=this;this.topicspeakerVisible=!0,this.$nextTick((function(){e.$refs.topicspeaker.init(e.dataForm.activityId,t)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/place/placeactivitytopic/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},placeactivitytopicschedule:function(t){this.dataForm.placeId?this.$router.push({name:"placeactivitytopicschedule",query:{activityId:this.dataForm.activityId,placeId:this.dataForm.placeId,topicId:t}}):this.$router.push({name:"placeactivitytopicschedule",query:{activityId:this.dataForm.activityId,topicId:t}})}}},d=n,u=a("2877"),p=Object(u["a"])(d,i,r,!1,null,null,null);e["default"]=p.exports},faea:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"主题嘉宾修改","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"}},[t.placeActivityTopicGuest.length>0?e("el-table",{staticStyle:{margin:"20px 0"},attrs:{data:t.placeActivityTopicGuest,border:""}},[e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"嘉宾名称"}}),e("el-table-column",{attrs:{prop:"orderBy","show-overflow-tooltip":"true","header-align":"center",align:"center",label:"排序"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-input",{attrs:{placeholder:"排序"},model:{value:a.row.orderBy,callback:function(e){t.$set(a.row,"orderBy",e)},expression:"scope.row.orderBy"}})],1)}}],null,!1,525224533)}),e("el-table-column",{attrs:{prop:"confirmStatus","show-overflow-tooltip":"true","header-align":"center",align:"center",label:"确认状态"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-select",{attrs:{placeholder:"确认状态",filterable:""},model:{value:a.row.confirmStatus,callback:function(e){t.$set(a.row,"confirmStatus",e)},expression:"scope.row.confirmStatus"}},t._l(t.confirmStatus,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1)}}],null,!1,1300827402)}),e("el-table-column",{attrs:{prop:"confirmTime","header-align":"center",align:"center",label:"确认时间"}}),e("el-table-column",{attrs:{prop:"confirmReason","header-align":"center",align:"center",label:"拒绝理由"}})],1):t._e()],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{disabled:0==t.placeActivityTopicGuest.length,type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],o=a("ed56"),s={data:function(){return{confirmStatus:o["a"],visible:!1,placeActivityTopicGuest:[],guestList:[],dataForm:{id:0,activityId:"",name:"",placeId:"",topicGuestIds:[],orderBy:0},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],name:[{required:!0,message:"会场名称不能为空",trigger:"blur"}],placeId:[{required:!0,message:"场地不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序，数值越小越靠前不能为空",trigger:"blur"}]}}},methods:{init:function(t,e){var a=this;this.dataForm.activityId=t,this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/place/placeactivitytopicguest/findByTopicId/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.placeActivityTopicGuest=e.result)}))}))},getGuest:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activityguest/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.guestList=a.result)}))},dataFormSubmit:function(){var t=this;this.$http({url:this.$http.adornUrl("/place/placeactivitytopicguest/updateBatch"),method:"post",data:this.placeActivityTopicGuest}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}}},l=s,c=a("2877"),n=Object(c["a"])(l,i,r,!1,null,null,null);e["default"]=n.exports}}]);
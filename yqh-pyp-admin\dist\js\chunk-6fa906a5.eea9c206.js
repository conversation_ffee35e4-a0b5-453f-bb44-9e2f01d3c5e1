(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6fa906a5"],{"466d":function(e,a,t){"use strict";var r=t("c65b"),n=t("d784"),i=t("825a"),o=t("7234"),l=t("50c4"),d=t("577e"),c=t("1d80"),u=t("dc4a"),s=t("8aa5"),m=t("14c3");n("match",(function(e,a,t){return[function(a){var t=c(this),n=o(a)?void 0:u(a,e);return n?r(n,a,t):new RegExp(a)[e](d(t))},function(e){var r=i(this),n=d(e),o=t(a,r,n);if(o.done)return o.value;if(!r.global)return m(r,n);var c=r.unicode;r.lastIndex=0;var u,p=[],k=0;while(null!==(u=m(r,n))){var f=d(u[0]);p[k]=f,""===f&&(r.lastIndex=s(n,l(r.lastIndex),c)),k++}return 0===k?null:p}]}))},"56ad":function(e,a,t){"use strict";t.r(a);t("b0c0");var r=function(){var e=this,a=e._self._c;return a("el-dialog",{attrs:{title:0==e.dataForm.type?"付款":"收款","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(a){e.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"100px"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.dataFormSubmit()}}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:0==e.dataForm.type?"付款金额":"收款金额",prop:"price"}},[a("el-input",{attrs:{placeholder:0==e.dataForm.type?"付款金额":"收款金额"},model:{value:e.dataForm.price,callback:function(a){e.$set(e.dataForm,"price",a)},expression:"dataForm.price"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:0==e.dataForm.type?"付款时间":"收款时间",prop:"payTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime",placeholder:0==e.dataForm.type?"付款时间":"收款时间","value-format":"yyyy/MM/dd HH:mm:ss"},model:{value:e.dataForm.payTime,callback:function(a){e.$set(e.dataForm,"payTime",a)},expression:"dataForm.payTime"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:0==e.dataForm.type?"付款账号":"收款账号",prop:"priceBankId"}},[a("el-select",{attrs:{placeholder:0==e.dataForm.type?"付款账号":"收款账号",filterable:""},model:{value:e.dataForm.priceBankId,callback:function(a){e.$set(e.dataForm,"priceBankId",a)},expression:"dataForm.priceBankId"}},e._l(e.priceBank,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"备注",prop:"remarks"}},[a("el-input",{attrs:{placeholder:"备注"},model:{value:e.dataForm.remarks,callback:function(a){e.$set(e.dataForm,"remarks",a)},expression:"dataForm.remarks"}})],1)],1)],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){e.visible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},n=[],i=t("c466"),o=t("7de9"),l={data:function(){return{yesOrNo:o["g"],companyBank:[],visible:!1,dataForm:{repeatToken:"",id:0,price:0,type:"",priceBankId:"",payTime:"",remarks:""},dataRule:{price:[{required:!0,message:"到款金额不能为空",trigger:"blur"}],priceBankId:[{required:!0,message:"付款账户id不能为空",trigger:"blur"}],payTime:[{required:!0,message:"付款时间不能为空",trigger:"blur"}]}}},methods:{init:function(e,a){var t=this;this.dataForm.id=e||0,this.dataForm.type=a||0,this.visible=!0,this.$nextTick((function(){t.$refs["dataForm"].resetFields(),t.dataForm.id?t.$http({url:t.$http.adornUrl("/price/pricetransform/info/".concat(t.dataForm.id)),method:"get",params:t.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.price=0==t.dataForm.type?a.priceTransform.price-a.priceTransform.buyArrivePrice:a.priceTransform.price-a.priceTransform.arrivePrice,t.dataForm.appid=a.priceTransform.appid,t.dataForm.remarks=a.priceTransform.remarks)})):t.dataForm.appid=t.$cookie.get("appid")})),this.findPriceBank(),this.getToken(),this.dataForm.payTime=Object(i["c"])(new Date,"yyyy/MM/dd hh:mm:ss")},findPriceBank:function(){var e=this;this.$http({url:this.$http.adornUrl("/price/pricebank/findAll"),method:"get",params:this.$http.adornParams({})}).then((function(a){var t=a.data;t&&200===t.code&&(e.priceBank=t.result,e.priceBank.length>0&&(e.dataForm.priceBankId=e.priceBank[0].id))}))},getToken:function(){var e=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(a){var t=a.data;t&&200===t.code&&(e.dataForm.repeatToken=t.result)}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(a){a&&e.$http({url:e.$http.adornUrl("/price/pricetransform/pay"),method:"post",data:e.$http.adornData({repeatToken:e.dataForm.repeatToken,id:e.dataForm.id||void 0,price:e.dataForm.price,priceBankId:e.dataForm.priceBankId,payTime:e.dataForm.payTime,type:e.dataForm.type,remarks:e.dataForm.remarks})}).then((function(a){var t=a.data;t&&200===t.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):(e.$message.error(t.msg),"不能重复提交"!=t.msg&&e.getToken())}))}))}}},d=l,c=t("2877"),u=Object(c["a"])(d,r,n,!1,null,null,null);a["default"]=u.exports},"7de9":function(e,a,t){"use strict";t.d(a,"g",(function(){return r})),t.d(a,"f",(function(){return n})),t.d(a,"e",(function(){return i})),t.d(a,"a",(function(){return o})),t.d(a,"b",(function(){return l})),t.d(a,"c",(function(){return d})),t.d(a,"d",(function(){return c}));var r=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],n=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],i=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],o=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],l=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],d=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],c=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]},c466:function(e,a,t){"use strict";t.d(a,"c",(function(){return l})),t.d(a,"a",(function(){return d})),t.d(a,"b",(function(){return c}));t("ac1f"),t("466d"),t("5319");var r=/([yMdhsm])(\1*)/g,n="yyyy/MM/dd",i="yyyy/MM/dd hh:mm:ss";function o(e,a){a-=(e+"").length;for(var t=0;t<a;t++)e="0"+e;return e}function l(e,a){return a=a||n,a.replace(r,(function(a){switch(a.charAt(0)){case"y":return o(e.getFullYear(),a.length);case"M":return o(e.getMonth()+1,a.length);case"d":return o(e.getDate(),a.length);case"w":return e.getDay()+1;case"h":return o(e.getHours(),a.length);case"m":return o(e.getMinutes(),a.length);case"s":return o(e.getSeconds(),a.length)}}))}function d(e,a){var t=new Date(e),r=new Date(t.getTime()+24*a*60*60*1e3);return l(r,i)}function c(e,a,t){var r=new Date(e),n=new Date(r.getTime()+60*a*1e3);return l(n,t||i)}}}]);
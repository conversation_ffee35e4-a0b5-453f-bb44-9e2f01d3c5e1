(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7695747e"],{4809:function(t,n,c){},"83cb":function(t,n,c){"use strict";c.r(n);var i=function(){var t=this,n=t._self._c;return n("div",[t.merchantInfo.content?n("div",{staticClass:"content",domProps:{innerHTML:t._s(t.merchantInfo.content)},on:{click:function(n){return t.showImg(n)}}}):n("van-empty",{attrs:{description:"暂无信息"}}),n("img",{staticClass:"back",attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png",alt:""},on:{click:t.cmsTurnBack}})],1)},o=[],e={components:{},data:function(){return{openid:void 0,hotelActivityId:void 0,activityId:void 0,merchantInfo:{}}},mounted:function(){document.title="酒店详情",this.hotelActivityId=this.$route.query.id,this.activityId=this.$route.query.activityId,this.getCmsInfo()},methods:{getCmsInfo:function(){var t=this;this.$fly.get("/pyp/web/hotel/hotelactivity/info/".concat(this.hotelActivityId)).then((function(n){200==n.code?t.merchantInfo=n.result:(vant.Toast(n.msg),t.merchantInfo={})}))},showImg:function(t){"IMG"==t.target.tagName&&t.target.src&&vant.ImagePreview({images:[t.target.src],closeable:!0})},cmsTurnBack:function(){this.$router.go(-1)}}},s=e,a=(c("8bc4"),c("2877")),r=Object(a["a"])(s,i,o,!1,null,"7b0f1878",null);n["default"]=r.exports},"8bc4":function(t,n,c){"use strict";c("4809")}}]);
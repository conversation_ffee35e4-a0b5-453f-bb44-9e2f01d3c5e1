(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-12e139cc","chunk-08b17d13","chunk-20b82794"],{"28a5":function(e,t,n){"use strict";var i=n("aae3"),r=n("cb7c"),s=n("ebd6"),a=n("0390"),o=n("9def"),c=n("5f1b"),u=n("520a"),l=n("79e5"),f=Math.min,p=[].push,h="split",d="length",g="lastIndex",y=4294967295,m=!l((function(){RegExp(y,"y")}));n("214f")("split",2,(function(e,t,n,l){var v;return v="c"=="abbc"[h](/(b)*/)[1]||4!="test"[h](/(?:)/,-1)[d]||2!="ab"[h](/(?:ab)*/)[d]||4!="."[h](/(.?)(.?)/)[d]||"."[h](/()()/)[d]>1||""[h](/.?/)[d]?function(e,t){var r=String(this);if(void 0===e&&0===t)return[];if(!i(e))return n.call(r,e,t);var s,a,o,c=[],l=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),f=0,h=void 0===t?y:t>>>0,m=new RegExp(e.source,l+"g");while(s=u.call(m,r)){if(a=m[g],a>f&&(c.push(r.slice(f,s.index)),s[d]>1&&s.index<r[d]&&p.apply(c,s.slice(1)),o=s[0][d],f=a,c[d]>=h))break;m[g]===s.index&&m[g]++}return f===r[d]?!o&&m.test("")||c.push(""):c.push(r.slice(f)),c[d]>h?c.slice(0,h):c}:"0"[h](void 0,0)[d]?function(e,t){return void 0===e&&0===t?[]:n.call(this,e,t)}:n,[function(n,i){var r=e(this),s=void 0==n?void 0:n[t];return void 0!==s?s.call(n,r,i):v.call(String(r),n,i)},function(e,t){var i=l(v,e,this,t,v!==n);if(i.done)return i.value;var u=r(e),p=String(this),h=s(u,RegExp),d=u.unicode,g=(u.ignoreCase?"i":"")+(u.multiline?"m":"")+(u.unicode?"u":"")+(m?"y":"g"),A=new h(m?u:"^(?:"+u.source+")",g),b=void 0===t?y:t>>>0;if(0===b)return[];if(0===p.length)return null===c(A,p)?[p]:[];var S=0,w=0,C=[];while(w<p.length){A.lastIndex=m?w:0;var k,x=c(A,m?p:p.slice(w));if(null===x||(k=f(o(A.lastIndex+(m?0:w)),p.length))===S)w=a(p,w,d);else{if(C.push(p.slice(S,w)),C.length===b)return C;for(var I=1;I<=x.length-1;I++)if(C.push(x[I]),C.length===b)return C;w=S=k}}return C.push(p.slice(S)),C}]}))},2909:function(e,t,n){"use strict";function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function r(e){if(Array.isArray(e))return i(e)}function s(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function a(e,t){if(e){if("string"===typeof e)return i(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(e,t):void 0}}function o(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(e){return r(e)||s(e)||a(e)||o()}n.d(t,"a",(function(){return c}))},5806:function(e,t,n){(function(t,n){e.exports=n()})("undefined"!==typeof self&&self,(function(){return function(e){var t={};function n(i){if(t[i])return t[i].exports;var r=t[i]={i:i,l:!1,exports:{}};return e[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(i,r,function(t){return e[t]}.bind(null,r));return i},n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s="112a")}({"05fd":function(e,t,n){e.exports=n("baa7")("native-function-to-string",Function.toString)},"065d":function(e,t,n){var i=n("bb8b"),r=n("5edc");e.exports=n("26df")?function(e,t,n){return i.f(e,t,r(1,n))}:function(e,t,n){return e[t]=n,e}},"0926":function(e,t){e.exports=function(e){try{return!!e()}catch(t){return!0}}},"0b34":function(e,t){var n=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},"112a":function(e,t,n){"use strict";var i;n.r(t),"undefined"!==typeof window&&(n("e67d"),(i=window.document.currentScript)&&(i=i.src.match(/(.+\/)[^/]+\.js(\?.*)?$/))&&(n.p=i[1])),n("a450");var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{attrs:{id:e.playerId}})},s=[];function a(e){return a="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}n("4057");var o={name:"VueAliplayerV2",props:{options:{required:!1,type:[Object],default:function(){return null}},source:{required:!1,type:[String],default:null},cssLink:{required:!1,type:[String],default:"https://g.alicdn.com/de/prismplayer/2.9.7/skins/default/aliplayer-min.css"},scriptSrc:{required:!1,type:[String],default:"https://g.alicdn.com/de/prismplayer/2.9.7/aliplayer-min.js"}},data:function(){return{player:null,playerId:"player-".concat(Math.random().toString(36).substr(2).toLocaleUpperCase()),config:{id:null,width:"100%",autoplay:!0},events:["ready","play","pause","canplay","playing","ended","liveStreamStop","onM3u8Retry","hideBar","showBar","waiting","timeupdate","snapshoted","requestFullScreen","cancelFullScreen","error","startSeek","completeSeek"]}},watch:{source:function(){this.init()},options:{handler:function(){this.init()},deep:!0}},mounted:function(){var e=this;this.$nextTick((function(){e.init()}))},updated:function(){var e=this;this.$nextTick((function(){e.init()}))},methods:{init:function(){var e=this,t="app__aliplayer-min-css",n="app__aliplayer-min-js",i=document.getElementsByTagName("head"),r=document.getElementsByTagName("html"),s=document.getElementById(n),a=document.getElementById(t);if(!a){var o=document.createElement("link");o.type="text/css",o.rel="stylesheet",o.href=this.cssLink,o.id=t,i[0].appendChild(o)}s?this.initPlayer():(s=document.createElement("script"),s.type="text/javascript",s.id=n,s.src=this.scriptSrc,r[0].appendChild(s)),s.addEventListener("load",(function(){e.initPlayer()}))},initPlayer:function(){var e=this;if("undefined"!=typeof window.Aliplayer){var t=this.deepCloneObject(this.options);if(t)for(var n in t)this.config[n]=t[n];this.source&&(this.config.source=this.source),this.config.id=this.playerId,this.player&&this.player.dispose(),this.player=Aliplayer(this.config);var i=function(t){e.player&&e.player.on(e.events[t],(function(n){e.$emit(e.events[t],n)}))};for(var r in this.events)i(r)}},getPlayer:function(){return this.player},play:function(){this.player&&this.player.play()},pause:function(){this.player&&this.player.pause()},replay:function(){this.player&&this.player.replay()},seek:function(e){this.player&&this.player.seek(e)},getCurrentTime:function(){return this.player&&this.player.getCurrentTime()},getDuration:function(){return this.player&&this.player.getDuration()},getVolume:function(){return this.player&&this.player.getVolume()},setVolume:function(e){this.player&&this.player.setVolume(e)},loadByUrl:function(e,t){this.player&&this.player.loadByUrl(e,t)},replayByVidAndPlayAuth:function(e,t){this.player&&this.player.replayByVidAndPlayAuth(e,t)},replayByVidAndAuthInfo:function(e,t,n,i,r,s){this.player&&this.player.replayByVidAndAuthInfo(e,t,n,i,r,s)},setPlayerSize:function(e,t){this.player&&this.player.setPlayerSize(e,t)},setSpeed:function(e){this.player&&this.player.setSpeed(e)},setSanpshotProperties:function(e,t,n){this.player&&this.player.setSanpshotProperties(e,t,n)},requestFullScreen:function(){this.player&&this.player.fullscreenService&&this.player.fullscreenService.requestFullScreen()},cancelFullScreen:function(){this.player&&this.player.fullscreenService&&this.player.fullscreenService.cancelFullScreen()},getIsFullScreen:function(){return this.player&&this.player.fullscreenService&&this.player.fullscreenService.getIsFullScreen()},getStatus:function(){return this.player&&this.player.getStatus()},setLiveTimeRange:function(e,t){this.player&&this.player.liveShiftSerivce&&this.player.liveShiftSerivce.setLiveTimeRange(e,t)},setRotate:function(e){this.player&&this.player.setRotate(e)},getRotate:function(){return this.player&&this.player.getRotate()},setImage:function(e){this.player&&this.player.setImage(e)},dispose:function(){this.player&&this.player.dispose()},setCover:function(e){this.player&&this.player.setCover(e)},setProgressMarkers:function(e){this.player&&this.player.setProgressMarkers(e)},setPreviewTime:function(e){this.player&&this.player.setPreviewTime(e)},getPreviewTime:function(){return this.player&&this.player.getPreviewTime()},isPreview:function(){this.player&&this.player.isPreview()},off:function(e,t){this.player&&this.player.off(e,t)},deepCloneObject:function(e){var t=Array.isArray(e)?[]:{};if(e&&"object"===a(e))for(var n in e)e.hasOwnProperty(n)&&(e[n]&&"object"===a(e[n])?t[n]=this.deepCloneObject(e[n]):t[n]=e[n]);return t}},beforeDestroy:function(){this.dispose()}},c=o;function u(e,t,n,i,r,s,a,o){var c,u="function"===typeof e?e.options:e;if(t&&(u.render=t,u.staticRenderFns=n,u._compiled=!0),i&&(u.functional=!0),s&&(u._scopeId="data-v-"+s),a?(c=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||"undefined"===typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),r&&r.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(a)},u._ssrRegister=c):r&&(c=o?function(){r.call(this,(u.functional?this.parent:this).$root.$options.shadowRoot)}:r),c)if(u.functional){u._injectStyles=c;var l=u.render;u.render=function(e,t){return c.call(t),l(e,t)}}else{var f=u.beforeCreate;u.beforeCreate=f?[].concat(f,c):[c]}return{exports:e,options:u}}var l=u(c,r,s,!1,null,null,null),f=l.exports;f.install=function(e,t){t&&t.cssLink&&(f.props.cssLink.default=t.cssLink),t&&t.scriptSrc&&(f.props.scriptSrc.default=t.scriptSrc),e.component(f.name,f)},f.Player=f;var p=f;t["default"]=p},"26df":function(e,t,n){e.exports=!n("0926")((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},"3d8a":function(e,t){e.exports=!1},4057:function(e,t,n){"use strict";n("de49");var i=n("a86f"),r=n("6bf8"),s=n("26df"),a="toString",o=/./[a],c=function(e){n("84e8")(RegExp.prototype,a,e,!0)};n("0926")((function(){return"/a/b"!=o.call({source:"a",flags:"b"})}))?c((function(){var e=i(this);return"/".concat(e.source,"/","flags"in e?e.flags:!s&&e instanceof RegExp?r.call(e):void 0)})):o.name!=a&&c((function(){return o.call(this)}))},"4fd4":function(e,t){var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},"5d10":function(e,t,n){var i=n("9cff");e.exports=function(e,t){if(!i(e))return e;var n,r;if(t&&"function"==typeof(n=e.toString)&&!i(r=n.call(e)))return r;if("function"==typeof(n=e.valueOf)&&!i(r=n.call(e)))return r;if(!t&&"function"==typeof(n=e.toString)&&!i(r=n.call(e)))return r;throw TypeError("Can't convert object to primitive value")}},"5edc":function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},"6bf8":function(e,t,n){"use strict";var i=n("a86f");e.exports=function(){var e=i(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t}},"76e3":function(e,t){var n=e.exports={version:"2.6.12"};"number"==typeof __e&&(__e=n)},"83d3":function(e,t,n){e.exports=!n("26df")&&!n("0926")((function(){return 7!=Object.defineProperty(n("e8d7")("div"),"a",{get:function(){return 7}}).a}))},"84e8":function(e,t,n){var i=n("0b34"),r=n("065d"),s=n("4fd4"),a=n("d8b3")("src"),o=n("05fd"),c="toString",u=(""+o).split(c);n("76e3").inspectSource=function(e){return o.call(e)},(e.exports=function(e,t,n,o){var c="function"==typeof n;c&&(s(n,"name")||r(n,"name",t)),e[t]!==n&&(c&&(s(n,a)||r(n,a,e[t]?""+e[t]:u.join(String(t)))),e===i?e[t]=n:o?e[t]?e[t]=n:r(e,t,n):(delete e[t],r(e,t,n)))})(Function.prototype,c,(function(){return"function"==typeof this&&this[a]||o.call(this)}))},"9cff":function(e,t){e.exports=function(e){return"object"===typeof e?null!==e:"function"===typeof e}},a450:function(e,t,n){var i=n("bb8b").f,r=Function.prototype,s=/^\s*function ([^ (]*)/,a="name";a in r||n("26df")&&i(r,a,{configurable:!0,get:function(){try{return(""+this).match(s)[1]}catch(e){return""}}})},a86f:function(e,t,n){var i=n("9cff");e.exports=function(e){if(!i(e))throw TypeError(e+" is not an object!");return e}},baa7:function(e,t,n){var i=n("76e3"),r=n("0b34"),s="__core-js_shared__",a=r[s]||(r[s]={});(e.exports=function(e,t){return a[e]||(a[e]=void 0!==t?t:{})})("versions",[]).push({version:i.version,mode:n("3d8a")?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},bb8b:function(e,t,n){var i=n("a86f"),r=n("83d3"),s=n("5d10"),a=Object.defineProperty;t.f=n("26df")?Object.defineProperty:function(e,t,n){if(i(e),t=s(t,!0),i(n),r)try{return a(e,t,n)}catch(o){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e}},d8b3:function(e,t){var n=0,i=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++n+i).toString(36))}},de49:function(e,t,n){n("26df")&&"g"!=/./g.flags&&n("bb8b").f(RegExp.prototype,"flags",{configurable:!0,get:n("6bf8")})},e67d:function(e,t){(function(e){var t="currentScript",n=e.getElementsByTagName("script");t in e||Object.defineProperty(e,t,{get:function(){try{throw new Error}catch(i){var e,t=(/.*at [^\(]*\((.*):.+:.+\)$/gi.exec(i.stack)||[!1])[1];for(e in n)if(n[e].src==t||"interactive"==n[e].readyState)return n[e];return null}}})})(document)},e8d7:function(e,t,n){var i=n("9cff"),r=n("0b34").document,s=i(r)&&i(r.createElement);e.exports=function(e){return s?r.createElement(e):{}}}})}))},"66c7":function(e,t,n){"use strict";n("4917"),n("a481");var i=/([yMdhsm])(\1*)/g,r="yyyy-MM-dd";function s(e,t){t-=(e+"").length;for(var n=0;n<t;n++)e="0"+e;return e}t["a"]={formatDate:{format:function(e,t){return t=t||r,t.replace(i,(function(t){switch(t.charAt(0)){case"y":return s(e.getFullYear(),t.length);case"M":return s(e.getMonth()+1,t.length);case"d":return s(e.getDate(),t.length);case"w":return e.getDay()+1;case"h":return s(e.getHours(),t.length);case"m":return s(e.getMinutes(),t.length);case"s":return s(e.getSeconds(),t.length)}}))},parse:function(e,t){var n=t.match(i),r=e.match(/(\d)+/g);if(n.length==r.length){for(var s=new Date(1970,0,1),a=0;a<n.length;a++){var o=parseInt(r[a]),c=n[a];switch(c.charAt(0)){case"y":s.setFullYear(o);break;case"M":s.setMonth(o-1);break;case"d":s.setDate(o);break;case"h":s.setHours(o);break;case"m":s.setMinutes(o);break;case"s":s.setSeconds(o);break}}return s}return null},toWeek:function(e){var t=new Date(e).getDay(),n="";switch(t){case 0:n="s";break;case 1:n="m";break;case 2:n="t";break;case 3:n="w";break;case 4:n="t";break;case 5:n="f";break;case 6:n="s";break}return n}},toUserLook:function(e){var t=Math.floor(e/3600%24),n=Math.floor(e/60%60);return t<1?n+"分":t+"时"+n+"分"}}},"684f":function(e,t){e.exports="data:image/png;base64,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"},"7dcb":function(e,t,n){"use strict";n("a481"),n("4917");t["a"]={getVersion:function(){var e=navigator.userAgent||window.navigator.userAgent,t=/HUAWEI|HONOR/gi,n=/[^;]+(?= Build)/gi,i=/CPU iPhone OS \d[_\d]*/gi,r=/CPU OS \d[_\d]*/gi,s=/Windows NT \d[\.\d]*/gi,a=/Linux x\d[_\d]*/gi,o=/Mac OS X \d[_|\.\d]*/gi;return/Android/gi.test(e)?t.test(e)?e.match(t)[0]+e.match(n)[0]:e.match(n)[0]:/iPhone/gi.test(e)?e.match(i)[0].replace(/CPU iPhone OS/gi,"iPhone").replace(/_/g,"."):/iPad/gi.test(e)?e.match(r)[0].replace(/CPU OS/gi,"iPad").replace(/_/g,"."):/Windows/gi.test(e)?"Windows "+(Math.min(parseInt(e.match(s)[0].replace(/Windows NT /,""))+1,10)<7?"XP":Math.min(parseInt(e.match(s)[0].replace(/Windows NT /,""))+1,10)):/Linux/gi.test(e)?e.match(a)[0]:/Macintosh/gi.test(e)?e.match(o)[0].replace(/_/g,"."):"unknown"}}},"84c1":function(e){e.exports=JSON.parse('[{"key":0,"value":"预告"},{"key":1,"value":"直播中"},{"key":2,"value":"回放中"}]')},a153:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e._self._c;return t("div",[t("div",{ref:"message-list",staticClass:"message-list"},[t("van-pull-refresh",{attrs:{disabled:e.finished},on:{refresh:e.getChatMsg},model:{value:e.loading,callback:function(t){e.loading=t},expression:"loading"}},e._l(e.chatMsg,(function(n){return t("div",{key:n.uuid},[n.isBack?e._e():t("div",{staticClass:"message-box"},[t("img",{staticClass:"message-img",attrs:{src:n.avatar},on:{click:function(t){return e.taboo(n)},error:function(t){return e.imgError(n)}}}),t("div",{staticClass:"message-item"},[t("div",{staticStyle:{display:"flex","align-items":"center","margin-bottom":"4px"}},[t("div",{staticClass:"message-nick"},[e._v(e._s(n.username))]),n.roleId&&1==n.roleId?t("van-tag",{staticStyle:{"margin-left":"5px"},attrs:{type:"success"}},[e._v("管理员")]):e._e(),t("div",{staticClass:"message-date"},[e._v(e._s(n.createOn))])],1),t("div",{staticClass:"message-container",on:{click:function(t){return e.messageHandler(n)}}},[t("div",{staticClass:"triangle"}),["img"==n.type?t("img",{attrs:{src:n.msg,width:"100px",height:"100px"}}):t("span",{staticClass:"message-text"},[e._v(e._s(n.msg))])]],2)])])])})),0)],1),t("van-field",{staticClass:"tabbar",attrs:{type:"textarea",rows:"1",autosize:e.height,center:"",placeholder:"我也要发言"},scopedSlots:e._u([{key:"left-icon",fn:function(){return[t("div",{staticStyle:{display:"flex"}},[t("van-icon",{attrs:{name:"smile-o"},on:{click:function(t){e.emojiShow=!e.emojiShow}}}),t("van-uploader",{attrs:{"after-read":e.afterRead,"before-read":e.beforeRead,accept:"image/*"}},[t("van-icon",{staticStyle:{"margin-left":"5px"},attrs:{slot:"default",name:"photo-o"},slot:"default"})],1)],1)]},proxy:!0},{key:"button",fn:function(){return[t("van-button",{ref:"mobileInput",staticClass:"send",attrs:{id:"sendInput"},on:{click:e.sendTextMessage}},[e._v("发送")])]},proxy:!0}]),model:{value:e.messageContent,callback:function(t){e.messageContent=t},expression:"messageContent"}}),t("div",{directives:[{name:"show",rawName:"v-show",value:e.emojiShow,expression:"emojiShow"}],staticClass:"emojis"},e._l(e.emoji,(function(n){return t("div",{key:n,staticClass:"emoji",on:{click:function(t){return e.chooseEmoji(n)}}},[e._v("\n      "+e._s(n)+"\n    ")])})),0)],1)},r=[],s=(n("96cf"),n("1da1")),a=(n("6762"),n("2fdb"),n("ac6a"),n("2909")),o=["😀","😂","😃","😄","😅","😆","😉","😊","😋","😎","😍","😘","😗","😚","😇","😐","😑","😶","😏","😣","😥","😮","😯","😪","😫","😴","😌","😛","😜","😝","😒","😓","😔","😕","😲","😷","😖","😞","😟","😤","😢","😭","😦","😧","😨","😬","😰","😱","😳","😵","😡","😠","🍇","🍈","🍉","🍊","🍋","🍌","🍍","🍎","🍏","🍐","🍑","🍒","🍓","🍅","🍆","🌽","🍄","🐁","🐂","🐅","🐇","🐉","🐍","🐎","🐐","🐒","🐓","🐕","🐖","♈","♉","♊","♋","♌","♍","♎","♏","♐","♑","♒","♓","⛎","🌰","🍞","🍖","🍗","🍔","🍟","🍕","🍳","🍲","🍱","🍘","🍙","🍚","🍛","🍜","🍝","🍠","🍢","🍣","🍤","🍥","🍡","🍦","🍧","🍨","🍩","🍪","🎂","🍰","🍫","🍬","🍭","🍮","🍯","🍼","☕","🍵","🍶","🍷","🍸","🍹","🍺","🍻","🍴","🎪","🎭","🎨","🌹","🍀","🍎","💰","📱","🌙","🍁","🍂","🍃","🌷","💎","🔪","🔫","🏀","⚽","⚡","👄","👍","🔥","👦","👧","👨","👩","👴","👵","👶","👱","👮","👲","👳","👷","👸","💂","🎅","👰","👼","💆","💇","🙍","🙎","🙅","🙆","💁","🙋","🙇","🙌","🙏","👤","👥","🚶","🏃","👯","💃","👫","👬","👭","💏","💑","👪","💪","👈","👉","☝","👆","👇","✌","✋","👌","👍","👎","✊","👊","👋","👏","👐","🌍","🌎","🌏","🌐","🌑","🌒","🌓","🌔","🌕","🌖","🌗","🌘","🌙","🌚","🌛","🌜","☀","🌝","🌞","⭐","🌟","🌠","☁","⛅","☔","⚡","❄","🔥","💧","🌊","🚂","🚃","🚄","🚅","🚆","🚇","🚈","🚉","🚊","🚝","🚞","🚋","🚌","🚍","🚎","🚏","🚐","🚑","🚒","🚓","🚔","🚕","🚖","🚗","🚘","🚚","🚛","🚜","🚲","⛽","🚨","🚥","🚦","🚧","⚓","⛵","🚣","🚤","🚢","✈","💺","🚁","🚟","🚠","🚡","🚀"],c={data:function(){return{emoji:Object(a["a"])(o),actions:[{name:"撤回"},{name:"预览图片"}],emojiShow:!1,showActionSheet:!1,timer:"",isShowScrollButtomTips:!1,preScrollHeight:0,loading:!1,finished:!1,dateCompare:0,chatMsg:[],uuid:[],pageIndex:2,pageSize:5,totalPage:0,height:{maxHeight:75},messageContent:"",indexMessage:{}}},props:["pushKey","activityId"],mounted:function(){var e=this;this.firstGetChatMsg(),window.addEventListener("beforeunload",this.clear),this.timer=setInterval((function(){console.log("---------------------定时器执行---------------------"),e.refreshMsg()}),1e4)},updated:function(){this.keepMessageListOnButtom()},beforeDestroy:function(){},methods:{imgError:function(e){},getChatMsg:function(){var e=this;this.$fly.get("/pyp/web/chat/list",{page:this.pageIndex,limit:this.pageSize,pushKey:this.pushKey}).then((function(t){200==t.code?t.list&&t.list.length>0?(t.list.forEach((function(t){e.uuid.includes(t.uuid)||(e.chatMsg.unshift(t),e.uuid.push(t.uuid))})),e.totalPage=t.totalPage,e.pageIndex++,e.loading=!1,e.totalPage<e.pageIndex?e.finished=!0:e.finished=!1):e.finished=!0:(e.chatMsg=[],e.totalPage=0,e.finished=!0)}))},firstGetChatMsg:function(){var e=this;this.$fly.get("/pyp/web/chat/list",{page:1,limit:5,pushKey:this.pushKey}).then((function(t){200==t.code?t.list&&t.list.length>0?t.list.forEach((function(t){e.uuid.includes(t.uuid)||(e.chatMsg.unshift(t),e.uuid.push(t.uuid))})):e.finished=!0:(e.chatMsg=[],e.finished=!0)}))},refreshMsg:function(){var e=this;this.$fly.get("/pyp/web/chat/list",{page:1,limit:5,pushKey:this.pushKey}).then((function(t){200==t.code&&t.list&&t.list.length>0?t.list.forEach((function(t){e.uuid.includes(t.uuid)||(e.chatMsg.push(t),e.uuid.push(t.uuid))})):(e.chatMsg=[],e.finished=!0)}))},handleLine:function(){this.messageContent+="\n"},handleEnter:function(){this.sendTextMessage()},sendTextMessage:function(){var e=this;if(window.scroll(0,0),""===this.messageContent||0===this.messageContent.trim().length)return this.messageContent="",vant.Toast("不能发送空消息"),!1;this.$fly.get("/pyp/web/chat/send",{pushKey:this.pushKey,msg:this.messageContent,activityId:this.activityId}).then((function(t){200==t.code&&(e.emojiShow=!1,e.refreshMsg(),e.$nextTick((function(){var t=e.$refs["message-list"];console.log(t),t.scrollTop=t.scrollHeight})))})),this.messageContent=""},keepMessageListOnButtom:function(){var e=this.$refs["message-list"];e&&(this.preScrollHeight-e.clientHeight-e.scrollTop<20?(this.$nextTick((function(){e.scrollTop=e.scrollHeight+60})),this.isShowScrollButtomTips=!1):this.isShowScrollButtomTips=!0,this.preScrollHeight=e.scrollHeight)},clear:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.timer=clearInterval(this.timer);case 1:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),select:function(e){0==e?this.back(this.indexMessage.index):1==e&&vant.ImagePreview({images:[this.indexMessage.msg],closeable:!0})},messageHandler:function(e){this.indexMessage=e,"img"==e.type?this.$emit("showImage"):this.back(e.index)},back:function(e){var t=this;vant.Dialog.confirm({title:"提示",message:"确认撤回消息?"}).then((function(){t.$fly.get("/pyp/web/chat/back",{index:e,pushKey:t.pushKey,activityId:t.activityId}).then((function(e){e&&200===e.code?(vant.Toast("撤回成功"),t.chatMsg=[],t.uuid=[],t.pageIndex=1,t.finished=!1,t.refreshMsg()):vant.Toast(e.msg)}))})).catch((function(){}))},taboo:function(e){var t=this;vant.Dialog.confirm({title:"提示",message:"确认禁言该用户?"}).then((function(){t.$fly.get("/pyp/web/chat/taboo",{userId:e.userId,activityId:t.activityId,pushKey:t.pushKey}).then((function(e){e&&200===e.code?(vant.Toast("禁言成功"),t.chatMsg=[],t.uuid=[],t.pageIndex=1,t.finished=!1,t.refreshMsg()):vant.Toast(e.msg)}))})).catch((function(){}))},chooseEmoji:function(e){this.messageContent+=e},afterRead:function(e){var t=this;e.status="uploading",e.message="上传中...";var n=new FormData;n.append("pushKey",this.pushKey),n.append("activityId",this.activityId),n.append("file",e.file),this.$fly.post("/pyp/web/chat/sendImage",n).then((function(e){e&&200===e.code&&(t.refreshMsg(),t.$nextTick((function(){var e=t.$refs["message-list"];console.log(e),e.scrollTop=e.scrollHeight})))}))},beforeRead:function(e){return!0}},destroyed:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:console.log("destroyed"),this.clear();case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},u=c,l=(n("af0b"),n("2877")),f=Object(l["a"])(u,i,r,!1,null,"1ecb2ec0",null);t["default"]=f.exports},ac6a:function(e,t,n){for(var i=n("cadf"),r=n("0d58"),s=n("2aba"),a=n("7726"),o=n("32e9"),c=n("84f2"),u=n("2b4c"),l=u("iterator"),f=u("toStringTag"),p=c.Array,h={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},d=r(h),g=0;g<d.length;g++){var y,m=d[g],v=h[m],A=a[m],b=A&&A.prototype;if(b&&(b[l]||o(b,l,p),b[f]||o(b,f,m),c[m]=p,v))for(y in i)b[y]||s(b,y,i[y],!0)}},af0b:function(e,t,n){"use strict";n("f325")},f325:function(e,t,n){}}]);
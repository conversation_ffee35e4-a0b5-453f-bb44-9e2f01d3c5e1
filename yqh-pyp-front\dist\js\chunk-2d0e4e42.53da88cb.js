(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0e4e42"],{"91c7":function(o,t,e){"use strict";e.r(t);var n=function(){var o=this,t=o._self._c;return t("van-dialog",{attrs:{"close-on-click-overlay":!0,title:"长按二维码",showConfirmButton:!1},on:{confirm:function(t){return o.$emit("close")}},model:{value:o.show,callback:function(t){o.show=t},expression:"show"}},[t("div",{staticClass:"text-center padding"},[t("van-image",{attrs:{width:"200",src:o.qrcodeImgUrl},scopedSlots:o._u([{key:"error",fn:function(){return[o._v("二维码加载失败\n            ")]},proxy:!0}])}),t("div",{staticStyle:{color:"grey","font-size":"14px"}},[o._v("长按二维码添加")])],1)])},r=[],l={name:"FollowModal",props:{show:{type:Boolean,default:!1},qrcodeImgUrl:{type:String,default:""}},data:function(){return{}}},c=l,s=e("2877"),a=Object(s["a"])(c,n,r,!1,null,null,null);t["default"]=a.exports}}]);
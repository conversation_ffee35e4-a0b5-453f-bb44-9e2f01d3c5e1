(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7a4689b4","chunk-529c5fff","chunk-2d20f394"],{"15a1":function(t,a,e){"use strict";e.r(a);e("b0c0");var i=function(){var t=this,a=t._self._c;return a("div",{staticClass:"mod-config"},[a("el-form",{attrs:{inline:!0,model:t.dataForm}},[a("el-form-item",[a("el-select",{attrs:{placeholder:"全部场地",filterable:""},model:{value:t.dataForm.placeId,callback:function(a){t.$set(t.dataForm,"placeId",a)},expression:"dataForm.placeId"}},[a("el-option",{attrs:{label:"全部场地",value:""}}),t._l(t.placeList,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}))],2)],1),a("el-form-item",[a("el-input",{attrs:{placeholder:"参会人姓名",clearable:""},model:{value:t.dataForm.contact,callback:function(a){t.$set(t.dataForm,"contact",a)},expression:"dataForm.contact"}})],1),a("el-form-item",[a("el-input",{attrs:{placeholder:"联系方式",clearable:""},model:{value:t.dataForm.mobile,callback:function(a){t.$set(t.dataForm,"mobile",a)},expression:"dataForm.mobile"}})],1),a("el-form-item",[a("el-button",{on:{click:function(a){return t.getDataList()}}},[t._v("查询")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.exportHandle()}}},[t._v("导出")])],1),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"success"},on:{click:function(a){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[a("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),a("el-table-column",{attrs:{prop:"contact","header-align":"center",align:"center",label:"参会人姓名"}}),a("el-table-column",{attrs:{prop:"mobile","header-align":"center",align:"center",label:"联系方式"}}),a("el-table-column",{attrs:{prop:"placeName","header-align":"center",align:"center",label:"房间名称"}}),a("el-table-column",{attrs:{prop:"count","header-align":"center",align:"center",label:"浏览时长/秒"}}),a("el-table-column",{attrs:{prop:"nowCount","header-align":"center",align:"center",label:"当前累计时长/秒"}}),a("el-table-column",{attrs:{prop:"ipAddr","header-align":"center",align:"center",label:"ip地址"}}),a("el-table-column",{attrs:{prop:"device","header-align":"center",align:"center",label:"设备"}}),a("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",label:"创建时间"}}),a("el-table-column",{attrs:{prop:"updateOn","header-align":"center",align:"center",label:"更新时间"}}),a("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.addOrUpdateHandle(e.row.id)}}},[t._v("修改")]),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.deleteHandle(e.row.id)}}},[t._v("删除")])]}}])})],1),a("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?a("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e(),t.hourexportVisible?a("hourexport",{ref:"hourexport",on:{refreshDataList:t.getDataList}}):t._e()],1)},r=[],o=(e("99af"),e("a15b"),e("d81d"),e("a573"),e("b35b")),l=e("5f0f"),n={data:function(){return{dataForm:{contact:"",mobile:"",activityId:void 0,placeId:void 0},placeList:[],dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,hourexportVisible:!1}},components:{AddOrUpdate:o["default"],hourexport:l["default"]},activated:function(){this.dataForm.activityId=this.$route.query.activityId,this.getDataList(),this.getPlace()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/place/placeactivityhourlog/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,activityId:this.dataForm.activityId,placeId:this.dataForm.placeId,contact:this.dataForm.contact,mobile:this.dataForm.mobile})}).then((function(a){var e=a.data;e&&200===e.code?(t.dataList=e.page.list,t.totalPage=e.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var a=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){a.$refs.addOrUpdate.init(t)}))},exportHandle:function(){var t=this;this.hourexportVisible=!0,this.$nextTick((function(){t.$refs.hourexport.init(t.dataForm.activityId)}))},getPlace:function(){var t=this;this.$http({url:this.$http.adornUrl("/place/placeactivity/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(a){var e=a.data;e&&200===e.code&&(t.placeList=e.result)}))},deleteHandle:function(t){var a=this,e=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(e.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.$http({url:a.$http.adornUrl("/place/placeactivityhourlog/delete"),method:"post",data:a.$http.adornData(e,!1)}).then((function(t){var e=t.data;e&&200===e.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.getDataList()}}):a.$message.error(e.msg)}))}))}}},d=n,c=e("2877"),s=Object(c["a"])(d,i,r,!1,null,null,null);a["default"]=s.exports},"5f0f":function(t,a,e){"use strict";e.r(a);var i=function(){var t=this,a=t._self._c;return a("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(a){t.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"选择导出时间",prop:"exportTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetimerange","value-format":"yyyy/MM/dd HH:mm:ss","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.dataForm.times,callback:function(a){t.$set(t.dataForm,"times",a)},expression:"dataForm.times"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){t.visible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],o=(e("a15b"),{data:function(){return{visible:!1,dataForm:{activityId:"",times:[]},dataRule:{times:[{required:!0,message:"导出时间不能为空",trigger:"blur"}]}}},methods:{init:function(t){this.dataForm.activityId=t,this.visible=!0},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(a){if(a){var e=t.$http.adornUrl("/place/placeactivityhourlog/export?"+["token="+t.$cookie.get("token"),"startTime="+t.dataForm.times[0],"endTime="+t.dataForm.times[1],"activityId="+t.dataForm.activityId].join("&"));window.open(e)}}))}}}),l=o,n=e("2877"),d=Object(n["a"])(l,i,r,!1,null,null,null);a["default"]=d.exports},a15b:function(t,a,e){"use strict";var i=e("23e7"),r=e("e330"),o=e("44ad"),l=e("fc6a"),n=e("a640"),d=r([].join),c=o!==Object,s=c||!n("join",",");i({target:"Array",proto:!0,forced:s},{join:function(t){return d(l(this),void 0===t?",":t)}})},a573:function(t,a,e){"use strict";e("ab43")},ab43:function(t,a,e){"use strict";var i=e("23e7"),r=e("d024"),o=e("c430");i({target:"Iterator",proto:!0,real:!0,forced:o},{map:r})},b35b:function(t,a,e){"use strict";e.r(a);var i=function(){var t=this,a=t._self._c;return a("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(a){t.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"会议id",prop:"activityId"}},[a("el-input",{attrs:{placeholder:"会议id"},model:{value:t.dataForm.activityId,callback:function(a){t.$set(t.dataForm,"activityId",a)},expression:"dataForm.activityId"}})],1),a("el-form-item",{attrs:{label:"ip地址",prop:"ipAddr"}},[a("el-input",{attrs:{placeholder:"ip地址"},model:{value:t.dataForm.ipAddr,callback:function(a){t.$set(t.dataForm,"ipAddr",a)},expression:"dataForm.ipAddr"}})],1),a("el-form-item",{attrs:{label:"设备",prop:"device"}},[a("el-input",{attrs:{placeholder:"设备"},model:{value:t.dataForm.device,callback:function(a){t.$set(t.dataForm,"device",a)},expression:"dataForm.device"}})],1),a("el-form-item",{attrs:{label:"mac地址",prop:"macAddr"}},[a("el-input",{attrs:{placeholder:"mac地址"},model:{value:t.dataForm.macAddr,callback:function(a){t.$set(t.dataForm,"macAddr",a)},expression:"dataForm.macAddr"}})],1),a("el-form-item",{attrs:{label:"浏览时长",prop:"count"}},[a("el-input",{attrs:{placeholder:"浏览时长"},model:{value:t.dataForm.count,callback:function(a){t.$set(t.dataForm,"count",a)},expression:"dataForm.count"}})],1),a("el-form-item",{attrs:{label:"房间id",prop:"placeId"}},[a("el-input",{attrs:{placeholder:"房间id"},model:{value:t.dataForm.placeId,callback:function(a){t.$set(t.dataForm,"placeId",a)},expression:"dataForm.placeId"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){t.visible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],o={data:function(){return{visible:!1,dataForm:{id:0,activityId:"",ipAddr:"",device:"",macAddr:"",count:"",placeId:""},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],ipAddr:[{required:!0,message:"ip地址不能为空",trigger:"blur"}],device:[{required:!0,message:"设备不能为空",trigger:"blur"}],macAddr:[{required:!0,message:"mac地址不能为空",trigger:"blur"}],count:[{required:!0,message:"浏览时长不能为空",trigger:"blur"}],placeId:[{required:!0,message:"房间id不能为空",trigger:"blur"}]}}},methods:{init:function(t){var a=this;this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/place/placeactivityhourlog/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.dataForm.activityId=e.placeActivityHourLog.activityId,a.dataForm.ipAddr=e.placeActivityHourLog.ipAddr,a.dataForm.device=e.placeActivityHourLog.device,a.dataForm.macAddr=e.placeActivityHourLog.macAddr,a.dataForm.createOn=e.placeActivityHourLog.createOn,a.dataForm.createBy=e.placeActivityHourLog.createBy,a.dataForm.updateOn=e.placeActivityHourLog.updateOn,a.dataForm.updateBy=e.placeActivityHourLog.updateBy,a.dataForm.count=e.placeActivityHourLog.count,a.dataForm.placeId=e.placeActivityHourLog.placeId)}))}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(a){a&&t.$http({url:t.$http.adornUrl("/place/placeactivityhourlog/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,activityId:t.dataForm.activityId,ipAddr:t.dataForm.ipAddr,device:t.dataForm.device,macAddr:t.dataForm.macAddr,count:t.dataForm.count,placeId:t.dataForm.placeId})}).then((function(a){var e=a.data;e&&200===e.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(e.msg)}))}))}}},l=o,n=e("2877"),d=Object(n["a"])(l,i,r,!1,null,null,null);a["default"]=d.exports},d024:function(t,a,e){"use strict";var i=e("c65b"),r=e("59ed"),o=e("825a"),l=e("46c4"),n=e("c5cc"),d=e("9bdd"),c=n((function(){var t=this.iterator,a=o(i(this.next,t)),e=this.done=!!a.done;if(!e)return d(t,this.mapper,[a.value,this.counter++],!0)}));t.exports=function(t){return o(this),r(t),new c(l(this),{mapper:t})}},d81d:function(t,a,e){"use strict";var i=e("23e7"),r=e("b727").map,o=e("1dde"),l=o("map");i({target:"Array",proto:!0,forced:!l},{map:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7ddfdfa6","chunk-0506e191","chunk-0506e191"],{1148:function(t,e,a){"use strict";var s=a("5926"),o=a("577e"),r=a("1d80"),i=RangeError;t.exports=function(t){var e=o(r(this)),a="",n=s(t);if(n<0||n===1/0)throw new i("Wrong number of repetitions");for(;n>0;(n>>>=1)&&(e+=e))1&n&&(a+=e);return a}},6672:function(t,e,a){},"7db0":function(t,e,a){"use strict";var s=a("23e7"),o=a("b727").find,r=a("44d2"),i="find",n=!0;i in[]&&Array(1)[i]((function(){n=!1})),s({target:"Array",proto:!0,forced:n},{find:function(t){return o(this,t,arguments.length>1?arguments[1]:void 0)}}),r(i)},a15b:function(t,e,a){"use strict";var s=a("23e7"),o=a("e330"),r=a("44ad"),i=a("fc6a"),n=a("a640"),l=o([].join),c=r!==Object,m=c||!n("join",",");s({target:"Array",proto:!0,forced:m},{join:function(t){return l(i(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var s=a("23e7"),o=a("d024"),r=a("c430");s({target:"Iterator",proto:!0,real:!0,forced:r},{map:o})},b680:function(t,e,a){"use strict";var s=a("23e7"),o=a("e330"),r=a("5926"),i=a("408a"),n=a("1148"),l=a("d039"),c=RangeError,m=String,d=Math.floor,u=o(n),p=o("".slice),h=o(1..toFixed),f=function(t,e,a){return 0===e?a:e%2===1?f(t,e-1,a*t):f(t*t,e/2,a)},g=function(t){var e=0,a=t;while(a>=4096)e+=12,a/=4096;while(a>=2)e+=1,a/=2;return e},v=function(t,e,a){var s=-1,o=a;while(++s<6)o+=e*t[s],t[s]=o%1e7,o=d(o/1e7)},b=function(t,e){var a=6,s=0;while(--a>=0)s+=t[a],t[a]=d(s/e),s=s%e*1e7},F=function(t){var e=6,a="";while(--e>=0)if(""!==a||0===e||0!==t[e]){var s=m(t[e]);a=""===a?s:a+u("0",7-s.length)+s}return a},y=l((function(){return"0.000"!==h(8e-5,3)||"1"!==h(.9,0)||"1.25"!==h(1.255,2)||"1000000000000000128"!==h(0xde0b6b3a7640080,0)}))||!l((function(){h({})}));s({target:"Number",proto:!0,forced:y},{toFixed:function(t){var e,a,s,o,n=i(this),l=r(t),d=[0,0,0,0,0,0],h="",y="0";if(l<0||l>20)throw new c("Incorrect fraction digits");if(n!==n)return"NaN";if(n<=-1e21||n>=1e21)return m(n);if(n<0&&(h="-",n=-n),n>1e-21)if(e=g(n*f(2,69,1))-69,a=e<0?n*f(2,-e,1):n/f(2,e,1),a*=4503599627370496,e=52-e,e>0){v(d,0,a),s=l;while(s>=7)v(d,1e7,0),s-=7;v(d,f(10,s,1),0),s=e-1;while(s>=23)b(d,1<<23),s-=23;b(d,1<<s),v(d,1,1),b(d,2),y=F(d)}else v(d,0,a),v(d,1<<-e,0),y=F(d)+u("0",l);return l>0?(o=y.length,y=h+(o<=l?"0."+u("0",l-o)+y:p(y,0,o-l)+"."+p(y,o-l))):y=h+y,y}})},baa3:function(t,e,a){"use strict";a.r(e);a("b680");var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[t.dataForm.salesmanName?e("div",{staticClass:"page-title",staticStyle:{"margin-bottom":"20px"}},[e("h3",[t._v(t._s(t.dataForm.salesmanName)+" - 业务订单")])]):t._e(),e("el-form",{attrs:{inline:!0,model:t.dataForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.getDataList()}}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"业务员姓名",clearable:""},model:{value:t.dataForm.salesmanName,callback:function(e){t.$set(t.dataForm,"salesmanName",e)},expression:"dataForm.salesmanName"}})],1),e("el-form-item",[e("el-input",{attrs:{placeholder:"用户姓名",clearable:""},model:{value:t.dataForm.userName,callback:function(e){t.$set(t.dataForm,"userName",e)},expression:"dataForm.userName"}})],1),e("el-form-item",[e("el-input",{attrs:{placeholder:"用户手机号",clearable:""},model:{value:t.dataForm.userMobile,callback:function(e){t.$set(t.dataForm,"userMobile",e)},expression:"dataForm.userMobile"}})],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"订单类型",clearable:""},model:{value:t.dataForm.orderType,callback:function(e){t.$set(t.dataForm,"orderType",e)},expression:"dataForm.orderType"}},[e("el-option",{attrs:{label:"充值套餐",value:1}}),e("el-option",{attrs:{label:"活动套餐",value:2}})],1)],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"订单状态",clearable:""},model:{value:t.dataForm.orderStatus,callback:function(e){t.$set(t.dataForm,"orderStatus",e)},expression:"dataForm.orderStatus"}},[e("el-option",{attrs:{label:"待支付",value:0}}),e("el-option",{attrs:{label:"已支付",value:1}}),e("el-option",{attrs:{label:"已取消",value:2}})],1)],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.handleQuery()}}},[t._v("查询")])],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("div",{staticClass:"stats-cards",staticStyle:{"margin-bottom":"20px"}},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v(t._s(t.stats.totalOrders||0))]),e("div",{staticClass:"stats-label"},[t._v("总订单数")])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v("¥"+t._s(t.stats.totalAmount||0))]),e("div",{staticClass:"stats-label"},[t._v("总金额")])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v("¥"+t._s(t.stats.totalPayAmount||0))]),e("div",{staticClass:"stats-label"},[t._v("已付款金额")])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v("¥"+t._s(t.stats.totalCommission||0))]),e("div",{staticClass:"stats-label"},[t._v("总佣金")])])])],1)],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"salesmanName","header-align":"center",align:"center",label:"业务员姓名"}}),e("el-table-column",{attrs:{prop:"orderSn","header-align":"center",align:"center",width:"180",label:"订单编号"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.orderSn||e.row.id)+" ")]}}])}),e("el-table-column",{attrs:{prop:"userName","header-align":"center",align:"center",width:"120",label:"用户姓名"}}),e("el-table-column",{attrs:{prop:"mobile","header-align":"center",align:"center",width:"130",label:"用户手机号"}}),e("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",width:"100",label:"订单状态"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:t.getOrderStatusType(a.row.status)}},[t._v(" "+t._s(t.getOrderStatusDesc(a.row.status))+" ")])]}}])}),e("el-table-column",{attrs:{prop:"rechargeType","header-align":"center",align:"center",width:"100",label:"订单类型"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:4===a.row.rechargeType?"success":"primary"}},[t._v(" "+t._s(4===a.row.rechargeType?"创建活动套餐":"充值套餐")+" ")])]}}])}),e("el-table-column",{attrs:{prop:"payAmount","header-align":"center",align:"center",label:"订单金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" ¥"+t._s(e.row.payAmount||e.row.amount)+" ")]}}])}),e("el-table-column",{attrs:{prop:"commissionRate","header-align":"center",align:"center",label:"佣金比例"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.commissionRate?(100*e.row.commissionRate).toFixed(2)+"%":"-")+" ")]}}])}),e("el-table-column",{attrs:{prop:"commissionAmount","header-align":"center",align:"center",label:"佣金金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.commissionAmount?"¥"+e.row.commissionAmount:"-")+" ")]}}])}),e("el-table-column",{attrs:{prop:"payTime","header-align":"center",align:"center",width:"180",label:"支付时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.payTime||"-")+" ")]}}])}),e("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",width:"180",label:"创建时间"}})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),e("el-dialog",{attrs:{title:"设置佣金",visible:t.commissionDialogVisible,width:"600px"},on:{"update:visible":function(e){t.commissionDialogVisible=e}}},[e("div",{staticStyle:{"margin-bottom":"15px",padding:"10px",background:"#f5f7fa","border-radius":"4px",color:"#606266","font-size":"13px"}},[e("i",{staticClass:"el-icon-info",staticStyle:{color:"#409EFF"}}),e("strong",[t._v("自动计算说明：")]),t._v("修改佣金比例时会自动计算佣金金额，修改佣金金额时会自动计算佣金比例。 ")]),e("el-form",{ref:"commissionForm",attrs:{model:t.commissionForm,rules:t.commissionRules,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"订单金额",prop:"orderAmount"}},[e("el-input-number",{attrs:{precision:2,min:0,disabled:"",placeholder:"订单金额"},model:{value:t.commissionForm.orderAmount,callback:function(e){t.$set(t.commissionForm,"orderAmount",e)},expression:"commissionForm.orderAmount"}}),e("span",{staticStyle:{"margin-left":"10px",color:"#999"}},[t._v("订单总金额")])],1),e("el-form-item",{attrs:{label:"佣金比例",prop:"commissionRate"}},[e("el-input-number",{attrs:{precision:4,step:.01,max:1,min:0,placeholder:"请输入佣金比例（0-1之间）"},on:{change:t.onCommissionRateChange},model:{value:t.commissionForm.commissionRate,callback:function(e){t.$set(t.commissionForm,"commissionRate",e)},expression:"commissionForm.commissionRate"}}),e("span",{staticStyle:{"margin-left":"10px",color:"#999"}},[t._v("例：0.05 表示 5%，修改后自动计算金额")])],1),e("el-form-item",{attrs:{label:"佣金金额",prop:"commissionAmount"}},[e("el-input-number",{attrs:{precision:2,step:.01,min:0,placeholder:"请输入佣金金额"},on:{change:t.onCommissionAmountChange},model:{value:t.commissionForm.commissionAmount,callback:function(e){t.$set(t.commissionForm,"commissionAmount",e)},expression:"commissionForm.commissionAmount"}}),e("span",{staticStyle:{"margin-left":"10px",color:"#999"}},[t._v("修改后自动计算比例")])],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.commissionDialogVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.updateCommission()}}},[t._v("确定")])],1)],1)],1)},o=[],r=(a("99af"),a("7db0"),a("a15b"),a("d81d"),a("d3b7"),a("0643"),a("fffc"),a("a573"),{data:function(){return{dataForm:{salesmanName:"",userName:"",userMobile:"",orderType:"",orderStatus:"",userId:"",salesmanId:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],stats:{},overallStats:{},commissionDialogVisible:!1,commissionForm:{id:"",orderAmount:0,commissionRate:0,commissionAmount:0},commissionRules:{commissionRate:[{required:!0,message:"请输入佣金比例",trigger:"blur"}],commissionAmount:[{required:!0,message:"请输入佣金金额",trigger:"blur"}]}}},activated:function(){this.initFromQuery(),this.getDataList(),this.getStats(),this.getOverallStats()},mounted:function(){this.initFromQuery()},methods:{initFromQuery:function(){var t=this.$route.query;t.salesmanId&&(this.dataForm.salesmanId=t.salesmanId),t.userId&&(this.dataForm.userId=t.userId),t.salesmanName&&(this.dataForm.salesmanName=t.salesmanName)},handleQuery:function(){this.getDataList(),this.getStats()},getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/activity/rechargerecord/salesmanOrderList"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,salesmanName:this.dataForm.salesmanName,userName:this.dataForm.userName,userMobile:this.dataForm.userMobile,salesmanId:this.dataForm.salesmanId,orderType:this.dataForm.orderType,userId:this.dataForm.userId,orderStatus:this.dataForm.orderStatus})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},getStats:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/rechargerecord/salesmanOrderStats"),method:"get",params:this.$http.adornParams({salesmanName:this.dataForm.salesmanName,userName:this.dataForm.userName,userMobile:this.dataForm.userMobile,salesmanId:this.dataForm.salesmanId,orderType:this.dataForm.orderType,userId:this.dataForm.userId,orderStatus:this.dataForm.orderStatus})}).then((function(e){var a=e.data;a&&200===a.code&&(t.stats=a.stats,t.statsVisible=!0)}))},getOverallStats:function(){var t=this;this.$http({url:this.$http.adornUrl("/salesman/salesman/orderStats"),method:"get"}).then((function(e){var a=e.data;a&&200===a.code&&(t.overallStats=a.stats)}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},updateCommissionHandle:function(t){this.$refs["commissionForm"]&&this.$refs["commissionForm"].resetFields();var e=this.dataList.find((function(e){return e.id===t}));e&&(this.commissionForm.id=t,this.commissionForm.orderAmount=parseFloat(e.orderAmount)||0,this.commissionForm.commissionRate=parseFloat(e.commissionRate)||0,this.commissionForm.commissionAmount=parseFloat(e.commissionAmount)||0),this.commissionDialogVisible=!0},onCommissionRateChange:function(t){if(null!==t&&void 0!==t&&this.commissionForm.orderAmount>0){var e=parseFloat((this.commissionForm.orderAmount*t).toFixed(2));this.commissionForm.commissionAmount=e}},onCommissionAmountChange:function(t){if(null!==t&&void 0!==t&&this.commissionForm.orderAmount>0){var e=parseFloat((t/this.commissionForm.orderAmount).toFixed(4));this.commissionForm.commissionRate=e}},updateCommission:function(){var t=this;this.$refs["commissionForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/activity/rechargerecord/updateSalesmanOrderCommission"),method:"post",data:t.$http.adornData(t.commissionForm)}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.commissionDialogVisible=!1,t.getDataList()}}):t.$message.error(a.msg)}))}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/activity/rechargerecord/deleteSalesmanOrder"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},getOrderStatusType:function(t){switch(t){case 0:return"warning";case 1:return"success";case 2:return"danger";case 3:return"info";default:return"info"}},getOrderStatusDesc:function(t){switch(t){case 0:return"待支付";case 1:return"已支付";case 2:return"已取消";case 3:return"已退款";default:return"未知"}}}}),i=r,n=(a("dcc2"),a("2877")),l=Object(n["a"])(i,s,o,!1,null,"9f45a402",null);e["default"]=l.exports},d024:function(t,e,a){"use strict";var s=a("c65b"),o=a("59ed"),r=a("825a"),i=a("46c4"),n=a("c5cc"),l=a("9bdd"),c=n((function(){var t=this.iterator,e=r(s(this.next,t)),a=this.done=!!e.done;if(!a)return l(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return r(this),o(t),new c(i(this),{mapper:t})}},d81d:function(t,e,a){"use strict";var s=a("23e7"),o=a("b727").map,r=a("1dde"),i=r("map");s({target:"Array",proto:!0,forced:!i},{map:function(t){return o(this,t,arguments.length>1?arguments[1]:void 0)}})},dcc2:function(t,e,a){"use strict";a("6672")},f665:function(t,e,a){"use strict";var s=a("23e7"),o=a("2266"),r=a("59ed"),i=a("825a"),n=a("46c4");s({target:"Iterator",proto:!0,real:!0},{find:function(t){i(this),r(t);var e=n(this),a=0;return o(e,(function(e,s){if(t(e,a++))return s(e)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},fffc:function(t,e,a){"use strict";a("f665")}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-44302a78","chunk-2d231253"],{a15b:function(e,t,a){"use strict";var i=a("23e7"),r=a("e330"),n=a("44ad"),o=a("fc6a"),l=a("a640"),c=r([].join),d=n!==Object,s=d||!l("join",",");i({target:"Array",proto:!0,forced:s},{join:function(e){return c(o(this),void 0===e?",":e)}})},a573:function(e,t,a){"use strict";a("ab43")},ab43:function(e,t,a){"use strict";var i=a("23e7"),r=a("d024"),n=a("c430");i({target:"Iterator",proto:!0,real:!0,forced:n},{map:r})},c1a6:function(e,t,a){"use strict";a.r(t);a("b0c0");var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"mod-config"},[t("el-form",{attrs:{inline:!0,model:e.dataForm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.onSearch()}}},[t("el-form-item",[t("el-input",{attrs:{placeholder:"关键词",clearable:""},model:{value:e.dataForm.name,callback:function(t){e.$set(e.dataForm,"name",t)},expression:"dataForm.name"}})],1),t("el-form-item",[t("el-button",{on:{click:function(t){return e.onSearch()}}},[e._v("查询")]),e.isAuth("activity:activitysettleinvoicelog:save")?t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addOrUpdateHandle()}}},[e._v("新增")]):e._e(),e.isAuth("activity:activitysettleinvoicelog:delete")?t("el-button",{attrs:{type:"danger",disabled:e.dataListSelections.length<=0},on:{click:function(t){return e.deleteHandle()}}},[e._v("批量删除")]):e._e()],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:e.dataList,border:""},on:{"selection-change":e.selectionChangeHandle}},[t("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),t("el-table-column",{attrs:{prop:"price","header-align":"center",align:"center",label:"到款金额"}}),t("el-table-column",{attrs:{prop:"remarks","header-align":"center",align:"center",label:"备注"}}),t("el-table-column",{attrs:{prop:"activitySettleId","header-align":"center",align:"center",label:"结算表ID"}}),t("el-table-column",{attrs:{prop:"createOn","show-overflow-tooltip":!0,"header-align":"center",align:"center",label:"创建时间"}}),t("el-table-column",{attrs:{prop:"updateOn","show-overflow-tooltip":!0,"header-align":"center",align:"center",label:"更新时间"}}),t("el-table-column",{attrs:{prop:"priceBankId","header-align":"center",align:"center",label:"银行账户ID"}}),t("el-table-column",{attrs:{prop:"payTime","header-align":"center",align:"center",label:"支付时间"}}),t("el-table-column",{attrs:{prop:"invoiceBillType","header-align":"center",align:"center",label:"开票类型"}}),t("el-table-column",{attrs:{prop:"invoiceType","header-align":"center",align:"center",label:"发票类型"}}),t("el-table-column",{attrs:{prop:"redActivitySettleInvoiceLogId","header-align":"center",align:"center",label:"红冲数据"}}),t("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.addOrUpdateHandle(a.row.id)}}},[e._v("修改")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHandle(a.row.id)}}},[e._v("删除")])]}}])})],1),t("el-pagination",{attrs:{"current-page":e.pageIndex,"page-sizes":[10,20,50,100],"page-size":e.pageSize,total:e.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle}}),e.addOrUpdateVisible?t("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:e.getDataList}}):e._e()],1)},r=[],n=(a("99af"),a("a15b"),a("d81d"),a("a573"),a("eefc")),o={data:function(){return{dataForm:{name:"",appid:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1}},components:{AddOrUpdate:n["default"]},activated:function(){this.getDataList()},methods:{onSearch:function(){this.pageIndex=1,this.getDataList()},getDataList:function(){var e=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/activity/activitysettleinvoicelog/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,name:this.dataForm.name,appid:this.$cookie.get("appid")})}).then((function(t){var a=t.data;a&&200===a.code?(e.dataList=a.page.list,e.totalPage=a.page.totalCount):(e.dataList=[],e.totalPage=0),e.dataListLoading=!1}))},sizeChangeHandle:function(e){this.pageSize=e,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(e){this.pageIndex=e,this.getDataList()},selectionChangeHandle:function(e){this.dataListSelections=e},addOrUpdateHandle:function(e){var t=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){t.$refs.addOrUpdate.init(e)}))},deleteHandle:function(e){var t=this,a=e?[e]:this.dataListSelections.map((function(e){return e.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(e?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:t.$http.adornUrl("/activity/activitysettleinvoicelog/delete"),method:"post",data:t.$http.adornData(a,!1)}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.getDataList()}}):t.$message.error(a.msg)}))}))}}},l=o,c=a("2877"),d=Object(c["a"])(l,i,r,!1,null,null,null);t["default"]=d.exports},d024:function(e,t,a){"use strict";var i=a("c65b"),r=a("59ed"),n=a("825a"),o=a("46c4"),l=a("c5cc"),c=a("9bdd"),d=l((function(){var e=this.iterator,t=n(i(this.next,e)),a=this.done=!!t.done;if(!a)return c(e,this.mapper,[t.value,this.counter++],!0)}));e.exports=function(e){return n(this),r(e),new d(o(this),{mapper:e})}},d81d:function(e,t,a){"use strict";var i=a("23e7"),r=a("b727").map,n=a("1dde"),o=n("map");i({target:"Array",proto:!0,forced:!o},{map:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}})},eefc:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.dataFormSubmit()}}},[t("el-form-item",{attrs:{label:"到款金额",prop:"price"}},[t("el-input",{attrs:{placeholder:"到款金额"},model:{value:e.dataForm.price,callback:function(t){e.$set(e.dataForm,"price",t)},expression:"dataForm.price"}})],1),t("el-form-item",{attrs:{label:"备注",prop:"remarks"}},[t("el-input",{attrs:{placeholder:"备注"},model:{value:e.dataForm.remarks,callback:function(t){e.$set(e.dataForm,"remarks",t)},expression:"dataForm.remarks"}})],1),t("el-form-item",{attrs:{label:"结算表ID",prop:"activitySettleId"}},[t("el-input",{attrs:{placeholder:"结算表ID"},model:{value:e.dataForm.activitySettleId,callback:function(t){e.$set(e.dataForm,"activitySettleId",t)},expression:"dataForm.activitySettleId"}})],1),t("el-form-item",{attrs:{label:"银行账户ID",prop:"priceBankId"}},[t("el-input",{attrs:{placeholder:"银行账户ID"},model:{value:e.dataForm.priceBankId,callback:function(t){e.$set(e.dataForm,"priceBankId",t)},expression:"dataForm.priceBankId"}})],1),t("el-form-item",{attrs:{label:"支付时间",prop:"payTime"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"请选择支付时间","value-format":"yyyy/MM/dd HH:mm:ss"},model:{value:e.dataForm.payTime,callback:function(t){e.$set(e.dataForm,"payTime",t)},expression:"dataForm.payTime"}})],1),t("el-form-item",{attrs:{label:"开票类型",prop:"invoiceBillType"}},[t("el-input",{attrs:{placeholder:"开票类型"},model:{value:e.dataForm.invoiceBillType,callback:function(t){e.$set(e.dataForm,"invoiceBillType",t)},expression:"dataForm.invoiceBillType"}})],1),t("el-form-item",{attrs:{label:"发票类型",prop:"invoiceType"}},[t("el-input",{attrs:{placeholder:"发票类型"},model:{value:e.dataForm.invoiceType,callback:function(t){e.$set(e.dataForm,"invoiceType",t)},expression:"dataForm.invoiceType"}})],1),t("el-form-item",{attrs:{label:"红冲数据",prop:"redActivitySettleInvoiceLogId"}},[t("el-input",{attrs:{placeholder:"红冲数据"},model:{value:e.dataForm.redActivitySettleInvoiceLogId,callback:function(t){e.$set(e.dataForm,"redActivitySettleInvoiceLogId",t)},expression:"dataForm.redActivitySettleInvoiceLogId"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},r=[],n={data:function(){return{visible:!1,dataForm:{repeatToken:"",id:0,price:"",remarks:"",activitySettleId:"",priceBankId:"",payTime:"",invoiceBillType:"",invoiceType:"",redActivitySettleInvoiceLogId:""},dataRule:{price:[{required:!0,message:"到款金额不能为空",trigger:"blur"}],remarks:[{required:!0,message:"备注不能为空",trigger:"blur"}],activitySettleId:[{required:!0,message:"结算表ID不能为空",trigger:"blur"}],priceBankId:[{required:!0,message:"银行账户ID不能为空",trigger:"blur"}],payTime:[{required:!0,message:"支付时间不能为空",trigger:"blur"}],invoiceBillType:[{required:!0,message:"开票类型不能为空",trigger:"blur"}],invoiceType:[{required:!0,message:"发票类型不能为空",trigger:"blur"}],redActivitySettleInvoiceLogId:[{required:!0,message:"红冲数据不能为空",trigger:"blur"}]}}},methods:{init:function(e){var t=this;this.getToken(),this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){t.$refs["dataForm"].resetFields(),t.dataForm.id&&t.$http({url:t.$http.adornUrl("/activity/activitysettleinvoicelog/info/".concat(t.dataForm.id)),method:"get",params:t.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.price=a.activitySettleInvoiceLog.price,t.dataForm.remarks=a.activitySettleInvoiceLog.remarks,t.dataForm.activitySettleId=a.activitySettleInvoiceLog.activitySettleId,t.dataForm.priceBankId=a.activitySettleInvoiceLog.priceBankId,t.dataForm.payTime=a.activitySettleInvoiceLog.payTime,t.dataForm.invoiceBillType=a.activitySettleInvoiceLog.invoiceBillType,t.dataForm.invoiceType=a.activitySettleInvoiceLog.invoiceType,t.dataForm.redActivitySettleInvoiceLogId=a.activitySettleInvoiceLog.redActivitySettleInvoiceLogId)}))}))},getToken:function(){var e=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm.repeatToken=a.result)}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$http({url:e.$http.adornUrl("/activity/activitysettleinvoicelog/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({repeatToken:e.dataForm.repeatToken,id:e.dataForm.id||void 0,price:e.dataForm.price,remarks:e.dataForm.remarks,appid:e.$cookie.get("appid"),activitySettleId:e.dataForm.activitySettleId,priceBankId:e.dataForm.priceBankId,payTime:e.dataForm.payTime,invoiceBillType:e.dataForm.invoiceBillType,invoiceType:e.dataForm.invoiceType,redActivitySettleInvoiceLogId:e.dataForm.redActivitySettleInvoiceLogId})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):(e.$message.error(a.msg),"不能重复提交"!=a.msg&&e.getToken())}))}))}}},o=n,l=a("2877"),c=Object(l["a"])(o,i,r,!1,null,null,null);t["default"]=c.exports}}]);
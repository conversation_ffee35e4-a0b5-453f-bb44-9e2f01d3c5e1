(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0a17b914","chunk-37a545c8"],{"1b69":function(t,a,e){"use strict";e.r(a);e("7f7f");var i,s=function(){var t=this,a=t._self._c;return a("div",{staticStyle:{background:"white","margin-bottom":"20px"}},[a("van-row",{attrs:{gutter:"20"}},[a("van-col",{attrs:{span:"16"}},[a("van-swipe",{staticStyle:{width:"100%"},attrs:{autoplay:3e3}},t._l(t.activityInfo.appFileList,(function(t,e){return a("van-swipe-item",{key:e},[a("van-image",{attrs:{width:"100%",src:t.url}})],1)})),1)],1),a("van-col",{attrs:{span:"8"}},[a("div",{staticStyle:{"margin-top":"20px"}},[a("van-cell",{attrs:{title:"会议名称",value:t.activityInfo.name}}),t.activityInfo.address?a("van-cell",{attrs:{title:"会议地点",value:t.activityInfo.address}}):t._e(),a("van-cell",{attrs:{title:"会议开始时间",value:t.activityInfo.startTime}}),a("van-cell",{attrs:{title:"会议结束时间",value:t.activityInfo.endTime}}),a("van-cell",{attrs:{title:"登录信息"},scopedSlots:t._u([{key:"default",fn:function(){return[t.userInfo?a("van-button",{attrs:{type:"primary",size:"small",round:""}},[t._v(t._s(t.userInfo.username)+"("+t._s(t.userInfo.mobile)+")")]):a("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.showLogin}},[t._v("未登录(点击登录)")])]},proxy:!0}])}),"1736999159118508033"!=t.activityId?a("van-cell",{attrs:{title:"报名状态"},scopedSlots:t._u([{key:"default",fn:function(){return[1==t.isPay?a("van-button",{attrs:{type:"primary",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名")]):2==t.isPay?a("van-button",{attrs:{type:"warning",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名未交费")]):a("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.turnApply}},[t._v("未报名")])]},proxy:!0}],null,!1,567203517)}):t._e()],1)])],1),a("van-tabs",{staticClass:"nav",on:{click:t.onClick},model:{value:t.cmsId,callback:function(a){t.cmsId=a},expression:"cmsId"}},t._l(t.cmsList,(function(t){return a("van-tab",{key:t.id,attrs:{title:t.title,name:t.id}})})),1),a("pclogin")],1)},n=[],r=e("ade3"),c=(e("a481"),e("6762"),e("2fdb"),e("cacf")),o=e("7dcb"),l=function(){var t=this,a=t._self._c;return a("van-dialog",{attrs:{title:"登录操作",width:"400px"},on:{confirm:t.login},model:{value:t.showPcLogin,callback:function(a){t.showPcLogin=a},expression:"showPcLogin"}},[a("div",{staticClass:"text-center padding"},[a("van-cell-group",{attrs:{inset:""}},[a("van-field",{attrs:{name:"手机号",label:"手机号",required:"",placeholder:"手机号",rules:[{required:!0,message:"请填写手机号"}]},model:{value:t.mobile,callback:function(a){t.mobile=a},expression:"mobile"}}),"1736999159118508033"!=t.activityId?a("van-field",{attrs:{center:"",clearable:"",maxlength:"6",label:"短信验证码",required:"",placeholder:"请输入短信验证码"},scopedSlots:t._u([{key:"button",fn:function(){return[a("van-button",{attrs:{size:"small",type:"primary",disabled:t.waiting},on:{click:function(a){return t.doSendSmsCode()}}},[t.waiting?a("span",[t._v(t._s(t.waitingTime)+"秒后重发")]):a("span",{staticStyle:{"font-size":"13px"}},[t._v("获取验证码")])])]},proxy:!0}],null,!1,850170227),model:{value:t.code,callback:function(a){t.code=a},expression:"code"}}):t._e()],1)],1)])},h=[],d={name:"pclogin",computed:{showPcLogin:{get:function(){return this.$store.state.user.showPcLogin},set:function(t){this.$store.commit("user/changePcLogin",t)}}},data:function(){return{activityId:void 0,waiting:!1,waitingTime:60,mobile:"",code:""}},mounted:function(){this.activityId=this.$route.query.id},methods:{login:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?this.code||0x181b0f883e693000==this.activityId?/^\d{6}$/.test(this.code)||0x181b0f883e693000==this.activityId?void this.$fly.post("/pyp/web/user/pcLogin",{mobile:this.mobile,code:0x181b0f883e693000==this.activityId?"121212":this.code}).then((function(a){a&&200===a.code?(vant.Toast("登录成功"),t.$store.commit("user/update",a.userInfo),t.showPcLogin=!1,"applyIndex"!=t.$route.name&&"livesDetail"!=t.$route.name||location.reload()):vant.Toast(a.msg)})):(vant.Toast("验证码错误"),!1):(vant.Toast("请输入验证码"),!1):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},doSendSmsCode:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.mobile,activityId:this.activityId}).then((function(a){a&&200===a.code?(t.countdown(),vant.Toast("发送验证码成功")):vant.Toast(a.msg)})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var t=this;this.waiting=!0;var a=window.setInterval((function(){t.waitingTime--,t.waitingTime<0&&(window.clearInterval(a),t.waitingTime=60,t.waiting=!1)}),1e3)}}},u=d,m=e("2877"),v=Object(m["a"])(u,l,h,!1,null,null,null),p=v.exports,f={components:{pclogin:p},data:function(){return{activityId:void 0,bannerIndex:0,cmsId:void 0,cmsList:[],cmsInfo:{},activityInfo:{}}},computed:{userInfo:{get:function(){return this.$store.state.user.userInfo},set:function(t){this.$store.commit("user/update",t)}},isPay:{get:function(){return this.$store.state.apply.isPay},set:function(t){this.$store.commit("apply/update",t)}}},mounted:function(){var t=(new Date).getTime();localStorage.setItem("logAddTime",t+36e5),this.openid=this.$cookie.get("openid"),this.activityId=this.$route.query.id,this.cmsId=sessionStorage.getItem("cmsId")||this.$route.query.cmsId,this.checkLogin(),this.getActivityInfo(),this.activityLogCount()},methods:(i={showLogin:function(){this.$store.commit("user/changePcLogin",!0)},checkLogin:function(){var t=this;this.$fly.get("/pyp/web/user/checkLogin").then((function(a){t.userInfo=a.result,t.userInfo?t.checkApply():t.isPay=0}))},bannerIndexChange:function(t){this.bannerIndex=t},checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(a){200==a.code?(t.isPay=a.isPay,1==t.isPay||2==t.isPay&&vant.Dialog.alert({title:"提示",message:"您有一笔注册费待支付，点击跳转"}).then((function(){t.$router.push({name:"applySuccess",query:{orderId:a.result,id:t.activityId}})}))):vant.Toast(a.msg)}))},changeShowType:function(t){this.$emit("changeShowType",t)},turnApply:function(){var t=this.cmsList.filter((function(t){return t.model&&t.model.includes("applyIndex")}));if(t){var a=t[0];sessionStorage.setItem("cmsId",a.id);var e=a.model.replace("${activityId}",a.activityId);this.$router.push(JSON.parse(e))}else vant.Toast("暂未开启报名")},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(a){if(t.loading=!1,200==a.code){t.activityInfo=a.activity,document.title=t.activityInfo.name;var e=t.activityInfo.startTime,i=new Date(e.replace(/-/g,"/")),s=new Date,n=i.getTime()-s.getTime();t.dateCompare=n>0?n:0,t.getCmsList()}}))},activityLogCount:function(){var t=localStorage.getItem("logAddTime"),a=(new Date).getTime();a>t&&this.$fly.post("/pyp/activity/activityviewlog/count",{activityId:this.activityId,device:o["a"].getVersion()}).then((function(t){}))},getCmsList:function(){var t=this;this.$fly.get("/pyp/cms/cms/findByActivityId/".concat(this.activityId)).then((function(a){t.loading=!1,200==a.code?(t.cmsList=a.result,t.cmsId?t.changeShowType(t.cmsId):t.changeShowType(t.cmsList[0].id)):(vant.Toast(a.msg),t.cmsList=[])}))},onClick:function(t){if(console.log(t),sessionStorage.setItem("cmsId",t),this.cmsInfo=this.cmsList.filter((function(a){return a.id==t}))[0],this.cmsInfo.url&&Object(c["d"])(this.cmsInfo.url))location.href=this.cmsInfo.url;else if(this.cmsInfo.model&&this.isJSON(this.cmsInfo.model)){var a=this.cmsInfo.model.replace("${activityId}",this.cmsInfo.activityId);this.$router.push(JSON.parse(a))}else"cmsIndex"==this.$route.name?this.changeShowType(t):this.$router.push({name:"cmsIndex",query:{id:this.activityId,cmsId:t}})}},Object(r["a"])(i,"bannerIndexChange",(function(t){this.bannerIndex=t})),Object(r["a"])(i,"isJSON",(function(t){try{return JSON.parse(t),!0}catch(a){return!1}})),i)},y=f,g=(e("dd7a"),Object(m["a"])(y,s,n,!1,null,"7bd3d808",null));a["default"]=g.exports},"669d":function(t,a,e){},"66c7":function(t,a,e){"use strict";e("4917"),e("a481");var i=/([yMdhsm])(\1*)/g,s="yyyy-MM-dd";function n(t,a){a-=(t+"").length;for(var e=0;e<a;e++)t="0"+t;return t}a["a"]={formatDate:{format:function(t,a){return a=a||s,a.replace(i,(function(a){switch(a.charAt(0)){case"y":return n(t.getFullYear(),a.length);case"M":return n(t.getMonth()+1,a.length);case"d":return n(t.getDate(),a.length);case"w":return t.getDay()+1;case"h":return n(t.getHours(),a.length);case"m":return n(t.getMinutes(),a.length);case"s":return n(t.getSeconds(),a.length)}}))},parse:function(t,a){var e=a.match(i),s=t.match(/(\d)+/g);if(e.length==s.length){for(var n=new Date(1970,0,1),r=0;r<e.length;r++){var c=parseInt(s[r]),o=e[r];switch(o.charAt(0)){case"y":n.setFullYear(c);break;case"M":n.setMonth(c-1);break;case"d":n.setDate(c);break;case"h":n.setHours(c);break;case"m":n.setMinutes(c);break;case"s":n.setSeconds(c);break}}return n}return null},toWeek:function(t){var a=new Date(t).getDay(),e="";switch(a){case 0:e="s";break;case 1:e="m";break;case 2:e="t";break;case 3:e="w";break;case 4:e="t";break;case 5:e="f";break;case 6:e="s";break}return e}},toUserLook:function(t){var a=Math.floor(t/3600%24),e=Math.floor(t/60%60);return a<1?e+"分":a+"时"+e+"分"}}},"7dcb":function(t,a,e){"use strict";e("a481"),e("4917");a["a"]={getVersion:function(){var t=navigator.userAgent||window.navigator.userAgent,a=/HUAWEI|HONOR/gi,e=/[^;]+(?= Build)/gi,i=/CPU iPhone OS \d[_\d]*/gi,s=/CPU OS \d[_\d]*/gi,n=/Windows NT \d[\.\d]*/gi,r=/Linux x\d[_\d]*/gi,c=/Mac OS X \d[_|\.\d]*/gi;return/Android/gi.test(t)?a.test(t)?t.match(a)[0]+t.match(e)[0]:t.match(e)[0]:/iPhone/gi.test(t)?t.match(i)[0].replace(/CPU iPhone OS/gi,"iPhone").replace(/_/g,"."):/iPad/gi.test(t)?t.match(s)[0].replace(/CPU OS/gi,"iPad").replace(/_/g,"."):/Windows/gi.test(t)?"Windows "+(Math.min(parseInt(t.match(n)[0].replace(/Windows NT /,""))+1,10)<7?"XP":Math.min(parseInt(t.match(n)[0].replace(/Windows NT /,""))+1,10)):/Linux/gi.test(t)?t.match(r)[0]:/Macintosh/gi.test(t)?t.match(c)[0].replace(/_/g,"."):"unknown"}}},"8cd9":function(t,a,e){"use strict";e.r(a);var i=function(){var t=this,a=t._self._c;return a("div",{class:t.isMobilePhone?"":"pc-container"},[t.isMobilePhone?t._e():a("pcheader"),a("div",{staticClass:"page-header"},[a("van-nav-bar",{attrs:{title:"航班改签申请","left-arrow":""},on:{"click-left":t.goBack}})],1),a("div",{staticClass:"trip-info-card"},[a("div",{staticClass:"card-title"},[t._v("原行程信息")]),a("div",{staticClass:"flight-info"},[a("div",{staticClass:"flight-route"},[a("span",{staticClass:"city"},[t._v(t._s(t.tripInfo.inStartPlace))]),a("van-icon",{attrs:{name:"arrow"}}),a("span",{staticClass:"city"},[t._v(t._s(t.tripInfo.inEndPlace))])],1),a("div",{staticClass:"flight-details"},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v(t._s(0==t.tripInfo.inType?"航班号：":"车次："))]),a("span",{staticClass:"value"},[t._v(t._s(t.tripInfo.inNumber))])]),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v(t._s(0==t.tripInfo.inType?"起飞时间：":"出发时间："))]),a("span",{staticClass:"value"},[t._v(t._s(t.formatDateTime(t.tripInfo.inStartDate)))])]),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v(t._s(0==t.tripInfo.inType?"到达时间：":"到站时间："))]),a("span",{staticClass:"value"},[t._v(t._s(t.formatDateTime(t.tripInfo.inEndDate)))])])])])]),a("div",{staticClass:"change-form"},[a("div",{staticClass:"form-title"},[t._v("改签信息")]),a("div",{staticClass:"travel-info-input"},[a("div",{staticClass:"location-selector"},[a("div",{staticClass:"location from",on:{click:function(a){return t.showCitySelector("start")}}},[a("div",{staticClass:"location-text"},[t._v(t._s(t.searchParams.inStartPlace||"出发城市"))])]),a("div",{staticClass:"location-arrow"},[a("van-icon",{attrs:{name:"arrow"}})],1),a("div",{staticClass:"location to",on:{click:function(a){return t.showCitySelector("end")}}},[a("div",{staticClass:"location-text"},[t._v(t._s(t.searchParams.inEndPlace||"到达城市"))])])]),a("div",{staticClass:"date-selector",on:{click:function(a){t.showTravelDatePicker=!0}}},[a("div",{staticClass:"date"},[t._v("\n                    "+t._s(t.formatDisplayDate(t.searchParams.travelDate)||"请选择日期")+"\n                ")]),a("van-icon",{attrs:{name:"calendar-o"}})],1),a("div",{staticClass:"search-button",on:{click:t.goToSelectTransport}},[a("span",[t._v(t._s(0==t.tripInfo.inType?"搜索机票":"搜索火车票"))])])]),t.hasSelectedTransport?a("div",{staticClass:"selected-transport"},[a("div",{staticClass:"card-title"},[t._v(t._s(0==t.tripInfo.inType?"已选择航班":"已选择火车票"))]),a("div",{staticClass:"transport-info"},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v(t._s(0==t.tripInfo.inType?"航班号：":"车次："))]),a("span",{staticClass:"value"},[t._v(t._s(t.changeForm.chaNumber))])]),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v("出发地：")]),a("span",{staticClass:"value"},[t._v(t._s(t.changeForm.chaStartPlace))])]),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v("目的地：")]),a("span",{staticClass:"value"},[t._v(t._s(t.changeForm.chaEndPlace))])]),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v(t._s(0==t.tripInfo.inType?"起飞时间：":"出发时间："))]),a("span",{staticClass:"value"},[t._v(t._s(t.formatDateTime(t.changeForm.chaStartDate)))])]),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v(t._s(0==t.tripInfo.inType?"到达时间：":"到站时间："))]),a("span",{staticClass:"value"},[t._v(t._s(t.formatDateTime(t.changeForm.chaEndDate)))])])]),a("div",{staticStyle:{margin:"16px"}},[a("van-button",{attrs:{round:"",block:"",type:"info"},on:{click:t.onSubmit}},[t._v("提交改签申请")])],1),a("div",{staticStyle:{margin:"16px"}},[a("van-button",{attrs:{round:"",block:"",type:"default"},on:{click:t.goBack}},[t._v("取消")])],1)]):t._e()]),a("van-popup",{style:{height:"70%"},attrs:{position:"bottom",round:""},model:{value:t.showStartSearch,callback:function(a){t.showStartSearch=a},expression:"showStartSearch"}},[a("div",{staticClass:"search-header"},[a("van-search",{attrs:{placeholder:"请输入城市名称或机场/车站名称",shape:"round",background:"#f7f8fa",autofocus:""},on:{input:t.searchStartPlaces},model:{value:t.startSearchText,callback:function(a){t.startSearchText=a},expression:"startSearchText"}}),a("van-icon",{staticClass:"close-icon",attrs:{name:"cross"},on:{click:function(a){t.showStartSearch=!1}}})],1),a("div",{staticClass:"search-results"},[0===t.startSearchResults.length&&t.startSearchText&&!t.startLoading?a("div",{staticClass:"empty-result"},[a("van-empty",{attrs:{description:"暂无搜索结果"}})],1):t._e(),t._l(t.startSearchResults,(function(e,i){return a("div",{key:i,staticClass:"search-item",on:{click:function(a){return t.selectStartPlace(e)}}},[a("div",{staticClass:"item-main"},[t._v(t._s(0==t.tripInfo.inType?e.airportName:e.stationName))]),a("div",{staticClass:"item-sub"},[t._v(t._s(e.cityName))])])}))],2)]),a("van-popup",{style:{height:"70%"},attrs:{position:"bottom",round:""},model:{value:t.showEndSearch,callback:function(a){t.showEndSearch=a},expression:"showEndSearch"}},[a("div",{staticClass:"search-header"},[a("van-search",{attrs:{placeholder:"请输入城市名称或机场/车站名称",shape:"round",background:"#f7f8fa",autofocus:""},on:{input:t.searchEndPlaces},model:{value:t.endSearchText,callback:function(a){t.endSearchText=a},expression:"endSearchText"}}),a("van-icon",{staticClass:"close-icon",attrs:{name:"cross"},on:{click:function(a){t.showEndSearch=!1}}})],1),a("div",{staticClass:"search-results"},[0===t.endSearchResults.length&&t.endSearchText&&!t.endLoading?a("div",{staticClass:"empty-result"},[a("van-empty",{attrs:{description:"暂无搜索结果"}})],1):t._e(),t._l(t.endSearchResults,(function(e,i){return a("div",{key:i,staticClass:"search-item",on:{click:function(a){return t.selectEndPlace(e)}}},[a("div",{staticClass:"item-main"},[t._v(t._s(0==t.tripInfo.inType?e.airportName:e.stationName))]),a("div",{staticClass:"item-sub"},[t._v(t._s(e.cityName))])])}))],2)]),a("van-popup",{attrs:{position:"bottom"},model:{value:t.showTravelDatePicker,callback:function(a){t.showTravelDatePicker=a},expression:"showTravelDatePicker"}},[a("van-datetime-picker",{attrs:{type:"date",title:"选择出行日期","min-date":t.minDate,"max-date":t.maxDate},on:{confirm:t.onTravelDateConfirm,cancel:function(a){t.showTravelDatePicker=!1}},model:{value:t.travelDate,callback:function(a){t.travelDate=a},expression:"travelDate"}})],1)],1)},s=[],n=e("66c7"),r=e("cacf"),c=e("1b69"),o={components:{pcheader:c["default"]},data:function(){return{isMobilePhone:Object(r["c"])(),id:void 0,tripId:void 0,tripInfo:{},loading:!1,showDatePicker:!1,showTravelDatePicker:!1,minDate:new Date(2024,0,1),maxDate:new Date(2030,10,1),currentDate:new Date,travelDate:new Date,hasSelectedTransport:!1,showStartSearch:!1,showEndSearch:!1,startSearchText:"",endSearchText:"",startSearchResults:[],endSearchResults:[],startLoading:!1,endLoading:!1,searchParams:{startCity:"",startCityCode:"",endCity:"",endCityCode:"",travelDate:""},changeForm:{chaDate:"",chaNumber:"",chaEndPlace:"",chaEndDate:"",chaStartDate:"",chaPrice:"",chaStartPlace:"",chaStartCityCode:"",chaEndCityCode:"",chaStartCity:"",chaEndCity:"",chaStartTerminal:"",chaEndTerminal:"",chaTicketNo:"",cabinCode:"",cabinBookPara:"",tripId:""}}},mounted:function(){this.id=this.$route.query.detailId,this.tripId=this.$route.query.tripId,this.changeForm.tripId=this.tripId,this.tripId?this.getTripInfo():(this.goBack(),vant.Toast("行程信息不存在")),this.checkSelectedTransport()},activated:function(){this.checkSelectedTransport()},methods:{showCitySelector:function(t){"start"===t?this.showStartSearch=!0:this.showEndSearch=!0},formatDateTime:function(t){return t?n["a"].formatDate.format(new Date(t),"yyyy/MM/dd hh:mm"):""},formatDisplayDate:function(t){if(!t)return"";var a=new Date(t),e=["周日","周一","周二","周三","周四","周五","周六"],i=a.getMonth()+1,s=a.getDate(),n=e[a.getDay()];return"".concat(i,"月").concat(s,"日 ").concat(n)},goBack:function(){this.$router.go(-1)},getTripInfo:function(){var t=this;this.loading=!0,this.$fly.get("/pyp/web/activity/activityguest/getTripById/".concat(this.tripId)).then((function(a){t.loading=!1,200==a.code?(t.tripInfo=a.result,t.currentDate=new Date,t.changeForm.chaDate=n["a"].formatDate.format(t.currentDate,"yyyy/MM/dd"),t.searchParams.inStartPlace=t.tripInfo.inStartPlace||"",t.searchParams.inEndPlace=t.tripInfo.inEndPlace||"",t.searchParams.startCityCode=t.tripInfo.startCityCode||"",t.searchParams.endCityCode=t.tripInfo.endCityCode||"",t.tripInfo.inStartDate?(t.travelDate=new Date(t.tripInfo.inStartDate),t.searchParams.travelDate=n["a"].formatDate.format(t.travelDate,"yyyy/MM/dd")):(t.travelDate=new Date,t.searchParams.travelDate=n["a"].formatDate.format(t.travelDate,"yyyy/MM/dd"))):(vant.Toast(a.msg),t.tripInfo={})})).catch((function(){t.loading=!1,vant.Toast("获取行程信息失败")}))},searchStartPlaces:function(){var t=this;""!==this.startSearchText?(this.startLoading=!0,clearTimeout(this.startTimeout),this.startTimeout=setTimeout((function(){var a=0==t.tripInfo.inType?"/pyp/web/config/configairport/findByName":"/pyp/web/config/configtrainstation/findByName";t.$fly.get(a,{name:t.startSearchText}).then((function(a){t.startLoading=!1,a&&200===a.code?t.startSearchResults=a.result:(vant.Toast(a.msg||"搜索失败"),t.startSearchResults=[])})).catch((function(){t.startLoading=!1,vant.Toast("网络错误，请重试")}))}),300)):this.startSearchResults=[]},searchEndPlaces:function(){var t=this;""!==this.endSearchText?(this.endLoading=!0,clearTimeout(this.endTimeout),this.endTimeout=setTimeout((function(){var a=0==t.tripInfo.inType?"/pyp/web/config/configairport/findByName":"/pyp/web/config/configtrainstation/findByName";t.$fly.get(a,{name:t.endSearchText}).then((function(a){t.endLoading=!1,a&&200===a.code?t.endSearchResults=a.result:(vant.Toast(a.msg||"搜索失败"),t.endSearchResults=[])})).catch((function(){t.endLoading=!1,vant.Toast("网络错误，请重试")}))}),300)):this.endSearchResults=[]},selectStartPlace:function(t){0==this.tripInfo.inType?(console.log(t),this.searchParams.inStartPlace=t.airportName,this.searchParams.inStartCity=t.cityName,this.searchParams.startCityCode=t.cityCode):(this.searchParams.inStartPlace=t.stationName,this.searchParams.inStartCity=t.cityName,this.searchParams.startCityCode=t.stationCode),this.showStartSearch=!1},selectEndPlace:function(t){0==this.tripInfo.inType?(this.searchParams.inEndPlace=t.airportName,this.searchParams.inEndCity=t.cityName,this.searchParams.endCityCode=t.cityCode):(this.searchParams.inEndPlace=t.stationName,this.searchParams.inEndCity=t.cityName,this.searchParams.endCityCode=t.stationCode),this.showEndSearch=!1},onTravelDateConfirm:function(t){this.travelDate=t,this.searchParams.travelDate=n["a"].formatDate.format(t,"yyyy/MM/dd"),this.showTravelDatePicker=!1},goToSelectTransport:function(){if(this.searchParams.inStartPlace)if(this.searchParams.inEndPlace)if(this.searchParams.travelDate){var t={from:"change",startCity:this.searchParams.inStartPlace,startCityCode:this.searchParams.startCityCode,endCity:this.searchParams.inEndPlace,endCityCode:this.searchParams.endCityCode,date:this.searchParams.travelDate,tripId:this.tripId};0==this.tripInfo.inType?this.$router.push({path:"/schedules/expert/components/plane-change-select",query:t}):this.$router.push({path:"/schedules/expert/components/train-select",query:t})}else vant.Toast("请选择出行日期");else vant.Toast("请选择目的地");else vant.Toast("请选择出发地")},checkSelectedTransport:function(){var t=localStorage.getItem("selectedTransport");t&&(this.handleSelectTransport(JSON.parse(t)),localStorage.removeItem("selectedTransport"))},handleSelectTransport:function(t){console.log(t),t&&(0==this.tripInfo.inType?(this.changeForm.chaNumber=t.flightNo||t.inNumber,this.changeForm.chaStartPlace=t.fromAirportName||t.inStartPlace,this.changeForm.chaEndPlace=t.toAirportName||t.inEndPlace,this.changeForm.chaStartDate=t.fromDateTime?(t.fromDateTime+":00").replaceAll("-","/"):t.inStartDate,this.changeForm.chaEndDate=t.toDateTime?(t.toDateTime+":00").replaceAll("-","/"):t.inEndDate,this.changeForm.chaStartCity=this.searchParams.inStartCity,this.changeForm.chaEndCity=this.searchParams.inEndCity,this.changeForm.chaStartTerminal=t.fromTerminal||t.inStartTerminal,this.changeForm.chaEndTerminal=t.toTerminal||t.inEndTerminal,this.changeForm.cabinCode=t.cabinCode||"",this.changeForm.cabinBookPara=t.cabinBookPara||"",this.changeForm.chaStartCityCode=t.fromAirportCode||t.startCityCode||"",this.changeForm.chaEndCityCode=t.toAirportCode||t.endCityCode||"",this.changeForm.price=t.price||""):(this.changeForm.chaNumber=t.trainNo||t.inNumber,this.changeForm.chaStartPlace=t.depStation||t.inStartPlace,this.changeForm.chaEndPlace=t.arrStation||t.inEndPlace,this.changeForm.chaStartDate=t.depTime||t.inStartDate,this.changeForm.chaEndDate=t.arrTime||t.inEndDate,this.changeForm.chaStartCity=this.searchParams.inStartCity,this.changeForm.chaEndCity=this.searchParams.inEndCity,this.changeForm.chaStartCityCode=t.startCityCode||"",this.changeForm.chaEndCityCode=t.endCityCode||"",this.changeForm.price=t.price||""),this.hasSelectedTransport=!0)},onDateConfirm:function(t){this.currentDate=t,this.changeForm.chaDate=n["a"].formatDate.format(t,"yyyy/MM/dd"),this.showDatePicker=!1},onSubmit:function(){var t=this;this.loading=!0,this.$fly.post("/pyp/web/activity/activityguest/applyChangeTicket",this.changeForm).then((function(a){t.loading=!1,200==a.code?(vant.Toast.success("改签申请提交成功"),setTimeout((function(){t.goBack()}),1500)):vant.Toast(a.msg||"提交失败")})).catch((function(){t.loading=!1,vant.Toast("网络错误，请稍后重试")}))}}},l=o,h=(e("cbda"),e("2877")),d=Object(h["a"])(l,i,s,!1,null,"6856f13c",null);a["default"]=d.exports},ade3:function(t,a,e){"use strict";e.d(a,"a",(function(){return r}));var i=e("53ca");function s(t,a){if("object"!==Object(i["a"])(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var s=e.call(t,a||"default");if("object"!==Object(i["a"])(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===a?String:Number)(t)}function n(t){var a=s(t,"string");return"symbol"===Object(i["a"])(a)?a:String(a)}function r(t,a,e){return a=n(a),a in t?Object.defineProperty(t,a,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[a]=e,t}},cad8:function(t,a,e){},cbda:function(t,a,e){"use strict";e("669d")},dd7a:function(t,a,e){"use strict";e("cad8")}}]);
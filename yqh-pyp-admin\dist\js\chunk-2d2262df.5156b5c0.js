(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d2262df"],{e80f:function(e,a,t){"use strict";t.r(a);var l=function(){var e=this,a=e._self._c;return a("el-dialog",{attrs:{title:"云存储配置","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(a){e.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"}},[a("el-form-item",{attrs:{size:"mini",label:"存储类型"}},[a("el-radio-group",{model:{value:e.dataForm.type,callback:function(a){e.$set(e.dataForm,"type",a)},expression:"dataForm.type"}},[a("el-radio",{attrs:{label:1}},[e._v("七牛")]),a("el-radio",{attrs:{label:2}},[e._v("阿里云")]),a("el-radio",{attrs:{label:3}},[e._v("腾讯云")])],1)],1),1===e.dataForm.type?[a("el-form-item",{attrs:{label:"域名"}},[a("el-input",{attrs:{placeholder:"七牛绑定的域名"},model:{value:e.dataForm.qiniuDomain,callback:function(a){e.$set(e.dataForm,"qiniuDomain",a)},expression:"dataForm.qiniuDomain"}})],1),a("el-form-item",{attrs:{label:"路径前缀"}},[a("el-input",{attrs:{placeholder:"不设置默认为空"},model:{value:e.dataForm.qiniuPrefix,callback:function(a){e.$set(e.dataForm,"qiniuPrefix",a)},expression:"dataForm.qiniuPrefix"}})],1),a("el-form-item",{attrs:{label:"AccessKey"}},[a("el-input",{attrs:{placeholder:"七牛AccessKey"},model:{value:e.dataForm.qiniuAccessKey,callback:function(a){e.$set(e.dataForm,"qiniuAccessKey",a)},expression:"dataForm.qiniuAccessKey"}})],1),a("el-form-item",{attrs:{label:"SecretKey"}},[a("el-input",{attrs:{placeholder:"七牛SecretKey"},model:{value:e.dataForm.qiniuSecretKey,callback:function(a){e.$set(e.dataForm,"qiniuSecretKey",a)},expression:"dataForm.qiniuSecretKey"}})],1),a("el-form-item",{attrs:{label:"空间名"}},[a("el-input",{attrs:{placeholder:"七牛存储空间名"},model:{value:e.dataForm.qiniuBucketName,callback:function(a){e.$set(e.dataForm,"qiniuBucketName",a)},expression:"dataForm.qiniuBucketName"}})],1)]:2===e.dataForm.type?[a("el-form-item",{attrs:{label:"域名"}},[a("el-input",{attrs:{placeholder:"阿里云绑定的域名"},model:{value:e.dataForm.aliyunDomain,callback:function(a){e.$set(e.dataForm,"aliyunDomain",a)},expression:"dataForm.aliyunDomain"}})],1),a("el-form-item",{attrs:{label:"路径前缀"}},[a("el-input",{attrs:{placeholder:"不设置默认为空"},model:{value:e.dataForm.aliyunPrefix,callback:function(a){e.$set(e.dataForm,"aliyunPrefix",a)},expression:"dataForm.aliyunPrefix"}})],1),a("el-form-item",{attrs:{label:"EndPoint"}},[a("el-input",{attrs:{placeholder:"阿里云EndPoint"},model:{value:e.dataForm.aliyunEndPoint,callback:function(a){e.$set(e.dataForm,"aliyunEndPoint",a)},expression:"dataForm.aliyunEndPoint"}})],1),a("el-form-item",{attrs:{label:"AccessKeyId"}},[a("el-input",{attrs:{placeholder:"阿里云AccessKeyId"},model:{value:e.dataForm.aliyunAccessKeyId,callback:function(a){e.$set(e.dataForm,"aliyunAccessKeyId",a)},expression:"dataForm.aliyunAccessKeyId"}})],1),a("el-form-item",{attrs:{label:"AccessKeySecret"}},[a("el-input",{attrs:{placeholder:"阿里云AccessKeySecret"},model:{value:e.dataForm.aliyunAccessKeySecret,callback:function(a){e.$set(e.dataForm,"aliyunAccessKeySecret",a)},expression:"dataForm.aliyunAccessKeySecret"}})],1),a("el-form-item",{attrs:{label:"BucketName"}},[a("el-input",{attrs:{placeholder:"阿里云BucketName"},model:{value:e.dataForm.aliyunBucketName,callback:function(a){e.$set(e.dataForm,"aliyunBucketName",a)},expression:"dataForm.aliyunBucketName"}})],1)]:3===e.dataForm.type?[a("el-form-item",{attrs:{label:"域名"}},[a("el-input",{attrs:{placeholder:"腾讯云绑定的域名"},model:{value:e.dataForm.qcloudDomain,callback:function(a){e.$set(e.dataForm,"qcloudDomain",a)},expression:"dataForm.qcloudDomain"}})],1),a("el-form-item",{attrs:{label:"路径前缀"}},[a("el-input",{attrs:{placeholder:"不设置默认为空"},model:{value:e.dataForm.qcloudPrefix,callback:function(a){e.$set(e.dataForm,"qcloudPrefix",a)},expression:"dataForm.qcloudPrefix"}})],1),a("el-form-item",{attrs:{label:"AppId"}},[a("el-input",{attrs:{placeholder:"腾讯云AppId"},model:{value:e.dataForm.qcloudAppId,callback:function(a){e.$set(e.dataForm,"qcloudAppId",a)},expression:"dataForm.qcloudAppId"}})],1),a("el-form-item",{attrs:{label:"SecretId"}},[a("el-input",{attrs:{placeholder:"腾讯云SecretId"},model:{value:e.dataForm.qcloudSecretId,callback:function(a){e.$set(e.dataForm,"qcloudSecretId",a)},expression:"dataForm.qcloudSecretId"}})],1),a("el-form-item",{attrs:{label:"SecretKey"}},[a("el-input",{attrs:{placeholder:"腾讯云SecretKey"},model:{value:e.dataForm.qcloudSecretKey,callback:function(a){e.$set(e.dataForm,"qcloudSecretKey",a)},expression:"dataForm.qcloudSecretKey"}})],1),a("el-form-item",{attrs:{label:"BucketName"}},[a("el-input",{attrs:{placeholder:"腾讯云BucketName"},model:{value:e.dataForm.qcloudBucketName,callback:function(a){e.$set(e.dataForm,"qcloudBucketName",a)},expression:"dataForm.qcloudBucketName"}})],1),a("el-form-item",{attrs:{label:"Bucket所属地区"}},[a("el-input",{attrs:{placeholder:"如：sh（可选值 ，华南：gz 华北：tj 华东：sh）"},model:{value:e.dataForm.qcloudRegion,callback:function(a){e.$set(e.dataForm,"qcloudRegion",a)},expression:"dataForm.qcloudRegion"}})],1)]:e._e()],2),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){e.visible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},o=[],r={data:function(){return{visible:!1,dataForm:{},dataRule:{}}},methods:{init:function(e){var a=this;this.visible=!0,this.$http({url:this.$http.adornUrl("/sys/oss/config"),method:"get",params:this.$http.adornParams()}).then((function(e){var t=e.data;a.dataForm=t&&200===t.code?t.config:[]}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(a){a&&e.$http({url:e.$http.adornUrl("/sys/oss/saveConfig"),method:"post",data:e.$http.adornData(e.dataForm)}).then((function(a){var t=a.data;t&&200===t.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1}}):e.$message.error(t.msg)}))}))}}},i=r,c=t("2877"),s=Object(c["a"])(i,l,o,!1,null,null,null);a["default"]=s.exports}}]);
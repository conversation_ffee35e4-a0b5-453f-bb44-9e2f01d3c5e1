(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6aad860a","chunk-278cc848"],{"619e":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("div",[t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.dataFormSubmit()}}},[t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"产品名称",prop:"name"}},[t("el-input",{attrs:{placeholder:"产品名称"},model:{value:e.dataForm.name,callback:function(t){e.$set(e.dataForm,"name",t)},expression:"dataForm.name"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"描述",prop:"brief"}},[t("el-input",{attrs:{placeholder:"描述"},model:{value:e.dataForm.brief,callback:function(t){e.$set(e.dataForm,"brief",t)},expression:"dataForm.brief"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"成本价",prop:"price"}},[t("el-input",{attrs:{placeholder:"描述"},model:{value:e.dataForm.price,callback:function(t){e.$set(e.dataForm,"price",t)},expression:"dataForm.price"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"对外售价",prop:"sellPrice"}},[t("el-input",{attrs:{placeholder:"描述"},model:{value:e.dataForm.sellPrice,callback:function(t){e.$set(e.dataForm,"sellPrice",t)},expression:"dataForm.sellPrice"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"单位",prop:"unit"}},[t("el-select",{attrs:{filterable:""},model:{value:e.dataForm.unit,callback:function(t){e.$set(e.dataForm,"unit",t)},expression:"dataForm.unit"}},e._l(e.productUnit,(function(e){return t("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"供应商",prop:"supplierId"}},[t("div",{staticStyle:{display:"flex"}},[t("el-select",{attrs:{filterable:""},model:{value:e.dataForm.supplierId,callback:function(t){e.$set(e.dataForm,"supplierId",t)},expression:"dataForm.supplierId"}},e._l(e.suppliers,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1),t("el-button",{attrs:{type:"text"},on:{click:e.supplierAddHandle}},[e._v("快速新增")])],1)])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"产品类别",prop:"productTypeId"}},[t("el-select",{attrs:{filterable:""},model:{value:e.dataForm.productTypeId,callback:function(t){e.$set(e.dataForm,"productTypeId",t)},expression:"dataForm.productTypeId"}},e._l(e.productTypes,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"图片",prop:"picUrl"}},[t("el-upload",{staticClass:"avatar-uploader",attrs:{"list-type":"picture-card","show-file-list":!1,accept:".jpg, .jpeg, .png, .gif","on-success":e.appSuccessHandle,action:e.url}},[e.dataForm.picUrl?t("img",{staticClass:"avatar",attrs:{width:"100px",src:e.dataForm.picUrl}}):t("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1)],1)],1),t("el-form-item",{attrs:{label:"内容",prop:"content"}},[t("tinymce-editor",{ref:"editor",model:{value:e.dataForm.content,callback:function(t){e.$set(e.dataForm,"content",t)},expression:"dataForm.content"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1),e.supplieraddVisible?t("supplieradd",{ref:"supplieradd",on:{refreshDataList:e.findSupplier}}):e._e()],1)},o=[],i=(a("d3b7"),a("3ca3"),a("ddb0"),a("68ea")),s={components:{TinymceEditor:function(){return Promise.all([a.e("chunk-2d0e1c2e"),a.e("chunk-03be236c"),a.e("chunk-2d0a4b8c")]).then(a.bind(null,"26dc"))},tagsEditor:function(){return a.e("chunk-3fef6851").then(a.bind(null,"a55c"))},OssUploader:function(){return a.e("chunk-2d0e97b1").then(a.bind(null,"8e5c"))},supplieradd:i["default"]},data:function(){return{company:{},contractPriceConfig:[],supplieraddVisible:!1,loading:!1,visible:!1,url:"",productUnit:[],dataForm:{id:0,name:"",brief:"",content:"",picUrl:"",supplierId:"",productTypeId:"",productType:1,inTaxRate:0,outTaxRate:0,unit:"",price:"",sellPrice:"",repeatToken:""},suppliers:[],productTypes:[],dataRule:{name:[{required:!0,message:"产品名称不能为空",trigger:"blur"}],supplierId:[{required:!0,message:"供应商id不能为空",trigger:"blur"}],contractPriceConfigId:[{required:!0,message:"产品科目不能为空",trigger:"blur"}],contractPriceConfigPid:[{required:!0,message:"产品科目不能为空",trigger:"blur"}],productType:[{required:!0,message:"产品类型不能为空",trigger:"blur"}]}}},methods:{supplierAddHandle:function(){var e=this;this.supplieraddVisible=!0,this.$nextTick((function(){e.$refs.supplieradd.init()}))},init:function(e,t,a){var r=this;this.dataForm.id=e||0,this.url=this.$http.adornUrl("/sys/oss/upload?token=".concat(this.$cookie.get("token"))),this.visible=!0,this.$nextTick((function(){r.$refs["dataForm"].resetFields(),r.dataForm.id?r.$http({url:r.$http.adornUrl("/supplier/supplierproduct/info/".concat(r.dataForm.id)),method:"get",params:r.$http.adornParams()}).then((function(e){var t=e.data;t&&200===t.code&&(r.dataForm.name=t.supplierProduct.name,r.dataForm.brief=t.supplierProduct.brief,r.dataForm.content=t.supplierProduct.content,r.dataForm.picUrl=t.supplierProduct.picUrl,r.dataForm.supplierId=t.supplierProduct.supplierId,r.dataForm.productType=t.supplierProduct.productType,r.dataForm.price=t.supplierProduct.price,r.dataForm.productTypeId=t.supplierProduct.productTypeId,r.dataForm.sellPrice=t.supplierProduct.sellPrice,r.dataForm.unit=t.supplierProduct.unit,r.dataForm.inTaxRate=t.supplierProduct.inTaxRate,r.dataForm.outTaxRate=t.supplierProduct.outTaxRate,r.dataForm.contractPriceConfigId=t.supplierProduct.contractPriceConfigId,r.dataForm.contractPriceConfigPid=t.supplierProduct.contractPriceConfigPid)})):(r.dataForm.productType=t,r.dataForm.supplierId=a)})),this.getResult(),this.findSupplier(),this.findProductType(),this.getToken()},getResult:function(){var e=this;this.$http({url:this.$http.adornUrl("/sys/config/findOnlyParamKey"),method:"get",params:this.$http.adornParams({paramKey:"productUnit"})}).then((function(t){var a=t.data;a&&200===a.code&&(e.productUnit=a.result.paramValue?a.result.paramValue.split(","):[])}))},getToken:function(){var e=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm.repeatToken=a.result)}))},findSupplier:function(e){var t=this;this.$http({url:this.$http.adornUrl("/supplier/supplier/findAll"),method:"get",params:this.$http.adornParams()}).then((function(a){var r=a.data;r&&200===r.code&&(t.suppliers=r.result,e&&(t.dataForm.supplierId=e,t.getToken()))}))},findProductType:function(){var e=this;this.$http({url:this.$http.adornUrl("/supplier/producttype/findAll"),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.productTypes=a.result)}))},imgUploadSuccess:function(e,t,a){console.log(e),200==e.code?(this.dataForm.picUrl=e.data,console.log("this.article",this.article)):this.$message.warning(e.msg)},beforeUploadHandle:function(e){if("image/jpg"!==e.type&&"image/jpeg"!==e.type&&"image/png"!==e.type&&"image/gif"!==e.type)return this.$message.error("只支持jpg、png、gif格式的图片！"),!1},appSuccessHandle:function(e,t,a){e&&200===e.code?this.dataForm.picUrl=e.url:this.$message.error(e.msg)},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.loading=!0,e.$http({url:e.$http.adornUrl("/supplier/supplierproduct/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,name:e.dataForm.name,brief:e.dataForm.brief,content:e.dataForm.content,picUrl:e.dataForm.picUrl,supplierId:e.dataForm.supplierId,productTypeId:e.dataForm.productTypeId,productType:e.dataForm.productType,price:e.dataForm.price,sellPrice:e.dataForm.sellPrice,unit:e.dataForm.unit,inTaxRate:e.dataForm.inTaxRate,outTaxRate:e.dataForm.outTaxRate,contractPriceConfigId:e.dataForm.contractPriceConfigId,contractPriceConfigPid:e.dataForm.contractPriceConfigPid,repeatToken:e.dataForm.repeatToken,appid:e.$cookie.get("appid")})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList",a.result)}}):(e.$message.error(a.msg),"不能重复提交"!=a.msg&&e.getToken()),e.loading=!1})))}))}}},l=s,n=a("2877"),d=Object(n["a"])(l,r,o,!1,null,null,null);t["default"]=d.exports},"68ea":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"150px"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.dataFormSubmit()}}},[t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"供应商名称",prop:"name"}},[t("el-input",{attrs:{placeholder:"供应商名称"},model:{value:e.dataForm.name,callback:function(t){e.$set(e.dataForm,"name",t)},expression:"dataForm.name"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"供应商类别",prop:"supplierTypeId"}},[t("el-select",{model:{value:e.dataForm.supplierTypeId,callback:function(t){e.$set(e.dataForm,"supplierTypeId",t)},expression:"dataForm.supplierTypeId"}},e._l(e.supplierTypes,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"联系人",prop:"username"}},[t("el-input",{attrs:{placeholder:"联系人"},model:{value:e.dataForm.username,callback:function(t){e.$set(e.dataForm,"username",t)},expression:"dataForm.username"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"联系方式",prop:"mobile"}},[t("el-input",{attrs:{placeholder:"联系方式"},model:{value:e.dataForm.mobile,callback:function(t){e.$set(e.dataForm,"mobile",t)},expression:"dataForm.mobile"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"区域",prop:"area"}},[t("el-cascader",{staticStyle:{width:"100%"},attrs:{size:"large",options:e.options},on:{change:e.handleChange},model:{value:e.dataForm.area,callback:function(t){e.$set(e.dataForm,"area",t)},expression:"dataForm.area"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"地址",prop:"address"}},[t("el-input",{attrs:{placeholder:"地址"},model:{value:e.dataForm.address,callback:function(t){e.$set(e.dataForm,"address",t)},expression:"dataForm.address"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[t("el-input",{attrs:{placeholder:"邮箱"},model:{value:e.dataForm.email,callback:function(t){e.$set(e.dataForm,"email",t)},expression:"dataForm.email"}})],1)],1)],1),t("el-form-item",{attrs:{label:"银行账户信息",prop:"isBank"}},[t("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#6f6f6f"},model:{value:e.dataForm.isBank,callback:function(t){e.$set(e.dataForm,"isBank",t)},expression:"dataForm.isBank"}})],1),e.dataForm.isBank?t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"银行卡号",prop:"bankAccount"}},[t("el-input",{attrs:{placeholder:"银行卡号"},model:{value:e.dataForm.bankAccount,callback:function(t){e.$set(e.dataForm,"bankAccount",t)},expression:"dataForm.bankAccount"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"开户行",prop:"bankAddress"}},[t("el-input",{attrs:{placeholder:"开户行"},model:{value:e.dataForm.bankAddress,callback:function(t){e.$set(e.dataForm,"bankAddress",t)},expression:"dataForm.bankAddress"}})],1)],1)],1):e._e(),t("el-form-item",{attrs:{label:"发票信息",prop:"isInvoice"}},[t("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#6f6f6f"},model:{value:e.dataForm.isInvoice,callback:function(t){e.$set(e.dataForm,"isInvoice",t)},expression:"dataForm.isInvoice"}})],1),e.dataForm.isInvoice?t("el-form-item",{attrs:{label:"统一社会编码",prop:"code"}},[t("el-input",{attrs:{placeholder:"统一社会编码"},model:{value:e.dataForm.code,callback:function(t){e.$set(e.dataForm,"code",t)},expression:"dataForm.code"}})],1):e._e(),e.dataForm.isInvoice?t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"注册地址（专票）",prop:"registerAddress"}},[t("el-input",{attrs:{placeholder:"注册地址（专票）"},model:{value:e.dataForm.registerAddress,callback:function(t){e.$set(e.dataForm,"registerAddress",t)},expression:"dataForm.registerAddress"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"注册电话（专票）",prop:"registerTelephone"}},[t("el-input",{attrs:{placeholder:"注册电话（专票）"},model:{value:e.dataForm.registerTelephone,callback:function(t){e.$set(e.dataForm,"registerTelephone",t)},expression:"dataForm.registerTelephone"}})],1)],1)],1):e._e(),e.dataForm.isInvoice?t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"注册银行（专票）",prop:"registerBank"}},[t("el-input",{attrs:{placeholder:"注册银行（专票）"},model:{value:e.dataForm.registerBank,callback:function(t){e.$set(e.dataForm,"registerBank",t)},expression:"dataForm.registerBank"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"注册账户（专票）",prop:"registerAccount"}},[t("el-input",{attrs:{placeholder:"注册账户（专票）"},model:{value:e.dataForm.registerAccount,callback:function(t){e.$set(e.dataForm,"registerAccount",t)},expression:"dataForm.registerAccount"}})],1)],1)],1):e._e(),t("el-form-item",{attrs:{label:"备注",prop:"remarks"}},[t("el-input",{attrs:{placeholder:"备注"},model:{value:e.dataForm.remarks,callback:function(t){e.$set(e.dataForm,"remarks",t)},expression:"dataForm.remarks"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},o=[],i=(a("a15b"),a("ef6c")),s={data:function(){return{options:i["regionData"],loading:!1,visible:!1,dataForm:{id:0,name:"",username:"",code:"",address:"",email:"",supplierTypeId:"",mobile:"",registerAddress:"",registerTelephone:"",registerBank:"",registerAccount:"",remarks:"",isBank:!0,isInvoice:!1,bankAccount:"",bankAddress:"",repeatToken:"",area:""},supplierTypes:[],dataRule:{name:[{required:!0,message:"供应商名称不能为空",trigger:"blur"}],username:[{required:!0,message:"联系人不能为空",trigger:"blur"}]}}},methods:{init:function(e){var t=this;this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){t.$refs["dataForm"].resetFields(),t.dataForm.id&&t.$http({url:t.$http.adornUrl("/supplier/supplier/info/".concat(t.dataForm.id)),method:"get",params:t.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.name=a.supplier.name,t.dataForm.username=a.supplier.username,t.dataForm.code=a.supplier.code,t.dataForm.address=a.supplier.address,t.dataForm.email=a.supplier.email,t.dataForm.supplierTypeId=a.supplier.supplierTypeId,t.dataForm.mobile=a.supplier.mobile,t.dataForm.registerAddress=a.supplier.registerAddress,t.dataForm.registerTelephone=a.supplier.registerTelephone,t.dataForm.registerBank=a.supplier.registerBank,t.dataForm.registerAccount=a.supplier.registerAccount,t.dataForm.remarks=a.supplier.remarks,t.dataForm.bankAccount=a.supplier.bankAccount,t.dataForm.bankAddress=a.supplier.bankAddress,t.dataForm.area=a.supplier.area?a.supplier.area.split(","):[],t.dataForm.bankAccount&&(t.dataForm.isBank=!0),t.dataForm.code&&(t.dataForm.isInvoice=!0))}))})),this.findSupplierType(),this.getToken()},getToken:function(){var e=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm.repeatToken=a.result)}))},findSupplierType:function(){var e=this;this.$http({url:this.$http.adornUrl("/supplier/suppliertype/findAll"),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.supplierTypes=a.result)}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.loading=!0,e.$http({url:e.$http.adornUrl("/supplier/supplier/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,name:e.dataForm.name,username:e.dataForm.username,code:e.dataForm.code,address:e.dataForm.address,email:e.dataForm.email,supplierTypeId:e.dataForm.supplierTypeId,mobile:e.dataForm.mobile,registerAddress:e.dataForm.registerAddress,registerTelephone:e.dataForm.registerTelephone,registerBank:e.dataForm.registerBank,appid:e.$cookie.get("appid"),registerAccount:e.dataForm.registerAccount,remarks:e.dataForm.remarks,bankAddress:e.dataForm.bankAddress,bankAccount:e.dataForm.bankAccount,area:e.dataForm.area?e.dataForm.area.join(","):"",repeatToken:e.dataForm.repeatToken})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList",a.result)}}):e.$message.error(a.msg),e.loading=!1})))}))}}},l=s,n=a("2877"),d=Object(n["a"])(l,r,o,!1,null,null,null);t["default"]=d.exports},a15b:function(e,t,a){"use strict";var r=a("23e7"),o=a("e330"),i=a("44ad"),s=a("fc6a"),l=a("a640"),n=o([].join),d=i!==Object,p=d||!l("join",",");r({target:"Array",proto:!0,forced:p},{join:function(e){return n(s(this),void 0===e?",":e)}})}}]);
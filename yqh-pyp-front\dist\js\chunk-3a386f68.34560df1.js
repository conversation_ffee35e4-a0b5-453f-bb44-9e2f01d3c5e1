(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3a386f68"],{"0c4a":function(t,a,i){"use strict";i.r(a);i("7514");var s=function(){var t=this,a=t._self._c;return a("div",{class:t.isMobilePhone?"":"pc-container"},[t.isMobilePhone?t._e():a("pcheader"),a("div",{staticClass:"trip-info-card"},[a("div",{staticClass:"card-title"},[t._v("行程信息")]),a("div",{staticClass:"flight-info"},[a("div",{staticClass:"flight-route"},[a("span",{staticClass:"city"},[t._v(t._s(t.tripInfo.inStartPlace))]),a("van-icon",{attrs:{name:"arrow"}}),a("span",{staticClass:"city"},[t._v(t._s(t.tripInfo.inEndPlace))])],1),a("div",{staticClass:"transport-info"},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v(t._s(0==t.tripInfo.inType?"航班号":"车次")+"：")]),a("span",{staticClass:"value"},[t._v(t._s(t.tripInfo.inNumber))])]),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v("出发时间：")]),a("span",{staticClass:"value"},[t._v(t._s(t.formatDateTime(t.tripInfo.inStartDate)))])]),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v("到达时间：")]),a("span",{staticClass:"value"},[t._v(t._s(t.formatDateTime(t.tripInfo.inEndDate)))])]),0==t.tripInfo.inType?a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v("出发航站楼：")]),a("span",{staticClass:"value"},[t._v(t._s(t.tripInfo.inStartTerminal||"未知"))])]):t._e(),0==t.tripInfo.inType?a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v("到达航站楼：")]),a("span",{staticClass:"value"},[t._v(t._s(t.tripInfo.inEndTerminal||"未知"))])]):t._e(),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v("出票状态：")]),a("span",{staticClass:"value"},[a("van-tag",{attrs:{size:"large",type:1==t.tripInfo.isBuy?"success":"warning",round:""}},[t._v("\n              "+t._s(t.isBuy[t.tripInfo.isBuy]?t.isBuy[t.tripInfo.isBuy].value:"未出票")+"\n            ")])],1)]),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v("行程状态：")]),a("span",{staticClass:"value"},[a("van-tag",{attrs:{size:"large",type:t.getStatusTagType(t.tripInfo),round:""}},[t._v("\n              "+t._s(0==t.tripInfo.inType?(t.tripPlaneStatus.find((function(a){return a.key===t.tripInfo.orderStatus}))||{}).value:(t.tripTrainStatus.find((function(a){return a.key===t.tripInfo.orderStatus}))||{}).value)+"\n            ")])],1)])])])]),1===t.tripInfo.isCha?a("div",{staticClass:"trip-info-card"},[a("div",{staticClass:"card-title"},[t._v("改签信息")]),a("div",{staticClass:"flight-info"},[a("div",{staticClass:"flight-route"},[a("span",{staticClass:"city"},[t._v(t._s(t.tripInfo.chaStartPlace))]),a("van-icon",{attrs:{name:"arrow"}}),a("span",{staticClass:"city"},[t._v(t._s(t.tripInfo.chaEndPlace))])],1),a("div",{staticClass:"transport-info"},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v(t._s(0==t.tripInfo.inType?"航班号":"车次")+"：")]),a("span",{staticClass:"value"},[t._v(t._s(t.tripInfo.chaNumber))])]),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v("出发时间：")]),a("span",{staticClass:"value"},[t._v(t._s(t.formatDateTime(t.tripInfo.chaStartDate)))])]),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v("到达时间：")]),a("span",{staticClass:"value"},[t._v(t._s(t.formatDateTime(t.tripInfo.chaEndDate)))])]),0==t.tripInfo.inType?a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v("出发航站楼：")]),a("span",{staticClass:"value"},[t._v(t._s(t.tripInfo.chaStartTerminal||"未知"))])]):t._e(),0==t.tripInfo.inType?a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v("到达航站楼：")]),a("span",{staticClass:"value"},[t._v(t._s(t.tripInfo.chaEndTerminal||"未知"))])]):t._e(),t.tripInfo.chaDate?a("div",{staticClass:"detail-item"},[a("span",{staticClass:"label"},[t._v("改签日期：")]),a("span",{staticClass:"value"},[t._v(t._s(t.formatDate(t.tripInfo.chaDate)))])]):t._e()])])]):t._e(),a("div",{staticClass:"action-buttons"},[0==t.tripInfo.inType&&4==t.tripInfo.orderStatus&&0==t.tripInfo.isCha?a("van-button",{attrs:{type:"primary",block:""},on:{click:t.handleChangeTicket}},[t._v("申请改签")]):t._e(),1!=t.tripInfo.isBuy?a("van-button",{staticStyle:{"margin-top":"12px"},attrs:{type:"info",block:""},on:{click:t.handleEdit}},[t._v("编辑行程")]):t._e(),1!=t.tripInfo.isBuy?a("van-button",{staticStyle:{"margin-top":"12px"},attrs:{type:"primary",block:""},on:{click:t.handleDelete}},[t._v("删除行程")]):t._e(),a("van-button",{staticStyle:{"margin-top":"12px"},attrs:{type:"default",block:""},on:{click:t.goBack}},[t._v("返回")])],1),t.tripInfo.image?a("div",{staticClass:"ticket-image"},[a("div",{staticClass:"card-title"},[t._v("票据图片")]),a("div",{staticClass:"image-container",on:{click:function(a){return t.previewImage(t.tripInfo.image)}}},[a("img",{attrs:{src:t.tripInfo.image,alt:"票据图片"}})])]):t._e()],1)},e=[],n=i("66c7"),l=i("cacf"),r=i("1b69"),c=i("7de9"),o={components:{pcheader:r["default"]},data:function(){return{isMobilePhone:Object(l["c"])(),tripId:void 0,tripInfo:{},loading:!1,isBuy:c["d"],tripPlaneStatus:c["g"],tripTrainStatus:c["h"]}},mounted:function(){this.tripId=this.$route.query.tripId,this.detailId=this.$route.query.detailId,this.tripId?this.getTripInfo():(this.goBack(),vant.Toast("行程信息不存在"))},methods:{formatDateTime:function(t){return t?n["a"].formatDate.format(new Date(t),"yyyy/MM/dd hh:mm"):""},formatDate:function(t){return t?n["a"].formatDate.format(new Date(t),"yyyy/MM/dd"):""},goBack:function(){this.$router.go(-1)},getTripInfo:function(){var t=this;this.loading=!0,this.$fly.get("/pyp/web/activity/activityguest/getTripById/".concat(this.tripId)).then((function(a){t.loading=!1,200==a.code?t.tripInfo=a.result:(vant.Toast(a.msg),t.tripInfo={})})).catch((function(){t.loading=!1,vant.Toast("获取行程信息失败")}))},getStatusTagType:function(t){var a=t.orderStatus;return void 0===a?"default":0===a?"primary":1===a?"success":2===a?"danger":"default"},handleChangeTicket:function(){this.$router.push({path:"/schedules/expert/tripChange",query:{detailId:this.detailId,tripId:this.tripId}})},handleEdit:function(){this.$router.push({path:"/schedules/expertTripEdit",query:{detailId:this.detailId,tripId:this.tripId}})},handleDelete:function(){var t=this;vant.Dialog.confirm({title:"提示",message:"确定删除该行程吗？",confirmButtonText:"确定",cancelButtonText:"取消",confirmButtonColor:"#1989fa"}).then((function(){t.$fly.get("/pyp/web/activity/activityguest/deleteTrip",{tripId:t.tripId}).then((function(a){200==a.code?(vant.Toast("删除行程成功"),t.goBack()):vant.Toast(a.msg)})).catch((function(){vant.Toast("删除行程失败")}))}))},previewImage:function(t){vant.ImagePreview({images:[t],closeable:!0})}}},p=o,d=(i("b4a0"),i("2877")),v=Object(d["a"])(p,s,e,!1,null,"398db568",null);a["default"]=v.exports},"9ed8":function(t,a,i){},b4a0:function(t,a,i){"use strict";i("9ed8")}}]);
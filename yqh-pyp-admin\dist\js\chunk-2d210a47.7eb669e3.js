(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d210a47"],{b982:function(t,a,i){"use strict";i.r(a);var e=function(){var t=this,a=t._self._c;return a("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(a){t.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&t._k(a.keyCode,"enter",13,a.key,"Enter")?null:t.dataFormSubmit()}}},[a("el-form-item",{attrs:{label:"会议id",prop:"activityId"}},[a("el-input",{attrs:{placeholder:"会议id"},model:{value:t.dataForm.activityId,callback:function(a){t.$set(t.dataForm,"activityId",a)},expression:"dataForm.activityId"}})],1),a("el-form-item",{attrs:{label:"",prop:"paixu"}},[a("el-input",{attrs:{placeholder:""},model:{value:t.dataForm.paixu,callback:function(a){t.$set(t.dataForm,"paixu",a)},expression:"dataForm.paixu"}})],1),a("el-form-item",{attrs:{label:"",prop:"activityVideoId"}},[a("el-input",{attrs:{placeholder:""},model:{value:t.dataForm.activityVideoId,callback:function(a){t.$set(t.dataForm,"activityVideoId",a)},expression:"dataForm.activityVideoId"}})],1),a("el-form-item",{attrs:{label:"",prop:"connectActivityVideoId"}},[a("el-input",{attrs:{placeholder:""},model:{value:t.dataForm.connectActivityVideoId,callback:function(a){t.$set(t.dataForm,"connectActivityVideoId",a)},expression:"dataForm.connectActivityVideoId"}})],1),a("el-form-item",{attrs:{label:"",prop:"activityImageId"}},[a("el-input",{attrs:{placeholder:""},model:{value:t.dataForm.activityImageId,callback:function(a){t.$set(t.dataForm,"activityImageId",a)},expression:"dataForm.activityImageId"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){t.visible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},o=[],d={data:function(){return{visible:!1,dataForm:{repeatToken:"",id:0,activityId:"",paixu:"",activityVideoId:"",connectActivityVideoId:"",activityImageId:""},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],paixu:[{required:!0,message:"不能为空",trigger:"blur"}],activityVideoId:[{required:!0,message:"不能为空",trigger:"blur"}],connectActivityVideoId:[{required:!0,message:"不能为空",trigger:"blur"}],activityImageId:[{required:!0,message:"不能为空",trigger:"blur"}]}}},methods:{init:function(t){var a=this;this.getToken(),this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/activity/activityvideoconnect/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var i=t.data;i&&200===i.code&&(a.dataForm.activityId=i.activityVideoConnect.activityId,a.dataForm.paixu=i.activityVideoConnect.paixu,a.dataForm.activityVideoId=i.activityVideoConnect.activityVideoId,a.dataForm.connectActivityVideoId=i.activityVideoConnect.connectActivityVideoId,a.dataForm.activityImageId=i.activityVideoConnect.activityImageId)}))}))},getToken:function(){var t=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(a){var i=a.data;i&&200===i.code&&(t.dataForm.repeatToken=i.result)}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(a){a&&t.$http({url:t.$http.adornUrl("/activity/activityvideoconnect/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({repeatToken:t.dataForm.repeatToken,id:t.dataForm.id||void 0,activityId:t.dataForm.activityId,paixu:t.dataForm.paixu,activityVideoId:t.dataForm.activityVideoId,connectActivityVideoId:t.dataForm.connectActivityVideoId,activityImageId:t.dataForm.activityImageId})}).then((function(a){var i=a.data;i&&200===i.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):(t.$message.error(i.msg),"不能重复提交"!=i.msg&&t.getToken())}))}))}}},r=d,c=i("2877"),n=Object(c["a"])(r,e,o,!1,null,null,null);a["default"]=n.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3d04d65a","chunk-f0db58a2"],{"05d2":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.getDataList()}}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"团购券名称",clearable:""},model:{value:t.dataForm.couponName,callback:function(e){t.$set(t.dataForm,"couponName",e)},expression:"dataForm.couponName"}})],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"平台类型",clearable:""},model:{value:t.dataForm.platformType,callback:function(e){t.$set(t.dataForm,"platformType",e)},expression:"dataForm.platformType"}},[e("el-option",{attrs:{label:"抖音团购",value:"douyin"}}),e("el-option",{attrs:{label:"美团团购",value:"meituan"}}),e("el-option",{attrs:{label:"大众点评团购",value:"dianping"}})],1)],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"状态",clearable:""},model:{value:t.dataForm.status,callback:function(e){t.$set(t.dataForm,"status",e)},expression:"dataForm.status"}},[e("el-option",{attrs:{label:"上架",value:"1"}}),e("el-option",{attrs:{label:"下架",value:"0"}})],1)],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),t.isAuth("groupbuying:coupon:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("groupbuying:coupon:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e(),t.isAuth("groupbuying:coupon:update")?e("el-button",{attrs:{type:"success",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.updateStatusHandle(1)}}},[t._v("批量上架")]):t._e(),t.isAuth("groupbuying:coupon:update")?e("el-button",{attrs:{type:"warning",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.updateStatusHandle(0)}}},[t._v("批量下架")]):t._e()],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"id","header-align":"center",align:"center",label:"ID"}}),e("el-table-column",{attrs:{prop:"couponName","header-align":"center",align:"center",label:"团购券名称"}}),e("el-table-column",{attrs:{prop:"platformType","header-align":"center",align:"center",label:"平台类型"},scopedSlots:t._u([{key:"default",fn:function(a){return["douyin"===a.row.platformType?e("el-tag",{attrs:{type:"primary"}},[t._v("抖音团购")]):"meituan"===a.row.platformType?e("el-tag",{attrs:{type:"warning"}},[t._v("美团团购")]):"dianping"===a.row.platformType?e("el-tag",{attrs:{type:"success"}},[t._v("大众点评团购")]):e("span",[t._v(t._s(a.row.platformType))])]}}])}),e("el-table-column",{attrs:{prop:"originalPrice","header-align":"center",align:"center",label:"原价"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.originalPrice?e("span",[t._v("¥"+t._s(a.row.originalPrice))]):e("span",[t._v("-")])]}}])}),e("el-table-column",{attrs:{prop:"groupPrice","header-align":"center",align:"center",label:"团购价"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticStyle:{color:"#f56c6c","font-weight":"bold"}},[t._v("¥"+t._s(a.row.groupPrice))])]}}])}),e("el-table-column",{attrs:{prop:"soldCount","header-align":"center",align:"center",label:"已售"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(a.row.soldCount||0))]),a.row.totalCount?e("span",[t._v("/ "+t._s(a.row.totalCount))]):t._e()]}}])}),e("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",label:"状态"},scopedSlots:t._u([{key:"default",fn:function(a){return[1===a.row.status?e("el-tag",{attrs:{type:"success"}},[t._v("上架")]):e("el-tag",{attrs:{type:"danger"}},[t._v("下架")])]}}])}),e("el-table-column",{attrs:{prop:"sortOrder","header-align":"center",align:"center",label:"排序"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e()],1)},r=[],i=(a("99af"),a("a15b"),a("d81d"),a("a573"),a("149e")),n={data:function(){return{dataForm:{couponName:"",platformType:"",activityId:"",status:""},dataList:[],activityList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1}},components:{AddOrUpdate:i["default"]},activated:function(){this.dataForm.activityId=this.$route.query.activityId,this.getDataList()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/groupbuying/coupon/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,couponName:this.dataForm.couponName,platformType:this.dataForm.platformType,activityId:this.dataForm.activityId,status:this.dataForm.status})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},getActivityList:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activity/list"),method:"get",params:this.$http.adornParams({page:1,limit:1e3})}).then((function(e){var a=e.data;a&&200===a.code&&(t.activityList=a.page.list)}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(t,e.dataForm.activityId)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/groupbuying/coupon/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},updateStatusHandle:function(t){var e=this,a=this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对选中的团购券进行[".concat(1===t?"上架":"下架","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/groupbuying/coupon/updateStatus"),method:"post",data:e.$http.adornData({ids:a,status:t},!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))}}},l=n,s=a("2877"),c=Object(s["a"])(l,o,r,!1,null,null,null);e["default"]=c.exports},"149e":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible,width:"800px"},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.dataFormSubmit()}}},[e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"平台类型",prop:"platformType"}},[e("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择平台类型"},model:{value:t.dataForm.platformType,callback:function(e){t.$set(t.dataForm,"platformType",e)},expression:"dataForm.platformType"}},[e("el-option",{attrs:{label:"抖音团购",value:"douyin"}}),e("el-option",{attrs:{label:"美团团购",value:"meituan"}}),e("el-option",{attrs:{label:"大众点评团购",value:"dianping"}})],1)],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"团购券名称",prop:"couponName"}},[e("el-input",{attrs:{placeholder:"团购券名称"},model:{value:t.dataForm.couponName,callback:function(e){t.$set(t.dataForm,"couponName",e)},expression:"dataForm.couponName"}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"团购券描述",prop:"couponDescription"}},[e("el-input",{attrs:{type:"textarea",placeholder:"团购券描述"},model:{value:t.dataForm.couponDescription,callback:function(e){t.$set(t.dataForm,"couponDescription",e)},expression:"dataForm.couponDescription"}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"原价",prop:"originalPrice"}},[e("el-input-number",{staticStyle:{width:"100%"},attrs:{precision:2,min:0,placeholder:"原价"},model:{value:t.dataForm.originalPrice,callback:function(e){t.$set(t.dataForm,"originalPrice",e)},expression:"dataForm.originalPrice"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"团购价",prop:"groupPrice"}},[e("el-input-number",{staticStyle:{width:"100%"},attrs:{precision:2,min:0,placeholder:"团购价"},model:{value:t.dataForm.groupPrice,callback:function(e){t.$set(t.dataForm,"groupPrice",e)},expression:"dataForm.groupPrice"}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"团购券ID",prop:"couponId"}},[e("el-input",{attrs:{placeholder:"平台提供的团购券ID"},model:{value:t.dataForm.couponId,callback:function(e){t.$set(t.dataForm,"couponId",e)},expression:"dataForm.couponId"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"封面图片",prop:"coverImage"}},[e("div",{staticClass:"image-upload-container"},[t.coverImages.length>0?e("div",{staticClass:"image-preview"},[e("img",{staticClass:"preview-image",attrs:{src:t.coverImages[0].url,alt:"封面图片"},on:{click:function(e){return t.previewImage(t.coverImages[0].url)}}}),e("div",{staticClass:"image-actions"},[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.openImageModal("coverImage")}}},[t._v("更换")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.removeCoverImage()}}},[t._v("删除")])],1)]):e("div",{staticClass:"upload-placeholder",on:{click:function(e){return t.openImageModal("coverImage")}}},[e("i",{staticClass:"el-icon-plus"}),e("div",[t._v("点击上传封面图片")])])]),e("div",{staticStyle:{color:"#909399","font-size":"12px","margin-top":"5px"}},[t._v("建议尺寸：750*400，大小2MB以下")])])],1)],1),e("el-row"),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"总数量",prop:"totalCount"}},[e("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,placeholder:"总数量（空表示不限量）"},model:{value:t.dataForm.totalCount,callback:function(e){t.$set(t.dataForm,"totalCount",e)},expression:"dataForm.totalCount"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"已售数量",prop:"soldCount"}},[e("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,placeholder:"已售数量"},model:{value:t.dataForm.soldCount,callback:function(e){t.$set(t.dataForm,"soldCount",e)},expression:"dataForm.soldCount"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[e("el-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"排序（数字越大越靠前）"},model:{value:t.dataForm.sortOrder,callback:function(e){t.$set(t.dataForm,"sortOrder",e)},expression:"dataForm.sortOrder"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"状态",prop:"status"}},[e("el-radio-group",{model:{value:t.dataForm.status,callback:function(e){t.$set(t.dataForm,"status",e)},expression:"dataForm.status"}},[e("el-radio",{attrs:{label:1}},[t._v("上架")]),e("el-radio",{attrs:{label:0}},[t._v("下架")])],1)],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"备注信息",prop:"remarks"}},[e("el-input",{attrs:{type:"textarea",placeholder:"备注信息"},model:{value:t.dataForm.remarks,callback:function(e){t.$set(t.dataForm,"remarks",e)},expression:"dataForm.remarks"}})],1)],1)],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1),e("ImageUploadModal",{attrs:{visible:t.imageModalVisible,multiple:!1,"max-count":1,"default-images":t.getCurrentImages()},on:{"update:visible":function(e){t.imageModalVisible=e},confirm:t.handleImageConfirm}}),e("el-dialog",{attrs:{visible:t.imgDialogVisible,width:"60%"},on:{"update:visible":function(e){t.imgDialogVisible=e}}},[e("img",{attrs:{width:"100%",src:t.dialogImageUrl,alt:""}})])],1)},r=[],i=(a("d3b7"),a("3ca3"),a("ddb0"),{components:{ImageUploadModal:function(){return Promise.all([a.e("chunk-2d0e1c2e"),a.e("chunk-e9144aa4"),a.e("chunk-297aad0f")]).then(a.bind(null,"4185"))}},data:function(){return{visible:!1,activityList:[],imageModalVisible:!1,currentImageField:"",coverImages:[],imgDialogVisible:!1,dialogImageUrl:"",dataForm:{id:0,activityId:"",platformType:"",couponName:"",couponDescription:"",originalPrice:null,groupPrice:null,discountInfo:"",couponUrl:"",couponId:"",qrCodeUrl:"",coverImage:"",startTime:null,endTime:null,totalCount:null,soldCount:0,status:1,sortOrder:0,remarks:""},dataRule:{activityId:[{required:!0,message:"活动不能为空",trigger:"change"}],platformType:[{required:!0,message:"平台类型不能为空",trigger:"change"}],couponName:[{required:!0,message:"团购券名称不能为空",trigger:"blur"}],groupPrice:[{required:!0,message:"团购价不能为空",trigger:"blur"}]}}},methods:{init:function(t,e){var a=this;this.dataForm.id=t||0,this.dataForm.activityId=e,this.visible=!0,this.getActivityList(),this.initImageArrays(),this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/groupbuying/coupon/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.dataForm.activityId=e.coupon.activityId,a.dataForm.platformType=e.coupon.platformType,a.dataForm.couponName=e.coupon.couponName,a.dataForm.couponDescription=e.coupon.couponDescription,a.dataForm.originalPrice=e.coupon.originalPrice,a.dataForm.groupPrice=e.coupon.groupPrice,a.dataForm.discountInfo=e.coupon.discountInfo,a.dataForm.couponUrl=e.coupon.couponUrl,a.dataForm.couponId=e.coupon.couponId,a.dataForm.qrCodeUrl=e.coupon.qrCodeUrl,a.dataForm.coverImage=e.coupon.coverImage,a.dataForm.startTime=e.coupon.startTime,a.dataForm.endTime=e.coupon.endTime,a.dataForm.totalCount=e.coupon.totalCount,a.dataForm.soldCount=e.coupon.soldCount,a.dataForm.status=e.coupon.status,a.dataForm.sortOrder=e.coupon.sortOrder,a.dataForm.remarks=e.coupon.remarks,a.initImageArrays())}))}))},getActivityList:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activity/list"),method:"get",params:this.$http.adornParams({page:1,limit:1e3})}).then((function(e){var a=e.data;a&&200===a.code&&(t.activityList=a.page.list)}))},initImageArrays:function(){this.dataForm.coverImage?this.coverImages=[{url:this.dataForm.coverImage,name:"cover.jpg"}]:this.coverImages=[]},openImageModal:function(t){this.currentImageField=t,this.imageModalVisible=!0},getCurrentImages:function(){switch(this.currentImageField){case"coverImage":return this.coverImages;default:return[]}},handleImageConfirm:function(t){switch(this.currentImageField){case"coverImage":this.coverImages=t,this.dataForm.coverImage=t.length>0?t[0].url:"";break}this.imageModalVisible=!1},previewImage:function(t){this.dialogImageUrl=t,this.imgDialogVisible=!0},removeCoverImage:function(){this.coverImages=[],this.dataForm.coverImage=""},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/groupbuying/coupon/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,activityId:t.dataForm.activityId,platformType:t.dataForm.platformType,couponName:t.dataForm.couponName,couponDescription:t.dataForm.couponDescription,originalPrice:t.dataForm.originalPrice,groupPrice:t.dataForm.groupPrice,discountInfo:t.dataForm.discountInfo,couponUrl:t.dataForm.couponUrl,couponId:t.dataForm.couponId,qrCodeUrl:t.dataForm.qrCodeUrl,coverImage:t.dataForm.coverImage,startTime:t.dataForm.startTime,endTime:t.dataForm.endTime,totalCount:t.dataForm.totalCount,soldCount:t.dataForm.soldCount,status:t.dataForm.status,sortOrder:t.dataForm.sortOrder,remarks:t.dataForm.remarks})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))}}}),n=i,l=(a("7f3c"),a("2877")),s=Object(l["a"])(n,o,r,!1,null,"3a4f6b44",null);e["default"]=s.exports},"7f3c":function(t,e,a){"use strict";a("e988")},a15b:function(t,e,a){"use strict";var o=a("23e7"),r=a("e330"),i=a("44ad"),n=a("fc6a"),l=a("a640"),s=r([].join),c=i!==Object,d=c||!l("join",",");o({target:"Array",proto:!0,forced:d},{join:function(t){return s(n(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var o=a("23e7"),r=a("d024"),i=a("c430");o({target:"Iterator",proto:!0,real:!0,forced:i},{map:r})},d024:function(t,e,a){"use strict";var o=a("c65b"),r=a("59ed"),i=a("825a"),n=a("46c4"),l=a("c5cc"),s=a("9bdd"),c=l((function(){var t=this.iterator,e=i(o(this.next,t)),a=this.done=!!e.done;if(!a)return s(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return i(this),r(t),new c(n(this),{mapper:t})}},d81d:function(t,e,a){"use strict";var o=a("23e7"),r=a("b727").map,i=a("1dde"),n=i("map");o({target:"Array",proto:!0,forced:!n},{map:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})},e988:function(t,e,a){}}]);
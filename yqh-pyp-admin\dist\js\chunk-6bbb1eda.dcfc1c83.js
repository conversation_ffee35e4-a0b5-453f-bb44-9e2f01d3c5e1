(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6bbb1eda","chunk-2d0aeb2a"],{"0abe":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"角色名称",prop:"roleName"}},[t("el-input",{attrs:{placeholder:"角色名称"},model:{value:e.dataForm.roleName,callback:function(t){e.$set(e.dataForm,"roleName",t)},expression:"dataForm.roleName"}})],1),t("el-form-item",{attrs:{label:"备注",prop:"remark"}},[t("el-input",{attrs:{placeholder:"备注"},model:{value:e.dataForm.remark,callback:function(t){e.$set(e.dataForm,"remark",t)},expression:"dataForm.remark"}})],1),t("el-form-item",{attrs:{size:"mini",label:"授权"}},[t("el-tree",{ref:"menuListTree",staticStyle:{height:"400px","overflow-x":"scroll"},attrs:{data:e.menuList,props:e.menuListTreeProps,"node-key":"menuId","default-expand-all":!0,"show-checkbox":""}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},n=[],o=(a("99af"),a("d3b7"),a("0643"),a("4e3e"),a("159b"),a("ed08")),i={data:function(){return{visible:!1,menuList:[],menuListTreeProps:{label:"name",children:"children"},dataForm:{id:0,roleName:"",remark:""},dataRule:{roleName:[{required:!0,message:"角色名称不能为空",trigger:"blur"}]}}},methods:{init:function(e){var t=this;this.dataForm.id=e||0,this.$http({url:this.$http.adornUrl("/sys/menu/list"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;t.menuList=Object(o["d"])(a,"menuId")})).then((function(){t.visible=!0,t.$nextTick((function(){t.$refs["dataForm"].resetFields(),t.$refs.menuListTree.setCheckedKeys([])}))})).then((function(){t.dataForm.id&&t.$http({url:t.$http.adornUrl("/sys/role/info/".concat(t.dataForm.id)),method:"get",params:t.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.roleName=a.role.roleName,t.dataForm.remark=a.role.remark,a.role.menuIdList.forEach((function(e){t.$refs.menuListTree.setChecked(e,!0)})))}))}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$http({url:e.$http.adornUrl("/sys/role/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({roleId:e.dataForm.id||void 0,roleName:e.dataForm.roleName,remark:e.dataForm.remark,menuIdList:[].concat(e.$refs.menuListTree.getCheckedKeys(),e.$refs.menuListTree.getHalfCheckedKeys())})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(a.msg)}))}))}}},s=i,l=a("2877"),d=Object(l["a"])(s,r,n,!1,null,null,null);t["default"]=d.exports},a15b:function(e,t,a){"use strict";var r=a("23e7"),n=a("e330"),o=a("44ad"),i=a("fc6a"),s=a("a640"),l=n([].join),d=o!==Object,c=d||!s("join",",");r({target:"Array",proto:!0,forced:c},{join:function(e){return l(i(this),void 0===e?",":e)}})},a573:function(e,t,a){"use strict";a("ab43")},ab43:function(e,t,a){"use strict";var r=a("23e7"),n=a("d024"),o=a("c430");r({target:"Iterator",proto:!0,real:!0,forced:o},{map:n})},d024:function(e,t,a){"use strict";var r=a("c65b"),n=a("59ed"),o=a("825a"),i=a("46c4"),s=a("c5cc"),l=a("9bdd"),d=s((function(){var e=this.iterator,t=o(r(this.next,e)),a=this.done=!!t.done;if(!a)return l(e,this.mapper,[t.value,this.counter++],!0)}));e.exports=function(e){return o(this),n(e),new d(i(this),{mapper:e})}},d81d:function(e,t,a){"use strict";var r=a("23e7"),n=a("b727").map,o=a("1dde"),i=o("map");r({target:"Array",proto:!0,forced:!i},{map:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})},e4a1:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"mod-role"},[t("el-form",{attrs:{inline:!0,model:e.dataForm}},[t("el-form-item",[t("el-input",{attrs:{placeholder:"角色名称",clearable:""},model:{value:e.dataForm.roleName,callback:function(t){e.$set(e.dataForm,"roleName",t)},expression:"dataForm.roleName"}})],1),t("el-form-item",[t("el-button",{on:{click:function(t){return e.getDataList()}}},[e._v("查询")]),e.isAuth("sys:role:save")?t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addOrUpdateHandle()}}},[e._v("新增")]):e._e(),e.isAuth("sys:role:delete")?t("el-button",{attrs:{type:"danger",disabled:e.dataListSelections.length<=0},on:{click:function(t){return e.deleteHandle()}}},[e._v("批量删除")]):e._e()],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:e.dataList,border:""},on:{"selection-change":e.selectionChangeHandle}},[t("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),t("el-table-column",{attrs:{prop:"roleId","header-align":"center",align:"center",width:"80",label:"ID"}}),t("el-table-column",{attrs:{prop:"roleName","header-align":"center",align:"center",label:"角色名称"}}),t("el-table-column",{attrs:{prop:"remark","header-align":"center",align:"center",label:"备注"}}),t("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[e.isAuth("sys:role:update")?t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.addOrUpdateHandle(a.row.roleId)}}},[e._v("修改")]):e._e(),e.isAuth("sys:role:delete")?t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHandle(a.row.roleId)}}},[e._v("删除")]):e._e()]}}])})],1),t("el-pagination",{attrs:{"current-page":e.pageIndex,"page-sizes":[10,20,50,100],"page-size":e.pageSize,total:e.totalCount,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle}}),e.addOrUpdateVisible?t("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:e.getDataList}}):e._e()],1)},n=[],o=(a("99af"),a("a15b"),a("d81d"),a("a573"),a("0abe")),i={data:function(){return{dataForm:{roleName:""},dataList:[],pageIndex:1,pageSize:10,totalCount:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1}},components:{AddOrUpdate:o["default"]},activated:function(){this.getDataList()},methods:{getDataList:function(){var e=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/sys/role/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,roleName:this.dataForm.roleName})}).then((function(t){var a=t.data;a&&200===a.code?(e.dataList=a.page.list,e.totalCount=a.page.totalCount):(e.dataList=[],e.totalCount=0),e.dataListLoading=!1}))},sizeChangeHandle:function(e){this.pageSize=e,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(e){this.pageIndex=e,this.getDataList()},selectionChangeHandle:function(e){this.dataListSelections=e},addOrUpdateHandle:function(e){var t=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){t.$refs.addOrUpdate.init(e)}))},deleteHandle:function(e){var t=this,a=e?[e]:this.dataListSelections.map((function(e){return e.roleId}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(e?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:t.$http.adornUrl("/sys/role/delete"),method:"post",data:t.$http.adornData(a,!1)}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){return t.getDataList()}}):t.$message.error(a.msg)}))})).catch((function(){}))}}},s=i,l=a("2877"),d=Object(l["a"])(s,r,n,!1,null,null,null);t["default"]=d.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f2d299ce","chunk-2d0a4b8c","chunk-2d0a4b8c","chunk-58faa667"],{"083a":function(a,t,e){"use strict";var i=e("0d51"),o=TypeError;a.exports=function(a,t){if(!delete a[t])throw new o("Cannot delete property "+i(t)+" of "+i(a))}},"42e9":function(a,t,e){},"498a":function(a,t,e){"use strict";var i=e("23e7"),o=e("58a8").trim,r=e("c8d2");i({target:"String",proto:!0,forced:r("trim")},{trim:function(){return o(this)}})},"7db0":function(a,t,e){"use strict";var i=e("23e7"),o=e("b727").find,r=e("44d2"),n="find",s=!0;n in[]&&Array(1)[n]((function(){s=!1})),i({target:"Array",proto:!0,forced:s},{find:function(a){return o(this,a,arguments.length>1?arguments[1]:void 0)}}),r(n)},"7de9":function(a,t,e){"use strict";e.d(t,"g",(function(){return i})),e.d(t,"f",(function(){return o})),e.d(t,"e",(function(){return r})),e.d(t,"a",(function(){return n})),e.d(t,"b",(function(){return s})),e.d(t,"c",(function(){return l})),e.d(t,"d",(function(){return d}));var i=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],o=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],r=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],n=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],s=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],l=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],d=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]},"9e59":function(a,t,e){"use strict";e.r(t);e("b0c0");var i=function(){var a=this,t=a._self._c;return t("div",{attrs:{title:a.dataForm.id?"修改":"新增","close-on-click-modal":!1}},[t("el-form",{ref:"dataForm",attrs:{model:a.dataForm,rules:a.dataRule,"label-width":"130px"}},[t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"活动名称",prop:"name"}},[t("el-input",{attrs:{placeholder:"会议名称"},model:{value:a.dataForm.name,callback:function(t){a.$set(a.dataForm,"name",t)},expression:"dataForm.name"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"手机号",prop:"mobile"}},[t("el-input",{attrs:{placeholder:"请输入手机号"},model:{value:a.dataForm.mobile,callback:function(t){a.$set(a.dataForm,"mobile",t)},expression:"dataForm.mobile"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:4}},[t("el-form-item",{attrs:{label:"logo",prop:"logo"}},[t("div",{staticClass:"image-upload-container"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return a.openImageModal("logo")}}},[t("i",{staticClass:"el-icon-picture"}),a._v(" 选择Logo ")]),a.logoImages.length>0?t("div",{staticClass:"selected-images-preview"},[t("div",{staticClass:"image-preview-item"},[t("img",{attrs:{src:a.logoImages[0].url,alt:a.logoImages[0].url},on:{click:function(t){return a.previewImage(a.logoImages[0].url)}}}),t("div",{staticClass:"image-actions"},[t("i",{staticClass:"el-icon-zoom-in",on:{click:function(t){return a.previewImage(a.logoImages[0].url)}}}),t("i",{staticClass:"el-icon-delete",on:{click:function(t){return a.removeImage("logo",0)}}})])])]):a._e()],1),t("div",{staticStyle:{color:"red","margin-top":"10px"}},[a._v("建议尺寸：200*200，大小100kb以下")])])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"主图片",prop:"mobileBanner"}},[t("div",{staticClass:"image-upload-container"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return a.openImageModal("mobileBanner")}}},[t("i",{staticClass:"el-icon-picture"}),a._v(" 选择图片 ")]),a.mobileBannerImages.length>0?t("div",{staticClass:"selected-images-preview"},a._l(a.mobileBannerImages,(function(e,i){return t("div",{key:i,staticClass:"image-preview-item"},[t("img",{attrs:{src:e.url,alt:e.url},on:{click:function(t){return a.previewImage(e.url)}}}),t("div",{staticClass:"image-actions"},[t("i",{staticClass:"el-icon-zoom-in",on:{click:function(t){return a.previewImage(e.url)}}}),t("i",{staticClass:"el-icon-delete",on:{click:function(t){return a.removeImage("mobileBanner",i)}}})])])})),0):a._e()],1),t("div",{staticStyle:{color:"red","margin-top":"10px"}},[a._v("建议尺寸：1920*1080，大小2mb以下")])])],1),t("el-col",{attrs:{span:4}},[t("el-form-item",{attrs:{label:"背景图",prop:"background"}},[t("div",{staticClass:"image-upload-container"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return a.openImageModal("background")}}},[t("i",{staticClass:"el-icon-picture"}),a._v(" 选择背景图 ")]),a.backgroundImages.length>0?t("div",{staticClass:"selected-images-preview"},[t("div",{staticClass:"image-preview-item"},[t("img",{attrs:{src:a.backgroundImages[0].url,alt:a.backgroundImages[0].url},on:{click:function(t){return a.previewImage(a.backgroundImages[0].url)}}}),t("div",{staticClass:"image-actions"},[t("i",{staticClass:"el-icon-zoom-in",on:{click:function(t){return a.previewImage(a.backgroundImages[0].url)}}}),t("i",{staticClass:"el-icon-delete",on:{click:function(t){return a.removeImage("background",0)}}})])])]):a._e()],1),t("div",{staticStyle:{color:"red","margin-top":"10px"}},[a._v("建议尺寸：1080*1920，大小1mb以下")])])],1),t("el-col",{attrs:{span:4}},[t("el-form-item",{attrs:{label:"分享图",prop:"shareUrl"}},[t("div",{staticClass:"image-upload-container"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return a.openImageModal("shareUrl")}}},[t("i",{staticClass:"el-icon-picture"}),a._v(" 选择分享图 ")]),a.shareUrlImages.length>0?t("div",{staticClass:"selected-images-preview"},[t("div",{staticClass:"image-preview-item"},[t("img",{attrs:{src:a.shareUrlImages[0].url,alt:a.shareUrlImages[0].url},on:{click:function(t){return a.previewImage(a.shareUrlImages[0].url)}}}),t("div",{staticClass:"image-actions"},[t("i",{staticClass:"el-icon-zoom-in",on:{click:function(t){return a.previewImage(a.shareUrlImages[0].url)}}}),t("i",{staticClass:"el-icon-delete",on:{click:function(t){return a.removeImage("shareUrl",0)}}})])])]):a._e()],1),t("div",{staticStyle:{color:"red","margin-top":"10px"}},[a._v("建议尺寸：400*400，大小300kb以下")])])],1)],1),t("el-row",[t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:"背景音乐",prop:"musicUrl"}},[t("el-select",{attrs:{placeholder:"背景音乐",filterable:""},model:{value:a.dataForm.musicUrl,callback:function(t){a.$set(a.dataForm,"musicUrl",t)},expression:"dataForm.musicUrl"}},[t("el-option",{attrs:{label:"无",value:""}}),a._l(a.music,(function(a){return t("el-option",{key:a.url,attrs:{label:a.name,value:a.url}})}))],2)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"活动过期时间",prop:"expirationTime"}},[t("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime",placeholder:"选择过期时间（留空表示永不过期）",format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:a.dataForm.expirationTime,callback:function(t){a.$set(a.dataForm,"expirationTime",t)},expression:"dataForm.expirationTime"}}),t("div",{staticStyle:{color:"#909399","font-size":"12px","margin-top":"5px"}},[a._v(" 设置活动的过期时间，过期后活动将无法正常使用。留空表示永不过期。 ")])],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"抖音类型",prop:"douyinType"}},[t("el-radio-group",{model:{value:a.dataForm.douyinType,callback:function(t){a.$set(a.dataForm,"douyinType",t)},expression:"dataForm.douyinType"}},[t("el-radio",{attrs:{label:0}},[a._v("视频")]),t("el-radio",{attrs:{label:1}},[a._v("图文")])],1)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"小红书类型",prop:"xiaohongshuType"}},[t("el-radio-group",{model:{value:a.dataForm.xiaohongshuType,callback:function(t){a.$set(a.dataForm,"xiaohongshuType",t)},expression:"dataForm.xiaohongshuType"}},[t("el-radio",{attrs:{label:0}},[a._v("视频")]),t("el-radio",{attrs:{label:1}},[a._v("图文")])],1)],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"标题生成配置"}},[t("span",{staticStyle:{color:"#909399","font-size":"12px"}},[a._v("配置AI生成标题的相关参数")])])],1)],1),t("el-row",[t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"标题生成模式",prop:"nameMode"}},[t("el-radio-group",{model:{value:a.dataForm.nameMode,callback:function(t){a.$set(a.dataForm,"nameMode",t)},expression:"dataForm.nameMode"}},[t("el-radio",{attrs:{label:"ai"}},[a._v("AI生成")]),t("el-radio",{attrs:{label:"manual"}},[a._v("手动填写")])],1)],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"默认标题",prop:"defaultName"}},[t("el-input",{attrs:{placeholder:"请输入默认标题",maxlength:"50","show-word-limit":""},model:{value:a.dataForm.defaultName,callback:function(t){a.$set(a.dataForm,"defaultName",t)},expression:"dataForm.defaultName"}})],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"默认提示词",prop:"defaultTitle"}},[t("el-input",{attrs:{placeholder:"请输入默认提示词",maxlength:"100","show-word-limit":""},model:{value:a.dataForm.defaultTitle,callback:function(t){a.$set(a.dataForm,"defaultTitle",t)},expression:"dataForm.defaultTitle"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"默认用户输入",prop:"defaultUserInput"}},[t("el-input",{attrs:{type:"textarea",placeholder:"设置用户自定义补充字段的默认值，如：请在这里补充您的特殊要求或想法",rows:2,maxlength:"200","show-word-limit":""},model:{value:a.dataForm.defaultUserInput,callback:function(t){a.$set(a.dataForm,"defaultUserInput",t)},expression:"dataForm.defaultUserInput"}}),t("div",{staticStyle:{"margin-top":"5px","font-size":"12px",color:"#909399"}},[t("i",{staticClass:"el-icon-info"}),a._v(' 这个值会作为文案生成页面"自定义补充"字段的默认提示文字 ')])],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"AI标签",prop:"aiTag"}},[t("tags-editorlist",{attrs:{placeholder:"添加AI标签，如：男、女、儿童、老人等","max-tags":8,"max-length":10},on:{change:a.onAiTagChange},model:{value:a.aiTagList,callback:function(t){a.aiTagList=t},expression:"aiTagList"}}),t("div",{staticStyle:{"margin-top":"5px","font-size":"12px",color:"#909399"}},[t("i",{staticClass:"el-icon-info"}),a._v(' AI标签用于生成针对特定受众的文案。用户在"换一篇"时可以选择不同标签生成对应的文案，建议不超过8个标签 ')])],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"抖音POI",prop:"douyinPoi"}},[t("el-input",{attrs:{placeholder:"请输入抖音POI"},model:{value:a.dataForm.douyinPoi,callback:function(t){a.$set(a.dataForm,"douyinPoi",t)},expression:"dataForm.douyinPoi"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"抖音点评",prop:"douyindianping"}},[t("el-input",{attrs:{placeholder:"请输入抖音点评"},model:{value:a.dataForm.douyindianping,callback:function(t){a.$set(a.dataForm,"douyindianping",t)},expression:"dataForm.douyindianping"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"美团",prop:"meituan"}},[t("el-input",{attrs:{placeholder:"请输入美团"},model:{value:a.dataForm.meituan,callback:function(t){a.$set(a.dataForm,"meituan",t)},expression:"dataForm.meituan"}})],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"大众点评",prop:"dazhongdianping"}},[t("el-input",{attrs:{placeholder:"请输入大众点评"},model:{value:a.dataForm.dazhongdianping,callback:function(t){a.$set(a.dataForm,"dazhongdianping",t)},expression:"dataForm.dazhongdianping"}})],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"企业微信",prop:"qiyeweixin"}},[t("el-input",{attrs:{placeholder:"请输入企业微信"},model:{value:a.dataForm.qiyeweixin,callback:function(t){a.$set(a.dataForm,"qiyeweixin",t)},expression:"dataForm.qiyeweixin"}})],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"WiFi账号",prop:"wifiAccount"}},[t("el-input",{attrs:{placeholder:"请输入WiFi账号"},model:{value:a.dataForm.wifiAccount,callback:function(t){a.$set(a.dataForm,"wifiAccount",t)},expression:"dataForm.wifiAccount"}})],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"WiFi密码",prop:"wifiPassword"}},[t("el-input",{attrs:{placeholder:"请输入WiFi密码"},model:{value:a.dataForm.wifiPassword,callback:function(t){a.$set(a.dataForm,"wifiPassword",t)},expression:"dataForm.wifiPassword"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("h4",{staticStyle:{margin:"20px 0 10px 0",color:"#409EFF"}},[a._v("携程三段式配置")])])],1),t("el-row",[t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"携程首页配置",prop:"ctripConfig"}},[t("el-input",{attrs:{placeholder:"请输入携程首页配置信息"},model:{value:a.dataForm.ctripConfig,callback:function(t){a.$set(a.dataForm,"ctripConfig",t)},expression:"dataForm.ctripConfig"}})],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"携程点评配置",prop:"ctripReviewConfig"}},[t("el-input",{attrs:{placeholder:"请输入携程点评配置信息"},model:{value:a.dataForm.ctripReviewConfig,callback:function(t){a.$set(a.dataForm,"ctripReviewConfig",t)},expression:"dataForm.ctripReviewConfig"}})],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"携程笔记配置",prop:"ctripNotesConfig"}},[t("el-input",{attrs:{placeholder:"请输入携程笔记配置信息"},model:{value:a.dataForm.ctripNotesConfig,callback:function(t){a.$set(a.dataForm,"ctripNotesConfig",t)},expression:"dataForm.ctripNotesConfig"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"公众号图片",prop:"wechatQrCode"}},[t("div",{staticClass:"image-upload-container"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return a.openImageModal("wechatQrCode")}}},[t("i",{staticClass:"el-icon-picture"}),a._v(" 选择公众号图片 ")]),a.wechatQrCodeImages.length>0?t("div",{staticClass:"selected-images-preview"},[t("div",{staticClass:"image-preview-item"},[t("img",{attrs:{src:a.wechatQrCodeImages[0].url,alt:a.wechatQrCodeImages[0].url},on:{click:function(t){return a.previewImage(a.wechatQrCodeImages[0].url)}}}),t("div",{staticClass:"image-actions"},[t("i",{staticClass:"el-icon-zoom-in",on:{click:function(t){return a.previewImage(a.wechatQrCodeImages[0].url)}}}),t("i",{staticClass:"el-icon-delete",on:{click:function(t){return a.removeImage("wechatQrCode",0)}}})])])]):a._e()],1)])],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"抖音主页",prop:"zhuyeDouyin"}},[t("el-input",{attrs:{placeholder:"请输入抖音主页"},model:{value:a.dataForm.zhuyeDouyin,callback:function(t){a.$set(a.dataForm,"zhuyeDouyin",t)},expression:"dataForm.zhuyeDouyin"}})],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"快手主页",prop:"zhuyeKuaishou"}},[t("el-input",{attrs:{placeholder:"请输入快手主页"},model:{value:a.dataForm.zhuyeKuaishou,callback:function(t){a.$set(a.dataForm,"zhuyeKuaishou",t)},expression:"dataForm.zhuyeKuaishou"}})],1)],1)],1),t("el-row",a._l(a.platformVisibilityFlags,(function(e){return t("el-col",{key:e.key,attrs:{span:6}},[t("el-form-item",{attrs:{label:e.label}},[t("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:a.dataForm[e.key],callback:function(t){a.$set(a.dataForm,e.key,t)},expression:"dataForm[flag.key]"}})],1)],1)})),1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"我的小店配置"}},[t("span",{staticStyle:{color:"#909399","font-size":"12px"}},[a._v("配置商户自己的网页或小程序跳转")])])],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"是否显示团购券"}},[t("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:a.dataForm.showGroupBuying,callback:function(t){a.$set(a.dataForm,"showGroupBuying",t)},expression:"dataForm.showGroupBuying"}})],1)],1),t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:"是否显示我的小店"}},[t("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:a.dataForm.showMyShop,callback:function(t){a.$set(a.dataForm,"showMyShop",t)},expression:"dataForm.showMyShop"}})],1)],1),a.dataForm.showMyShop?t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:"小店类型",prop:"shopType"}},[t("el-radio-group",{model:{value:a.dataForm.shopType,callback:function(t){a.$set(a.dataForm,"shopType",t)},expression:"dataForm.shopType"}},[t("el-radio",{attrs:{label:0}},[a._v("网页")]),t("el-radio",{attrs:{label:1}},[a._v("小程序")])],1)],1)],1):a._e(),a.dataForm.showMyShop?t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"小店描述",prop:"shopDescription"}},[t("el-input",{attrs:{placeholder:"请输入小店描述，如：商户专属店铺"},model:{value:a.dataForm.shopDescription,callback:function(t){a.$set(a.dataForm,"shopDescription",t)},expression:"dataForm.shopDescription"}})],1)],1):a._e()],1),0===a.dataForm.shopType&&a.dataForm.showMyShop?t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"小店网页URL",prop:"shopUrl"}},[t("el-input",{attrs:{placeholder:"请输入小店网页URL，如：https://www.example.com/shop"},model:{value:a.dataForm.shopUrl,callback:function(t){a.$set(a.dataForm,"shopUrl",t)},expression:"dataForm.shopUrl"}})],1)],1)],1):a._e(),1===a.dataForm.shopType&&a.dataForm.showMyShop?t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"小程序AppID",prop:"shopAppid"}},[t("el-input",{attrs:{placeholder:"请输入小程序AppID"},model:{value:a.dataForm.shopAppid,callback:function(t){a.$set(a.dataForm,"shopAppid",t)},expression:"dataForm.shopAppid"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"小程序页面路径",prop:"shopPagePath"}},[t("el-input",{attrs:{placeholder:"请输入页面路径，如：pages/shop/index"},model:{value:a.dataForm.shopPagePath,callback:function(t){a.$set(a.dataForm,"shopPagePath",t)},expression:"dataForm.shopPagePath"}})],1)],1)],1):a._e()],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){return a.turnBack()}}},[a._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return a.dataFormSubmit()}}},[a._v("确定")])],1),t("ImageUploadModal",{attrs:{visible:a.imageModalVisible,multiple:"mobileBanner"===a.currentImageField,"max-count":"mobileBanner"===a.currentImageField?9:1,"default-images":a.getCurrentImages()},on:{"update:visible":function(t){a.imageModalVisible=t},confirm:a.handleImageConfirm}}),t("el-dialog",{attrs:{visible:a.imgDialogVisible,width:"60%"},on:{"update:visible":function(t){a.imgDialogVisible=t}}},[t("img",{attrs:{width:"100%",src:a.dialogImageUrl,alt:""}})])],1)},o=[],r=(e("d9e2"),e("4de4"),e("7db0"),e("a15b"),e("d81d"),e("a434"),e("d3b7"),e("ac1f"),e("00b4"),e("25f0"),e("3ca3"),e("5319"),e("498a"),e("0643"),e("2382"),e("fffc"),e("a573"),e("ddb0"),e("7c8d")),n=e.n(r),s=e("7de9"),l={components:{TinymceEditor:function(){return e.e("chunk-03be236c").then(e.bind(null,"26dc"))},OssUploader:function(){return e.e("chunk-2d0e97b1").then(e.bind(null,"8e5c"))},ImageUploadModal:function(){return Promise.all([e.e("chunk-e9144aa4"),e.e("chunk-0506e191")]).then(e.bind(null,"4185"))},TagsEditorlist:function(){return e.e("chunk-2a27cccc").then(e.bind(null,"9bdf"))}},data:function(){var a=function(a,t,e){var i=/^[^\u4e00-\u9fa5]{3,10}$/;i.test(t)?e():e(new Error("会议编号3到10位，不能包含中文"))};return{platformVisibilityFlags:[{key:"showDouyin",label:"是否显示抖音"},{key:"showXiaohongshu",label:"是否显示小红书"},{key:"showKuaishou",label:"是否显示快手"},{key:"showShipinhao",label:"是否显示视频号"},{key:"showDouyindianping",label:"是否显示抖音点评"},{key:"showDazhongdianping",label:"是否显示大众点评"},{key:"showMeituandianping",label:"是否显示美团点评"},{key:"showQiyeweixin",label:"是否显示企业微信"},{key:"showMiniProgram",label:"是否显示微信小程序"},{key:"showWifi",label:"是否显示wifi"},{key:"showGuanzhukuaishou",label:"是否显示关注快手"},{key:"showGuanzhudouyin",label:"是否显示关注抖音"},{key:"showShipindianzan",label:"是否显示视频点赞"},{key:"showCtrip",label:"是否显示携程首页"},{key:"showCtripReview",label:"是否显示携程点评"},{key:"showCtripNotes",label:"是否显示携程笔记"},{key:"showWechatQr",label:"是否显示微信公众号二维码"}],yesOrNo:s["g"],btnLoading:!1,configActivityType:[],appFileList:[],con:[],fileList:[],cityName:"",imgDialogVisible:!1,imgAppDialogVisible:!1,dialogImageUrl:"",dialogAppImageUrl:"",imageModalVisible:!1,currentImageField:"",mobileBannerImages:[],logoImages:[],backgroundImages:[],shareUrlImages:[],wechatQrCodeImages:[],adImages:[],music:[],provinces:[],cities:[],url:"",dataForm:{id:0,times:[],code:"",name:"",startTime:"",endTime:"",provinceId:"",cityId:"",pcBanner:"",mobileBanner:"",background:"",type:["0"],subscribeImg:"",applyBackground:"",backImg:"",pvCount:"",uvCount:"",longitude:"",latitude:"",address:"",bottomColor:"",fontColor:"",templateId:"1",applyNotify:"",hotelNotify:"",turnurl:"",isCountdown:1,isShow:1,showSub:0,isHot:0,isIndex:0,appid:"",topicGuest:0,topicSpeaker:0,scheduleGuest:0,scheduleSpeaker:0,scheduleDiscuss:0,introduction:"",showApplyNumber:0,hotelNeedApply:0,liveNeedApply:1,applySuccessLive:1,applySuccessHotel:1,onlyOneHotel:0,cancelApplyHotel:0,backUrl:"",musicUrl:"",ad:"",adColor:"",adTime:"",shareUrl:"",configActivityTypeId:"",logo:"",showDouyin:1,showXiaohongshu:1,showKuaishou:1,showShipinhao:1,showDouyindianping:1,showDazhongdianping:1,showMeituandianping:1,showQiyeweixin:1,showMiniProgram:1,showWifi:1,showGuanzhukuaishou:1,showGuanzhudouyin:1,showShipindianzan:1,douyinType:0,xiaohongshuType:1,douyinPoi:"",douyindianping:"",meituan:"",dazhongdianping:"",qiyeweixin:"",wifiAccount:"",wifiPassword:"",wechatQrCode:"",zhuyeDouyin:"",zhuyeKuaishou:"",nameMode:"ai",defaultName:"",defaultTitle:"",defaultUserInput:"",aiTag:"",showCtrip:0,ctripConfig:"",showCtripReview:0,showCtripNotes:0,ctripReviewConfig:"",ctripNotesConfig:"",showWechatQr:0,mobile:"",adminAccount:"",expirationTime:null,shopType:0,shopUrl:"",shopAppid:"",shopPagePath:"",shopDescription:"商户专属店铺",showMyShop:0,showGroupBuying:0},typeList:[{id:"0",name:"自定义"},{id:"1",name:"banner广告"},{id:"2",name:"文件下载"}],templateList:[{id:"1",name:"模板1-传统九宫格"},{id:"2",name:"模板2-含背景九宫格"},{id:"31",name:"模板3-4114布局"},{id:"4",name:"模板4-圆形九宫格"},{id:"5",name:"模板5-纯图标九宫格"},{id:"61",name:"模板61-3-3(1-2)-3-1布局"},{id:"62",name:"模板62-3-3(1-2)-2布局"},{id:"63",name:"模板63-3-3(1-2)-2-2-2布局"}],sysTemplate:[],aiTagList:[],dataRule:{code:[{required:!0,message:"会议编号不能为空",trigger:"blur"},{validator:a,trigger:"blur"}],name:[{required:!0,message:"会议名称不能为空",trigger:"blur"}],mobile:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],times:[{required:!0,message:"会议时间不能为空",trigger:"blur"}],provinceId:[{required:!0,message:"省份不能为空",trigger:"blur"}],cityId:[{required:!0,message:"城市不能为空",trigger:"blur"}],pcBanner:[{required:!0,message:"会议图片不能为空",trigger:"blur"}],mobileBanner:[{required:!0,message:"手机端图片不能为空",trigger:"blur"}],isShow:[{required:!0,message:"公众号显示不能为空",trigger:"blur"}]}}},activated:function(){this.init()},methods:{init:function(){var a=this;this.dataForm.id=this.$route.query.id||0,this.url=this.$http.adornUrl("/sys/oss/upload?token=".concat(this.$cookie.get("token"))),this.appFileList=[],this.fileList=[],this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.appid=a.$cookie.get("appid"),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/activity/activity/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.$set(a.dataForm,"times",[e.activity.startTime,e.activity.endTime]),a.appFileList=e.activity.appFileList,a.fileList=e.activity.fileList,a.dataForm.code=e.activity.code,a.dataForm.name=e.activity.name,a.dataForm.startTime=e.activity.startTime,a.dataForm.endTime=e.activity.endTime,a.dataForm.provinceId=e.activity.provinceId,a.dataForm.cityId=e.activity.cityId,a.dataForm.pcBanner=e.activity.pcBanner,a.dataForm.mobileBanner=e.activity.mobileBanner,a.dataForm.subscribeImg=e.activity.subscribeImg,a.dataForm.applyBackground=e.activity.applyBackground,a.dataForm.backImg=e.activity.backImg,a.dataForm.background=e.activity.background,a.dataForm.type=e.activity.type?e.activity.type.split(","):[],a.dataForm.pvCount=e.activity.pvCount,a.dataForm.uvCount=e.activity.uvCount,a.dataForm.longitude=e.activity.longitude,a.dataForm.latitude=e.activity.latitude,a.dataForm.address=e.activity.address,a.dataForm.introduction=e.activity.introduction,a.dataForm.bottomColor=e.activity.bottomColor,a.dataForm.fontColor=e.activity.fontColor,a.dataForm.templateId=e.activity.templateId,a.dataForm.applyNotify=e.activity.applyNotify,a.dataForm.hotelNotify=e.activity.hotelNotify,a.dataForm.isCountdown=e.activity.isCountdown,a.dataForm.isShow=e.activity.isShow,a.dataForm.showSub=e.activity.showSub,a.dataForm.isHot=e.activity.isHot,a.dataForm.isIndex=e.activity.isIndex,a.dataForm.appid=e.activity.appid,a.dataForm.topicGuest=e.activity.topicGuest,a.dataForm.topicSpeaker=e.activity.topicSpeaker,a.dataForm.scheduleGuest=e.activity.scheduleGuest,a.dataForm.scheduleSpeaker=e.activity.scheduleSpeaker,a.dataForm.scheduleDiscuss=e.activity.scheduleDiscuss,a.dataForm.showApplyNumber=e.activity.showApplyNumber,a.dataForm.hotelNeedApply=e.activity.hotelNeedApply,a.dataForm.liveNeedApply=e.activity.liveNeedApply,a.dataForm.applySuccessLive=e.activity.applySuccessLive,a.dataForm.applySuccessHotel=e.activity.applySuccessHotel,a.dataForm.onlyOneHotel=e.activity.onlyOneHotel,a.dataForm.cancelApplyHotel=e.activity.cancelApplyHotel,a.dataForm.turnurl=e.activity.turnurl,a.dataForm.backUrl=e.activity.backUrl,a.dataForm.musicUrl=e.activity.musicUrl,a.dataForm.ad=e.activity.ad,a.dataForm.adColor=e.activity.adColor,a.dataForm.adTime=e.activity.adTime,a.dataForm.shareUrl=e.activity.shareUrl,a.dataForm.configActivityTypeId=e.activity.configActivityTypeId,a.dataForm.showDouyin=e.activity.showDouyin,a.dataForm.showXiaohongshu=e.activity.showXiaohongshu,a.dataForm.showKuaishou=e.activity.showKuaishou,a.dataForm.showShipinhao=e.activity.showShipinhao,a.dataForm.showDouyindianping=e.activity.showDouyindianping,a.dataForm.showDazhongdianping=e.activity.showDazhongdianping,a.dataForm.showMeituandianping=e.activity.showMeituandianping,a.dataForm.showQiyeweixin=e.activity.showQiyeweixin,a.dataForm.showMiniProgram=e.activity.showMiniProgram,a.dataForm.showWifi=e.activity.showWifi,a.dataForm.showGuanzhukuaishou=e.activity.showGuanzhukuaishou,a.dataForm.showGuanzhudouyin=e.activity.showGuanzhudouyin,a.dataForm.showShipindianzan=e.activity.showShipindianzan,a.dataForm.logo=e.activity.logo,a.dataForm.douyinType=e.activity.douyinType,a.dataForm.xiaohongshuType=e.activity.xiaohongshuType,a.dataForm.douyinPoi=e.activity.douyinPoi,a.dataForm.douyindianping=e.activity.douyindianping,a.dataForm.meituan=e.activity.meituan,a.dataForm.dazhongdianping=e.activity.dazhongdianping,a.dataForm.qiyeweixin=e.activity.qiyeweixin,a.dataForm.wifiAccount=e.activity.wifiAccount,a.dataForm.wifiPassword=e.activity.wifiPassword,a.dataForm.wechatQrCode=e.activity.wechatQrCode,a.dataForm.zhuyeDouyin=e.activity.zhuyeDouyin,a.dataForm.zhuyeKuaishou=e.activity.zhuyeKuaishou,a.dataForm.nameMode=e.activity.nameMode||"ai",a.dataForm.defaultName=e.activity.defaultName||"",a.dataForm.defaultTitle=e.activity.defaultTitle||"",a.dataForm.defaultUserInput=e.activity.defaultUserInput||"",a.dataForm.aiTag=e.activity.aiTag||"",a.aiTagList=e.activity.aiTag?e.activity.aiTag.split(",").map((function(a){return a.trim()})).filter((function(a){return a})):[],a.dataForm.showCtrip=e.activity.showCtrip||0,a.dataForm.ctripConfig=e.activity.ctripConfig||"",a.dataForm.showCtripReview=e.activity.showCtripReview||0,a.dataForm.showCtripNotes=e.activity.showCtripNotes||0,a.dataForm.ctripReviewConfig=e.activity.ctripReviewConfig||"",a.dataForm.ctripNotesConfig=e.activity.ctripNotesConfig||"",a.dataForm.showWechatQr=e.activity.showWechatQr||0,a.dataForm.mobile=e.activity.mobile||"",a.dataForm.adminAccount=e.activity.adminAccount||"",a.dataForm.shopType=e.activity.shopType||0,a.dataForm.shopUrl=e.activity.shopUrl||"",a.dataForm.shopAppid=e.activity.shopAppid||"",a.dataForm.shopPagePath=e.activity.shopPagePath||"",a.dataForm.shopDescription=e.activity.shopDescription||"商户专属店铺",a.dataForm.showMyShop=e.activity.showMyShop||0,a.dataForm.showGroupBuying=e.activity.showGroupBuying||0,a.initImageArrays())}))})),this.getProvinces(),this.getMusic(),this.getTempalte(),this.getConfigActivityType()},turnBack:function(){this.$router.go(-1)},dataFormSubmit:function(){var a=this;this.$refs["dataForm"].validate((function(t){t&&a.$http({url:a.$http.adornUrl("/activity/activity/".concat(a.dataForm.id?"update":"save")),method:"post",data:a.$http.adornData({id:a.dataForm.id||void 0,code:a.dataForm.code,name:a.dataForm.name,startTime:a.dataForm.startTime,endTime:a.dataForm.endTime,provinceId:a.dataForm.provinceId,cityId:a.dataForm.cityId,pcBanner:a.dataForm.pcBanner,mobileBanner:a.dataForm.mobileBanner,subscribeImg:a.dataForm.subscribeImg,applyBackground:a.dataForm.applyBackground,backImg:a.dataForm.backImg,background:a.dataForm.background,type:a.dataForm.type.toString(),pvCount:a.dataForm.pvCount,uvCount:a.dataForm.uvCount,longitude:a.dataForm.longitude,latitude:a.dataForm.latitude,address:a.dataForm.address,introduction:a.dataForm.introduction,bottomColor:a.dataForm.bottomColor,fontColor:a.dataForm.fontColor,templateId:a.dataForm.templateId,applyNotify:a.dataForm.applyNotify,hotelNotify:a.dataForm.hotelNotify,isCountdown:a.dataForm.isCountdown,isShow:a.dataForm.isShow,showSub:a.dataForm.showSub,isIndex:a.dataForm.isIndex,isHot:a.dataForm.isHot,appid:a.dataForm.appid,topicGuest:a.dataForm.topicGuest,topicSpeaker:a.dataForm.topicSpeaker,scheduleGuest:a.dataForm.scheduleGuest,scheduleSpeaker:a.dataForm.scheduleSpeaker,scheduleDiscuss:a.dataForm.scheduleDiscuss,showApplyNumber:a.dataForm.showApplyNumber,hotelNeedApply:a.dataForm.hotelNeedApply,liveNeedApply:a.dataForm.liveNeedApply,applySuccessLive:a.dataForm.applySuccessLive,applySuccessHotel:a.dataForm.applySuccessHotel,onlyOneHotel:a.dataForm.onlyOneHotel,cancelApplyHotel:a.dataForm.cancelApplyHotel,turnurl:a.dataForm.turnurl,backUrl:a.dataForm.backUrl,musicUrl:a.dataForm.musicUrl,ad:a.dataForm.ad,adColor:a.dataForm.adColor,adTime:a.dataForm.adTime,shareUrl:a.dataForm.shareUrl,configActivityTypeId:a.dataForm.configActivityTypeId,showDouyin:a.dataForm.showDouyin,showXiaohongshu:a.dataForm.showXiaohongshu,showKuaishou:a.dataForm.showKuaishou,showShipinhao:a.dataForm.showShipinhao,showDouyindianping:a.dataForm.showDouyindianping,showDazhongdianping:a.dataForm.showDazhongdianping,showMeituandianping:a.dataForm.showMeituandianping,showQiyeweixin:a.dataForm.showQiyeweixin,showMiniProgram:a.dataForm.showMiniProgram,showWifi:a.dataForm.showWifi,showGuanzhukuaishou:a.dataForm.showGuanzhukuaishou,showGuanzhudouyin:a.dataForm.showGuanzhudouyin,showShipindianzan:a.dataForm.showShipindianzan,logo:a.dataForm.logo,douyinType:a.dataForm.douyinType,xiaohongshuType:a.dataForm.xiaohongshuType,douyinPoi:a.dataForm.douyinPoi,douyindianping:a.dataForm.douyindianping,meituan:a.dataForm.meituan,dazhongdianping:a.dataForm.dazhongdianping,qiyeweixin:a.dataForm.qiyeweixin,wifiAccount:a.dataForm.wifiAccount,wifiPassword:a.dataForm.wifiPassword,wechatQrCode:a.dataForm.wechatQrCode,zhuyeDouyin:a.dataForm.zhuyeDouyin,zhuyeKuaishou:a.dataForm.zhuyeKuaishou,nameMode:a.dataForm.nameMode,defaultName:a.dataForm.defaultName,defaultTitle:a.dataForm.defaultTitle,defaultUserInput:a.dataForm.defaultUserInput,aiTag:a.aiTagList.join(","),showCtrip:a.dataForm.showCtrip,ctripConfig:a.dataForm.ctripConfig,showCtripReview:a.dataForm.showCtripReview,showCtripNotes:a.dataForm.showCtripNotes,ctripReviewConfig:a.dataForm.ctripReviewConfig,ctripNotesConfig:a.dataForm.ctripNotesConfig,showWechatQr:a.dataForm.showWechatQr,mobile:a.dataForm.mobile,adminAccount:a.dataForm.adminAccount,expirationTime:a.dataForm.expirationTime,shopType:a.dataForm.shopType,shopUrl:a.dataForm.shopUrl,shopAppid:a.dataForm.shopAppid,shopPagePath:a.dataForm.shopPagePath,shopDescription:a.dataForm.shopDescription,showMyShop:a.dataForm.showMyShop,showGroupBuying:a.dataForm.showGroupBuying})}).then((function(t){var e=t.data;e&&200===e.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.turnBack()}}):a.$message.error(e.msg)}))}))},getProvinces:function(){var a=this;this.$http({url:this.$http.adornUrl("/sys/region/pid/100000"),method:"get",params:this.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code?a.provinces=e.list:a.provinces=[]}))},getMusic:function(){var a=this;this.$http({url:this.$http.adornUrl("/sys/sysmusic/findAll"),method:"get"}).then((function(t){var e=t.data;e&&200===e.code?a.music=e.result:a.music=[]}))},getTempalte:function(){var a=this;this.$http({url:this.$http.adornUrl("/sys/systemplate/findAll"),method:"get"}).then((function(t){var e=t.data;e&&200===e.code?a.sysTemplate=e.result:a.sysTemplate=[]}))},getConfigActivityType:function(){var a=this;this.$http({url:this.$http.adornUrl("/config/configactivitytype/findByAppid"),method:"get"}).then((function(t){var e=t.data;e&&200===e.code?a.configActivityType=e.result:a.configActivityType=[]}))},provinceChange:function(a){var t=this;void 0!==a&&(this.cities={},this.dataForm.cityId=void 0,this.dataForm.jieSongCityName=[],this.$http({url:this.$http.adornUrl("/sys/region/pid/".concat(a)),method:"get",params:this.$http.adornParams()}).then((function(a){var e=a.data;e&&200===e.code?t.cities=e.list:t.cities=[]})))},getCityName:function(a){var t=this.cities.find((function(t){return t.id===a}));this.cityName=t.name.replace("市","")},checkFileSize:function(a){return a.size/1024/1024>6?(this.$message.error("".concat(a.name,"文件大于6MB，请选择小于6MB大小的图片")),!1):!(a.size/1024>100)||new Promise((function(t,e){new n.a(a,{quality:.8,success:function(a){t(a)}})}))},beforeUploadHandle:function(a){if("image/jpg"!==a.type&&"image/jpeg"!==a.type&&"image/png"!==a.type&&"image/gif"!==a.type)return this.$message.error("只支持jpg、png、gif格式的图片！"),!1},successHandle:function(a,t,e){this.fileList=e,this.successNum1++,a&&200===a.code?this.dataForm.pcBanner&&0!=this.dataForm.pcBanner.length?this.dataForm.pcBanner+=","+a.url:this.dataForm.pcBanner=a.url:this.$message.error(a.msg)},appSuccessHandle:function(a,t,e){this.appFileList=e,this.successNum++,a&&200===a.code?this.dataForm.mobileBanner&&0!=this.dataForm.mobileBanner.length?this.dataForm.mobileBanner+=","+a.url:this.dataForm.mobileBanner=a.url:this.$message.error(a.msg)},backgroundSuccessHandle:function(a,t,e){a&&200===a.code?(this.dataForm.background=a.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(a.msg)},subscribeImgSuccessHandle:function(a,t,e){a&&200===a.code?(this.dataForm.subscribeImg=a.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(a.msg)},applyBackgroundSuccessHandle:function(a,t,e){a&&200===a.code?(this.dataForm.applyBackground=a.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(a.msg)},backImgSuccessHandle:function(a,t,e){a&&200===a.code?(this.dataForm.backImg=a.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(a.msg)},adSuccessHandle:function(a,t,e){a&&200===a.code?(this.dataForm.ad=a.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(a.msg)},shareUrlSuccessHandle:function(a,t,e){a&&200===a.code?(this.dataForm.shareUrl=a.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(a.msg)},logoSuccessHandle:function(a,t,e){a&&200===a.code?(this.dataForm.logo=a.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(a.msg)},handleRemove:function(a){this.dataForm.pcBanner=(","+this.dataForm.pcBanner+",").replace(","+a.url+",",",").substr(1).replace(/,$/,""),this.fileList.splice(this.fileList.indexOf(a),1)},handleAppRemove:function(a){this.dataForm.mobileBanner=(","+this.dataForm.mobileBanner+",").replace(","+a.url+",",",").substr(1).replace(/,$/,""),this.appFileList.splice(this.appFileList.indexOf(a),1)},handlePictureCardPreview:function(a){this.dialogImageUrl=a.url,this.imgDialogVisible=!0},handleAppPictureCardPreview:function(a){this.dialogAppImageUrl=a.url,this.imgAppDialogVisible=!0},dateChange:function(a){this.dataForm.startTime=a[0],this.dataForm.endTime=a[1],console.log(a)},openImageModal:function(a){this.currentImageField=a,this.imageModalVisible=!0},getCurrentImages:function(){switch(this.currentImageField){case"mobileBanner":return this.mobileBannerImages;case"logo":return this.logoImages;case"background":return this.backgroundImages;case"shareUrl":return this.shareUrlImages;case"wechatQrCode":return this.wechatQrCodeImages;case"ad":return this.adImages;default:return[]}},handleImageConfirm:function(a){switch(this.currentImageField){case"mobileBanner":this.mobileBannerImages=a,this.dataForm.mobileBanner=a.map((function(a){return a.url})).join(",");break;case"logo":this.logoImages=a,this.dataForm.logo=a.length>0?a[0].url:"";break;case"background":this.backgroundImages=a,this.dataForm.background=a.length>0?a[0].url:"";break;case"shareUrl":this.shareUrlImages=a,this.dataForm.shareUrl=a.length>0?a[0].url:"";break;case"wechatQrCode":this.wechatQrCodeImages=a,this.dataForm.wechatQrCode=a.length>0?a[0].url:"";break;case"ad":this.adImages=a,this.dataForm.ad=a.length>0?a[0].url:"";break}},removeImage:function(a,t){switch(a){case"mobileBanner":this.mobileBannerImages.splice(t,1),this.dataForm.mobileBanner=this.mobileBannerImages.map((function(a){return a.url})).join(",");break;case"logo":this.logoImages.splice(t,1),this.dataForm.logo=this.logoImages.length>0?this.logoImages[0].url:"";break;case"background":this.backgroundImages.splice(t,1),this.dataForm.background=this.backgroundImages.length>0?this.backgroundImages[0].url:"";break;case"shareUrl":this.shareUrlImages.splice(t,1),this.dataForm.shareUrl=this.shareUrlImages.length>0?this.shareUrlImages[0].url:"";break;case"wechatQrCode":this.wechatQrCodeImages.splice(t,1),this.dataForm.wechatQrCode=this.wechatQrCodeImages.length>0?this.wechatQrCodeImages[0].url:"";break;case"ad":this.adImages.splice(t,1),this.dataForm.ad=this.adImages.length>0?this.adImages[0].url:"";break}},previewImage:function(a){this.dialogImageUrl=a,this.imgDialogVisible=!0},initImageArrays:function(){this.dataForm.mobileBanner&&(this.mobileBannerImages=this.dataForm.mobileBanner.split(",").map((function(a,t){return{id:"mobile_".concat(t),url:a.trim(),createDate:(new Date).toISOString()}})).filter((function(a){return a.url}))),this.dataForm.logo&&(this.logoImages=[{id:"logo_1",url:this.dataForm.logo,createDate:(new Date).toISOString()}]),this.dataForm.background&&(this.backgroundImages=[{id:"background_1",url:this.dataForm.background,createDate:(new Date).toISOString()}]),this.dataForm.shareUrl&&(this.shareUrlImages=[{id:"share_1",url:this.dataForm.shareUrl,createDate:(new Date).toISOString()}]),this.dataForm.wechatQrCode&&(this.wechatQrCodeImages=[{id:"wechatQrCode_1",url:this.dataForm.wechatQrCode,createDate:(new Date).toISOString()}]),this.dataForm.ad&&(this.adImages=[{id:"ad_1",url:this.dataForm.ad,createDate:(new Date).toISOString()}])},onAiTagChange:function(a){this.aiTagList=a,this.dataForm.aiTag=a.join(",")}}},d=l,c=(e("d073"),e("2877")),m=Object(c["a"])(d,i,o,!1,null,"0c39ecea",null);t["default"]=m.exports},a15b:function(a,t,e){"use strict";var i=e("23e7"),o=e("e330"),r=e("44ad"),n=e("fc6a"),s=e("a640"),l=o([].join),d=r!==Object,c=d||!s("join",",");i({target:"Array",proto:!0,forced:c},{join:function(a){return l(n(this),void 0===a?",":a)}})},a434:function(a,t,e){"use strict";var i=e("23e7"),o=e("7b0b"),r=e("23cb"),n=e("5926"),s=e("07fa"),l=e("3a34"),d=e("3511"),c=e("65f0"),m=e("8418"),u=e("083a"),p=e("1dde"),h=p("splice"),g=Math.max,y=Math.min;i({target:"Array",proto:!0,forced:!h},{splice:function(a,t){var e,i,p,h,f,F,v=o(this),b=s(v),w=r(a,b),k=arguments.length;for(0===k?e=i=0:1===k?(e=0,i=b-w):(e=k-2,i=y(g(n(t),0),b-w)),d(b+e-i),p=c(v,i),h=0;h<i;h++)f=w+h,f in v&&m(p,h,v[f]);if(p.length=i,e<i){for(h=w;h<b-i;h++)f=h+i,F=h+e,f in v?v[F]=v[f]:u(v,F);for(h=b;h>b-i+e;h--)u(v,h-1)}else if(e>i)for(h=b-i;h>w;h--)f=h+i-1,F=h+e-1,f in v?v[F]=v[f]:u(v,F);for(h=0;h<e;h++)v[h+w]=arguments[h+2];return l(v,b-i+e),p}})},a573:function(a,t,e){"use strict";e("ab43")},ab43:function(a,t,e){"use strict";var i=e("23e7"),o=e("d024"),r=e("c430");i({target:"Iterator",proto:!0,real:!0,forced:r},{map:o})},c8d2:function(a,t,e){"use strict";var i=e("5e77").PROPER,o=e("d039"),r=e("5899"),n="​᠎";a.exports=function(a){return o((function(){return!!r[a]()||n[a]()!==n||i&&r[a].name!==a}))}},d024:function(a,t,e){"use strict";var i=e("c65b"),o=e("59ed"),r=e("825a"),n=e("46c4"),s=e("c5cc"),l=e("9bdd"),d=s((function(){var a=this.iterator,t=r(i(this.next,a)),e=this.done=!!t.done;if(!e)return l(a,this.mapper,[t.value,this.counter++],!0)}));a.exports=function(a){return r(this),o(a),new d(n(this),{mapper:a})}},d073:function(a,t,e){"use strict";e("42e9")},d81d:function(a,t,e){"use strict";var i=e("23e7"),o=e("b727").map,r=e("1dde"),n=r("map");i({target:"Array",proto:!0,forced:!n},{map:function(a){return o(this,a,arguments.length>1?arguments[1]:void 0)}})},f665:function(a,t,e){"use strict";var i=e("23e7"),o=e("2266"),r=e("59ed"),n=e("825a"),s=e("46c4");i({target:"Iterator",proto:!0,real:!0},{find:function(a){n(this),r(a);var t=s(this),e=0;return o(t,(function(t,i){if(a(t,e++))return i(t)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},fffc:function(a,t,e){"use strict";e("f665")}}]);
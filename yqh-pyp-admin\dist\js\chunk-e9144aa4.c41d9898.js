(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e9144aa4"],{1951:function(e,t,a){},2909:function(e,t,a){"use strict";function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}function r(e){if(Array.isArray(e))return i(e)}a.d(t,"a",(function(){return l}));a("a4d3"),a("e01a"),a("d28b"),a("a630"),a("d3b7"),a("3ca3"),a("ddb0");function s(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}a("fb6a"),a("b0c0"),a("ac1f"),a("00b4"),a("25f0");function n(e,t){if(e){if("string"==typeof e)return i(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?i(e,t):void 0}}a("d9e2");function o(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(e){return r(e)||s(e)||n(e)||o()}},"2ca0":function(e,t,a){"use strict";var i=a("23e7"),r=a("4625"),s=a("06cf").f,n=a("50c4"),o=a("577e"),l=a("5a34"),c=a("1d80"),d=a("ab13"),u=a("c430"),h=r("".slice),f=Math.min,g=d("startsWith"),p=!u&&!g&&!!function(){var e=s(String.prototype,"startsWith");return e&&!e.writable}();i({target:"String",proto:!0,forced:!p&&!g},{startsWith:function(e){var t=o(c(this));l(e);var a=n(f(arguments.length>1?arguments[1]:void 0,t.length)),i=o(e);return h(t,a,a+i.length)===i}})},"2ed9":function(e,t,a){"use strict";a("1951")},4185:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e._self._c;return t("el-dialog",{staticClass:"image-upload-dialog",attrs:{title:"选择图片",visible:e.dialogVisible,width:"80%","close-on-click-modal":!1,"append-to-body":"","modal-append-to-body":!1},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[t("el-tabs",{on:{"tab-click":e.handleTabClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[t("el-tab-pane",{attrs:{label:"素材库",name:"library"}},[t("div",{staticClass:"library-content"},[t("div",{staticClass:"search-bar"},[t("el-input",{staticStyle:{width:"300px","margin-bottom":"20px"},attrs:{placeholder:"搜索图片..."},on:{input:e.handleSearch},model:{value:e.searchKeyword,callback:function(t){e.searchKeyword=t},expression:"searchKeyword"}},[t("i",{staticClass:"el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.libraryLoading,expression:"libraryLoading"}],staticClass:"image-grid"},e._l(e.libraryImages,(function(a){return t("div",{key:a.id,staticClass:"image-item",class:{selected:e.isSelected(a)},on:{click:function(t){return e.toggleSelect(a)}}},[t("img",{attrs:{src:a.url,alt:a.url}}),t("div",{staticClass:"image-overlay"},[e.isSelected(a)?t("i",{staticClass:"el-icon-check selected-icon"}):e._e()])])})),0),e.libraryTotal>0?t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"center"},attrs:{"current-page":e.currentPage,"page-sizes":[12,24,48,96],"page-size":e.pageSize,total:e.libraryTotal,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}):e._e()],1)]),t("el-tab-pane",{attrs:{label:"本地上传",name:"upload"}},[t("div",{staticClass:"upload-content"},[t("el-upload",{ref:"upload",attrs:{action:e.uploadUrl,headers:e.uploadHeaders,"before-upload":e.beforeUpload,"on-success":e.handleUploadSuccess,"on-error":e.handleUploadError,"file-list":e.uploadFileList,multiple:e.multiple,limit:e.maxCount,"on-exceed":e.handleExceed,"list-type":"picture-card",accept:"image/*"}},[t("i",{staticClass:"el-icon-plus"})]),t("div",{staticClass:"upload-tips"},[t("p",[e._v("支持格式：JPG、PNG、GIF")]),t("p",[e._v("单张图片大小不超过 "+e._s(e.maxSize)+"MB")]),e.multiple?t("p",[e._v("最多可上传 "+e._s(e.maxCount)+" 张图片")]):e._e()])],1)])],1),e.selectedImages.length>0?t("div",{staticClass:"selected-preview"},[t("h4",[e._v("已选择 "+e._s(e.selectedImages.length)+" 张图片：")]),t("div",{staticClass:"selected-images"},e._l(e.selectedImages,(function(a,i){return t("div",{key:a.id||a.url,staticClass:"selected-item"},[t("img",{attrs:{src:a.url,alt:a.url}}),t("div",{staticClass:"remove-btn",on:{click:function(t){return e.removeSelected(i)}}},[t("i",{staticClass:"el-icon-close"})])])})),0)]):e._e(),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.handleClose}},[e._v("取消")]),t("el-button",{attrs:{type:"primary",disabled:0===e.selectedImages.length},on:{click:e.handleConfirm}},[e._v(" 确定选择 ("+e._s(e.selectedImages.length)+") ")])],1)],1)},r=[],s=a("2909"),n=(a("4de4"),a("c740"),a("14d9"),a("a434"),a("b0c0"),a("a9e3"),a("b680"),a("d3b7"),a("ac1f"),a("00b4"),a("2ca0"),a("0643"),a("2382"),a("9a9a"),a("7c8d")),o=a.n(n),l={name:"ImageUploadModal",props:{visible:{type:Boolean,default:!1},multiple:{type:Boolean,default:!0},maxCount:{type:Number,default:9},maxSize:{type:Number,default:2},defaultImages:{type:Array,default:function(){return[]}}},data:function(){return{dialogVisible:this.visible,activeTab:"library",searchKeyword:"",libraryLoading:!1,libraryImages:[],libraryTotal:0,currentPage:1,pageSize:12,selectedImages:[],uploadFileList:[],uploadUrl:"",uploadHeaders:{}}},watch:{visible:function(e){var t=this;this.dialogVisible=e,e&&(this.init(),this.$nextTick((function(){t.setDialogZIndex()})))},defaultImages:{handler:function(e){this.selectedImages=Object(s["a"])(e)},immediate:!0}},mounted:function(){this.initUploadConfig(),this.setDialogZIndex()},methods:{init:function(){var e=this;this.selectedImages=Object(s["a"])(this.defaultImages),this.uploadFileList=[],this.activeTab="library",this.searchKeyword="",this.currentPage=1,this.libraryImages=[],this.libraryTotal=0,this.$nextTick((function(){e.$refs.upload&&e.$refs.upload.clearFiles()})),"library"===this.activeTab&&this.loadLibraryImages()},initUploadConfig:function(){this.uploadUrl=this.$http.adornUrl("/sys/oss/upload?token=".concat(this.$cookie.get("token"))),this.uploadHeaders={token:this.$cookie.get("token")}},setDialogZIndex:function(){this.$nextTick((function(){var e=document.querySelector(".image-upload-dialog .el-dialog__wrapper");e&&(e.style.zIndex="3000");var t=document.querySelector(".image-upload-dialog .el-overlay");t&&(t.style.zIndex="2999")}))},handleTabClick:function(e){"library"===e.name&&this.loadLibraryImages()},loadLibraryImages:function(){var e=this;this.libraryLoading=!0,this.$http({url:this.$http.adornUrl("/sys/oss/list"),method:"get",params:this.$http.adornParams({page:this.currentPage,limit:this.pageSize,appid:this.$cookie.get("appid"),sidx:"id",order:"desc"})}).then((function(t){var a=t.data;a&&200===a.code?(e.libraryImages=a.page.list.filter((function(t){return e.isImageUrl(t.url)})),e.libraryTotal=a.page.totalCount):(e.libraryImages=[],e.libraryTotal=0),e.libraryLoading=!1})).catch((function(){e.libraryLoading=!1}))},handleSearch:function(){this.currentPage=1,this.loadLibraryImages()},handleSizeChange:function(e){this.pageSize=e,this.currentPage=1,this.loadLibraryImages()},handleCurrentChange:function(e){this.currentPage=e,this.loadLibraryImages()},isImageUrl:function(e){return e&&/.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(e)},toggleSelect:function(e){var t=this.selectedImages.findIndex((function(t){return t.id&&t.id===e.id||t.url===e.url}));t>-1?this.selectedImages.splice(t,1):this.multiple?this.selectedImages.length<this.maxCount?this.selectedImages.push(e):this.$message.warning("最多只能选择 ".concat(this.maxCount," 张图片")):this.selectedImages=[e]},isSelected:function(e){return this.selectedImages.some((function(t){return t.id&&t.id===e.id||t.url===e.url}))},removeSelected:function(e){this.selectedImages.splice(e,1)},beforeUpload:function(e){var t=e.type.startsWith("image/"),a=e.size/1024/1024<this.maxSize;return t?a?!(e.size/1024>100)||new Promise((function(t,a){new o.a(e,{quality:.8,success:function(e){t(e)},error:function(e){a(e)}})})):(this.$message.error("图片大小不能超过 ".concat(this.maxSize,"MB!")),!1):(this.$message.error("只能上传图片文件!"),!1)},handleUploadSuccess:function(e,t,a){if(e&&200===e.code){var i={id:Date.now(),url:e.url,createDate:(new Date).toISOString(),fileSize:t.size,fileName:t.name};this.multiple?this.selectedImages.length<this.maxCount&&this.selectedImages.push(i):this.selectedImages=[i],this.$message.success("上传成功")}else this.$message.error(e.msg||"上传失败")},handleUploadError:function(e,t,a){this.$message.error("上传失败: "+e.message)},handleExceed:function(e,t){this.$message.warning("最多只能上传 ".concat(this.maxCount," 张图片"))},handleConfirm:function(){this.$emit("confirm",this.selectedImages),this.handleClose()},handleClose:function(){var e=this;this.dialogVisible=!1,this.$emit("update:visible",!1),this.$emit("close"),this.$nextTick((function(){e.selectedImages=[],e.uploadFileList=[],e.searchKeyword="",e.currentPage=1,e.libraryImages=[],e.libraryTotal=0,e.$refs.upload&&e.$refs.upload.clearFiles()}))},formatFileSize:function(e){if(!e||0===e)return"0.00 MB";var t=e/1048576;return t.toFixed(2)+" MB"}}},c=l,d=(a("2ed9"),a("5968"),a("2877")),u=Object(d["a"])(c,i,r,!1,null,"560511b0",null);t["default"]=u.exports},"44e7":function(e,t,a){"use strict";var i=a("861d"),r=a("c6b6"),s=a("b622"),n=s("match");e.exports=function(e){var t;return i(e)&&(void 0!==(t=e[n])?!!t:"RegExp"===r(e))}},"4df4":function(e,t,a){"use strict";var i=a("0366"),r=a("c65b"),s=a("7b0b"),n=a("9bdd"),o=a("e95a"),l=a("68ee"),c=a("07fa"),d=a("8418"),u=a("9a1f"),h=a("35a1"),f=Array;e.exports=function(e){var t=s(e),a=l(this),g=arguments.length,p=g>1?arguments[1]:void 0,m=void 0!==p;m&&(p=i(p,g>2?arguments[2]:void 0));var b,v,y,I,x,C,S=h(t),w=0;if(!S||this===f&&o(S))for(b=c(t),v=a?new this(b):f(b);b>w;w++)C=m?p(t[w],w):t[w],d(v,w,C);else for(v=a?new this:[],I=u(t,S),x=I.next;!(y=r(x,I)).done;w++)C=m?n(I,p,[y.value,w],!0):y.value,d(v,w,C);return v.length=w,v}},5968:function(e,t,a){"use strict";a("e7a8")},"5a34":function(e,t,a){"use strict";var i=a("44e7"),r=TypeError;e.exports=function(e){if(i(e))throw new r("The method doesn't accept regular expressions");return e}},"9a9a":function(e,t,a){"use strict";a("a732")},a630:function(e,t,a){"use strict";var i=a("23e7"),r=a("4df4"),s=a("1c7e"),n=!s((function(e){Array.from(e)}));i({target:"Array",stat:!0,forced:n},{from:r})},a732:function(e,t,a){"use strict";var i=a("23e7"),r=a("2266"),s=a("59ed"),n=a("825a"),o=a("46c4");i({target:"Iterator",proto:!0,real:!0},{some:function(e){n(this),s(e);var t=o(this),a=0;return r(t,(function(t,i){if(e(t,a++))return i()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},ab13:function(e,t,a){"use strict";var i=a("b622"),r=i("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(a){try{return t[r]=!1,"/./"[e](t)}catch(i){}}return!1}},c740:function(e,t,a){"use strict";var i=a("23e7"),r=a("b727").findIndex,s=a("44d2"),n="findIndex",o=!0;n in[]&&Array(1)[n]((function(){o=!1})),i({target:"Array",proto:!0,forced:o},{findIndex:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}}),s(n)},e7a8:function(e,t,a){},fb6a:function(e,t,a){"use strict";var i=a("23e7"),r=a("e8b5"),s=a("68ee"),n=a("861d"),o=a("23cb"),l=a("07fa"),c=a("fc6a"),d=a("8418"),u=a("b622"),h=a("1dde"),f=a("f36a"),g=h("slice"),p=u("species"),m=Array,b=Math.max;i({target:"Array",proto:!0,forced:!g},{slice:function(e,t){var a,i,u,h=c(this),g=l(h),v=o(e,g),y=o(void 0===t?g:t,g);if(r(h)&&(a=h.constructor,s(a)&&(a===m||r(a.prototype))?a=void 0:n(a)&&(a=a[p],null===a&&(a=void 0)),a===m||void 0===a))return f(h,v,y);for(i=new(void 0===a?m:a)(b(y-v,0)),u=0;v<y;v++,u++)v in h&&d(i,u,h[v]);return i.length=u,i}})}}]);
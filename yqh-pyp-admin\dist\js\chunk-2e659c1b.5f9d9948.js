(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2e659c1b","chunk-0506e191","chunk-0506e191"],{1148:function(t,e,a){"use strict";var i=a("5926"),r=a("577e"),o=a("1d80"),s=RangeError;t.exports=function(t){var e=r(o(this)),a="",n=i(t);if(n<0||n===1/0)throw new s("Wrong number of repetitions");for(;n>0;(n>>>=1)&&(e+=e))1&n&&(a+=e);return a}},2356:function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("div",[e("el-dialog",{attrs:{title:t.dataForm.id?"修改活动图片":"新增活动图片","close-on-click-modal":!1,visible:t.visible,width:"800px"},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.dataFormSubmit()}}},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"图片名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"请输入图片名称"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"排序",prop:"paixu"}},[e("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,max:9999,placeholder:"排序值"},model:{value:t.dataForm.paixu,callback:function(e){t.$set(t.dataForm,"paixu",e)},expression:"dataForm.paixu"}})],1)],1)],1),e("el-form-item",{attrs:{label:"图片文件",prop:"mediaUrl"}},[e("div",{staticClass:"image-upload-section"},[e("el-button",{attrs:{type:"primary",disabled:t.uploading},on:{click:t.openImageModal}},[e("i",{staticClass:"el-icon-picture"}),t._v(" "+t._s(t.selectedImages.length>0?"重新选择图片":"选择图片")+" ")]),t.selectedImages.length>0?e("div",{staticClass:"selected-image-preview"},[e("div",{staticClass:"image-item"},[e("img",{attrs:{src:t.selectedImages[0].url,alt:t.selectedImages[0].url}}),e("div",{staticClass:"image-info"},[e("p",{staticClass:"image-name"},[t._v(t._s(t.dataForm.name||"未命名图片"))]),e("p",{staticClass:"image-url"},[t._v(t._s(t.selectedImages[0].url))]),t.dataForm.fileSize?e("p",{staticClass:"image-size"},[t._v("文件大小: "+t._s(t.formatFileSize(t.dataForm.fileSize)))]):t._e()]),e("div",{staticClass:"image-actions"},[e("el-button",{attrs:{size:"mini"},on:{click:function(e){return t.previewImage(t.selectedImages[0].url)}}},[e("i",{staticClass:"el-icon-zoom-in"}),t._v(" 预览 ")]),e("el-button",{attrs:{size:"mini",type:"danger"},on:{click:t.removeImage}},[e("i",{staticClass:"el-icon-delete"}),t._v(" 删除 ")])],1)])]):t._e(),t.selectedImages.length||t.dataForm.mediaUrl?t._e():e("div",{staticClass:"upload-tip"},[e("i",{staticClass:"el-icon-picture-outline"}),e("p",[t._v("请选择图片文件")])])],1)]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"图片类型",prop:"type"}},[e("el-radio-group",{model:{value:t.dataForm.type,callback:function(e){t.$set(t.dataForm,"type",e)},expression:"dataForm.type"}},[e("el-radio",{attrs:{label:0}},[e("span",{staticClass:"radio-label"},[e("i",{staticClass:"el-icon-files"}),t._v(" 素材 ")])]),e("el-radio",{attrs:{label:1}},[e("span",{staticClass:"radio-label"},[e("i",{staticClass:"el-icon-picture"}),t._v(" 成品 ")])])],1),e("div",{staticClass:"type-description"},[0===t.dataForm.type?e("p",{staticClass:"type-desc material"},[t._v("素材：原始图片文件，可用于后期编辑制作")]):t._e(),1===t.dataForm.type?e("p",{staticClass:"type-desc product"},[t._v("成品：最终制作完成的图片，可直接使用")]):t._e()])],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"使用次数"}},[e("el-input",{attrs:{readonly:""},model:{value:t.dataForm.useCount,callback:function(e){t.$set(t.dataForm,"useCount",e)},expression:"dataForm.useCount"}},[e("template",{slot:"append"},[t._v("次")])],2)],1)],1),t.dataForm.id?e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"关联文案",prop:"activityTextId"}},[e("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择关联文案",clearable:"",filterable:""},model:{value:t.dataForm.activityTextId,callback:function(e){t.$set(t.dataForm,"activityTextId",e)},expression:"dataForm.activityTextId"}},t._l(t.activityTexts,(function(a){return e("el-option",{key:a.id,attrs:{label:a.title||a.content,value:a.id}},[e("span",{staticStyle:{float:"left"}},[t._v(t._s(a.title||a.content))]),e("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v("ID: "+t._s(a.id))])])})),1)],1)],1):t._e()],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"AI标签",prop:"aiTag"}},[e("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"从活动AI标签中选择"},on:{change:t.onAiTagSelectionChange},model:{value:t.selectedAiTags,callback:function(e){t.selectedAiTags=e},expression:"selectedAiTags"}},t._l(t.activityAiTags,(function(t){return e("el-option",{key:t,attrs:{label:t,value:t}})})),1),e("div",{staticStyle:{"margin-top":"5px","font-size":"12px",color:"#909399"}},[e("i",{staticClass:"el-icon-info"}),t._v(" 从活动的AI标签中选择适合此图片的标签。当用户选择特定标签生成文案时，会优先使用对应标签的图片。不选择表示通用图片 ")])],1)],1)],1),t.dataForm.id?e("el-row",{attrs:{gutter:20}}):t._e()],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary",loading:t.submitting},on:{click:function(e){return t.dataFormSubmit()}}},[t._v(" "+t._s(t.submitting?"保存中...":"确定")+" ")])],1),e("ImageUploadModal",{attrs:{visible:t.imageModalVisible,multiple:!1,"max-count":1,"default-images":t.selectedImages},on:{"update:visible":function(e){t.imageModalVisible=e},confirm:t.handleImageConfirm}}),e("el-dialog",{attrs:{visible:t.previewVisible,width:"60%","append-to-body":"","z-index":3100},on:{"update:visible":function(e){t.previewVisible=e}}},[e("img",{attrs:{width:"100%",src:t.previewImageUrl,alt:"图片预览"}})])],1)],1)},r=[],o=(a("4de4"),a("a15b"),a("d81d"),a("b680"),a("d3b7"),a("3ca3"),a("498a"),a("0643"),a("2382"),a("a573"),a("ddb0"),{components:{ImageUploadModal:function(){return Promise.all([a.e("chunk-2d0e1c2e"),a.e("chunk-e9144aa4"),a.e("chunk-58faa667")]).then(a.bind(null,"4185"))}},data:function(){return{visible:!1,submitting:!1,uploading:!1,imageModalVisible:!1,previewVisible:!1,previewImageUrl:"",selectedImages:[],activityTexts:[],activityAiTags:[],selectedAiTags:[],dataForm:{repeatToken:"",id:0,name:"",fileSize:0,mediaUrl:"",activityId:"",paixu:0,type:0,useCount:0,activityTextId:"",aiTag:""},dataRule:{name:[{required:!0,message:"图片名称不能为空",trigger:"blur"},{min:2,max:50,message:"图片名称长度在 2 到 50 个字符",trigger:"blur"}],mediaUrl:[{required:!0,message:"请选择图片文件",trigger:"change"}],type:[{required:!0,message:"请选择图片类型",trigger:"change"}],paixu:[{type:"number",message:"排序必须为数字值",trigger:"blur"}]}}},methods:{init:function(t,e){var a=this;this.getToken(),this.loadActivityTexts(e),this.loadActivityAiTags(e),this.dataForm.id=t||0,this.dataForm.activityId=e,this.visible=!0,this.selectedImages=[],this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id?a.loadActivityImageInfo():(a.dataForm.type=0,a.dataForm.paixu=0,a.dataForm.useCount=0)}))},loadActivityImageInfo:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activityimage/info/".concat(this.dataForm.id)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;if(a&&200===a.code){var i=a.activityImage;t.dataForm.name=i.name,t.dataForm.fileSize=i.fileSize||0,t.dataForm.mediaUrl=i.mediaUrl,t.dataForm.paixu=i.paixu||0,t.dataForm.type=i.type||0,t.dataForm.useCount=i.useCount||0,t.dataForm.activityTextId=i.activityTextId||"",t.dataForm.aiTag=i.aiTag||"",t.selectedAiTags=i.aiTag?i.aiTag.split(",").map((function(t){return t.trim()})).filter((function(t){return t})):[],i.mediaUrl&&(t.selectedImages=[{id:"existing_".concat(t.dataForm.id),url:i.mediaUrl,createDate:(new Date).toISOString()}])}})).catch((function(e){t.$message.error("加载图片信息失败"),console.error(e)}))},loadActivityTexts:function(t){var e=this;this.$http({url:this.$http.adornUrl("/activity/activitytext/list"),method:"get",params:this.$http.adornParams({activityId:t,page:1,limit:1e3})}).then((function(t){var a,i=t.data;i&&200===i.code&&(e.activityTexts=(null===(a=i.page)||void 0===a?void 0:a.list)||[])})).catch((function(t){console.error("加载文案列表失败:",t),e.activityTexts=[]}))},getToken:function(){var t=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.repeatToken=a.result)})).catch((function(t){console.error("获取token失败:",t)}))},openImageModal:function(){this.imageModalVisible=!0},handleImageConfirm:function(t){if(t&&t.length>0){if(this.selectedImages=t,this.dataForm.mediaUrl=t[0].url,!this.dataForm.name&&t[0].url){var e=t[0].url.split("/").pop().split(".")[0];this.dataForm.name=e}t[0].fileSize?this.dataForm.fileSize=t[0].fileSize:this.dataForm.fileSize=0}},removeImage:function(){this.selectedImages=[],this.dataForm.mediaUrl="",this.dataForm.fileSize=0},previewImage:function(t){this.previewImageUrl=t,this.previewVisible=!0},formatFileSize:function(t){if(!t||0===t)return"0.00 MB";var e=t/1048576;return e.toFixed(2)+" MB"},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){if(e){if(!t.dataForm.mediaUrl)return void t.$message.error("请选择图片文件");t.submitting=!0;var a={repeatToken:t.dataForm.repeatToken,id:t.dataForm.id||void 0,name:t.dataForm.name,fileSize:t.dataForm.fileSize||0,mediaUrl:t.dataForm.mediaUrl,activityId:t.dataForm.activityId,paixu:t.dataForm.paixu||0,type:t.dataForm.type,useCount:t.dataForm.useCount||0,activityTextId:t.dataForm.activityTextId||"",aiTag:t.selectedAiTags.join(",")};t.$http({url:t.$http.adornUrl("/activity/activityimage/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData(a)}).then((function(e){var a=e.data;t.submitting=!1,a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):(t.$message.error(a.msg||"操作失败"),"不能重复提交"!==a.msg&&t.getToken())})).catch((function(e){t.submitting=!1,t.$message.error("提交失败，请重试"),console.error("提交失败:",e)}))}}))},loadActivityAiTags:function(t){var e=this;this.$http({url:this.$http.adornUrl("/web/activity/activitytext/getAiTags"),method:"get",params:this.$http.adornParams({activityId:t})}).then((function(t){var a=t.data;a&&200===a.code&&(e.activityAiTags=a.tags||[])})).catch((function(t){console.error("加载活动AI标签失败:",t),e.activityAiTags=[]}))},onAiTagSelectionChange:function(t){this.selectedAiTags=t,this.dataForm.aiTag=t.join(",")}}}),s=o,n=(a("500d"),a("2877")),l=Object(n["a"])(s,i,r,!1,null,"3430b466",null);e["default"]=l.exports},"498a":function(t,e,a){"use strict";var i=a("23e7"),r=a("58a8").trim,o=a("c8d2");i({target:"String",proto:!0,forced:o("trim")},{trim:function(){return r(this)}})},"500d":function(t,e,a){"use strict";a("8b5b")},"8b5b":function(t,e,a){},a15b:function(t,e,a){"use strict";var i=a("23e7"),r=a("e330"),o=a("44ad"),s=a("fc6a"),n=a("a640"),l=r([].join),c=o!==Object,d=c||!n("join",",");i({target:"Array",proto:!0,forced:d},{join:function(t){return l(s(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var i=a("23e7"),r=a("d024"),o=a("c430");i({target:"Iterator",proto:!0,real:!0,forced:o},{map:r})},b680:function(t,e,a){"use strict";var i=a("23e7"),r=a("e330"),o=a("5926"),s=a("408a"),n=a("1148"),l=a("d039"),c=RangeError,d=String,m=Math.floor,u=r(n),p=r("".slice),g=r(1..toFixed),f=function(t,e,a){return 0===e?a:e%2===1?f(t,e-1,a*t):f(t*t,e/2,a)},v=function(t){var e=0,a=t;while(a>=4096)e+=12,a/=4096;while(a>=2)e+=1,a/=2;return e},h=function(t,e,a){var i=-1,r=a;while(++i<6)r+=e*t[i],t[i]=r%1e7,r=m(r/1e7)},b=function(t,e){var a=6,i=0;while(--a>=0)i+=t[a],t[a]=m(i/e),i=i%e*1e7},y=function(t){var e=6,a="";while(--e>=0)if(""!==a||0===e||0!==t[e]){var i=d(t[e]);a=""===a?i:a+u("0",7-i.length)+i}return a},F=l((function(){return"0.000"!==g(8e-5,3)||"1"!==g(.9,0)||"1.25"!==g(1.255,2)||"1000000000000000128"!==g(0xde0b6b3a7640080,0)}))||!l((function(){g({})}));i({target:"Number",proto:!0,forced:F},{toFixed:function(t){var e,a,i,r,n=s(this),l=o(t),m=[0,0,0,0,0,0],g="",F="0";if(l<0||l>20)throw new c("Incorrect fraction digits");if(n!==n)return"NaN";if(n<=-1e21||n>=1e21)return d(n);if(n<0&&(g="-",n=-n),n>1e-21)if(e=v(n*f(2,69,1))-69,a=e<0?n*f(2,-e,1):n/f(2,e,1),a*=4503599627370496,e=52-e,e>0){h(m,0,a),i=l;while(i>=7)h(m,1e7,0),i-=7;h(m,f(10,i,1),0),i=e-1;while(i>=23)b(m,1<<23),i-=23;b(m,1<<i),h(m,1,1),b(m,2),F=y(m)}else h(m,0,a),h(m,1<<-e,0),F=y(m)+u("0",l);return l>0?(r=F.length,F=g+(r<=l?"0."+u("0",l-r)+F:p(F,0,r-l)+"."+p(F,r-l))):F=g+F,F}})},c8d2:function(t,e,a){"use strict";var i=a("5e77").PROPER,r=a("d039"),o=a("5899"),s="​᠎";t.exports=function(t){return r((function(){return!!o[t]()||s[t]()!==s||i&&o[t].name!==t}))}},d024:function(t,e,a){"use strict";var i=a("c65b"),r=a("59ed"),o=a("825a"),s=a("46c4"),n=a("c5cc"),l=a("9bdd"),c=n((function(){var t=this.iterator,e=o(i(this.next,t)),a=this.done=!!e.done;if(!a)return l(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return o(this),r(t),new c(s(this),{mapper:t})}},d81d:function(t,e,a){"use strict";var i=a("23e7"),r=a("b727").map,o=a("1dde"),s=o("map");i({target:"Array",proto:!0,forced:!s},{map:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-712ead25"],{"9fe2":function(t,n,s){"use strict";s.r(n);s("14d9");var e=function(){var t=this,n=t._self._c;return n("div",{staticClass:"site-wrapper site-page--not-found"},[n("div",{staticClass:"site-content__wrapper"},[n("div",{staticClass:"site-content"},[n("h2",{staticClass:"not-found-title"},[t._v("400")]),t._m(0),n("el-button",{on:{click:function(n){return t.$router.go(-1)}}},[t._v("返回上一页")]),n("el-button",{staticClass:"not-found-btn-gohome",attrs:{type:"primary"},on:{click:function(n){return t.$router.push({name:"home"})}}},[t._v("进入首页")])],1)])])},a=[function(){var t=this,n=t._self._c;return n("p",{staticClass:"not-found-desc"},[t._v("抱歉！您访问的页面"),n("em",[t._v("失联")]),t._v("啦 ...")])}],o={},i=o,c=(s("a8a2"),s("2877")),u=Object(c["a"])(i,e,a,!1,null,null,null);n["default"]=u.exports},a8a2:function(t,n,s){"use strict";s("da01")},da01:function(t,n,s){}}]);
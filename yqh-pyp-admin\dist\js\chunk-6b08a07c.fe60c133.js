(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6b08a07c"],{"7de9":function(e,t,a){"use strict";a.d(t,"g",(function(){return i})),a.d(t,"f",(function(){return r})),a.d(t,"e",(function(){return n})),a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return l})),a.d(t,"c",(function(){return d})),a.d(t,"d",(function(){return s}));var i=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],r=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],n=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],o=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],l=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],d=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],s=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]},b291:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"是否显示联系酒店按钮",prop:"isMobile"}},[t("el-select",{attrs:{placeholder:"是否显示联系酒店按钮",filterable:""},model:{value:e.dataForm.isMobile,callback:function(t){e.$set(e.dataForm,"isMobile",t)},expression:"dataForm.isMobile"}},e._l(e.yesOrNo,(function(e){return t("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"是否显示详情按钮",prop:"isDetail"}},[t("el-select",{attrs:{placeholder:"是否显示详情按钮",filterable:""},model:{value:e.dataForm.isDetail,callback:function(t){e.$set(e.dataForm,"isDetail",t)},expression:"dataForm.isDetail"}},e._l(e.yesOrNo,(function(e){return t("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"联系方式",prop:"mobile"}},[t("el-input",{attrs:{placeholder:"联系方式"},model:{value:e.dataForm.mobile,callback:function(t){e.$set(e.dataForm,"mobile",t)},expression:"dataForm.mobile"}})],1),t("el-form-item",{attrs:{label:"地址",prop:"address"}},[t("el-input",{attrs:{placeholder:"地址"},model:{value:e.dataForm.address,callback:function(t){e.$set(e.dataForm,"address",t)},expression:"dataForm.address"}})],1),t("el-form-item",{attrs:{label:"标签(逗号隔开)",prop:"brief"}},[t("el-input",{attrs:{placeholder:"标签(逗号隔开)"},model:{value:e.dataForm.brief,callback:function(t){e.$set(e.dataForm,"brief",t)},expression:"dataForm.brief"}})],1),t("el-form-item",{attrs:{label:"内容",prop:"content"}},[t("tinymce-editor",{ref:"editor",model:{value:e.dataForm.content,callback:function(t){e.$set(e.dataForm,"content",t)},expression:"dataForm.content"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},r=[],n=(a("d3b7"),a("3ca3"),a("ddb0"),a("7de9")),o={data:function(){return{yesOrNo:n["g"],visible:!1,dataForm:{id:0,activityId:"",hotelId:"",status:"",orderBy:"",brief:"",content:"",address:"",mobile:"",isMobile:1,isDetail:1},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],hotelId:[{required:!0,message:"酒店id不能为空",trigger:"blur"}],status:[{required:!0,message:"销售状态：0-未开启，1-已开启不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序，数值越小越靠前不能为空",trigger:"blur"}]}}},components:{TinymceEditor:function(){return Promise.all([a.e("chunk-2d0e1c2e"),a.e("chunk-03be236c"),a.e("chunk-2d0a4b8c")]).then(a.bind(null,"26dc"))}},methods:{init:function(e){var t=this;this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){t.$refs["dataForm"].resetFields(),t.dataForm.id&&t.$http({url:t.$http.adornUrl("/hotel/hotelactivity/info/".concat(t.dataForm.id)),method:"get",params:t.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.activityId=a.hotelActivity.activityId,t.dataForm.hotelId=a.hotelActivity.hotelId,t.dataForm.status=a.hotelActivity.status,t.dataForm.orderBy=a.hotelActivity.orderBy,t.dataForm.brief=a.hotelActivity.brief,t.dataForm.content=a.hotelActivity.content,t.dataForm.isMobile=a.hotelActivity.isMobile,t.dataForm.address=a.hotelActivity.address,t.dataForm.isDetail=a.hotelActivity.isDetail,t.dataForm.mobile=a.hotelActivity.mobile)}))}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$http({url:e.$http.adornUrl("/hotel/hotelactivity/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,activityId:e.dataForm.activityId,hotelId:e.dataForm.hotelId,status:e.dataForm.status,orderBy:e.dataForm.orderBy,content:e.dataForm.content,brief:e.dataForm.brief,isMobile:e.dataForm.isMobile,address:e.dataForm.address,isDetail:e.dataForm.isDetail,mobile:e.dataForm.mobile})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(a.msg)}))}))}}},l=o,d=a("2877"),s=Object(d["a"])(l,i,r,!1,null,null,null);t["default"]=s.exports}}]);
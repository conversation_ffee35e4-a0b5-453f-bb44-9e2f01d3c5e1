(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d237145"],{fa1e:function(a,t,e){"use strict";e.r(t);e("b0c0");var i=function(){var a=this,t=a._self._c;return t("el-dialog",{attrs:{title:a.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:a.visible},on:{"update:visible":function(t){a.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:a.dataForm,rules:a.dataRule,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"类型",prop:"type"}},[t("el-select",{attrs:{disabled:4==a.dataForm.type},model:{value:a.dataForm.type,callback:function(t){a.$set(a.dataForm,"type",t)},expression:"dataForm.type"}},a._l(a.applyTypeList,(function(a){return t("el-option",{key:a.id,attrs:{label:a.name,value:a.id,disabled:a.disabled}})})),1)],1),!a.dataForm.type||1!=a.dataForm.type&&2!=a.dataForm.type?a._e():t("el-form-item",{attrs:{label:"选项数据",prop:"selectData"}},[t("el-input",{attrs:{placeholder:"选项数据，请使用“,”隔开"},model:{value:a.dataForm.selectData,callback:function(t){a.$set(a.dataForm,"selectData",t)},expression:"dataForm.selectData"}})],1),t("el-form-item",{attrs:{label:"字段名称",prop:"finalName"}},[t("el-input",{attrs:{placeholder:"字段最终名称"},model:{value:a.dataForm.finalName,callback:function(t){a.$set(a.dataForm,"finalName",t)},expression:"dataForm.finalName"}})],1),t("el-form-item",{attrs:{label:"提示文字信息",prop:"placeholder"}},[t("el-input",{attrs:{placeholder:"字段最终名称"},model:{value:a.dataForm.placeholder,callback:function(t){a.$set(a.dataForm,"placeholder",t)},expression:"dataForm.placeholder"}})],1),t("el-form-item",{attrs:{label:"是否必填",prop:"required"}},[t("el-select",{model:{value:a.dataForm.required,callback:function(t){a.$set(a.dataForm,"required",t)},expression:"dataForm.required"}},a._l(a.requiredList,(function(a){return t("el-option",{key:a.id,attrs:{label:a.name,value:a.id}})})),1)],1),t("el-form-item",{attrs:{label:"排序",prop:"paixu"}},[t("el-input-number",{attrs:{min:0,max:100,label:"排序"},model:{value:a.dataForm.paixu,callback:function(t){a.$set(a.dataForm,"paixu",t)},expression:"dataForm.paixu"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){a.visible=!1}}},[a._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return a.dataFormSubmit()}}},[a._v("确定")])],1)],1)},r=[],l={data:function(){return{visible:!1,dataForm:{id:0,activityId:"",applyConfigId:"",applyActivityChannelConfigId:"",defaultValue:"",selectData:"",required:0,type:0,finalName:"",placeholder:"",extra:"",paixu:0},applyTypeList:[{id:0,name:"填空"},{id:1,name:"单选"},{id:2,name:"多选"},{id:3,name:"扩展",disabled:!0},{id:4,name:"特殊",disabled:!0},{id:5,name:"日期"}],requiredList:[{id:0,name:"否"},{id:1,name:"是"}],dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],applyConfigId:[{required:!0,message:"报名配置表id不能为空",trigger:"blur"}],applyActivityChannelConfigId:[{required:!0,message:"报名通道表id不能为空",trigger:"blur"}],type:[{required:!0,message:"类型不能为空",trigger:"blur"}],finalName:[{required:!0,message:"字段最终名称不能为空",trigger:"blur"}],paixu:[{required:!0,message:"排序不能为空",trigger:"blur"}],selectData:[{required:!0,message:"选择数据不能为空",trigger:"blur"}],required:[{required:!0,message:"是否必填不能为空",trigger:"blur"}]}}},methods:{init:function(a,t,e,i,r){var l=this;this.visible=!0,this.$nextTick((function(){l.$refs["dataForm"].resetFields(),l.dataForm.activityId=a,l.dataForm.applyConfigId=e,6==l.dataForm.applyConfigId||12==l.dataForm.applyConfigId?l.dataForm.type=4:l.dataForm.type=0,l.dataForm.applyActivityChannelConfigId=t,l.dataForm.finalName=i,l.dataForm.id=r||0,l.dataForm.id?l.$http({url:l.$http.adornUrl("/apply/applyactivityconfig/info/".concat(l.dataForm.id)),method:"get",params:l.$http.adornParams()}).then((function(a){var t=a.data;t&&200===t.code&&(l.dataForm.activityId=t.applyActivityConfig.activityId,l.dataForm.applyConfigId=t.applyActivityConfig.applyConfigId,l.dataForm.applyActivityChannelConfigId=t.applyActivityConfig.applyActivityChannelConfigId,l.dataForm.type=t.applyActivityConfig.type,l.dataForm.finalName=t.applyActivityConfig.finalName,l.dataForm.placeholder=t.applyActivityConfig.placeholder,l.dataForm.extra=t.applyActivityConfig.extra,l.dataForm.paixu=t.applyActivityConfig.paixu,l.dataForm.selectData=t.applyActivityConfig.selectData,l.dataForm.defaultValue=t.applyActivityConfig.defaultValue,l.dataForm.required=t.applyActivityConfig.required)})):l.getApplyConfig()}))},getApplyConfig:function(){var a=this;this.$http({url:this.$http.adornUrl("/apply/applyconfig/info/".concat(this.dataForm.applyConfigId)),method:"get",params:this.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.applyConfig=e.applyConfig,a.dataForm.finalName=e.applyConfig.name)}))},dataFormSubmit:function(){var a=this;this.$refs["dataForm"].validate((function(t){t&&a.$http({url:a.$http.adornUrl("/apply/applyactivityconfig/".concat(a.dataForm.id?"update":"save")),method:"post",data:a.$http.adornData({id:a.dataForm.id||void 0,activityId:a.dataForm.activityId,applyConfigId:a.dataForm.applyConfigId,applyActivityChannelConfigId:a.dataForm.applyActivityChannelConfigId,type:a.dataForm.type,finalName:a.dataForm.finalName,placeholder:a.dataForm.placeholder,extra:a.dataForm.extra,paixu:a.dataForm.paixu,required:a.dataForm.required,defaultValue:a.dataForm.defaultValue,selectData:a.dataForm.selectData})}).then((function(t){var e=t.data;e&&200===e.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.visible=!1,a.$emit("refreshDataList")}}):a.$message.error(e.msg)}))}))}}},o=l,d=e("2877"),n=Object(d["a"])(o,i,r,!1,null,null,null);t["default"]=n.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-74750435"],{"41c38":function(e,a,t){"use strict";t.r(a);t("b0c0");var r=function(){var e=this,a=e._self._c;return a("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(a){e.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"资讯名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"资讯名称"},model:{value:e.dataForm.name,callback:function(a){e.$set(e.dataForm,"name",a)},expression:"dataForm.name"}})],1),a("el-form-item",{attrs:{label:"资讯类型",prop:"newsTypeId"}},[a("el-select",{model:{value:e.dataForm.newsTypeId,callback:function(a){e.$set(e.dataForm,"newsTypeId",a)},expression:"dataForm.newsTypeId"}},e._l(e.newsType,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),a("el-form-item",{attrs:{label:"第三方链接",prop:"url"}},[a("el-input",{attrs:{placeholder:"第三方链接"},model:{value:e.dataForm.url,callback:function(a){e.$set(e.dataForm,"url",a)},expression:"dataForm.url"}})],1),a("el-form-item",{attrs:{label:"作者",prop:"username"}},[a("el-input",{attrs:{placeholder:"作者"},model:{value:e.dataForm.username,callback:function(a){e.$set(e.dataForm,"username",a)},expression:"dataForm.username"}})],1),a("el-form-item",{attrs:{label:"图片",prop:"picUrl"}},[a("el-upload",{staticClass:"avatar-uploader",attrs:{"before-upload":e.checkFileSize,"list-type":"picture-card","show-file-list":!1,accept:".jpg, .jpeg, .png, .gif","on-success":e.appSuccessHandle,action:e.url}},[e.dataForm.picUrl?a("img",{staticClass:"avatar",attrs:{width:"100px",src:e.dataForm.picUrl}}):a("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),a("el-form-item",{attrs:{label:"简介",prop:"brief"}},[a("el-input",{attrs:{placeholder:"简介"},model:{value:e.dataForm.brief,callback:function(a){e.$set(e.dataForm,"brief",a)},expression:"dataForm.brief"}})],1),a("el-form-item",{attrs:{label:"详细介绍",prop:"content"}},[a("tinymce-editor",{ref:"editor",model:{value:e.dataForm.content,callback:function(a){e.$set(e.dataForm,"content",a)},expression:"dataForm.content"}})],1),a("el-form-item",{attrs:{label:"排序",prop:"paixu"}},[a("el-input",{attrs:{placeholder:"排序"},model:{value:e.dataForm.paixu,callback:function(a){e.$set(e.dataForm,"paixu",a)},expression:"dataForm.paixu"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){e.visible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},n=[],o=(t("d3b7"),t("3ca3"),t("ddb0"),t("7c8d")),i=t.n(o),s={components:{TinymceEditor:function(){return Promise.all([t.e("chunk-03be236c"),t.e("chunk-2d0a4b8c")]).then(t.bind(null,"26dc"))},OssUploader:function(){return t.e("chunk-2d0e97b1").then(t.bind(null,"8e5c"))}},data:function(){return{url:"",newsType:[],visible:!1,dataForm:{id:0,name:"",newsTypeId:"",paixu:0,picUrl:"",brief:"",content:"",url:"",username:""},dataRule:{name:[{required:!0,message:"资讯名称不能为空",trigger:"blur"}],newsTypeId:[{required:!0,message:"资讯类型不能为空",trigger:"blur"}],paixu:[{required:!0,message:"排序不能为空",trigger:"blur"}],content:[{required:!0,message:"详细介绍不能为空",trigger:"blur"}]}}},methods:{init:function(e){var a=this;this.url=this.$http.adornUrl("/sys/oss/upload?token=".concat(this.$cookie.get("token"))),this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/news/news/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(e){var t=e.data;t&&200===t.code&&(a.dataForm.name=t.news.name,a.dataForm.newsTypeId=t.news.newsTypeId,a.dataForm.paixu=t.news.paixu,a.dataForm.picUrl=t.news.picUrl,a.dataForm.brief=t.news.brief,a.dataForm.content=t.news.content,a.dataForm.username=t.news.username,a.dataForm.url=t.news.url)}))})),this.findByAppid()},findByAppid:function(){var e=this;this.$http({url:this.$http.adornUrl("/news/newstype/findByAppid"),method:"get",params:this.$http.adornParams()}).then((function(a){var t=a.data;t&&200===t.code&&(e.newsType=t.result)}))},checkFileSize:function(e){return e.size/1024/1024>6?(this.$message.error("".concat(e.name,"文件大于6MB，请选择小于6MB大小的图片")),!1):!(e.size/1024>100)||new Promise((function(a,t){new i.a(e,{quality:.8,success:function(e){a(e)}})}))},beforeUploadHandle:function(e){if("image/jpg"!==e.type&&"image/jpeg"!==e.type&&"image/png"!==e.type&&"image/gif"!==e.type)return this.$message.error("只支持jpg、png、gif格式的图片！"),!1},appSuccessHandle:function(e,a,t){e&&200===e.code?this.dataForm.picUrl=e.url:this.$message.error(e.msg)},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(a){a&&e.$http({url:e.$http.adornUrl("/news/news/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,name:e.dataForm.name,newsTypeId:e.dataForm.newsTypeId,paixu:e.dataForm.paixu,picUrl:e.dataForm.picUrl,brief:e.dataForm.brief,content:e.dataForm.content,url:e.dataForm.url,username:e.dataForm.username})}).then((function(a){var t=a.data;t&&200===t.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(t.msg)}))}))}}},l=s,d=t("2877"),c=Object(d["a"])(l,r,n,!1,null,null,null);a["default"]=c.exports}}]);
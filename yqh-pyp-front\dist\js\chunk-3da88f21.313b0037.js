(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3da88f21"],{8788:function(t,e,n){"use strict";n("aec6")},aec6:function(t,e,n){},f4b0:function(t,e,n){(function(e,n){t.exports=n()})("undefined"!==typeof self&&self,(function(){return function(t){var e={};function n(i){if(e[i])return e[i].exports;var o=e[i]={i:i,l:!1,exports:{}};return t[i].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=t,n.c=e,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},n.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)n.d(i,o,function(e){return t[e]}.bind(null,o));return i},n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s="fb15")}({"0bfb":function(t,e,n){"use strict";var i=n("cb7c");t.exports=function(){var t=i(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},"230e":function(t,e,n){var i=n("d3f4"),o=n("7726").document,a=i(o)&&i(o.createElement);t.exports=function(t){return a?o.createElement(t):{}}},"2aba":function(t,e,n){var i=n("7726"),o=n("32e9"),a=n("69a8"),s=n("ca5a")("src"),r=n("fa5b"),c="toString",u=(""+r).split(c);n("8378").inspectSource=function(t){return r.call(t)},(t.exports=function(t,e,n,r){var c="function"==typeof n;c&&(a(n,"name")||o(n,"name",e)),t[e]!==n&&(c&&(a(n,s)||o(n,s,t[e]?""+t[e]:u.join(String(e)))),t===i?t[e]=n:r?t[e]?t[e]=n:o(t,e,n):(delete t[e],o(t,e,n)))})(Function.prototype,c,(function(){return"function"==typeof this&&this[s]||r.call(this)}))},"2d00":function(t,e){t.exports=!1},"32e9":function(t,e,n){var i=n("86cc"),o=n("4630");t.exports=n("9e1e")?function(t,e,n){return i.f(t,e,o(1,n))}:function(t,e,n){return t[e]=n,t}},3846:function(t,e,n){n("9e1e")&&"g"!=/./g.flags&&n("86cc").f(RegExp.prototype,"flags",{configurable:!0,get:n("0bfb")})},4630:function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},5537:function(t,e,n){var i=n("8378"),o=n("7726"),a="__core-js_shared__",s=o[a]||(o[a]={});(t.exports=function(t,e){return s[t]||(s[t]=void 0!==e?e:{})})("versions",[]).push({version:i.version,mode:n("2d00")?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},"69a8":function(t,e){var n={}.hasOwnProperty;t.exports=function(t,e){return n.call(t,e)}},"6a99":function(t,e,n){var i=n("d3f4");t.exports=function(t,e){if(!i(t))return t;var n,o;if(e&&"function"==typeof(n=t.toString)&&!i(o=n.call(t)))return o;if("function"==typeof(n=t.valueOf)&&!i(o=n.call(t)))return o;if(!e&&"function"==typeof(n=t.toString)&&!i(o=n.call(t)))return o;throw TypeError("Can't convert object to primitive value")}},"6b54":function(t,e,n){"use strict";n("3846");var i=n("cb7c"),o=n("0bfb"),a=n("9e1e"),s="toString",r=/./[s],c=function(t){n("2aba")(RegExp.prototype,s,t,!0)};n("79e5")((function(){return"/a/b"!=r.call({source:"a",flags:"b"})}))?c((function(){var t=i(this);return"/".concat(t.source,"/","flags"in t?t.flags:!a&&t instanceof RegExp?o.call(t):void 0)})):r.name!=s&&c((function(){return r.call(this)}))},7726:function(t,e){var n=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},"79e5":function(t,e){t.exports=function(t){try{return!!t()}catch(e){return!0}}},"7f7f":function(t,e,n){var i=n("86cc").f,o=Function.prototype,a=/^\s*function ([^ (]*)/,s="name";s in o||n("9e1e")&&i(o,s,{configurable:!0,get:function(){try{return(""+this).match(a)[1]}catch(t){return""}}})},8378:function(t,e){var n=t.exports={version:"2.6.10"};"number"==typeof __e&&(__e=n)},"86cc":function(t,e,n){var i=n("cb7c"),o=n("c69a"),a=n("6a99"),s=Object.defineProperty;e.f=n("9e1e")?Object.defineProperty:function(t,e,n){if(i(t),e=a(e,!0),i(n),o)try{return s(t,e,n)}catch(r){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(t[e]=n.value),t}},"9e1e":function(t,e,n){t.exports=!n("79e5")((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},c69a:function(t,e,n){t.exports=!n("9e1e")&&!n("79e5")((function(){return 7!=Object.defineProperty(n("230e")("div"),"a",{get:function(){return 7}}).a}))},ca5a:function(t,e){var n=0,i=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++n+i).toString(36))}},cb7c:function(t,e,n){var i=n("d3f4");t.exports=function(t){if(!i(t))throw TypeError(t+" is not an object!");return t}},d3f4:function(t,e){t.exports=function(t){return"object"===typeof t?null!==t:"function"===typeof t}},f6fd:function(t,e){(function(t){var e="currentScript",n=t.getElementsByTagName("script");e in t||Object.defineProperty(t,e,{get:function(){try{throw new Error}catch(i){var t,e=(/.*at [^\(]*\((.*):.+:.+\)$/gi.exec(i.stack)||[!1])[1];for(t in n)if(n[t].src==e||"interactive"==n[t].readyState)return n[t];return null}}})})(document)},fa5b:function(t,e,n){t.exports=n("5537")("native-function-to-string",Function.toString)},fb15:function(t,e,n){"use strict";var i;n.r(e),"undefined"!==typeof window&&(n("f6fd"),(i=window.document.currentScript)&&(i=i.src.match(/(.+\/)[^/]+\.js(\?.*)?$/))&&(n.p=i[1])),n("7f7f");var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("canvas",{ref:t.domId,staticClass:"app-sign-canvas",attrs:{id:t.domId},on:{mousedown:function(e){return e.preventDefault(),e.stopPropagation(),t.handleMousedown(e)},mousemove:function(e){return e.preventDefault(),e.stopPropagation(),t.handleMousemove(e)},mouseup:function(e){return e.preventDefault(),e.stopPropagation(),t.handleMouseup(e)},mouseleave:function(e){return e.preventDefault(),e.stopPropagation(),t.handleMouseleave(e)},touchstart:function(e){return e.preventDefault(),e.stopPropagation(),t.handleTouchstart(e)},touchmove:function(e){return e.preventDefault(),e.stopPropagation(),t.handleTouchmove(e)},touchend:function(e){return e.preventDefault(),e.stopPropagation(),t.handleTouchend(e)}}},[t._v("\n    您的浏览器不支持canvas技术,请升级浏览器!\n")])},a=[],s=(n("6b54"),{name:"SignCanvas",model:{value:"image",event:"confirm"},props:{image:{required:!1,type:[String],default:null},options:{required:!1,type:[Object],default:function(){return null}}},data:function(){return{value:null,domId:"sign-canvas-".concat(Math.random().toString(36).substr(2)),canvas:null,context:null,dpr:1,config:{isFullScreen:!1,isFullCover:!1,isDpr:!1,lastWriteSpeed:1,lastWriteWidth:2,lineCap:"round",lineJoin:"round",canvasWidth:600,canvasHeight:600,isShowBorder:!0,bgColor:"#fcc",borderWidth:1,borderColor:"#ff787f",writeWidth:5,maxWriteWidth:30,minWriteWidth:5,writeColor:"#101010",isSign:!1,imgType:"png"},resizeTimer:null}},mounted:function(){var t=this;this.init(),window.addEventListener("resize",(function(){t.resizeTimer&&clearTimeout(t.resizeTimer),t.resizeTimer=setTimeout((function(){t.init()}),100)}))},watch:{options:{handler:function(){this.init()},deep:!0}},methods:{init:function(){var t=this.options;if(t)for(var e in t)this.config[e]=t[e];this.dpr="undefined"!==typeof window&&this.config.isDpr&&(window.devicePixelRatio||window.webkitDevicePixelRatio||window.mozDevicePixelRatio)||1,this.canvas=document.getElementById(this.domId),this.context=this.canvas.getContext("2d"),this.canvas.style.background=this.config.bgColor,this.config.isFullScreen&&(this.config.canvasWidth=window.innerWidth||document.body.clientWidth,this.config.canvasHeight=window.innerHeight||document.body.clientHeight,this.config.isFullCover&&(this.canvas.style.position="fixed",this.canvas.style.top=0,this.canvas.style.left=0,this.canvas.style.margin=0,this.canvas.style.zIndex=20001)),this.canvas.height=this.config.canvasWidth,this.canvas.width=this.config.canvasHeight,this.canvasInit(),this.canvasClear()},setLineWidth:function(){var t=(new Date).getTime(),e=t-this.config.lastWriteTime;this.config.lastWriteTime=t;var n=this.config.minWriteWidth+(this.config.maxWriteWidth-this.config.minWriteWidth)*e/30;if(n<this.config.minWriteWidth?n=this.config.minWriteWidth:n>this.config.maxWriteWidth&&(n=this.config.maxWriteWidth),n=n.toFixed(2),this.config.isSign)this.context.lineWidth=this.config.writeWidth*this.dpr;else{var i=this.config.lastWriteWidth=this.config.lastWriteWidth/4*3+n/4;this.context.lineWidth=i*this.dpr}},writeBegin:function(t){this.config.isWrite=!0,this.config.lastWriteTime=(new Date).getTime(),this.config.lastPoint=t,this.writeContextStyle()},writing:function(t){this.context.beginPath(),this.context.moveTo(this.config.lastPoint.x*this.dpr,this.config.lastPoint.y*this.dpr),this.context.lineTo(t.x*this.dpr,t.y*this.dpr),this.setLineWidth(),this.context.stroke(),this.config.lastPoint=t,this.context.closePath()},writeEnd:function(t){this.config.isWrite=!1,this.config.lastPoint=t,this.saveAsImg()},writeContextStyle:function(){this.context.beginPath(),this.context.strokeStyle=this.config.writeColor,this.context.lineCap=this.config.lineCap,this.context.lineJoin=this.config.lineJoin},canvasClear:function(){this.context.save(),this.context.strokeStyle=this.config.writeColor,this.context.clearRect(0,0,this.canvas.width,this.canvas.height),this.context.beginPath(),this.context.lineWidth=this.config.borderWidth*this.dpr,this.context.strokeStyle=this.config.borderColor;var t=this.config.borderWidth/2*this.dpr;this.config.isShowBorder&&(this.context.moveTo(t,t),this.context.lineTo(this.canvas.width-t,t),this.context.lineTo(this.canvas.width-t,this.canvas.height-t),this.context.lineTo(t,this.canvas.height-t),this.context.closePath(),this.context.stroke()),this.config.isShowBorder&&!this.config.isSign&&(this.context.moveTo(0,0),this.context.lineTo(this.canvas.width,this.canvas.height),this.context.lineTo(this.canvas.width,this.canvas.height/2),this.context.lineTo(0,this.canvas.height/2),this.context.lineTo(0,this.canvas.height),this.context.lineTo(this.canvas.width,0),this.context.lineTo(this.canvas.width/2,0),this.context.lineTo(this.canvas.width/2,this.canvas.height),this.context.stroke()),this.$emit("confirm",null),this.context.restore()},saveAsImg:function(){var t=new Image;return t.src=this.canvas.toDataURL("image/".concat(this.config.imgType)),this.$emit("confirm",t.src),t.src},canvasInit:function(){this.canvas.width=this.config.canvasWidth*this.dpr,this.canvas.height=this.config.canvasHeight*this.dpr,this.canvas.style.width="".concat(this.config.canvasWidth,"px"),this.canvas.style.height="".concat(this.config.canvasHeight,"px"),this.config.emptyCanvas=this.canvas.toDataURL("image/".concat(this.config.imgType))},handleMousedown:function(t){this.writeBegin({x:t.offsetX||t.clientX,y:t.offsetY||t.clientY})},handleMousemove:function(t){this.config.isWrite&&this.writing({x:t.offsetX,y:t.offsetY})},handleMouseup:function(t){this.writeEnd({x:t.offsetX,y:t.offsetY})},handleMouseleave:function(t){this.config.isWrite=!1,this.config.lastPoint={x:t.offsetX,y:t.offsetY}},handleTouchstart:function(t){var e=t.targetTouches[0],n=e.clientX?e.clientX-this.getRect().left:e.pageX-this.offset(e.target,"left"),i=e.clientY?e.clientY-this.getRect().top:e.pageY-this.offset(e.target,"top");this.writeBegin({x:n,y:i})},handleTouchmove:function(t){var e=t.targetTouches[0],n=e.clientX?e.clientX-this.getRect().left:e.pageX-this.offset(e.target,"left"),i=e.clientY?e.clientY-this.getRect().top:e.pageY-this.offset(e.target,"top");this.config.isWrite&&this.writing({x:n,y:i})},handleTouchend:function(t){var e=t.targetTouches,n=t.changedTouches,i=e&&e.length&&e[0]||n&&n.length&&n[0],o=i.clientX?i.clientX-this.getRect().left:i.pageX-this.offset(i.target,"left"),a=i.clientY?i.clientY-this.getRect().top:i.pageY-this.offset(i.target,"top");this.writeEnd({x:o,y:a})},downloadSignImg:function(t){var e=document.getElementById(this.domId),n=e.toDataURL("image/png");this.saveFile(n,t?"".concat(t,".").concat(this.config.imgType):"".concat(Date.parse(new Date),".").concat(this.config.imgType))},saveFile:function(t,e){var n=document.createElementNS("http://www.w3.org/1999/xhtml","a");n.href=t,n.download=e;var i=document.createEvent("MouseEvents");i.initMouseEvent("click",!0,!1,window,0,0,0,0,0,!1,!1,!1,!1,0,null),n.dispatchEvent(i)},getRect:function(){return this.$refs[this.domId].getBoundingClientRect()},offset:function(t,e){var n="offset"+e[0].toUpperCase()+e.substring(1),i=t[n],o=t.offsetParent;while(null!=o)i+=o[n],o=o.offsetParent;return i}}}),r=s;function c(t,e,n,i,o,a,s,r){var c,u="function"===typeof t?t.options:t;if(e&&(u.render=e,u.staticRenderFns=n,u._compiled=!0),i&&(u.functional=!0),a&&(u._scopeId="data-v-"+a),s?(c=function(t){t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,t||"undefined"===typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),o&&o.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(s)},u._ssrRegister=c):o&&(c=r?function(){o.call(this,this.$root.$options.shadowRoot)}:o),c)if(u.functional){u._injectStyles=c;var f=u.render;u.render=function(t,e){return c.call(e),f(t,e)}}else{var l=u.beforeCreate;u.beforeCreate=l?[].concat(l,c):[c]}return{exports:t,options:u}}var u=c(r,o,a,!1,null,null,null),f=u.exports;f.install=function(t){t.component(f.name,f)};var l=f;e["default"]=l}})}))},fd18:function(t,e,n){"use strict";n.r(e);n("a481"),n("7f7f");var i=function(){var t=this,e=t._self._c;return e("div",{class:t.isMobilePhone?"":"pc-container"},[t.isMobilePhone?t._e():e("pcheader"),e("van-card",{staticStyle:{background:"white"},attrs:{thumb:t.guestInfo.avatar?t.guestInfo.avatar:"http://mpjoy.oss-cn-beijing.aliyuncs.com/20240917/d9301e81ab674112b3acf4a84aff4e96.png"}},[e("div",{staticStyle:{"font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.guestInfo.name))]),e("div",{staticStyle:{"padding-top":"10px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[t.guestInfo.unit?e("van-tag",{staticStyle:{margin:"5px 10px 5px 0px"},attrs:{size:"medium",round:"",type:"primary",plain:""}},[t._v(t._s(t.guestInfo.unit))]):t._e(),t.guestInfo.duties?e("van-tag",{staticStyle:{margin:"5px 10px 5px 0px"},attrs:{size:"medium",round:"",type:"warning",plain:""}},[t._v(t._s(t.guestInfo.duties))]):t._e()],1)]),t._m(0),e("van-cell-group",{attrs:{inset:""}},[e("van-field",{attrs:{name:"专家姓名",label:"专家姓名",required:"",rules:[{required:!0,message:"请填写专家姓名"}]},model:{value:t.guestInfo.name,callback:function(e){t.$set(t.guestInfo,"name",e)},expression:"guestInfo.name"}}),e("van-field",{attrs:{name:"联系方式",label:"联系方式",required:"",rules:[{required:!0,message:"请填写联系方式"}]},model:{value:t.guestInfo.mobile,callback:function(e){t.$set(t.guestInfo,"mobile",e)},expression:"guestInfo.mobile"}}),e("van-cell",{attrs:{center:"",title:"是否参会"},scopedSlots:t._u([{key:"right-icon",fn:function(){return[e("van-switch",{attrs:{size:"24"},model:{value:t.guestInfo.isAttend,callback:function(e){t.$set(t.guestInfo,"isAttend",e)},expression:"guestInfo.isAttend"}})]},proxy:!0}])}),t.guestInfo.isAttend?e("div",{staticStyle:{"font-size":"12px",padding:"5px 10px",color:"red"}},[t._v("\n            专家头像用于简介（必填）\n        ")]):t._e(),e("van-cell",{attrs:{title:"专家头像",required:t.guestInfo.isAttend}},[e("van-uploader",{attrs:{"after-read":t.afterRead,name:"avatar","before-read":t.beforeRead,accept:"image/*"}},[t.guestInfo.avatar?e("van-image",{attrs:{height:"50px",src:t.guestInfo.avatar,fit:"contain"}}):e("van-icon",{staticStyle:{"margin-left":"5px"},attrs:{slot:"default",name:"plus",size:"50px"},slot:"default"})],1)],1),e("van-field",{attrs:{name:"工作单位",label:"工作单位",required:"",rules:[{required:!0,message:"请填写工作单位"}]},model:{value:t.guestInfo.unit,callback:function(e){t.$set(t.guestInfo,"unit",e)},expression:"guestInfo.unit"}}),e("van-field",{attrs:{center:"",clearable:"",label:"职务/职称",required:"",placeholder:"请输入职务/职称"},scopedSlots:t._u([{key:"button",fn:function(){return[e("van-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){t.dutiesShow=!0}}},[e("span",{staticStyle:{"font-size":"13px"}},[t._v("选择职务/职称")])])]},proxy:!0}]),model:{value:t.guestInfo.duties,callback:function(e){t.$set(t.guestInfo,"duties",e)},expression:"guestInfo.duties"}}),e("van-cell",{attrs:{required:"",title:"地区","is-link":""},on:{click:function(e){t.areaShow=!0}},model:{value:t.guestInfo.areaName,callback:function(e){t.$set(t.guestInfo,"areaName",e)},expression:"guestInfo.areaName"}}),e("div",{staticStyle:{"font-size":"12px",padding:"10px",color:"red"}},[t._v("\n            专家简介如果是文件，就使用上传文件，如果是文本，就使用文本框（必填）\n        ")]),e("van-field",{attrs:{name:"radio",label:"专家简介",required:t.guestInfo.isAttend},scopedSlots:t._u([{key:"input",fn:function(){return[e("van-radio-group",{attrs:{direction:"horizontal"},model:{value:t.contentType,callback:function(e){t.contentType=e},expression:"contentType"}},[e("van-radio",{attrs:{name:1}},[t._v("文件上传")]),e("van-radio",{attrs:{name:2}},[t._v("文本输入")])],1)]},proxy:!0}])}),2==t.contentType?e("van-field",{attrs:{name:"专家简介",rows:"6",type:"textarea",label:"",required:t.guestInfo.isAttend,rules:[{required:2==t.contentType&&t.guestInfo.isAttend,message:"请填写专家简介"}]},model:{value:t.guestInfo.content,callback:function(e){t.$set(t.guestInfo,"content",e)},expression:"guestInfo.content"}}):t._e(),1==t.contentType?e("van-cell",{attrs:{title:""}},[t.guestInfo.contentFile?t._e():e("van-uploader",{attrs:{"after-read":t.afterRead,name:"contentFile","before-read":t.beforeRead,accept:"*"}},[[e("van-icon",{staticStyle:{"margin-left":"5px"},attrs:{name:"description",size:"40px"}}),e("div",{staticStyle:{"font-size":"12px"}},[t._v("点击上传文件（必填）")])]],2),t.guestInfo.contentFile?e("div",{staticClass:"file-info"},[t.isImage(t.guestInfo.contentFile)?[e("van-image",{attrs:{height:"40px",src:t.guestInfo.contentFile,fit:"contain"},on:{click:function(e){return e.stopPropagation(),t.preImage(t.guestInfo.contentFile)}}})]:[e("van-icon",{attrs:{name:"description",size:"20px"}}),e("span",{staticClass:"file-text"},[t._v("已上传文件")])],e("van-icon",{staticClass:"remove-icon",attrs:{name:"cross"},on:{click:function(e){return e.stopPropagation(),t.removeFile.apply(null,arguments)}}})],2):t._e()],1):t._e()],1),e("div",{staticStyle:{margin:"16px"}},[e("van-button",{attrs:{round:"",block:"",type:"info",loading:t.loading,"loading-text":"提交中"},on:{click:t.submit}},[t._v(t._s(t.guestInfo.isInfo?"核对无误，请点击确认":"提交"))])],1),e("div",{staticStyle:{margin:"16px"}},[e("van-button",{attrs:{round:"",block:"",type:"primary"},on:{click:function(e){return t.$router.replace({path:"/schedules/expertIndex",query:{detailId:t.id}})}}},[t._v("返回上一页面")])],1),e("van-popup",{style:{height:"45%"},attrs:{position:"bottom"},model:{value:t.areaShow,callback:function(e){t.areaShow=e},expression:"areaShow"}},[e("van-area",{attrs:{title:"区域选择","area-list":t.areaList,value:t.areaCode},on:{cancel:function(e){t.areaShow=!1},confirm:t.areaSelect}})],1),e("van-action-sheet",{attrs:{actions:t.idCardType},on:{select:t.idCardTypeSelect},model:{value:t.idCardTypeShow,callback:function(e){t.idCardTypeShow=e},expression:"idCardTypeShow"}}),e("van-action-sheet",{attrs:{actions:t.dutiesOptions},on:{select:t.dutiesSelect},model:{value:t.dutiesShow,callback:function(e){t.dutiesShow=e},expression:"dutiesShow"}})],1)},o=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"nav-title",staticStyle:{"margin-top":"8px"}},[e("div",{staticClass:"color"}),e("div",{staticClass:"text"},[t._v("专家基本信息确认")])])}],a=(n("2fdb"),n("6762"),n("28a5"),n("66c7")),s=n("cacf"),r=n("1b69"),c=n("f4b0"),u=n.n(c),f=n("7de9"),l=n("434d"),d=n("7c8d"),h=n.n(d),p={components:{pcheader:r["default"],SignCanvas:u.a},data:function(){return{contentType:2,areaList:l["a"],areaCode:"",idCardType:f["c"],loading:!1,areaShow:!1,idCardTypeShow:!1,dutiesShow:!1,isMobilePhone:Object(s["c"])(),openid:void 0,activeName:["1","2","3"],activityId:void 0,id:void 0,guestInfo:{isAttend:!0},schedule:[],scheduleDiscuss:[],scheduleSpeaker:[],topic:[],topicSpeaker:[],activityInfo:{},value:null,dutiesOptions:[{name:"主任医师"},{name:"副主任医师"},{name:"主治医师"},{name:"医师"},{name:"主任护师"},{name:"副主任护师"},{name:"主管护师"},{name:"护师"},{name:"主任药师"},{name:"副主任药师"},{name:"主管药师"},{name:"药师"}],options:{isFullScreen:!0,isFullCover:!1,isDpr:!1,lastWriteSpeed:1,lastWriteWidth:2,lineCap:"round",lineJoin:"bevel",canvasWidth:350,canvasHeight:370,isShowBorder:!1,bgColor:"#ffffff",borderWidth:1,borderColor:"#ff787f",writeWidth:5,maxWriteWidth:30,minWriteWidth:5,writeColor:"#101010",isSign:!0,imgType:"png"}}},mounted:function(){this.id=this.$route.query.detailId,this.openid=this.$cookie.get("openid"),this.getActivityList(),this.getTopicAndSchedule()},methods:{isImage:function(t){var e=["jpg","jpeg","png","gif","bmp","webp"],n=t.split(".").pop().toLowerCase();return e.includes(n)},preImage:function(t){vant.ImagePreview({images:[t],closeable:!0})},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(e){if(t.loading=!1,200==e.code){t.activityInfo=e.activity,t.activityInfo.backImg=t.activityInfo.backImg||"http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png",document.title="专家基本信息确认-"+t.guestInfo.name;var n=a["a"].formatDate.format(new Date(t.activityInfo.startTime),"yyyy年MM月dd日"),i=a["a"].formatDate.format(new Date(t.activityInfo.endTime),"MM月dd日");if(n.includes(i)){var o="时间:"+n+"\n地址:"+t.activityInfo.address;t.$wxShare(t.guestInfo.name+"-专家基本信息确认-"+t.activityInfo.name,t.activityInfo.shareUrl?t.activityInfo.shareUrl:t.activityInfo.mobileBanner.split(",")[0],o)}else{var s="时间:"+n+"-"+i+"\n地址:"+t.activityInfo.address;t.$wxShare(t.guestInfo.name+"-专家基本信息确认-"+t.activityInfo.name,t.activityInfo.shareUrl?t.activityInfo.shareUrl:t.activityInfo.mobileBanner.split(",")[0],s)}}else vant.Toast(e.msg),t.activityInfo={}}))},getTopicAndSchedule:function(){var t=this;this.$fly.get("/pyp/web/activity/activityguest/getTopicAndSchedule/".concat(this.id)).then((function(e){t.loading=!1,200==e.code?(t.topic=e.result.topic,t.topicSpeaker=e.result.topicSpeaker,t.schedule=e.result.schedule,t.scheduleSpeaker=e.result.scheduleSpeaker,t.scheduleDiscuss=e.result.scheduleDiscuss):(vant.Toast(e.msg),t.activityInfo={})}))},getActivityList:function(){var t=this;this.$fly.get("/pyp/web/activity/activityguest/getById/".concat(this.id)).then((function(e){200==e.code?(t.guestInfo=e.result,t.activityId=e.result.activityId,t.guestInfo.isAttend=!!e.result.isAttend,t.getActivityInfo()):(vant.Toast(e.msg),t.guestInfo={})}))},showTask:function(){this.$router.push({name:"schedulesExpertDetail",query:{detailId:this.id,id:this.activityId}})},cmsTurnBack:function(){this.activityInfo.backUrl?window.open(this.activityInfo.backUrl):this.$router.replace({name:"cmsIndex",query:{id:this.activityInfo.id}})},afterRead:function(t,e){console.log(t);var n=e.name;t.status="uploading",t.message="上传中...";var i=new FormData,o=t.file,a=this;"image/jpeg"!==o.type&&"image/png"!==o.type&&"image/jpg"!==o.type?(i.append("file",o),a.$fly.post("/pyp/web/upload",i).then((function(t){t&&200===t.code&&a.$set(a.guestInfo,n,t.result)}))):(console.log(o),new h.a(o,{quality:.7,success:function(t){i.append("file",new window.File([t],o.name,{type:o.type})),a.$fly.post("/pyp/web/upload",i).then((function(t){t&&200===t.code&&a.$set(a.guestInfo,n,t.result)}))}}))},beforeRead:function(t){return!0},dutiesSelect:function(t){this.dutiesShow=!1,this.guestInfo.duties=t.name},idCardTypeSelect:function(t){this.idCardTypeShow=!1,this.guestInfo.idCardType=t.name},areaSelect:function(t){var e=t[0].code+","+t[1].code+","+t[2].code,n=t[0].name+","+t[1].name+","+t[2].name;this.areaCode=t[2].code,this.$set(this.guestInfo,"area",e),this.$set(this.guestInfo,"areaName",n),this.areaShow=!1},submit:function(){var t=this;return this.guestInfo.name?this.guestInfo.mobile?Object(s["b"])(this.guestInfo.mobile)?!this.guestInfo.avatar&&this.guestInfo.isAttend?(vant.Toast("请选择专家头像"),!1):this.guestInfo.unit?this.guestInfo.duties?this.guestInfo.area?1==this.contentType&&!this.guestInfo.contentFile&&this.guestInfo.isAttend?(vant.Toast("请上传专家简介文件"),!1):2==this.contentType&&!this.guestInfo.content&&this.guestInfo.isAttend?(vant.Toast("请填写专家简介"),!1):(this.loading=!0,this.guestInfo.isInfo=1,this.guestInfo.isInfoTime=a["a"].formatDate.format(new Date,"yyyy/MM/dd hh:mm:ss"),this.guestInfo.isAttend=this.guestInfo.isAttend?1:0,void this.$fly.post("/pyp/web/activity/activityguest/updateInfo",this.guestInfo).then((function(e){t.loading=!1,e&&200===e.code?vant.Dialog.confirm({title:"更新成功",message:"点击确定，返回继续完善其他信息"}).then((function(){t.$router.replace({path:"/schedules/expertIndex",query:{detailId:t.id}})})).catch((function(){t.getActivityList()})):vant.Toast(e.msg)}))):(vant.Toast("请选择地区"),!1):(vant.Toast("请输入职务/职称"),!1):(vant.Toast("请输入工作单位"),!1):(vant.Toast("请输入正确的手机号"),!1):(vant.Toast("请输入联系方式"),!1):(vant.Toast("请输入专家姓名"),!1)},removeFile:function(){this.guestInfo.contentFile=""}}},g=p,v=(n("8788"),n("2877")),m=Object(v["a"])(g,i,o,!1,null,"a8542e98",null);e["default"]=m.exports}}]);
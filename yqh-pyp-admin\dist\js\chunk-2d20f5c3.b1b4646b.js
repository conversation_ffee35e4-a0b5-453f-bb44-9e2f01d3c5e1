(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d20f5c3"],{b2fc:function(a,t,e){"use strict";e.r(t);var r=function(){var a=this,t=a._self._c;return t("el-dialog",{attrs:{title:a.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:a.visible},on:{"update:visible":function(t){a.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:a.dataForm,rules:a.dataRule,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"用户名",prop:"userName"}},[t("el-input",{attrs:{placeholder:"登录帐号"},model:{value:a.dataForm.userName,callback:function(t){a.$set(a.dataForm,"userName",t)},expression:"dataForm.userName"}})],1),t("el-form-item",{class:{"is-required":!a.dataForm.id},attrs:{label:"密码",prop:"password"}},[t("el-input",{attrs:{type:"password",placeholder:"密码"},model:{value:a.dataForm.password,callback:function(t){a.$set(a.dataForm,"password",t)},expression:"dataForm.password"}})],1),t("el-form-item",{class:{"is-required":!a.dataForm.id},attrs:{label:"确认密码",prop:"comfirmPassword"}},[t("el-input",{attrs:{type:"password",placeholder:"确认密码"},model:{value:a.dataForm.comfirmPassword,callback:function(t){a.$set(a.dataForm,"comfirmPassword",t)},expression:"dataForm.comfirmPassword"}})],1),t("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[t("el-input",{attrs:{placeholder:"邮箱"},model:{value:a.dataForm.email,callback:function(t){a.$set(a.dataForm,"email",t)},expression:"dataForm.email"}})],1),t("el-form-item",{attrs:{label:"手机号",prop:"mobile"}},[t("el-input",{attrs:{placeholder:"手机号"},model:{value:a.dataForm.mobile,callback:function(t){a.$set(a.dataForm,"mobile",t)},expression:"dataForm.mobile"}})],1),t("el-form-item",{attrs:{label:"角色",size:"mini",prop:"roleIdList"}},[t("el-checkbox-group",{model:{value:a.dataForm.roleIdList,callback:function(t){a.$set(a.dataForm,"roleIdList",t)},expression:"dataForm.roleIdList"}},a._l(a.roleList,(function(e){return t("el-checkbox",{key:e.roleId,attrs:{label:e.roleId}},[a._v(a._s(e.roleName))])})),1)],1),t("el-form-item",{attrs:{label:"状态",size:"mini",prop:"status"}},[t("el-radio-group",{model:{value:a.dataForm.status,callback:function(t){a.$set(a.dataForm,"status",t)},expression:"dataForm.status"}},[t("el-radio",{attrs:{label:0}},[a._v("禁用")]),t("el-radio",{attrs:{label:1}},[a._v("正常")])],1)],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){a.visible=!1}}},[a._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return a.dataFormSubmit()}}},[a._v("确定")])],1)],1)},o=[],s=(e("d9e2"),e("ac1f"),e("00b4"),e("61f7"),{data:function(){var a=this,t=function(t,e,r){a.dataForm.id||/\S/.test(e)?r():r(new Error("密码不能为空"))},e=function(t,e,r){a.dataForm.id||/\S/.test(e)?a.dataForm.password!==e?r(new Error("确认密码与密码输入不一致")):r():r(new Error("确认密码不能为空"))};return{visible:!1,roleList:[],dataForm:{id:0,userName:"",password:"",comfirmPassword:"",salt:"",email:"",mobile:"",roleIdList:[],status:1,appid:""},dataRule:{userName:[{required:!0,message:"用户名不能为空",trigger:"blur"}],password:[{validator:t,trigger:"blur"}],comfirmPassword:[{validator:e,trigger:"blur"}]}}},methods:{init:function(a){var t=this;this.dataForm.id=a||0,this.$http({url:this.$http.adornUrl("/sys/role/select"),method:"get",params:this.$http.adornParams()}).then((function(a){var e=a.data;t.roleList=e&&200===e.code?e.list:[]})).then((function(){t.visible=!0,t.$nextTick((function(){t.$refs["dataForm"].resetFields()}))})).then((function(){t.dataForm.id?t.$http({url:t.$http.adornUrl("/sys/user/info/".concat(t.dataForm.id)),method:"get",params:t.$http.adornParams()}).then((function(a){var e=a.data;e&&200===e.code&&(t.dataForm.userName=e.user.username,t.dataForm.salt=e.user.salt,t.dataForm.email=e.user.email,t.dataForm.mobile=e.user.mobile,t.dataForm.roleIdList=e.user.roleIdList,t.dataForm.status=e.user.status,t.dataForm.appid=e.user.appid)})):t.dataForm.appid=t.$cookies.get("appid")}))},dataFormSubmit:function(){var a=this;this.$refs["dataForm"].validate((function(t){t&&a.$http({url:a.$http.adornUrl("/sys/user/".concat(a.dataForm.id?"update":"save")),method:"post",data:a.$http.adornData({userId:a.dataForm.id||void 0,username:a.dataForm.userName,password:a.dataForm.password,salt:a.dataForm.salt,email:a.dataForm.email,mobile:a.dataForm.mobile,status:a.dataForm.status,appid:a.dataForm.appid,roleIdList:a.dataForm.roleIdList})}).then((function(t){var e=t.data;e&&200===e.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.visible=!1,a.$emit("refreshDataList")}}):a.$message.error(e.msg)}))}))}}}),l=s,i=e("2877"),d=Object(i["a"])(l,r,o,!1,null,null,null);t["default"]=d.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2278989f"],{"0196":function(t,e,n){const r=n("58b4"),i=n("bbf0");function o(t){this.mode=i.BYTE,"string"===typeof t&&(t=r(t)),this.data=new Uint8Array(t)}o.getBitsLength=function(t){return 8*t},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){for(let e=0,n=this.data.length;e<n;e++)t.put(this.data[e],8)},t.exports=o},"0425":function(t,e){const n="[0-9]+",r="[A-Z $%*+\\-./:]+";let i="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";i=i.replace(/u/g,"\\u");const o="(?:(?![A-Z0-9 $%*+\\-./:]|"+i+")(?:.|[\r\n]))+";e.KANJI=new RegExp(i,"g"),e.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),e.BYTE=new RegExp(o,"g"),e.NUMERIC=new RegExp(n,"g"),e.ALPHANUMERIC=new RegExp(r,"g");const s=new RegExp("^"+i+"$"),a=new RegExp("^"+n+"$"),c=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");e.testKanji=function(t){return s.test(t)},e.testNumeric=function(t){return a.test(t)},e.testAlphanumeric=function(t){return c.test(t)}},"10b0":function(t,e,n){"use strict";var r={single_source_shortest_paths:function(t,e,n){var i={},o={};o[e]=0;var s,a,c,u,l,f,d,h,g,p=r.PriorityQueue.make();p.push(e,0);while(!p.empty())for(c in s=p.pop(),a=s.value,u=s.cost,l=t[a]||{},l)l.hasOwnProperty(c)&&(f=l[c],d=u+f,h=o[c],g="undefined"===typeof o[c],(g||h>d)&&(o[c]=d,p.push(c,d),i[c]=a));if("undefined"!==typeof n&&"undefined"===typeof o[n]){var v=["Could not find a path from ",e," to ",n,"."].join("");throw new Error(v)}return i},extract_shortest_path_from_predecessor_list:function(t,e){var n=[],r=e;while(r)n.push(r),t[r],r=t[r];return n.reverse(),n},find_path:function(t,e,n){var i=r.single_source_shortest_paths(t,e,n);return r.extract_shortest_path_from_predecessor_list(i,n)},PriorityQueue:{make:function(t){var e,n=r.PriorityQueue,i={};for(e in t=t||{},n)n.hasOwnProperty(e)&&(i[e]=n[e]);return i.queue=[],i.sorter=t.sorter||n.default_sorter,i},default_sorter:function(t,e){return t.cost-e.cost},push:function(t,e){var n={value:t,cost:e};this.queue.push(n),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};t.exports=r},2732:function(t,e,n){const r=n("699e");e.mul=function(t,e){const n=new Uint8Array(t.length+e.length-1);for(let i=0;i<t.length;i++)for(let o=0;o<e.length;o++)n[i+o]^=r.mul(t[i],e[o]);return n},e.mod=function(t,e){let n=new Uint8Array(t);while(n.length-e.length>=0){const t=n[0];for(let o=0;o<e.length;o++)n[o]^=r.mul(e[o],t);let i=0;while(i<n.length&&0===n[i])i++;n=n.slice(i)}return n},e.generateECPolynomial=function(t){let n=new Uint8Array([1]);for(let i=0;i<t;i++)n=e.mul(n,new Uint8Array([1,r.exp(i)]));return n}},"27a3":function(t,e){e.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40}},"2f3a":function(t,e,n){const r=n("bbf0"),i=n("7bf0");function o(t){this.mode=r.KANJI,this.data=t}o.getBitsLength=function(t){return 13*t},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let n=i.toSJIS(this.data[e]);if(n>=33088&&n<=40956)n-=33088;else{if(!(n>=57408&&n<=60351))throw new Error("Invalid SJIS character: "+this.data[e]+"\nMake sure your charset is UTF-8");n-=49472}n=192*(n>>>8&255)+(255&n),t.put(n,13)}},t.exports=o},"34fc":function(t,e,n){const r=n("7a43"),i=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],o=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];e.getBlocksCount=function(t,e){switch(e){case r.L:return i[4*(t-1)+0];case r.M:return i[4*(t-1)+1];case r.Q:return i[4*(t-1)+2];case r.H:return i[4*(t-1)+3];default:return}},e.getTotalCodewordsCount=function(t,e){switch(e){case r.L:return o[4*(t-1)+0];case r.M:return o[4*(t-1)+1];case r.Q:return o[4*(t-1)+2];case r.H:return o[4*(t-1)+3];default:return}}},4006:function(t,e,n){const r=n("45be");function i(t,e){const n=t.a/255,r=e+'="'+t.hex+'"';return n<1?r+" "+e+'-opacity="'+n.toFixed(2).slice(1)+'"':r}function o(t,e,n){let r=t+e;return"undefined"!==typeof n&&(r+=" "+n),r}function s(t,e,n){let r="",i=0,s=!1,a=0;for(let c=0;c<t.length;c++){const u=Math.floor(c%e),l=Math.floor(c/e);u||s||(s=!0),t[c]?(a++,c>0&&u>0&&t[c-1]||(r+=s?o("M",u+n,.5+l+n):o("m",i,0),i=0,s=!1),u+1<e&&t[c+1]||(r+=o("h",a),a=0)):i++}return r}e.render=function(t,e,n){const o=r.getOptions(e),a=t.modules.size,c=t.modules.data,u=a+2*o.margin,l=o.color.light.a?"<path "+i(o.color.light,"fill")+' d="M0 0h'+u+"v"+u+'H0z"/>':"",f="<path "+i(o.color.dark,"stroke")+' d="'+s(c,a,o.margin)+'"/>',d='viewBox="0 0 '+u+" "+u+'"',h=o.width?'width="'+o.width+'" height="'+o.width+'" ':"",g='<svg xmlns="http://www.w3.org/2000/svg" '+h+d+' shape-rendering="crispEdges">'+l+f+"</svg>\n";return"function"===typeof n&&n(null,g),g}},4146:function(t,e,n){const r=n("45be");function i(t,e,n){t.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=n,e.width=n,e.style.height=n+"px",e.style.width=n+"px"}function o(){try{return document.createElement("canvas")}catch(t){throw new Error("You need to specify a canvas element")}}e.render=function(t,e,n){let s=n,a=e;"undefined"!==typeof s||e&&e.getContext||(s=e,e=void 0),e||(a=o()),s=r.getOptions(s);const c=r.getImageWidth(t.modules.size,s),u=a.getContext("2d"),l=u.createImageData(c,c);return r.qrToImageData(l.data,t,s),i(u,a,c),u.putImageData(l,0,0),a},e.renderToDataURL=function(t,n,r){let i=r;"undefined"!==typeof i||n&&n.getContext||(i=n,n=void 0),i||(i={});const o=e.render(t,n,i),s=i.type||"image/png",a=i.rendererOpts||{};return o.toDataURL(s,a.quality)}},"45be":function(t,e){function n(t){if("number"===typeof t&&(t=t.toString()),"string"!==typeof t)throw new Error("Color should be defined as hex string");let e=t.slice().replace("#","").split("");if(e.length<3||5===e.length||e.length>8)throw new Error("Invalid hex color: "+t);3!==e.length&&4!==e.length||(e=Array.prototype.concat.apply([],e.map((function(t){return[t,t]})))),6===e.length&&e.push("F","F");const n=parseInt(e.join(""),16);return{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:255&n,hex:"#"+e.slice(0,6).join("")}}e.getOptions=function(t){t||(t={}),t.color||(t.color={});const e="undefined"===typeof t.margin||null===t.margin||t.margin<0?4:t.margin,r=t.width&&t.width>=21?t.width:void 0,i=t.scale||4;return{width:r,scale:r?4:i,margin:e,color:{dark:n(t.color.dark||"#000000ff"),light:n(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},e.getScale=function(t,e){return e.width&&e.width>=t+2*e.margin?e.width/(t+2*e.margin):e.scale},e.getImageWidth=function(t,n){const r=e.getScale(t,n);return Math.floor((t+2*n.margin)*r)},e.qrToImageData=function(t,n,r){const i=n.modules.size,o=n.modules.data,s=e.getScale(i,r),a=Math.floor((i+2*r.margin)*s),c=r.margin*s,u=[r.color.light,r.color.dark];for(let e=0;e<a;e++)for(let n=0;n<a;n++){let l=4*(e*a+n),f=r.color.light;if(e>=c&&n>=c&&e<a-c&&n<a-c){const t=Math.floor((e-c)/s),r=Math.floor((n-c)/s);f=u[o[t*i+r]?1:0]}t[l++]=f.r,t[l++]=f.g,t[l++]=f.b,t[l]=f.a}}},"47c9":function(t,e,n){},"54ae":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"invite-customer-page"},[e("van-nav-bar",{attrs:{title:"邀请客户","left-text":"返回","left-arrow":""},on:{"click-left":function(e){return t.$router.go(-1)}}}),e("div",{staticClass:"invite-methods"},[e("div",{staticClass:"method-card",on:{click:t.showQrCode}},[e("div",{staticClass:"method-icon qr-icon"},[e("van-icon",{attrs:{name:"qr",size:"32"}})],1),t._m(0),e("van-icon",{attrs:{name:"arrow"}})],1),e("div",{staticClass:"method-card",on:{click:t.showInviteLink}},[e("div",{staticClass:"method-icon link-icon"},[e("van-icon",{attrs:{name:"share-o",size:"32"}})],1),t._m(1),e("van-icon",{attrs:{name:"arrow"}})],1)]),e("div",{staticClass:"invite-stats"},[e("div",{staticClass:"stats-title"},[t._v("邀请统计")]),e("div",{staticClass:"stats-grid"},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.inviteStats.totalInvites||0))]),e("div",{staticClass:"stat-label"},[t._v("总邀请数")])]),e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.inviteStats.successInvites||0))]),e("div",{staticClass:"stat-label"},[t._v("成功绑定")])]),e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.inviteStats.todayInvites||0))]),e("div",{staticClass:"stat-label"},[t._v("今日邀请")])]),e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.inviteStats.conversionRate||"0%"))]),e("div",{staticClass:"stat-label"},[t._v("转化率")])])])]),e("div",{staticClass:"recent-invites"},[e("div",{staticClass:"section-title"},[t._v("最近邀请记录")]),e("div",{staticClass:"invite-list"},[t._l(t.recentInvites,(function(n){return e("div",{key:n.id,staticClass:"invite-item"},[e("div",{staticClass:"invite-info"},[e("div",{staticClass:"customer-name"},[t._v(t._s(n.customerName||"未知客户"))]),e("div",{staticClass:"invite-time"},[t._v(t._s(n.inviteTime))])]),e("div",{staticClass:"invite-status"},[e("van-tag",{attrs:{type:t.getStatusTagType(n.status),size:"small"}},[t._v("\n            "+t._s(t.getStatusText(n.status))+"\n          ")])],1)])})),0===t.recentInvites.length?e("div",{staticClass:"empty-state"},[e("van-empty",{attrs:{description:"暂无邀请记录"}})],1):t._e()],2)]),e("van-popup",{style:{height:"70%"},attrs:{position:"bottom"},model:{value:t.qrCodeVisible,callback:function(e){t.qrCodeVisible=e},expression:"qrCodeVisible"}},[e("div",{staticClass:"qr-popup"},[e("div",{staticClass:"popup-header"},[e("div",{staticClass:"popup-title"},[t._v("我的邀请二维码")]),e("van-icon",{attrs:{name:"cross"},on:{click:function(e){t.qrCodeVisible=!1}}})],1),e("div",{staticClass:"qr-content"},[e("div",{staticClass:"qr-code-container"},[e("canvas",{ref:"qrCanvas",staticClass:"qr-canvas"})]),e("div",{staticClass:"qr-info"},[e("div",{staticClass:"qr-title"},[t._v("扫码绑定业务员")]),e("div",{staticClass:"qr-desc"},[t._v("客户扫描此二维码即可与您建立业务员关系")])]),e("div",{staticClass:"qr-actions"},[e("van-button",{attrs:{type:"primary",block:""},on:{click:t.saveQrCode}},[t._v("保存二维码")]),e("van-button",{staticStyle:{"margin-top":"12px"},attrs:{block:""},on:{click:t.shareQrCode}},[t._v("分享二维码")])],1)])])]),e("van-popup",{style:{height:"60%"},attrs:{position:"bottom"},model:{value:t.linkVisible,callback:function(e){t.linkVisible=e},expression:"linkVisible"}},[e("div",{staticClass:"link-popup"},[e("div",{staticClass:"popup-header"},[e("div",{staticClass:"popup-title"},[t._v("邀请链接")]),e("van-icon",{attrs:{name:"cross"},on:{click:function(e){t.linkVisible=!1}}})],1),e("div",{staticClass:"link-content"},[e("div",{staticClass:"link-container"},[e("div",{staticClass:"link-text"},[t._v(t._s(t.inviteLink))]),e("van-button",{attrs:{type:"primary",size:"small"},on:{click:t.copyLink}},[t._v("复制")])],1),e("div",{staticClass:"link-info"},[e("div",{staticClass:"link-title"},[t._v("分享邀请链接")]),e("div",{staticClass:"link-desc"},[t._v("将此链接发送给客户，点击即可建立业务员关系")])]),e("div",{staticClass:"link-actions"},[e("van-button",{attrs:{type:"primary",block:""},on:{click:t.shareLink}},[t._v("分享链接")])],1)])])])],1)},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"method-content"},[e("div",{staticClass:"method-title"},[t._v("二维码邀请")]),e("div",{staticClass:"method-desc"},[t._v("生成专属二维码，客户扫码即可绑定")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"method-content"},[e("div",{staticClass:"method-title"},[t._v("邀请链接")]),e("div",{staticClass:"method-desc"},[t._v("分享邀请链接，客户点击即可绑定")])])}],o=(n("96cf"),n("1da1")),s=n("d055"),a=n.n(s),c={name:"InviteCustomer",data:function(){return{activityId:null,salesmanInfo:null,qrCodeVisible:!1,linkVisible:!1,inviteLink:"",inviteStats:{},recentInvites:[],loading:!1}},created:function(){this.activityId=this.$route.query.activityId||localStorage.getItem("activityId"),this.loadSalesmanInfo()},methods:{loadSalesmanInfo:function(){var t=Object(o["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.loading=!0,t.prev=1,t.next=4,this.$fly.get("/pyp/web/salesman/checkSalesmanStatus",{appid:this.activityId});case 4:e=t.sent,200===e.code?(this.salesmanInfo=e.salesman,this.generateInviteData(),this.loadInviteStats(),this.loadRecentInvites()):this.$toast("获取业务员信息失败"),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](1),console.error("获取业务员信息失败:",t.t0),this.$toast("获取业务员信息失败");case 12:return t.prev=12,this.loading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,this,[[1,8,12,15]])})));function e(){return t.apply(this,arguments)}return e}(),generateInviteData:function(){if(this.salesmanInfo){var t=this.salesmanInfo.id;this.inviteLink="".concat(window.location.origin,"/#/salesman/bind?salesmanId=").concat(t,"&activityId=").concat(this.activityId)}},showQrCode:function(){var t=Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.qrCodeVisible=!0,t.next=3,this.$nextTick();case 3:this.generateQrCode();case 4:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),generateQrCode:function(){var t=Object(o["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e=this.$refs.qrCanvas,t.next=4,a.a.toCanvas(e,this.inviteLink,{width:200,margin:2,color:{dark:"#000000",light:"#FFFFFF"}});case 4:t.next=10;break;case 6:t.prev=6,t.t0=t["catch"](0),console.error("生成二维码失败:",t.t0),this.$toast("生成二维码失败");case 10:case"end":return t.stop()}}),t,this,[[0,6]])})));function e(){return t.apply(this,arguments)}return e}(),showInviteLink:function(){this.linkVisible=!0},copyLink:function(){this.copyToClipboard(this.inviteLink,"邀请链接已复制")},copyToClipboard:function(t,e){var n=this;if(navigator.clipboard)navigator.clipboard.writeText(t).then((function(){n.$toast(e)}));else{var r=document.createElement("textarea");r.value=t,document.body.appendChild(r),r.select(),document.execCommand("copy"),document.body.removeChild(r),this.$toast(e)}},saveQrCode:function(){var t=this.$refs.qrCanvas,e=document.createElement("a");e.download="邀请二维码.png",e.href=t.toDataURL(),e.click(),this.$toast("二维码已保存")},shareQrCode:function(){if(navigator.share){var t=this.$refs.qrCanvas;t.toBlob((function(t){var e=new File([t],"邀请二维码.png",{type:"image/png"});navigator.share({title:"邀请二维码",text:"扫码绑定业务员",files:[e]})}))}else this.copyLink()},shareLink:function(){navigator.share?navigator.share({title:"邀请链接",text:"点击绑定业务员",url:this.inviteLink}):this.copyLink()},loadInviteStats:function(){var t=Object(o["a"])(regeneratorRuntime.mark((function t(){var e,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.salesmanInfo){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,t.next=5,this.$fly.get("/pyp/salesman/wxuserbinding/customerStats",{salesmanId:this.salesmanInfo.id});case 5:e=t.sent,200===e.code&&(n=e.stats,this.inviteStats={totalInvites:n.totalCustomers||0,successInvites:n.activeCustomers||0,todayInvites:n.todayBindings||0,conversionRate:n.totalCustomers>0?Math.round(n.activeCustomers/n.totalCustomers*100)+"%":"0%"}),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](2),console.error("获取邀请统计失败:",t.t0);case 12:case"end":return t.stop()}}),t,this,[[2,9]])})));function e(){return t.apply(this,arguments)}return e}(),loadRecentInvites:function(){var t=Object(o["a"])(regeneratorRuntime.mark((function t(){var e,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.salesmanInfo){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,t.next=5,this.$fly.get("/pyp/web/salesman/getCustomerBindings");case 5:e=t.sent,200===e.code?(n=e.result,this.recentInvites=n.map((function(t){return{id:t.id,customerName:t.wxUserName||"未知客户",inviteTime:t.bindingTime,status:t.status}})),console.log("最近邀请记录:",this.recentInvites)):console.log("获取邀请记录失败:",e.msg),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](2),console.error("获取邀请记录失败:",t.t0);case 12:case"end":return t.stop()}}),t,this,[[2,9]])})));function e(){return t.apply(this,arguments)}return e}(),getStatusText:function(t){var e={1:"已绑定",0:"待绑定","-1":"已过期"};return e[t]||"未知"},getStatusTagType:function(t){var e={1:"success",0:"warning","-1":"danger"};return e[t]||"default"}}},u=c,l=(n("61fa"),n("2877")),f=Object(l["a"])(u,r,i,!1,null,"263ecbb4",null);e["default"]=f.exports},"577e":function(t,e){function n(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}n.prototype.set=function(t,e,n,r){const i=t*this.size+e;this.data[i]=n,r&&(this.reservedBit[i]=!0)},n.prototype.get=function(t,e){return this.data[t*this.size+e]},n.prototype.xor=function(t,e,n){this.data[t*this.size+e]^=n},n.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]},t.exports=n},"58b4":function(t,e,n){"use strict";t.exports=function(t){for(var e=[],n=t.length,r=0;r<n;r++){var i=t.charCodeAt(r);if(i>=55296&&i<=56319&&n>r+1){var o=t.charCodeAt(r+1);o>=56320&&o<=57343&&(i=1024*(i-55296)+o-56320+65536,r+=1)}i<128?e.push(i):i<2048?(e.push(i>>6|192),e.push(63&i|128)):i<55296||i>=57344&&i<65536?(e.push(i>>12|224),e.push(i>>6&63|128),e.push(63&i|128)):i>=65536&&i<=1114111?(e.push(i>>18|240),e.push(i>>12&63|128),e.push(i>>6&63|128),e.push(63&i|128)):e.push(239,191,189)}return new Uint8Array(e).buffer}},"61fa":function(t,e,n){"use strict";n("47c9")},"67dd":function(t,e){t.exports=function(){return"function"===typeof Promise&&Promise.prototype&&Promise.prototype.then}},"699e":function(t,e){const n=new Uint8Array(512),r=new Uint8Array(256);(function(){let t=1;for(let e=0;e<255;e++)n[e]=t,r[t]=e,t<<=1,256&t&&(t^=285);for(let e=255;e<512;e++)n[e]=n[e-255]})(),e.log=function(t){if(t<1)throw new Error("log("+t+")");return r[t]},e.exp=function(t){return n[t]},e.mul=function(t,e){return 0===t||0===e?0:n[r[t]+r[e]]}},7903:function(t,e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const n={N1:3,N2:3,N3:40,N4:10};function r(t,n,r){switch(t){case e.Patterns.PATTERN000:return(n+r)%2===0;case e.Patterns.PATTERN001:return n%2===0;case e.Patterns.PATTERN010:return r%3===0;case e.Patterns.PATTERN011:return(n+r)%3===0;case e.Patterns.PATTERN100:return(Math.floor(n/2)+Math.floor(r/3))%2===0;case e.Patterns.PATTERN101:return n*r%2+n*r%3===0;case e.Patterns.PATTERN110:return(n*r%2+n*r%3)%2===0;case e.Patterns.PATTERN111:return(n*r%3+(n+r)%2)%2===0;default:throw new Error("bad maskPattern:"+t)}}e.isValid=function(t){return null!=t&&""!==t&&!isNaN(t)&&t>=0&&t<=7},e.from=function(t){return e.isValid(t)?parseInt(t,10):void 0},e.getPenaltyN1=function(t){const e=t.size;let r=0,i=0,o=0,s=null,a=null;for(let c=0;c<e;c++){i=o=0,s=a=null;for(let u=0;u<e;u++){let e=t.get(c,u);e===s?i++:(i>=5&&(r+=n.N1+(i-5)),s=e,i=1),e=t.get(u,c),e===a?o++:(o>=5&&(r+=n.N1+(o-5)),a=e,o=1)}i>=5&&(r+=n.N1+(i-5)),o>=5&&(r+=n.N1+(o-5))}return r},e.getPenaltyN2=function(t){const e=t.size;let r=0;for(let n=0;n<e-1;n++)for(let i=0;i<e-1;i++){const e=t.get(n,i)+t.get(n,i+1)+t.get(n+1,i)+t.get(n+1,i+1);4!==e&&0!==e||r++}return r*n.N2},e.getPenaltyN3=function(t){const e=t.size;let r=0,i=0,o=0;for(let n=0;n<e;n++){i=o=0;for(let s=0;s<e;s++)i=i<<1&2047|t.get(n,s),s>=10&&(1488===i||93===i)&&r++,o=o<<1&2047|t.get(s,n),s>=10&&(1488===o||93===o)&&r++}return r*n.N3},e.getPenaltyN4=function(t){let e=0;const r=t.data.length;for(let n=0;n<r;n++)e+=t.data[n];const i=Math.abs(Math.ceil(100*e/r/5)-10);return i*n.N4},e.applyMask=function(t,e){const n=e.size;for(let i=0;i<n;i++)for(let o=0;o<n;o++)e.isReserved(o,i)||e.xor(o,i,r(t,o,i))},e.getBestMask=function(t,n){const r=Object.keys(e.Patterns).length;let i=0,o=1/0;for(let s=0;s<r;s++){n(s),e.applyMask(s,t);const r=e.getPenaltyN1(t)+e.getPenaltyN2(t)+e.getPenaltyN3(t)+e.getPenaltyN4(t);e.applyMask(s,t),r<o&&(o=r,i=s)}return i}},"7a43":function(t,e){function n(t){if("string"!==typeof t)throw new Error("Param is not a string");const n=t.toLowerCase();switch(n){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw new Error("Unknown EC Level: "+t)}}e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2},e.isValid=function(t){return t&&"undefined"!==typeof t.bit&&t.bit>=0&&t.bit<4},e.from=function(t,r){if(e.isValid(t))return t;try{return n(t)}catch(i){return r}}},"7ba0":function(t,e){function n(){this.buffer=[],this.length=0}n.prototype={get:function(t){const e=Math.floor(t/8);return 1===(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(let n=0;n<e;n++)this.putBit(1===(t>>>e-n-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}},t.exports=n},"7bf0":function(t,e){let n;const r=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];e.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return 4*t+17},e.getSymbolTotalCodewords=function(t){return r[t]},e.getBCHDigit=function(t){let e=0;while(0!==t)e++,t>>>=1;return e},e.setToSJISFunction=function(t){if("function"!==typeof t)throw new Error('"toSJISFunc" is not a valid function.');n=t},e.isKanjiModeEnabled=function(){return"undefined"!==typeof n},e.toSJIS=function(t){return n(t)}},"8d23":function(t,e,n){const r=n("2732");function i(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}i.prototype.initialize=function(t){this.degree=t,this.genPoly=r.generateECPolynomial(this.degree)},i.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(t.length+this.degree);e.set(t);const n=r.mod(e,this.genPoly),i=this.degree-n.length;if(i>0){const t=new Uint8Array(this.degree);return t.set(n,i),t}return n},t.exports=i},"924f":function(t,e,n){const r=n("7bf0").getSymbolSize,i=7;e.getPositions=function(t){const e=r(t);return[[0,0],[e-i,0],[0,e-i]]}},9582:function(t,e,n){const r=n("7bf0"),i=1335,o=21522,s=r.getBCHDigit(i);e.getEncodedBits=function(t,e){const n=t.bit<<3|e;let a=n<<10;while(r.getBCHDigit(a)-s>=0)a^=i<<r.getBCHDigit(a)-s;return(n<<10|a)^o}},"9d94":function(t,e,n){const r=n("bbf0"),i=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function o(t){this.mode=r.ALPHANUMERIC,this.data=t}o.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let n=45*i.indexOf(this.data[e]);n+=i.indexOf(this.data[e+1]),t.put(n,11)}this.data.length%2&&t.put(i.indexOf(this.data[e]),6)},t.exports=o},aa63:function(t,e,n){const r=n("7bf0"),i=n("7a43"),o=n("7ba0"),s=n("577e"),a=n("d6c0"),c=n("924f"),u=n("7903"),l=n("34fc"),f=n("8d23"),d=n("c8aa"),h=n("9582"),g=n("bbf0"),p=n("befa");function v(t,e){const n=t.size,r=c.getPositions(e);for(let i=0;i<r.length;i++){const e=r[i][0],o=r[i][1];for(let r=-1;r<=7;r++)if(!(e+r<=-1||n<=e+r))for(let i=-1;i<=7;i++)o+i<=-1||n<=o+i||(r>=0&&r<=6&&(0===i||6===i)||i>=0&&i<=6&&(0===r||6===r)||r>=2&&r<=4&&i>=2&&i<=4?t.set(e+r,o+i,!0,!0):t.set(e+r,o+i,!1,!0))}}function m(t){const e=t.size;for(let n=8;n<e-8;n++){const e=n%2===0;t.set(n,6,e,!0),t.set(6,n,e,!0)}}function w(t,e){const n=a.getPositions(e);for(let r=0;r<n.length;r++){const e=n[r][0],i=n[r][1];for(let n=-2;n<=2;n++)for(let r=-2;r<=2;r++)-2===n||2===n||-2===r||2===r||0===n&&0===r?t.set(e+n,i+r,!0,!0):t.set(e+n,i+r,!1,!0)}}function C(t,e){const n=t.size,r=d.getEncodedBits(e);let i,o,s;for(let a=0;a<18;a++)i=Math.floor(a/3),o=a%3+n-8-3,s=1===(r>>a&1),t.set(i,o,s,!0),t.set(o,i,s,!0)}function y(t,e,n){const r=t.size,i=h.getEncodedBits(e,n);let o,s;for(o=0;o<15;o++)s=1===(i>>o&1),o<6?t.set(o,8,s,!0):o<8?t.set(o+1,8,s,!0):t.set(r-15+o,8,s,!0),o<8?t.set(8,r-o-1,s,!0):o<9?t.set(8,15-o-1+1,s,!0):t.set(8,15-o-1,s,!0);t.set(r-8,8,1,!0)}function b(t,e){const n=t.size;let r=-1,i=n-1,o=7,s=0;for(let a=n-1;a>0;a-=2){6===a&&a--;while(1){for(let n=0;n<2;n++)if(!t.isReserved(i,a-n)){let r=!1;s<e.length&&(r=1===(e[s]>>>o&1)),t.set(i,a-n,r),o--,-1===o&&(s++,o=7)}if(i+=r,i<0||n<=i){i-=r,r=-r;break}}}}function E(t,e,n){const i=new o;n.forEach((function(e){i.put(e.mode.bit,4),i.put(e.getLength(),g.getCharCountIndicator(e.mode,t)),e.write(i)}));const s=r.getSymbolTotalCodewords(t),a=l.getTotalCodewordsCount(t,e),c=8*(s-a);i.getLengthInBits()+4<=c&&i.put(0,4);while(i.getLengthInBits()%8!==0)i.putBit(0);const u=(c-i.getLengthInBits())/8;for(let r=0;r<u;r++)i.put(r%2?17:236,8);return I(i,t,e)}function I(t,e,n){const i=r.getSymbolTotalCodewords(e),o=l.getTotalCodewordsCount(e,n),s=i-o,a=l.getBlocksCount(e,n),c=i%a,u=a-c,d=Math.floor(i/a),h=Math.floor(s/a),g=h+1,p=d-h,v=new f(p);let m=0;const w=new Array(a),C=new Array(a);let y=0;const b=new Uint8Array(t.buffer);for(let r=0;r<a;r++){const t=r<u?h:g;w[r]=b.slice(m,m+t),C[r]=v.encode(w[r]),m+=t,y=Math.max(y,t)}const E=new Uint8Array(i);let I,A,k=0;for(I=0;I<y;I++)for(A=0;A<a;A++)I<w[A].length&&(E[k++]=w[A][I]);for(I=0;I<p;I++)for(A=0;A<a;A++)E[k++]=C[A][I];return E}function A(t,e,n,i){let o;if(Array.isArray(t))o=p.fromArray(t);else{if("string"!==typeof t)throw new Error("Invalid data");{let r=e;if(!r){const e=p.rawSplit(t);r=d.getBestVersionForData(e,n)}o=p.fromString(t,r||40)}}const a=d.getBestVersionForData(o,n);if(!a)throw new Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<a)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+a+".\n")}else e=a;const c=E(e,n,o),l=r.getSymbolSize(e),f=new s(l);return v(f,e),m(f),w(f,e),y(f,n,0),e>=7&&C(f,e),b(f,c),isNaN(i)&&(i=u.getBestMask(f,y.bind(null,f,n))),u.applyMask(i,f),y(f,n,i),{modules:f,version:e,errorCorrectionLevel:n,maskPattern:i,segments:o}}e.create=function(t,e){if("undefined"===typeof t||""===t)throw new Error("No input text");let n,o,s=i.M;return"undefined"!==typeof e&&(s=i.from(e.errorCorrectionLevel,i.M),n=d.from(e.version),o=u.from(e.maskPattern),e.toSJISFunc&&r.setToSJISFunction(e.toSJISFunc)),A(t,n,s,o)}},bbf0:function(t,e,n){const r=n("27a3"),i=n("0425");function o(t){if("string"!==typeof t)throw new Error("Param is not a string");const n=t.toLowerCase();switch(n){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw new Error("Unknown mode: "+t)}}e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(t,e){if(!t.ccBits)throw new Error("Invalid mode: "+t);if(!r.isValid(e))throw new Error("Invalid version: "+e);return e>=1&&e<10?t.ccBits[0]:e<27?t.ccBits[1]:t.ccBits[2]},e.getBestModeForData=function(t){return i.testNumeric(t)?e.NUMERIC:i.testAlphanumeric(t)?e.ALPHANUMERIC:i.testKanji(t)?e.KANJI:e.BYTE},e.toString=function(t){if(t&&t.id)return t.id;throw new Error("Invalid mode")},e.isValid=function(t){return t&&t.bit&&t.ccBits},e.from=function(t,n){if(e.isValid(t))return t;try{return o(t)}catch(r){return n}}},befa:function(t,e,n){const r=n("bbf0"),i=n("dd7e"),o=n("9d94"),s=n("0196"),a=n("2f3a"),c=n("0425"),u=n("7bf0"),l=n("10b0");function f(t){return unescape(encodeURIComponent(t)).length}function d(t,e,n){const r=[];let i;while(null!==(i=t.exec(n)))r.push({data:i[0],index:i.index,mode:e,length:i[0].length});return r}function h(t){const e=d(c.NUMERIC,r.NUMERIC,t),n=d(c.ALPHANUMERIC,r.ALPHANUMERIC,t);let i,o;u.isKanjiModeEnabled()?(i=d(c.BYTE,r.BYTE,t),o=d(c.KANJI,r.KANJI,t)):(i=d(c.BYTE_KANJI,r.BYTE,t),o=[]);const s=e.concat(n,i,o);return s.sort((function(t,e){return t.index-e.index})).map((function(t){return{data:t.data,mode:t.mode,length:t.length}}))}function g(t,e){switch(e){case r.NUMERIC:return i.getBitsLength(t);case r.ALPHANUMERIC:return o.getBitsLength(t);case r.KANJI:return a.getBitsLength(t);case r.BYTE:return s.getBitsLength(t)}}function p(t){return t.reduce((function(t,e){const n=t.length-1>=0?t[t.length-1]:null;return n&&n.mode===e.mode?(t[t.length-1].data+=e.data,t):(t.push(e),t)}),[])}function v(t){const e=[];for(let n=0;n<t.length;n++){const i=t[n];switch(i.mode){case r.NUMERIC:e.push([i,{data:i.data,mode:r.ALPHANUMERIC,length:i.length},{data:i.data,mode:r.BYTE,length:i.length}]);break;case r.ALPHANUMERIC:e.push([i,{data:i.data,mode:r.BYTE,length:i.length}]);break;case r.KANJI:e.push([i,{data:i.data,mode:r.BYTE,length:f(i.data)}]);break;case r.BYTE:e.push([{data:i.data,mode:r.BYTE,length:f(i.data)}])}}return e}function m(t,e){const n={},i={start:{}};let o=["start"];for(let s=0;s<t.length;s++){const a=t[s],c=[];for(let t=0;t<a.length;t++){const u=a[t],l=""+s+t;c.push(l),n[l]={node:u,lastCount:0},i[l]={};for(let t=0;t<o.length;t++){const s=o[t];n[s]&&n[s].node.mode===u.mode?(i[s][l]=g(n[s].lastCount+u.length,u.mode)-g(n[s].lastCount,u.mode),n[s].lastCount+=u.length):(n[s]&&(n[s].lastCount=u.length),i[s][l]=g(u.length,u.mode)+4+r.getCharCountIndicator(u.mode,e))}}o=c}for(let r=0;r<o.length;r++)i[o[r]].end=0;return{map:i,table:n}}function w(t,e){let n;const c=r.getBestModeForData(t);if(n=r.from(e,c),n!==r.BYTE&&n.bit<c.bit)throw new Error('"'+t+'" cannot be encoded with mode '+r.toString(n)+".\n Suggested mode is: "+r.toString(c));switch(n!==r.KANJI||u.isKanjiModeEnabled()||(n=r.BYTE),n){case r.NUMERIC:return new i(t);case r.ALPHANUMERIC:return new o(t);case r.KANJI:return new a(t);case r.BYTE:return new s(t)}}e.fromArray=function(t){return t.reduce((function(t,e){return"string"===typeof e?t.push(w(e,null)):e.data&&t.push(w(e.data,e.mode)),t}),[])},e.fromString=function(t,n){const r=h(t,u.isKanjiModeEnabled()),i=v(r),o=m(i,n),s=l.find_path(o.map,"start","end"),a=[];for(let e=1;e<s.length-1;e++)a.push(o.table[s[e]].node);return e.fromArray(p(a))},e.rawSplit=function(t){return e.fromArray(h(t,u.isKanjiModeEnabled()))}},c8aa:function(t,e,n){const r=n("7bf0"),i=n("34fc"),o=n("7a43"),s=n("bbf0"),a=n("27a3"),c=7973,u=r.getBCHDigit(c);function l(t,n,r){for(let i=1;i<=40;i++)if(n<=e.getCapacity(i,r,t))return i}function f(t,e){return s.getCharCountIndicator(t,e)+4}function d(t,e){let n=0;return t.forEach((function(t){const r=f(t.mode,e);n+=r+t.getBitsLength()})),n}function h(t,n){for(let r=1;r<=40;r++){const i=d(t,r);if(i<=e.getCapacity(r,n,s.MIXED))return r}}e.from=function(t,e){return a.isValid(t)?parseInt(t,10):e},e.getCapacity=function(t,e,n){if(!a.isValid(t))throw new Error("Invalid QR Code version");"undefined"===typeof n&&(n=s.BYTE);const o=r.getSymbolTotalCodewords(t),c=i.getTotalCodewordsCount(t,e),u=8*(o-c);if(n===s.MIXED)return u;const l=u-f(n,t);switch(n){case s.NUMERIC:return Math.floor(l/10*3);case s.ALPHANUMERIC:return Math.floor(l/11*2);case s.KANJI:return Math.floor(l/13);case s.BYTE:default:return Math.floor(l/8)}},e.getBestVersionForData=function(t,e){let n;const r=o.from(e,o.M);if(Array.isArray(t)){if(t.length>1)return h(t,r);if(0===t.length)return 1;n=t[0]}else n=t;return l(n.mode,n.getLength(),r)},e.getEncodedBits=function(t){if(!a.isValid(t)||t<7)throw new Error("Invalid QR Code version");let e=t<<12;while(r.getBCHDigit(e)-u>=0)e^=c<<r.getBCHDigit(e)-u;return t<<12|e}},d055:function(t,e,n){const r=n("67dd"),i=n("aa63"),o=n("4146"),s=n("4006");function a(t,e,n,o,s){const a=[].slice.call(arguments,1),c=a.length,u="function"===typeof a[c-1];if(!u&&!r())throw new Error("Callback required as last argument");if(!u){if(c<1)throw new Error("Too few arguments provided");return 1===c?(n=e,e=o=void 0):2!==c||e.getContext||(o=n,n=e,e=void 0),new Promise((function(r,s){try{const s=i.create(n,o);r(t(s,e,o))}catch(a){s(a)}}))}if(c<2)throw new Error("Too few arguments provided");2===c?(s=n,n=e,e=o=void 0):3===c&&(e.getContext&&"undefined"===typeof s?(s=o,o=void 0):(s=o,o=n,n=e,e=void 0));try{const r=i.create(n,o);s(null,t(r,e,o))}catch(l){s(l)}}e.create=i.create,e.toCanvas=a.bind(null,o.render),e.toDataURL=a.bind(null,o.renderToDataURL),e.toString=a.bind(null,(function(t,e,n){return s.render(t,n)}))},d6c0:function(t,e,n){const r=n("7bf0").getSymbolSize;e.getRowColCoords=function(t){if(1===t)return[];const e=Math.floor(t/7)+2,n=r(t),i=145===n?26:2*Math.ceil((n-13)/(2*e-2)),o=[n-7];for(let r=1;r<e-1;r++)o[r]=o[r-1]-i;return o.push(6),o.reverse()},e.getPositions=function(t){const n=[],r=e.getRowColCoords(t),i=r.length;for(let e=0;e<i;e++)for(let t=0;t<i;t++)0===e&&0===t||0===e&&t===i-1||e===i-1&&0===t||n.push([r[e],r[t]]);return n}},dd7e:function(t,e,n){const r=n("bbf0");function i(t){this.mode=r.NUMERIC,this.data=t.toString()}i.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){let e,n,r;for(e=0;e+3<=this.data.length;e+=3)n=this.data.substr(e,3),r=parseInt(n,10),t.put(r,10);const i=this.data.length-e;i>0&&(n=this.data.substr(e),r=parseInt(n,10),t.put(r,3*i+1))},t.exports=i}}]);
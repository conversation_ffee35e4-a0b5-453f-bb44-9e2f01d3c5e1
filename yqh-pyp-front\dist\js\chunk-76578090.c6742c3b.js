(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-76578090"],{6030:function(t,s,i){},"824c":function(t,s,i){"use strict";i.r(s);var v=function(){var t=this,s=t._self._c;return s("div",{staticClass:"agreement-container"},[s("div",{staticClass:"header"},[s("van-nav-bar",{staticClass:"custom-nav-bar",attrs:{title:"用户协议","left-text":"返回","left-arrow":""},on:{"click-left":function(s){return t.$router.go(-1)}}})],1),t._m(0)])},c=[function(){var t=this,s=t._self._c;return s("div",{staticClass:"content"},[s("div",{staticClass:"agreement-content"},[s("h1",[t._v("易企化AI爆店码用户协议")]),s("div",{staticClass:"update-time"},[s("p",[t._v("更新时间：2024年12月30日")]),s("p",[t._v("生效时间：2024年12月30日")])]),s("div",{staticClass:"section"},[s("h2",[t._v("1. 协议的范围")]),s("p",[t._v('1.1 本协议是您与易企化AI爆店码平台（以下简称"本平台"）之间关于您使用本平台服务所订立的协议。')]),s("p",[t._v('1.2 本协议描述本平台与用户之间关于"易企化AI爆店码"软件服务（以下简称"服务"）的权利义务。')]),s("p",[t._v('1.3 "用户"是指注册、登录、使用本服务的个人或组织。')])]),s("div",{staticClass:"section"},[s("h2",[t._v("2. 服务内容")]),s("p",[t._v("2.1 本平台为用户提供AI助力商家营销获客的数字化解决方案。")]),s("p",[t._v("2.2 服务包括但不限于：")]),s("ul",[s("li",[t._v("活动管理和创建")]),s("li",[t._v("视频、图片、文本等营销素材生成")]),s("li",[t._v("AI智能营销工具")]),s("li",[t._v("数据分析和统计")]),s("li",[t._v("其他相关增值服务")])])]),s("div",{staticClass:"section"},[s("h2",[t._v("3. 账户注册")]),s("p",[t._v("3.1 用户需要注册账户才能使用本服务。")]),s("p",[t._v("3.2 用户应提供真实、准确、完整的个人信息。")]),s("p",[t._v("3.3 用户有义务及时更新注册信息，确保其真实有效。")]),s("p",[t._v("3.4 用户应妥善保管账户信息，对账户下的所有活动承担责任。")])]),s("div",{staticClass:"section"},[s("h2",[t._v("4. 用户行为规范")]),s("p",[t._v("4.1 用户在使用服务时应遵守法律法规，不得：")]),s("ul",[s("li",[t._v("发布违法、有害、威胁、辱骂、骚扰、侵权的内容")]),s("li",[t._v("传播虚假信息或进行欺诈活动")]),s("li",[t._v("侵犯他人知识产权")]),s("li",[t._v("恶意攻击或破坏平台系统")]),s("li",[t._v("进行其他违法违规行为")])])]),s("div",{staticClass:"section"},[s("h2",[t._v("5. 知识产权")]),s("p",[t._v("5.1 本平台拥有服务中包含的所有内容（包括但不限于文字、图片、音频、视频、软件等）的知识产权。")]),s("p",[t._v("5.2 未经本平台书面许可，用户不得复制、传播、修改或商业性使用上述内容。")]),s("p",[t._v("5.3 用户上传的内容，用户保证拥有相应权利，并授权本平台在服务范围内使用。")])]),s("div",{staticClass:"section"},[s("h2",[t._v("6. 隐私保护")]),s("p",[t._v("6.1 本平台重视用户隐私保护，具体内容请参见《隐私政策》。")]),s("p",[t._v("6.2 本平台将按照隐私政策收集、使用、存储和保护用户信息。")])]),s("div",{staticClass:"section"},[s("h2",[t._v("7. 服务变更与终止")]),s("p",[t._v("7.1 本平台有权根据业务发展需要修改或终止服务。")]),s("p",[t._v("7.2 如服务发生重大变更，本平台将提前通知用户。")]),s("p",[t._v("7.3 用户违反本协议的，本平台有权暂停或终止提供服务。")])]),s("div",{staticClass:"section"},[s("h2",[t._v("8. 免责声明")]),s("p",[t._v("8.1 本平台不对因不可抗力、网络故障、系统维护等原因导致的服务中断承担责任。")]),s("p",[t._v("8.2 用户理解并同意，使用本服务存在一定风险，应自行承担相应后果。")])]),s("div",{staticClass:"section"},[s("h2",[t._v("9. 协议修改")]),s("p",[t._v("9.1 本平台有权随时修改本协议条款。")]),s("p",[t._v("9.2 协议修改后，将在平台内公布，用户继续使用服务即视为同意修改后的协议。")])]),s("div",{staticClass:"section"},[s("h2",[t._v("10. 联系我们")]),s("p",[t._v("如您对本协议有任何疑问，请通过以下方式联系我们：")]),s("p",[t._v("客服电话：15880293295")]),s("p",[t._v("邮箱：<EMAIL>")])])])])}],a={name:"UserAgreement",mounted:function(){document.title="用户协议"}},_=a,e=(i("cced"),i("2877")),n=Object(e["a"])(_,v,c,!1,null,"d64f2ea0",null);s["default"]=n.exports},cced:function(t,s,i){"use strict";i("6030")}}]);
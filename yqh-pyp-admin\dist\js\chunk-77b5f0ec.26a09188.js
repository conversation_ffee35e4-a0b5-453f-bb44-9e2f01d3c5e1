(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-77b5f0ec","chunk-6b08a07c","chunk-2d216783"],{"7de9":function(t,e,a){"use strict";a.d(e,"g",(function(){return i})),a.d(e,"f",(function(){return n})),a.d(e,"e",(function(){return r})),a.d(e,"a",(function(){return o})),a.d(e,"b",(function(){return l})),a.d(e,"c",(function(){return s})),a.d(e,"d",(function(){return d}));var i=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],n=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],r=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],o=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],l=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],s=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],d=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]},a15b:function(t,e,a){"use strict";var i=a("23e7"),n=a("e330"),r=a("44ad"),o=a("fc6a"),l=a("a640"),s=n([].join),d=r!==Object,c=d||!l("join",",");i({target:"Array",proto:!0,forced:c},{join:function(t){return s(o(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var i=a("23e7"),n=a("d024"),r=a("c430");i({target:"Iterator",proto:!0,real:!0,forced:r},{map:n})},b291:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"是否显示联系酒店按钮",prop:"isMobile"}},[e("el-select",{attrs:{placeholder:"是否显示联系酒店按钮",filterable:""},model:{value:t.dataForm.isMobile,callback:function(e){t.$set(t.dataForm,"isMobile",e)},expression:"dataForm.isMobile"}},t._l(t.yesOrNo,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1),e("el-form-item",{attrs:{label:"是否显示详情按钮",prop:"isDetail"}},[e("el-select",{attrs:{placeholder:"是否显示详情按钮",filterable:""},model:{value:t.dataForm.isDetail,callback:function(e){t.$set(t.dataForm,"isDetail",e)},expression:"dataForm.isDetail"}},t._l(t.yesOrNo,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1),e("el-form-item",{attrs:{label:"联系方式",prop:"mobile"}},[e("el-input",{attrs:{placeholder:"联系方式"},model:{value:t.dataForm.mobile,callback:function(e){t.$set(t.dataForm,"mobile",e)},expression:"dataForm.mobile"}})],1),e("el-form-item",{attrs:{label:"地址",prop:"address"}},[e("el-input",{attrs:{placeholder:"地址"},model:{value:t.dataForm.address,callback:function(e){t.$set(t.dataForm,"address",e)},expression:"dataForm.address"}})],1),e("el-form-item",{attrs:{label:"标签(逗号隔开)",prop:"brief"}},[e("el-input",{attrs:{placeholder:"标签(逗号隔开)"},model:{value:t.dataForm.brief,callback:function(e){t.$set(t.dataForm,"brief",e)},expression:"dataForm.brief"}})],1),e("el-form-item",{attrs:{label:"内容",prop:"content"}},[e("tinymce-editor",{ref:"editor",model:{value:t.dataForm.content,callback:function(e){t.$set(t.dataForm,"content",e)},expression:"dataForm.content"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},n=[],r=(a("d3b7"),a("3ca3"),a("ddb0"),a("7de9")),o={data:function(){return{yesOrNo:r["g"],visible:!1,dataForm:{id:0,activityId:"",hotelId:"",status:"",orderBy:"",brief:"",content:"",address:"",mobile:"",isMobile:1,isDetail:1},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],hotelId:[{required:!0,message:"酒店id不能为空",trigger:"blur"}],status:[{required:!0,message:"销售状态：0-未开启，1-已开启不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序，数值越小越靠前不能为空",trigger:"blur"}]}}},components:{TinymceEditor:function(){return Promise.all([a.e("chunk-2d0e1c2e"),a.e("chunk-03be236c"),a.e("chunk-2d0a4b8c")]).then(a.bind(null,"26dc"))}},methods:{init:function(t){var e=this;this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.dataForm.id&&e.$http({url:e.$http.adornUrl("/hotel/hotelactivity/info/".concat(e.dataForm.id)),method:"get",params:e.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm.activityId=a.hotelActivity.activityId,e.dataForm.hotelId=a.hotelActivity.hotelId,e.dataForm.status=a.hotelActivity.status,e.dataForm.orderBy=a.hotelActivity.orderBy,e.dataForm.brief=a.hotelActivity.brief,e.dataForm.content=a.hotelActivity.content,e.dataForm.isMobile=a.hotelActivity.isMobile,e.dataForm.address=a.hotelActivity.address,e.dataForm.isDetail=a.hotelActivity.isDetail,e.dataForm.mobile=a.hotelActivity.mobile)}))}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/hotel/hotelactivity/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,activityId:t.dataForm.activityId,hotelId:t.dataForm.hotelId,status:t.dataForm.status,orderBy:t.dataForm.orderBy,content:t.dataForm.content,brief:t.dataForm.brief,isMobile:t.dataForm.isMobile,address:t.dataForm.address,isDetail:t.dataForm.isDetail,mobile:t.dataForm.mobile})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))}}},l=o,s=a("2877"),d=Object(s["a"])(l,i,n,!1,null,null,null);e["default"]=d.exports},c330:function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{closed:t.closeDialog,"update:visible":function(e){t.visible=e}}},[e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"酒店名称",clearable:""},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""}},[e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"酒店名称"}}),e("el-table-column",{attrs:{prop:"star","header-align":"center",align:"center",label:"星级",width:"150px"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-rate",{attrs:{disabled:"",colors:["#99A9BF","#F7BA2A","#FF9900"]},model:{value:a.row.star,callback:function(e){t.$set(a.row,"star",e)},expression:"scope.row.star"}})],1)}}])}),e("el-table-column",{attrs:{prop:"mobile","header-align":"center",align:"center",label:"联系方式"}}),e("el-table-column",{attrs:{prop:"provinceName","header-align":"center",align:"center",label:"省份"}}),e("el-table-column",{attrs:{prop:"cityName","header-align":"center",align:"center",label:"城市"}}),e("el-table-column",{attrs:{prop:"address","header-align":"center",align:"center",label:"详细地址"}}),e("el-table-column",{attrs:{prop:"imageUrl","header-align":"center",align:"center",label:"图片"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[t.isImageUrl(a.row.imageUrl)?e("img",{staticClass:"image-sm",attrs:{src:a.row.imageUrl}}):e("a",{attrs:{href:a.row.imageUrl,target:"_blank"}},[t._v(t._s(a.row.imageUrl))])])}}])}),t.isAuth("hotel:hotelactivity:save")?e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.isSelect?e("el-button",{attrs:{type:"text",size:"small",disabled:""}},[t._v("已选择")]):e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.select(a.row)}}},[t._v("选择")])]}}],null,!1,565229855)}):t._e()],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("关闭页面")])],1)],1)},n=[],r=(a("ac1f"),a("00b4"),{data:function(){return{visible:!1,dataForm:{name:"",appid:"",activityId:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1}},methods:{init:function(t){this.dataForm.activityId=t,this.visible=!0,this.getDataList()},getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/hotel/hotelactivity/selectList"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,activityId:this.dataForm.activityId,appid:this.$cookie.get("appid"),name:this.dataForm.name})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},select:function(t){var e=this;this.$confirm("确定选择酒店?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/hotel/hotelactivity/save"),method:"post",data:e.$http.adornData({activityId:e.dataForm.activityId,hotelId:t.id,address:t.address,mobile:t.mobile,status:0,orderBy:0})}).then((function(t){var a=t.data;a&&200===a.code?(e.$message({message:"操作成功",type:"success",duration:1500}),e.getDataList()):e.$message.error(a.msg)}))}))},closeDialog:function(){this.$emit("refreshDataList")},isImageUrl:function(t){return t&&/.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(t)}}}),o=r,l=a("2877"),s=Object(l["a"])(o,i,n,!1,null,null,null);e["default"]=s.exports},d024:function(t,e,a){"use strict";var i=a("c65b"),n=a("59ed"),r=a("825a"),o=a("46c4"),l=a("c5cc"),s=a("9bdd"),d=l((function(){var t=this.iterator,e=r(i(this.next,t)),a=this.done=!!e.done;if(!a)return s(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return r(this),n(t),new d(o(this),{mapper:t})}},d81d:function(t,e,a){"use strict";var i=a("23e7"),n=a("b727").map,r=a("1dde"),o=r("map");i({target:"Array",proto:!0,forced:!o},{map:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}})},ebd9:function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("div",{staticStyle:{"text-align":"center",padding:"20px","font-weight":"bold","font-size":"28px"}},[t._v(t._s(t.activityInfo.name)+"的酒店列表")]),e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"参数名",clearable:""},model:{value:t.dataForm.key,callback:function(e){t.$set(t.dataForm,"key",e)},expression:"dataForm.key"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),t.isAuth("hotel:hotelactivity:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addHandle()}}},[t._v("新增")]):t._e()],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"hotelName","header-align":"center",align:"center",label:"酒店名称"}}),e("el-table-column",{attrs:{prop:"hotelMobile","header-align":"center",align:"center",label:"酒店联系方式"}}),e("el-table-column",{attrs:{prop:"hotelAddress","header-align":"center",align:"center",label:"酒店地址"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showRoom(a.row.id)}}},[t._v("查看房型")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")])]}}])}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"120",label:"销售状态"},scopedSlots:t._u([{key:"default",fn:function(a){return[1==a.row.status?e("el-button",{attrs:{type:"danger",size:"small"},on:{click:function(e){return t.changeStatus(a.row.id,0)}}},[t._v("关闭售房")]):e("el-button",{attrs:{type:"success",size:"small"},on:{click:function(e){return t.changeStatus(a.row.id,1)}}},[t._v("开启售房")])]}}])}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"120",label:"排序"},scopedSlots:t._u([{key:"default",fn:function(a){return[0==a.$index?e("el-button",{attrs:{type:"danger",size:"small"},on:{click:function(e){return t.changeOrderBy(a.row.id,1)}}},[t._v("取消置顶")]):e("el-button",{attrs:{type:"success",size:"small"},on:{click:function(e){return t.changeOrderBy(a.row.id,0)}}},[t._v("置顶")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e(),t.hotelactivityAddVisible?e("hotelactivity-add",{ref:"hotelactivityAdd",on:{refreshDataList:t.getDataList}}):t._e()],1)},n=[],r=(a("99af"),a("a15b"),a("d81d"),a("14d9"),a("a573"),a("b291")),o=a("c330"),l={data:function(){return{dataForm:{key:"",activityId:void 0},dataList:[],activityInfo:{},pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,hotelactivityAddVisible:!1}},components:{AddOrUpdate:r["default"],HotelactivityAdd:o["default"]},activated:function(){this.dataForm.activityId=this.$route.query.activityId,this.getDataList(),this.getActivity()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/hotel/hotelactivity/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,key:this.dataForm.key,activityId:this.dataForm.activityId})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},getActivity:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activity/info/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.activityInfo=a.activity)}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(t)}))},addHandle:function(){var t=this;this.hotelactivityAddVisible=!0,this.$nextTick((function(){t.$refs.hotelactivityAdd.init(t.dataForm.activityId)}))},changeStatus:function(t,e){var a=this;this.$confirm("确定"+(0==e?"关闭":"开启")+"售房","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.$http({url:a.$http.adornUrl("/hotel/hotelactivity/update"),method:"post",data:a.$http.adornData({id:t,status:e})}).then((function(t){var e=t.data;e&&200===e.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.getDataList()}}):a.$message.error(e.msg)}))}))},changeOrderBy:function(t,e){var a=this;this.$confirm("确定"+(0==e?"置顶":"取消置顶"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.$http({url:a.$http.adornUrl("/hotel/hotelactivity/update"),method:"post",data:a.$http.adornData({id:t,orderBy:e})}).then((function(t){var e=t.data;e&&200===e.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.getDataList()}}):a.$message.error(e.msg)}))}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/hotel/hotelactivity/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},showRoom:function(t){this.$router.push({name:"hotelactivityroom",query:{activityId:this.dataForm.activityId,hotelActivityId:t}})}}},s=l,d=a("2877"),c=Object(d["a"])(s,i,n,!1,null,null,null);e["default"]=c.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d22d72d"],{f816:function(a,t,e){"use strict";e.r(t);e("b0c0");var o=function(){var a=this,t=a._self._c;return t("el-dialog",{attrs:{title:a.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:a.visible},on:{"update:visible":function(t){a.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:a.dataForm,rules:a.dataRule,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"名称",prop:"name"}},[t("el-input",{attrs:{placeholder:"名称"},model:{value:a.dataForm.name,callback:function(t){a.$set(a.dataForm,"name",t)},expression:"dataForm.name"}})],1),t("el-form-item",{attrs:{label:"字段名称",prop:"fieldName"}},[t("el-input",{attrs:{placeholder:"字段名称"},model:{value:a.dataForm.fieldName,callback:function(t){a.$set(a.dataForm,"fieldName",t)},expression:"dataForm.fieldName"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){a.visible=!1}}},[a._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return a.dataFormSubmit()}}},[a._v("确定")])],1)],1)},i=[],r={data:function(){return{visible:!1,dataForm:{id:0,name:"",fieldName:""},dataRule:{name:[{required:!0,message:"名称不能为空",trigger:"blur"}],fieldName:[{required:!0,message:"字段名称不能为空",trigger:"blur"}]}}},methods:{init:function(a){var t=this;this.dataForm.id=a||0,this.visible=!0,this.$nextTick((function(){t.$refs["dataForm"].resetFields(),t.dataForm.id&&t.$http({url:t.$http.adornUrl("/apply/applyconfig/info/".concat(t.dataForm.id)),method:"get",params:t.$http.adornParams()}).then((function(a){var e=a.data;e&&200===e.code&&(t.dataForm.name=e.applyConfig.name,t.dataForm.fieldName=e.applyConfig.fieldName)}))}))},dataFormSubmit:function(){var a=this;this.$refs["dataForm"].validate((function(t){t&&a.$http({url:a.$http.adornUrl("/apply/applyconfig/".concat(a.dataForm.id?"update":"save")),method:"post",data:a.$http.adornData({id:a.dataForm.id||void 0,name:a.dataForm.name,fieldName:a.dataForm.fieldName})}).then((function(t){var e=t.data;e&&200===e.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.visible=!1,a.$emit("refreshDataList")}}):a.$message.error(e.msg)}))}))}}},l=r,d=e("2877"),n=Object(d["a"])(l,o,i,!1,null,null,null);t["default"]=n.exports}}]);
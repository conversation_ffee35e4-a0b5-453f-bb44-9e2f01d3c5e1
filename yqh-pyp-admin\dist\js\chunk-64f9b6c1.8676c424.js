(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-64f9b6c1","chunk-2d0ab51b"],{1589:function(t,a,e){"use strict";e.r(a);var n=function(){var t=this,a=t._self._c;return a("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(a){t.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&t._k(a.keyCode,"enter",13,a.key,"Enter")?null:t.dataFormSubmit()}}},[a("el-form-item",{attrs:{label:"机场名称",prop:"stationName"}},[a("el-input",{attrs:{placeholder:"机场名称"},model:{value:t.dataForm.stationName,callback:function(a){t.$set(t.dataForm,"stationName",a)},expression:"dataForm.stationName"}})],1),a("el-form-item",{attrs:{label:"机场简称",prop:"stationShortName"}},[a("el-input",{attrs:{placeholder:"机场简称"},model:{value:t.dataForm.stationShortName,callback:function(a){t.$set(t.dataForm,"stationShortName",a)},expression:"dataForm.stationShortName"}})],1),a("el-form-item",{attrs:{label:"机场三字码",prop:"stationCode"}},[a("el-input",{attrs:{placeholder:"机场三字码"},model:{value:t.dataForm.stationCode,callback:function(a){t.$set(t.dataForm,"stationCode",a)},expression:"dataForm.stationCode"}})],1),a("el-form-item",{attrs:{label:"城市拼音",prop:"stationPinYin"}},[a("el-input",{attrs:{placeholder:"城市拼音"},model:{value:t.dataForm.stationPinYin,callback:function(a){t.$set(t.dataForm,"stationPinYin",a)},expression:"dataForm.stationPinYin"}})],1),a("el-form-item",{attrs:{label:"城市名称",prop:"cityName"}},[a("el-input",{attrs:{placeholder:"城市名称"},model:{value:t.dataForm.cityName,callback:function(a){t.$set(t.dataForm,"cityName",a)},expression:"dataForm.cityName"}})],1),a("el-form-item",{attrs:{label:"是否热门",prop:"isHot"}},[a("el-input",{attrs:{placeholder:"是否热门"},model:{value:t.dataForm.isHot,callback:function(a){t.$set(t.dataForm,"isHot",a)},expression:"dataForm.isHot"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){t.visible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},i=[],o={data:function(){return{visible:!1,dataForm:{repeatToken:"",id:0,stationName:"",stationShortName:"",stationCode:"",stationPinYin:"",cityName:"",isHot:""},dataRule:{stationName:[{required:!0,message:"机场名称不能为空",trigger:"blur"}],stationShortName:[{required:!0,message:"机场简称不能为空",trigger:"blur"}],stationCode:[{required:!0,message:"机场三字码不能为空",trigger:"blur"}],stationPinYin:[{required:!0,message:"城市拼音不能为空",trigger:"blur"}],cityName:[{required:!0,message:"城市名称不能为空",trigger:"blur"}],isHot:[{required:!0,message:"是否热门不能为空",trigger:"blur"}]}}},methods:{init:function(t){var a=this;this.getToken(),this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/config/configtrainstation/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.dataForm.stationName=e.configTrainStation.stationName,a.dataForm.stationShortName=e.configTrainStation.stationShortName,a.dataForm.stationCode=e.configTrainStation.stationCode,a.dataForm.stationPinYin=e.configTrainStation.stationPinYin,a.dataForm.cityName=e.configTrainStation.cityName,a.dataForm.isHot=e.configTrainStation.isHot)}))}))},getToken:function(){var t=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(a){var e=a.data;e&&200===e.code&&(t.dataForm.repeatToken=e.result)}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(a){a&&t.$http({url:t.$http.adornUrl("/config/configtrainstation/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({repeatToken:t.dataForm.repeatToken,id:t.dataForm.id||void 0,stationName:t.dataForm.stationName,stationShortName:t.dataForm.stationShortName,stationCode:t.dataForm.stationCode,stationPinYin:t.dataForm.stationPinYin,cityName:t.dataForm.cityName,isHot:t.dataForm.isHot})}).then((function(a){var e=a.data;e&&200===e.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):(t.$message.error(e.msg),"不能重复提交"!=e.msg&&t.getToken())}))}))}}},r=o,s=e("2877"),l=Object(s["a"])(r,n,i,!1,null,null,null);a["default"]=l.exports},7317:function(t,a,e){"use strict";e.r(a);e("b0c0");var n=function(){var t=this,a=t._self._c;return a("div",{staticClass:"mod-config"},[a("el-form",{attrs:{inline:!0,model:t.dataForm},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&t._k(a.keyCode,"enter",13,a.key,"Enter")?null:t.onSearch()}}},[a("el-form-item",[a("el-input",{attrs:{placeholder:"关键词",clearable:""},model:{value:t.dataForm.name,callback:function(a){t.$set(t.dataForm,"name",a)},expression:"dataForm.name"}})],1),a("el-form-item",[a("el-button",{on:{click:function(a){return t.onSearch()}}},[t._v("查询")]),t.isAuth("config:configtrainstation:save")?a("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("config:configtrainstation:delete")?a("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(a){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[a("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),a("el-table-column",{attrs:{prop:"createOn","show-overflow-tooltip":!0,"header-align":"center",align:"center",label:"创建时间"}}),a("el-table-column",{attrs:{prop:"updateOn","show-overflow-tooltip":!0,"header-align":"center",align:"center",label:"更新时间"}}),a("el-table-column",{attrs:{prop:"stationName","header-align":"center",align:"center",label:"机场名称"}}),a("el-table-column",{attrs:{prop:"stationShortName","header-align":"center",align:"center",label:"机场简称"}}),a("el-table-column",{attrs:{prop:"stationCode","header-align":"center",align:"center",label:"机场三字码"}}),a("el-table-column",{attrs:{prop:"stationPinYin","header-align":"center",align:"center",label:"城市拼音"}}),a("el-table-column",{attrs:{prop:"cityName","header-align":"center",align:"center",label:"城市名称"}}),a("el-table-column",{attrs:{prop:"isHot","header-align":"center",align:"center",label:"是否热门"}}),a("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.addOrUpdateHandle(e.row.id)}}},[t._v("修改")]),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.deleteHandle(e.row.id)}}},[t._v("删除")])]}}])})],1),a("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?a("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e()],1)},i=[],o=(e("99af"),e("a15b"),e("d81d"),e("a573"),e("1589")),r={data:function(){return{dataForm:{name:"",appid:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1}},components:{AddOrUpdate:o["default"]},activated:function(){this.getDataList()},methods:{onSearch:function(){this.pageIndex=1,this.getDataList()},getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/config/configtrainstation/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,name:this.dataForm.name,appid:this.$cookie.get("appid")})}).then((function(a){var e=a.data;e&&200===e.code?(t.dataList=e.page.list,t.totalPage=e.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var a=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){a.$refs.addOrUpdate.init(t)}))},deleteHandle:function(t){var a=this,e=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(e.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.$http({url:a.$http.adornUrl("/config/configtrainstation/delete"),method:"post",data:a.$http.adornData(e,!1)}).then((function(t){var e=t.data;e&&200===e.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.getDataList()}}):a.$message.error(e.msg)}))}))}}},s=r,l=e("2877"),d=Object(l["a"])(s,n,i,!1,null,null,null);a["default"]=d.exports},a15b:function(t,a,e){"use strict";var n=e("23e7"),i=e("e330"),o=e("44ad"),r=e("fc6a"),s=e("a640"),l=i([].join),d=o!==Object,c=d||!s("join",",");n({target:"Array",proto:!0,forced:c},{join:function(t){return l(r(this),void 0===t?",":t)}})},a573:function(t,a,e){"use strict";e("ab43")},ab43:function(t,a,e){"use strict";var n=e("23e7"),i=e("d024"),o=e("c430");n({target:"Iterator",proto:!0,real:!0,forced:o},{map:i})},d024:function(t,a,e){"use strict";var n=e("c65b"),i=e("59ed"),o=e("825a"),r=e("46c4"),s=e("c5cc"),l=e("9bdd"),d=s((function(){var t=this.iterator,a=o(n(this.next,t)),e=this.done=!!a.done;if(!e)return l(t,this.mapper,[a.value,this.counter++],!0)}));t.exports=function(t){return o(this),i(t),new d(r(this),{mapper:t})}},d81d:function(t,a,e){"use strict";var n=e("23e7"),i=e("b727").map,o=e("1dde"),r=o("map");n({target:"Array",proto:!0,forced:!r},{map:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})}}]);
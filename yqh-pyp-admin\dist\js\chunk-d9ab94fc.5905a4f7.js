(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d9ab94fc"],{"7db0":function(e,t,a){"use strict";var r=a("23e7"),i=a("b727").find,c=a("44d2"),s="find",o=!0;s in[]&&Array(1)[s]((function(){o=!1})),r({target:"Array",proto:!0,forced:o},{find:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}}),c(s)},"8b66":function(e,t,a){"use strict";a.r(t);a("99af"),a("b0c0");var r=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:"系统赠送次数","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"选择活动",prop:"activityId"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择活动",filterable:""},model:{value:e.dataForm.activityId,callback:function(t){e.$set(e.dataForm,"activityId",t)},expression:"dataForm.activityId"}},e._l(e.activityList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"充值方式",prop:"rechargeType"}},[t("el-radio-group",{on:{change:e.onRechargeTypeChange},model:{value:e.dataForm.rechargeType,callback:function(t){e.$set(e.dataForm,"rechargeType",t)},expression:"dataForm.rechargeType"}},[t("el-radio",{attrs:{label:1}},[e._v("套餐充值")]),t("el-radio",{attrs:{label:3}},[e._v("系统赠送")])],1)],1),1===e.dataForm.rechargeType?t("el-form-item",{attrs:{label:"选择套餐",prop:"packageId"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择套餐"},on:{change:e.onPackageChange},model:{value:e.dataForm.packageId,callback:function(t){e.$set(e.dataForm,"packageId",t)},expression:"dataForm.packageId"}},e._l(e.packageList,(function(a){return t("el-option",{key:a.id,attrs:{label:"".concat(a.name," - ¥").concat(a.price," (").concat(1===a.packageType?a.countValue+"次":a.activityCount+"个活动",")"),value:a.id}},[t("span",{staticStyle:{float:"left"}},[e._v(e._s(a.name))]),t("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v("¥"+e._s(a.price))])])})),1)],1):e._e(),3===e.dataForm.rechargeType?t("el-form-item",{attrs:{label:"赠送次数",prop:"count"}},[t("el-input-number",{attrs:{min:1,max:1e4,placeholder:"赠送次数"},model:{value:e.dataForm.count,callback:function(t){e.$set(e.dataForm,"count",t)},expression:"dataForm.count"}})],1):e._e(),e.selectedPackage?t("el-form-item",{attrs:{label:"套餐信息"}},[t("el-card",{staticClass:"package-info"},[t("div",[t("strong",[e._v("套餐名称：")]),e._v(e._s(e.selectedPackage.name))]),t("div",[t("strong",[e._v("套餐类型：")]),e._v(e._s(1===e.selectedPackage.type?"充值次数套餐":"创建活动套餐"))]),1===e.selectedPackage.type?t("div",[t("strong",[e._v("充值次数：")]),e._v(e._s(e.selectedPackage.countValue)+"次")]):e._e(),2===e.selectedPackage.type?t("div",[t("strong",[e._v("活动数量：")]),e._v(e._s(e.selectedPackage.activityCount)+"个")]):e._e(),t("div",[t("strong",[e._v("价格：")]),t("span",{staticStyle:{color:"#f56c6c","font-weight":"bold"}},[e._v("¥"+e._s(e.selectedPackage.price))])]),e.selectedPackage.originalPrice?t("div",[t("strong",[e._v("原价：")]),t("span",{staticStyle:{"text-decoration":"line-through"}},[e._v("¥"+e._s(e.selectedPackage.originalPrice))])]):e._e()])],1):e._e(),t("el-form-item",{attrs:{label:"有效期(天)",prop:"validDays"}},[t("el-input-number",{attrs:{min:1,max:3650,placeholder:"有效期天数"},model:{value:e.dataForm.validDays,callback:function(t){e.$set(e.dataForm,"validDays",t)},expression:"dataForm.validDays"}})],1),t("el-form-item",{attrs:{label:"备注",prop:"remarks"}},[t("el-input",{attrs:{type:"textarea",rows:3,placeholder:"赠送原因或备注"},model:{value:e.dataForm.remarks,callback:function(t){e.$set(e.dataForm,"remarks",t)},expression:"dataForm.remarks"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},i=[],c=a("ade3"),s=(a("7db0"),a("d3b7"),a("0643"),a("fffc"),{data:function(){return{visible:!1,activityList:[],packageList:[],selectedPackage:null,dataForm:{repeatToken:"",activityId:"",rechargeType:3,packageId:"",count:1,validDays:365,remarks:""},dataRule:{activityId:[{required:!0,message:"活动ID不能为空",trigger:"blur"}],rechargeType:[{required:!0,message:"充值方式不能为空",trigger:"change"}],packageId:[{required:!0,message:"套餐不能为空",trigger:"change"}],count:[{required:!0,message:"赠送次数不能为空",trigger:"blur"}],validDays:[{required:!0,message:"有效期不能为空",trigger:"blur"}]}}},methods:{init:function(){var e=this;this.getToken(),this.getActivityList(),this.getPackageList(),this.visible=!0,this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.updateValidationRules()}))},getActivityList:function(){var e=this;this.$http({url:this.$http.adornUrl("/activity/activity/list"),method:"get",params:this.$http.adornParams({page:1,limit:1e3})}).then((function(t){var a=t.data;a&&200===a.code?e.activityList=a.page.list:e.activityList=[]}))},getPackageList:function(){var e=this;this.$http({url:this.$http.adornUrl("/activity/rechargepackage/list"),method:"get",params:this.$http.adornParams({page:1,limit:1e3,status:1,packageType:1})}).then((function(t){var a=t.data;a&&200===a.code?e.packageList=a.page.list:e.packageList=[]}))},onRechargeTypeChange:function(e){var t=this;this.selectedPackage=null,this.dataForm.packageId="",this.dataForm.count=1,this.updateValidationRules(),this.$nextTick((function(){t.$refs["dataForm"].clearValidate()}))},onPackageChange:function(e){this.selectedPackage=this.packageList.find((function(t){return t.id===e})),this.selectedPackage&&(1===this.selectedPackage.type?this.dataForm.count=this.selectedPackage.countValue:this.dataForm.count=this.selectedPackage.activityCount)},updateValidationRules:function(){1===this.dataForm.rechargeType?(this.$set(this.dataRule,"packageId",[{required:!0,message:"套餐不能为空",trigger:"change"}]),this.$delete(this.dataRule,"count")):(this.$set(this.dataRule,"count",[{required:!0,message:"赠送次数不能为空",trigger:"blur"}]),this.$delete(this.dataRule,"packageId"))},getToken:function(){var e=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm.repeatToken=a.result)}))},dataFormSubmit:function(){var e=this;this.updateValidationRules(),this.$refs["dataForm"].validate((function(t){if(t){var a="",r={appid:e.$cookie.get("appid"),repeatToken:e.dataForm.repeatToken,activityId:e.dataForm.activityId,validDays:e.dataForm.validDays,remarks:e.dataForm.remarks};1===e.dataForm.rechargeType?(a="/activity/rechargerecord/recharge",r.rechargeType=1,r.packageId=e.dataForm.packageId,r.count=e.selectedPackage?1===e.selectedPackage.type?e.selectedPackage.countValue:e.selectedPackage.activityCount:1,r.amount=e.selectedPackage?e.selectedPackage.price:0):(a="/activity/rechargerecord/gift",r.count=e.dataForm.count),e.$http(Object(c["a"])({url:e.$http.adornUrl(a),method:1===e.dataForm.rechargeType?"post":"get"},1===e.dataForm.rechargeType?"data":"params",1===e.dataForm.rechargeType?e.$http.adornData(r):e.$http.adornParams(r))).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:1===e.dataForm.rechargeType?"充值成功":"赠送成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):(e.$message.error(a.msg),"不能重复提交"!=a.msg&&e.getToken())}))}}))}}}),o=s,n=(a("d791"),a("2877")),l=Object(n["a"])(o,r,i,!1,null,"41bdadbe",null);t["default"]=l.exports},c658:function(e,t,a){},d791:function(e,t,a){"use strict";a("c658")},f665:function(e,t,a){"use strict";var r=a("23e7"),i=a("2266"),c=a("59ed"),s=a("825a"),o=a("46c4");r({target:"Iterator",proto:!0,real:!0},{find:function(e){s(this),c(e);var t=o(this),a=0;return i(t,(function(t,r){if(e(t,a++))return r(t)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},fffc:function(e,t,a){"use strict";a("f665")}}]);
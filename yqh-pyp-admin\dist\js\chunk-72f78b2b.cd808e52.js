(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-72f78b2b","chunk-2d0a4b8c","chunk-2d0a4b8c","chunk-58faa667"],{"083a":function(e,t,i){"use strict";var a=i("0d51"),l=TypeError;e.exports=function(e,t){if(!delete e[t])throw new l("Cannot delete property "+a(t)+" of "+a(e))}},"669c":function(e,t,i){},"9c0b":function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"image-upload-test"},[t("h2",[e._v("图片上传弹窗组件测试")]),t("el-card",{staticStyle:{"margin-bottom":"20px"}},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("多图片选择测试")])]),t("el-button",{attrs:{type:"primary"},on:{click:e.openMultipleModal}},[t("i",{staticClass:"el-icon-picture"}),e._v(" 选择多张图片 (最多9张) ")]),e.multipleImages.length>0?t("div",{staticClass:"selected-images"},[t("h4",[e._v("已选择 "+e._s(e.multipleImages.length)+" 张图片：")]),t("div",{staticClass:"image-list"},e._l(e.multipleImages,(function(i,a){return t("div",{key:a,staticClass:"image-item"},[t("img",{attrs:{src:i.url,alt:i.url}}),t("div",{staticClass:"image-info"},[t("p",[e._v(e._s(i.url))]),t("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.removeMultipleImage(a)}}},[e._v("删除")])],1)])})),0)]):e._e()],1),t("el-card",{staticStyle:{"margin-bottom":"20px"}},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("单图片选择测试")])]),t("el-button",{attrs:{type:"primary"},on:{click:e.openSingleModal}},[t("i",{staticClass:"el-icon-picture"}),e._v(" 选择单张图片 ")]),e.singleImage?t("div",{staticClass:"selected-images"},[t("h4",[e._v("已选择图片：")]),t("div",{staticClass:"image-list"},[t("div",{staticClass:"image-item"},[t("img",{attrs:{src:e.singleImage.url,alt:e.singleImage.url}}),t("div",{staticClass:"image-info"},[t("p",[e._v(e._s(e.singleImage.url))]),t("el-button",{attrs:{size:"mini",type:"danger"},on:{click:e.removeSingleImage}},[e._v("删除")])],1)])])]):e._e()],1),t("el-card",[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("小尺寸图片测试 (最大1MB)")])]),t("el-button",{attrs:{type:"primary"},on:{click:e.openSmallModal}},[t("i",{staticClass:"el-icon-picture"}),e._v(" 选择小尺寸图片 ")]),e.smallImages.length>0?t("div",{staticClass:"selected-images"},[t("h4",[e._v("已选择 "+e._s(e.smallImages.length)+" 张图片：")]),t("div",{staticClass:"image-list"},e._l(e.smallImages,(function(i,a){return t("div",{key:a,staticClass:"image-item"},[t("img",{attrs:{src:i.url,alt:i.url}}),t("div",{staticClass:"image-info"},[t("p",[e._v(e._s(i.url))]),t("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.removeSmallImage(a)}}},[e._v("删除")])],1)])})),0)]):e._e()],1),t("ImageUploadModal",{attrs:{visible:e.imageModalVisible,multiple:e.currentConfig.multiple,"max-count":e.currentConfig.maxCount,"max-size":e.currentConfig.maxSize,"default-images":e.currentConfig.defaultImages},on:{"update:visible":function(t){e.imageModalVisible=t},confirm:e.handleImageConfirm}})],1)},l=[],s=(i("a434"),i("d3b7"),i("3ca3"),i("ddb0"),{name:"ImageUploadTest",components:{ImageUploadModal:function(){return Promise.all([i.e("chunk-2d0e1c2e"),i.e("chunk-e9144aa4"),i.e("chunk-0506e191")]).then(i.bind(null,"4185"))}},data:function(){return{imageModalVisible:!1,multipleImages:[],singleImage:null,smallImages:[],currentConfig:{multiple:!0,maxCount:9,maxSize:2,defaultImages:[],type:"multiple"}}},methods:{openMultipleModal:function(){this.currentConfig={multiple:!0,maxCount:9,maxSize:2,defaultImages:this.multipleImages,type:"multiple"},this.imageModalVisible=!0},openSingleModal:function(){this.currentConfig={multiple:!1,maxCount:1,maxSize:2,defaultImages:this.singleImage?[this.singleImage]:[],type:"single"},this.imageModalVisible=!0},openSmallModal:function(){this.currentConfig={multiple:!0,maxCount:5,maxSize:1,defaultImages:this.smallImages,type:"small"},this.imageModalVisible=!0},handleImageConfirm:function(e){switch(this.currentConfig.type){case"multiple":this.multipleImages=e;break;case"single":this.singleImage=e.length>0?e[0]:null;break;case"small":this.smallImages=e;break}},removeMultipleImage:function(e){this.multipleImages.splice(e,1)},removeSingleImage:function(){this.singleImage=null},removeSmallImage:function(e){this.smallImages.splice(e,1)}}}),n=s,m=(i("e751"),i("2877")),r=Object(m["a"])(n,a,l,!1,null,"0236c0c6",null);t["default"]=r.exports},a434:function(e,t,i){"use strict";var a=i("23e7"),l=i("7b0b"),s=i("23cb"),n=i("5926"),m=i("07fa"),r=i("3a34"),o=i("3511"),c=i("65f0"),u=i("8418"),g=i("083a"),d=i("1dde"),p=d("splice"),f=Math.max,h=Math.min;a({target:"Array",proto:!0,forced:!p},{splice:function(e,t){var i,a,d,p,v,I,b=l(this),C=m(b),_=s(e,C),y=arguments.length;for(0===y?i=a=0:1===y?(i=0,a=C-_):(i=y-2,a=h(f(n(t),0),C-_)),o(C+i-a),d=c(b,a),p=0;p<a;p++)v=_+p,v in b&&u(d,p,b[v]);if(d.length=a,i<a){for(p=_;p<C-a;p++)v=p+a,I=p+i,v in b?b[I]=b[v]:g(b,I);for(p=C;p>C-a+i;p--)g(b,p-1)}else if(i>a)for(p=C-a;p>_;p--)v=p+a-1,I=p+i-1,v in b?b[I]=b[v]:g(b,I);for(p=0;p<i;p++)b[p+_]=arguments[p+2];return r(b,C-a+i),d}})},e751:function(e,t,i){"use strict";i("669c")}}]);
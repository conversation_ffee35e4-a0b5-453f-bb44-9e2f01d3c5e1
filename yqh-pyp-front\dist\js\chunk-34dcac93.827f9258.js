(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-34dcac93"],{"11e9":function(t,n,e){var o=e("52a7"),r=e("4630"),c=e("6821"),i=e("6a99"),u=e("69a8"),a=e("c69a"),s=Object.getOwnPropertyDescriptor;n.f=e("9e1e")?s:function(t,n){if(t=c(t),n=i(n,!0),a)try{return s(t,n)}catch(e){}if(u(t,n))return r(!o.f.call(t,n),t[n])}},"1d52":function(t,n,e){"use strict";e.d(n,"a",(function(){return u}));e("386d"),e("4917"),e("3b2b"),e("a481");var o=e("c135"),r="wx6a7f38e0347e6669",c="https://open.weixin.qq.com/connect/oauth2/authorize?appid="+r+"&redirect_uri=REDIRECT_URI&response_type=code&scope=snsapi_userinfo&state=0#wechat_redirect",i=!1;function u(t){return new Promise((function(n,e){if(i)return console.log("微信授权正在进行中，请勿重复操作"),void e(new Error("授权正在进行中"));i=!0;var r=a("code");if(console.log("获取到的code:",r),r)console.log("获取到code，开始换取用户信息:",r),o["a"].post("/pyp/wxAuth/codeToUserInfo",{code:r}).then((function(t){i=!1,200==t.code?(console.log("微信授权完成"),n(t.data)):(console.log("换取openid失败:",t.msg),e(new Error(t.msg||"换取用户信息失败")))})).catch((function(t){i=!1,console.error("微信授权请求失败:",t),e(t)}));else{console.log("未获取到code，跳转微信授权"),i=!1;var u=encodeURIComponent(t||window.location.href);window.location.href=c.replace("REDIRECT_URI",u)}}))}function a(t){var n=new RegExp("(^|&)"+t+"=([^&]*)(&|$)"),e=window.location.search.substring(1).match(n);return e?decodeURIComponent(e[2]):""}},"386d":function(t,n,e){"use strict";var o=e("cb7c"),r=e("83a1"),c=e("5f1b");e("214f")("search",1,(function(t,n,e,i){return[function(e){var o=t(this),r=void 0==e?void 0:e[n];return void 0!==r?r.call(e,o):new RegExp(e)[n](String(o))},function(t){var n=i(e,t,this);if(n.done)return n.value;var u=o(t),a=String(this),s=u.lastIndex;r(s,0)||(u.lastIndex=0);var f=c(u,a);return r(u.lastIndex,s)||(u.lastIndex=s),null===f?-1:f.index}]}))},"3b2b":function(t,n,e){var o=e("7726"),r=e("5dbc"),c=e("86cc").f,i=e("9093").f,u=e("aae3"),a=e("0bfb"),s=o.RegExp,f=s,p=s.prototype,d=/a/g,l=/a/g,h=new s(d)!==d;if(e("9e1e")&&(!h||e("79e5")((function(){return l[e("2b4c")("match")]=!1,s(d)!=d||s(l)==l||"/a/i"!=s(d,"i")})))){s=function(t,n){var e=this instanceof s,o=u(t),c=void 0===n;return!e&&o&&t.constructor===s&&c?t:r(h?new f(o&&!c?t.source:t,n):f((o=t instanceof s)?t.source:t,o&&c?a.call(t):n),e?this:p,s)};for(var b=function(t){t in s||c(s,t,{configurable:!0,get:function(){return f[t]},set:function(n){f[t]=n}})},v=i(f),w=0;v.length>w;)b(v[w++]);p.constructor=s,s.prototype=p,e("2aba")(o,"RegExp",s)}e("7a56")("RegExp")},"5dbc":function(t,n,e){var o=e("d3f4"),r=e("8b97").set;t.exports=function(t,n,e){var c,i=n.constructor;return i!==e&&"function"==typeof i&&(c=i.prototype)!==e.prototype&&o(c)&&r&&r(t,c),t}},"691c":function(t,n,e){"use strict";e("e4fb")},"83a1":function(t,n){t.exports=Object.is||function(t,n){return t===n?0!==t||1/t===1/n:t!=t&&n!=n}},"8b97":function(t,n,e){var o=e("d3f4"),r=e("cb7c"),c=function(t,n){if(r(t),!o(n)&&null!==n)throw TypeError(n+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,n,o){try{o=e("9b43")(Function.call,e("11e9").f(Object.prototype,"__proto__").set,2),o(t,[]),n=!(t instanceof Array)}catch(r){n=!0}return function(t,e){return c(t,e),n?t.__proto__=e:o(t,e),t}}({},!1):void 0),check:c}},9093:function(t,n,e){var o=e("ce10"),r=e("e11e").concat("length","prototype");n.f=Object.getOwnPropertyNames||function(t){return o(t,r)}},b0c3:function(t,n,e){"use strict";e.r(n);var o=function(){var t=this,n=t._self._c;return n("div")},r=[],c=e("1d52"),i={components:{},data:function(){return{wxOpenid:"",returnUrl:""}},mounted:function(){var t=this;this.returnUrl=decodeURIComponent(this.$route.query.returnUrl),document.title="微信授权登录中......",Object(c["a"])().then((function(n){location.href=t.returnUrl}))},methods:{}},u=i,a=(e("691c"),e("2877")),s=Object(a["a"])(u,o,r,!1,null,"0972937b",null);n["default"]=s.exports},e4fb:function(t,n,e){}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e75f2d0c"],{5075:function(t,e,a){},8826:function(t,e,a){"use strict";a.r(e);a("b0c0"),a("498a");var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.onSearch()}}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"关键词",clearable:""},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.onSearch()}}},[t._v("查询")]),t.isAuth("activity:activityimage:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("activity:activityimage:save")?e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.batchUploadHandle()}}},[t._v("批量上传")]):t._e(),t.isAuth("activity:activityimage:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e(),t.isAuth("activity:activityimage:update")?e("el-button",{attrs:{type:"warning",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.batchSetAiTagHandle()}}},[t._v("批量设置AI标签")]):t._e()],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"图片名称"}}),e("el-table-column",{attrs:{prop:"mediaUrl","header-align":"center",align:"center",width:"120",label:"图片预览"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.mediaUrl?e("div",{staticClass:"image-preview"},[e("img",{staticClass:"preview-img",attrs:{src:a.row.mediaUrl,alt:a.row.name},on:{click:function(e){return t.previewImage(a.row.mediaUrl)}}})]):e("span",{staticClass:"no-image"},[t._v("无图片")])]}}])}),e("el-table-column",{attrs:{prop:"fileSize","header-align":"center",align:"center",width:"100",label:"文件大小"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatFileSize(e.row.fileSize))+" ")]}}])}),e("el-table-column",{attrs:{prop:"paixu","header-align":"center",align:"center",width:"80",label:"排序"}}),e("el-table-column",{attrs:{prop:"type","header-align":"center",align:"center",width:"100",label:"类型"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:0===a.row.type?"info":"success",size:"small"}},[t._v(" "+t._s(0===a.row.type?"素材":"成品")+" ")])]}}])}),e("el-table-column",{attrs:{prop:"useCount","header-align":"center",align:"center",width:"120",label:"全局使用次数"}}),e("el-table-column",{attrs:{prop:"aiTag","header-align":"center",align:"center",width:"150",label:"AI标签"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.aiTag?e("div",{staticClass:"ai-tags"},t._l(a.row.aiTag.split(","),(function(a){return e("el-tag",{key:a,staticStyle:{margin:"2px"},attrs:{size:"mini"}},[t._v(" "+t._s(a.trim())+" ")])})),1):e("span",{staticClass:"no-tags"},[t._v("通用图片")])]}}])}),e("el-table-column",{attrs:{"header-align":"center",align:"center",width:"200",label:"平台使用情况"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(e){return t.showPlatformUsage(a.row)}}},[t._v(" 查看详情 ")])]}}])}),e("el-table-column",{attrs:{prop:"activityTextId","header-align":"center",align:"center",label:"使用文案id"}}),e("el-table-column",{attrs:{prop:"createOn","show-overflow-tooltip":!0,"header-align":"center",align:"center",label:"创建时间"}}),e("el-table-column",{attrs:{prop:"updateOn","show-overflow-tooltip":!0,"header-align":"center",align:"center",label:"更新时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e(),e("image-upload-modal",{attrs:{visible:t.batchUploadVisible,multiple:!0,"max-count":20},on:{"update:visible":function(e){t.batchUploadVisible=e},confirm:t.handleBatchUploadConfirm,close:function(e){t.batchUploadVisible=!1}}}),e("el-dialog",{attrs:{visible:t.previewVisible,width:"60%","append-to-body":""},on:{"update:visible":function(e){t.previewVisible=e}}},[e("img",{attrs:{width:"100%",src:t.previewImageUrl,alt:"图片预览"}})]),e("el-dialog",{attrs:{visible:t.platformUsageVisible,width:"50%",title:"平台使用情况","append-to-body":""},on:{"update:visible":function(e){t.platformUsageVisible=e}}},[t.currentImageInfo?e("div",[e("h4",[t._v("图片信息")]),e("p",[e("strong",[t._v("图片名称：")]),t._v(t._s(t.currentImageInfo.name))]),e("p",[e("strong",[t._v("全局使用次数：")]),t._v(t._s(t.realTotalUsageCount))]),e("h4",{staticStyle:{"margin-top":"20px"}},[t._v("各平台使用情况")]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.platformUsageList,border:""}},[e("el-table-column",{attrs:{prop:"platform","header-align":"center",align:"center",label:"平台"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:t.getPlatformTagType(a.row.platform),size:"small"}},[t._v(" "+t._s(t.getPlatformName(a.row.platform))+" ")])]}}],null,!1,4047797321)}),e("el-table-column",{attrs:{prop:"useCount","header-align":"center",align:"center",label:"使用次数"}}),e("el-table-column",{attrs:{prop:"firstUsedTime","header-align":"center",align:"center",label:"首次使用时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.firstUsedTime?t.formatDateTime(e.row.firstUsedTime):"-")+" ")]}}],null,!1,3791656310)}),e("el-table-column",{attrs:{prop:"lastUsedTime","header-align":"center",align:"center",label:"最后使用时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.lastUsedTime?t.formatDateTime(e.row.lastUsedTime):"-")+" ")]}}],null,!1,3948290454)})],1),0===t.platformUsageList.length?e("div",{staticStyle:{"text-align":"center",padding:"20px",color:"#999"}},[t._v(" 该图片暂未在任何平台使用 ")]):t._e()],1):t._e(),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.platformUsageVisible=!1}}},[t._v("关闭")])],1)]),e("el-dialog",{attrs:{title:"批量设置AI标签",visible:t.batchAiTagVisible,width:"500px","append-to-body":""},on:{"update:visible":function(e){t.batchAiTagVisible=e}}},[e("el-form",{attrs:{model:t.batchAiTagForm,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"选择AI标签"}},[e("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"从活动AI标签中选择"},model:{value:t.batchAiTagForm.selectedTags,callback:function(e){t.$set(t.batchAiTagForm,"selectedTags",e)},expression:"batchAiTagForm.selectedTags"}},t._l(t.activityAiTags,(function(t){return e("el-option",{key:t,attrs:{label:t,value:t}})})),1),e("div",{staticStyle:{"margin-top":"10px","font-size":"12px",color:"#909399"}},[e("i",{staticClass:"el-icon-info"}),t._v(" 将为选中的 "+t._s(t.dataListSelections.length)+" 张图片设置AI标签。不选择任何标签表示设置为通用图片 ")])],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.batchAiTagVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary",loading:t.batchAiTagSubmitting},on:{click:t.confirmBatchSetAiTag}},[t._v(" "+t._s(t.batchAiTagSubmitting?"设置中...":"确定")+" ")])],1)],1)],1)},n=[],r=(a("99af"),a("4de4"),a("a15b"),a("d81d"),a("b680"),a("d3b7"),a("3ca3"),a("0643"),a("2382"),a("a573"),a("ddb0"),a("2356")),o={data:function(){return{dataForm:{name:"",appid:"",activityId:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,batchUploadVisible:!1,previewVisible:!1,previewImageUrl:"",platformUsageVisible:!1,currentImageInfo:null,platformUsageList:[],realTotalUsageCount:0,batchAiTagVisible:!1,batchAiTagSubmitting:!1,activityAiTags:[],batchAiTagForm:{selectedTags:[]}}},components:{AddOrUpdate:r["default"],ImageUploadModal:function(){return Promise.all([a.e("chunk-2d0e1c2e"),a.e("chunk-e9144aa4"),a.e("chunk-58faa667")]).then(a.bind(null,"4185"))}},activated:function(){this.dataForm.activityId=this.$route.query.activityId,this.getDataList(),this.loadActivityAiTags()},methods:{onSearch:function(){this.pageIndex=1,this.getDataList()},getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/activity/activityimage/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,name:this.dataForm.name,appid:this.$cookie.get("appid"),activityId:this.dataForm.activityId})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(t,e.dataForm.activityId)}))},batchUploadHandle:function(){this.batchUploadVisible=!0},handleBatchUploadConfirm:function(t){var e=this;if(t&&0!==t.length){var a=t.map((function(t,a){return e.$http({url:e.$http.adornUrl("/activity/activityimage/save"),method:"post",data:e.$http.adornData({activityId:e.dataForm.activityId,name:t.fileName||"批量上传图片".concat(a+1),mediaUrl:t.url,fileSize:t.fileSize||0,type:0,paixu:a+1,useCount:0})})})),i=this.$loading({lock:!0,text:"正在保存图片...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});Promise.all(a).then((function(t){i.close();var a=t.filter((function(t){return t.data&&200===t.data.code})).length,n=t.length-a;0===n?e.$message.success("成功保存 ".concat(a," 张图片")):e.$message.warning("成功保存 ".concat(a," 张图片，失败 ").concat(n," 张")),e.getDataList()})).catch((function(t){i.close(),console.error("批量保存失败:",t),e.$message.error("批量保存失败，请重试")}))}else this.$message.warning("请选择要上传的图片")},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/activity/activityimage/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},previewImage:function(t){this.previewImageUrl=t,this.previewVisible=!0},formatFileSize:function(t){if(!t||0===t)return"0.00 MB";var e=t/1048576;return e.toFixed(2)+" MB"},showPlatformUsage:function(t){this.currentImageInfo=t,this.platformUsageVisible=!0,this.getPlatformUsageData(t.id)},getPlatformUsageData:function(t){var e=this;this.$http({url:this.$http.adornUrl("/activity/activityimage/platform-usage"),method:"get",params:this.$http.adornParams({imageId:t})}).then((function(t){var a=t.data;a&&200===a.code?(e.platformUsageList=a.data||[],e.realTotalUsageCount=a.totalUsageCount||0):(e.platformUsageList=[],e.realTotalUsageCount=0,e.$message.error(a.msg||"获取平台使用数据失败"))})).catch((function(t){console.error("获取平台使用数据失败:",t),e.platformUsageList=[],e.realTotalUsageCount=0,e.$message.error("获取平台使用数据失败")}))},getPlatformName:function(t){var e={douyin:"抖音",xiaohongshu:"小红书",dianping:"大众点评",meituan:"美团点评",ctrip:"携程",kuaishou:"快手",weixin:"微信朋友圈",weibo:"微博",bilibili:"B站",zhihu:"知乎",taobao:"淘宝",jingdong:"京东",general:"通用",douyin_review:"抖音点评",xiaohongshu_review:"小红书点评",dianping_review:"大众点评",meituan_review:"美团点评",ctrip_review:"携程点评",ctrip_notes:"携程笔记",ctrip_group_buying:"携程团购"};return e[t]||t},getPlatformTagType:function(t){var e={douyin:"danger",xiaohongshu:"warning",dianping:"success",meituan:"primary",ctrip:"info",kuaishou:"danger",weixin:"success",weibo:"warning",bilibili:"primary",zhihu:"success",taobao:"warning",jingdong:"danger",general:"default",douyin_review:"danger",xiaohongshu_review:"warning",dianping_review:"success",meituan_review:"primary",ctrip_review:"info",ctrip_notes:"info",ctrip_group_buying:"info"};return e[t]||"default"},formatDateTime:function(t){if(!t)return"-";var e=new Date(t);return e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})},loadActivityAiTags:function(){var t=this;this.dataForm.activityId&&this.$http({url:this.$http.adornUrl("/web/activity/activitytext/getAiTags"),method:"get",params:this.$http.adornParams({activityId:this.dataForm.activityId})}).then((function(e){var a=e.data;a&&200===a.code&&(t.activityAiTags=a.tags||[])})).catch((function(e){console.error("加载活动AI标签失败:",e),t.activityAiTags=[]}))},batchSetAiTagHandle:function(){this.dataListSelections.length<=0?this.$message.warning("请选择要设置标签的图片"):0!==this.activityAiTags.length?(this.batchAiTagForm.selectedTags=[],this.batchAiTagVisible=!0):this.$message.warning("当前活动未配置AI标签，请先在活动管理中配置AI标签")},confirmBatchSetAiTag:function(){var t=this;this.batchAiTagSubmitting=!0;var e=this.dataListSelections.map((function(t){return t.id})),a=this.batchAiTagForm.selectedTags.join(",");this.$http({url:this.$http.adornUrl("/activity/activityimage/batchSetAiTag"),method:"post",data:this.$http.adornData({imageIds:e,aiTag:a})}).then((function(e){var a=e.data;t.batchAiTagSubmitting=!1,a&&200===a.code?(t.$message.success("批量设置AI标签成功"),t.batchAiTagVisible=!1,t.getDataList()):t.$message.error(a.msg||"批量设置AI标签失败")})).catch((function(e){t.batchAiTagSubmitting=!1,console.error("批量设置AI标签失败:",e),t.$message.error("批量设置AI标签失败")}))}}},l=o,s=(a("a77e"),a("2877")),c=Object(s["a"])(l,i,n,!1,null,"aba141c4",null);e["default"]=c.exports},a77e:function(t,e,a){"use strict";a("5075")}}]);
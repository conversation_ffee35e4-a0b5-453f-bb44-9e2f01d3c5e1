(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-79fe9f98","chunk-2d0a4b8c","chunk-2d0a4b8c","chunk-58faa667"],{"083a":function(t,e,a){"use strict";var i=a("0d51"),r=TypeError;t.exports=function(t,e){if(!delete t[e])throw new r("Cannot delete property "+i(e)+" of "+i(t))}},a434:function(t,e,a){"use strict";var i=a("23e7"),r=a("7b0b"),o=a("23cb"),l=a("5926"),n=a("07fa"),s=a("3a34"),d=a("3511"),c=a("65f0"),p=a("8418"),u=a("083a"),m=a("1dde"),h=m("splice"),f=Math.max,g=Math.min;i({target:"Array",proto:!0,forced:!h},{splice:function(t,e){var a,i,m,h,F,b,v=r(this),k=n(v),$=o(t,k),y=arguments.length;for(0===y?a=i=0:1===y?(a=0,i=k-$):(a=y-2,i=g(f(l(e),0),k-$)),d(k+a-i),m=c(v,i),h=0;h<i;h++)F=$+h,F in v&&p(m,h,v[F]);if(m.length=i,a<i){for(h=$;h<k-i;h++)F=h+i,b=h+a,F in v?v[b]=v[F]:u(v,b);for(h=k;h>k-i+a;h--)u(v,h-1)}else if(a>i)for(h=k-i;h>$;h--)F=h+i-1,b=h+a-1,F in v?v[b]=v[F]:u(v,b);for(h=0;h<a;h++)v[h+$]=arguments[h+2];return s(v,k-i+a),m}})},df53:function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.dataFormSubmit()}}},[e("el-form-item",{attrs:{label:"客户文件名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"客户文件名称"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",{attrs:{label:"文件地址",prop:"url"}},[e("el-upload",{attrs:{"list-type":"picture-card","before-upload":t.checkFileSize,"on-success":t.appSuccessHandle,"file-list":t.appFileList,action:t.url},scopedSlots:t._u([{key:"file",fn:function(a){var i=a.file;return e("div",{},[e("img",{staticClass:"el-upload-list__item-thumbnail",attrs:{src:i.url,alt:""}}),e("span",{staticClass:"el-upload-list__item-actions"},[e("span",{staticClass:"el-upload-list__item-preview",on:{click:function(e){return t.handleAppPictureCardPreview(i)}}},[e("i",{staticClass:"el-icon-zoom-in"})]),e("span",{staticClass:"el-upload-list__item-delete",on:{click:function(e){return t.handleAppRemove(i)}}},[e("i",{staticClass:"el-icon-delete"})])])])}}])},[e("i",{staticClass:"el-icon-plus",attrs:{slot:"default"},slot:"default"})]),e("el-dialog",{attrs:{visible:t.imgAppDialogVisible},on:{"update:visible":function(e){t.imgAppDialogVisible=e}}},[e("img",{attrs:{width:"100%",src:t.dialogAppImageUrl,alt:""}})])],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],o=(a("a434"),a("ac1f"),a("5319"),a("7c8d"),{data:function(){return{url:"",appFileList:[],imgAppDialogVisible:!1,dialogAppImageUrl:"",visible:!1,dataForm:{repeatToken:"",id:0,name:"",url:"",clientId:"",appid:"",companyRoleId:""},dataRule:{name:[{required:!0,message:"客户文件名称不能为空",trigger:"blur"}],url:[{required:!0,message:"文件地址不能为空",trigger:"blur"}],clientId:[{required:!0,message:"客户ID不能为空",trigger:"blur"}],appid:[{required:!0,message:"公司ID不能为空",trigger:"blur"}],companyRoleId:[{required:!0,message:"公司权限ID不能为空",trigger:"blur"}]}}},methods:{init:function(t,e){var a=this;this.url=this.$http.adornUrl("/sys/oss/upload?token=".concat(this.$cookie.get("token"))),this.getToken(),this.appFileList=[],this.dataForm.id=t||0,this.dataForm.clientId=e,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id?a.$http({url:a.$http.adornUrl("/client/clientthing/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.appFileList=e.clientThing.appFileList,a.dataForm.name=e.clientThing.name,a.dataForm.url=e.clientThing.url,a.dataForm.clientId=e.clientThing.clientId,a.dataForm.appid=e.clientThing.appid,a.dataForm.companyRoleId=e.clientThing.companyRoleId)})):(a.dataForm.appid=a.$cookie.get("appid"),a.dataForm.companyRoleId=a.$cookie.get("companyRoleId"))}))},getToken:function(){var t=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.repeatToken=a.result)}))},checkFileSize:function(t){return!(t.size/1024/1024>6)||(this.$message.error("".concat(t.name,"文件大于6MB，请选择小于6MB大小的图片")),!1)},appSuccessHandle:function(t,e,a){this.appFileList=a,this.successNum++,t&&200===t.code?this.dataForm.url&&0!=this.dataForm.url.length?this.dataForm.url+=","+t.url:this.dataForm.url=t.url:this.$message.error(t.msg)},handleAppPictureCardPreview:function(t){this.dialogAppImageUrl=t.url,this.imgAppDialogVisible=!0},handleAppRemove:function(t){this.dataForm.url=(","+this.dataForm.url+",").replace(","+t.url+",",",").substr(1).replace(/,$/,""),this.appFileList.splice(this.appFileList.indexOf(t),1)},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/client/clientthing/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({repeatToken:t.dataForm.repeatToken,id:t.dataForm.id||void 0,name:t.dataForm.name,url:t.dataForm.url,clientId:t.dataForm.clientId,appid:t.dataForm.appid,companyRoleId:t.dataForm.companyRoleId})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):(t.$message.error(a.msg),"不能重复提交"!=a.msg&&t.getToken())}))}))}}}),l=o,n=a("2877"),s=Object(n["a"])(l,i,r,!1,null,null,null);e["default"]=s.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5f0521d1"],{e739:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:"日程讨论嘉宾修改","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"80px"}},[e.placeActivityTopicGuest.length>0?t("el-table",{staticStyle:{margin:"20px 0"},attrs:{data:e.placeActivityTopicGuest,border:""}},[t("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"嘉宾名称"}}),t("el-table-column",{attrs:{prop:"orderBy","show-overflow-tooltip":"true","header-align":"center",align:"center",label:"排序"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[t("el-input",{attrs:{placeholder:"排序"},model:{value:a.row.orderBy,callback:function(t){e.$set(a.row,"orderBy",t)},expression:"scope.row.orderBy"}})],1)}}],null,!1,525224533)}),t("el-table-column",{attrs:{prop:"confirmStatus","show-overflow-tooltip":"true","header-align":"center",align:"center",label:"确认状态"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[t("el-select",{attrs:{placeholder:"确认状态",filterable:""},model:{value:a.row.confirmStatus,callback:function(t){e.$set(a.row,"confirmStatus",t)},expression:"scope.row.confirmStatus"}},e._l(e.confirmStatus,(function(e){return t("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1)}}],null,!1,1300827402)}),t("el-table-column",{attrs:{prop:"confirmTime","header-align":"center",align:"center",label:"确认时间"}}),t("el-table-column",{attrs:{prop:"confirmReason","header-align":"center",align:"center",label:"拒绝理由"}})],1):e._e()],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{disabled:0==e.placeActivityTopicGuest.length,type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},i=[],l=a("ed56"),o={data:function(){return{confirmStatus:l["a"],visible:!1,placeActivityTopicGuest:[],guestList:[],dataForm:{id:0,activityId:"",name:"",placeId:"",topicGuestIds:[],orderBy:0},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],name:[{required:!0,message:"会场名称不能为空",trigger:"blur"}],placeId:[{required:!0,message:"场地不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序，数值越小越靠前不能为空",trigger:"blur"}]}}},methods:{init:function(e,t){var a=this;this.dataForm.activityId=e,this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/place/placeactivitytopicschedulediscuss/findBySchedulesId/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(e){var t=e.data;t&&200===t.code&&(a.placeActivityTopicGuest=t.result)}))}))},getGuest:function(){var e=this;this.$http({url:this.$http.adornUrl("/activity/activityguest/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.guestList=a.result)}))},dataFormSubmit:function(){var e=this;this.$http({url:this.$http.adornUrl("/place/placeactivitytopicschedulediscuss/updateBatch"),method:"post",data:this.placeActivityTopicGuest}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(a.msg)}))}}},n=o,s=a("2877"),c=Object(s["a"])(n,r,i,!1,null,null,null);t["default"]=c.exports},ed56:function(e,t,a){"use strict";a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return l}));var r=[{key:0,value:"预告"},{key:1,value:"直播"},{key:2,value:"录播"}],i=[{key:0,value:"未确认"},{key:1,value:"确认通过"},{key:2,value:"确认不通过"}],l=[{key:0,value:"自定义内容"},{key:1,value:"自定义链接"},{key:2,value:"会议日程"},{key:3,value:"会议嘉宾"},{key:4,value:"聊天室"},{key:5,value:"考试&问卷"},{key:6,value:"展商列表"},{key:7,value:"录播视频列表"}]}}]);
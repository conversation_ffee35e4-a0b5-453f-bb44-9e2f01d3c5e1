(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e607d9f0","chunk-2d21af2b"],{a15b:function(t,e,a){"use strict";var r=a("23e7"),o=a("e330"),n=a("44ad"),i=a("fc6a"),l=a("a640"),s=o([].join),d=n!==Object,p=d||!l("join",",");r({target:"Array",proto:!0,forced:p},{join:function(t){return s(i(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var r=a("23e7"),o=a("d024"),n=a("c430");r({target:"Iterator",proto:!0,real:!0,forced:n},{map:o})},be4f:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible,width:"80%"},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.dataFormSubmit()}}},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"类型编码",prop:"typeCode"}},[e("el-input",{attrs:{placeholder:"类型编码（如：douyin）",disabled:!!t.dataForm.id},model:{value:t.dataForm.typeCode,callback:function(e){t.$set(t.dataForm,"typeCode",e)},expression:"dataForm.typeCode"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"类型名称",prop:"typeName"}},[e("el-input",{attrs:{placeholder:"类型名称（如：抖音）"},model:{value:t.dataForm.typeName,callback:function(e){t.$set(t.dataForm,"typeName",e)},expression:"dataForm.typeName"}})],1)],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"平台名称",prop:"platform"}},[e("el-input",{attrs:{placeholder:"平台名称"},model:{value:t.dataForm.platform,callback:function(e){t.$set(t.dataForm,"platform",e)},expression:"dataForm.platform"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"内容类型",prop:"contentType"}},[e("el-input",{attrs:{placeholder:"内容类型（如：短视频）"},model:{value:t.dataForm.contentType,callback:function(e){t.$set(t.dataForm,"contentType",e)},expression:"dataForm.contentType"}})],1)],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"标题长度要求",prop:"titleLength"}},[e("el-input",{attrs:{placeholder:"标题长度要求"},model:{value:t.dataForm.titleLength,callback:function(e){t.$set(t.dataForm,"titleLength",e)},expression:"dataForm.titleLength"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"内容长度要求",prop:"contentLength"}},[e("el-input",{attrs:{placeholder:"内容长度要求"},model:{value:t.dataForm.contentLength,callback:function(e){t.$set(t.dataForm,"contentLength",e)},expression:"dataForm.contentLength"}})],1)],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"话题数量",prop:"topicsCount"}},[e("el-input-number",{attrs:{min:1,max:20,placeholder:"话题数量"},model:{value:t.dataForm.topicsCount,callback:function(e){t.$set(t.dataForm,"topicsCount",e)},expression:"dataForm.topicsCount"}})],1)],1),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[e("el-input-number",{attrs:{min:0,placeholder:"排序"},model:{value:t.dataForm.sortOrder,callback:function(e){t.$set(t.dataForm,"sortOrder",e)},expression:"dataForm.sortOrder"}})],1)],1),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"状态",prop:"status"}},[e("el-radio-group",{model:{value:t.dataForm.status,callback:function(e){t.$set(t.dataForm,"status",e)},expression:"dataForm.status"}},[e("el-radio",{attrs:{label:1}},[t._v("启用")]),e("el-radio",{attrs:{label:0}},[t._v("禁用")])],1)],1)],1)],1),e("el-form-item",{attrs:{label:"话题格式要求",prop:"topicsFormat"}},[e("el-input",{attrs:{placeholder:"话题格式要求（如：不带#号，用逗号分隔）"},model:{value:t.dataForm.topicsFormat,callback:function(e){t.$set(t.dataForm,"topicsFormat",e)},expression:"dataForm.topicsFormat"}})],1),e("el-form-item",{attrs:{label:"风格特点",prop:"style"}},[e("el-input",{attrs:{placeholder:"风格特点"},model:{value:t.dataForm.style,callback:function(e){t.$set(t.dataForm,"style",e)},expression:"dataForm.style"}})],1),e("el-form-item",{attrs:{label:"内容要求",prop:"requirements"}},[e("el-input",{attrs:{type:"textarea",rows:4,placeholder:"内容要求（每行一个要求）"},model:{value:t.dataForm.requirements,callback:function(e){t.$set(t.dataForm,"requirements",e)},expression:"dataForm.requirements"}})],1),e("el-form-item",{attrs:{label:"Prompt模板",prop:"promptTemplate"}},[e("el-input",{attrs:{type:"textarea",rows:8,placeholder:"Prompt模板，支持占位符：{platform}, {content_type}, {keyword}, {title_section}, {title_length}, {content_length}, {topics_count}, {topics_format}, {requirements}, {style}"},model:{value:t.dataForm.promptTemplate,callback:function(e){t.$set(t.dataForm,"promptTemplate",e)},expression:"dataForm.promptTemplate"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},o=[],n={data:function(){return{visible:!1,dataForm:{id:0,typeCode:"",typeName:"",platform:"",contentType:"",titleLength:"",contentLength:"",topicsCount:5,topicsFormat:"",requirements:"",style:"",promptTemplate:"",sortOrder:0,status:1},dataRule:{typeCode:[{required:!0,message:"类型编码不能为空",trigger:"blur"}],typeName:[{required:!0,message:"类型名称不能为空",trigger:"blur"}],platform:[{required:!0,message:"平台名称不能为空",trigger:"blur"}],contentType:[{required:!0,message:"内容类型不能为空",trigger:"blur"}],titleLength:[{required:!0,message:"标题长度要求不能为空",trigger:"blur"}],contentLength:[{required:!0,message:"内容长度要求不能为空",trigger:"blur"}],topicsCount:[{required:!0,message:"话题数量不能为空",trigger:"blur"}],topicsFormat:[{required:!0,message:"话题格式要求不能为空",trigger:"blur"}],requirements:[{required:!0,message:"内容要求不能为空",trigger:"blur"}],style:[{required:!0,message:"风格特点不能为空",trigger:"blur"}],promptTemplate:[{required:!0,message:"Prompt模板不能为空",trigger:"blur"}]}}},methods:{init:function(t){var e=this;this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.dataForm.id?e.$http({url:e.$http.adornUrl("/activity/adtypeconfig/info/".concat(e.dataForm.id)),method:"get",params:e.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm.typeCode=a.adTypeConfig.typeCode,e.dataForm.typeName=a.adTypeConfig.typeName,e.dataForm.platform=a.adTypeConfig.platform,e.dataForm.contentType=a.adTypeConfig.contentType,e.dataForm.titleLength=a.adTypeConfig.titleLength,e.dataForm.contentLength=a.adTypeConfig.contentLength,e.dataForm.topicsCount=a.adTypeConfig.topicsCount,e.dataForm.topicsFormat=a.adTypeConfig.topicsFormat,e.dataForm.requirements=a.adTypeConfig.requirements,e.dataForm.style=a.adTypeConfig.style,e.dataForm.promptTemplate=a.adTypeConfig.promptTemplate,e.dataForm.sortOrder=a.adTypeConfig.sortOrder,e.dataForm.status=a.adTypeConfig.status)})):e.dataForm.promptTemplate="请为{platform}平台生成{content_type}文案。\n\n关键词：{keyword}\n{title_section}\n要求：\n{requirements}\n\n请以JSON格式返回以下内容（键名使用英文）：\n- 标题（title，{title_length}）\n- 内容（content，{content_length}）\n- 话题（topics，{topics_count}个，用逗号分隔，{topics_format}）\n\n风格特点：{style}"}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/activity/adtypeconfig/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,typeCode:t.dataForm.typeCode,typeName:t.dataForm.typeName,platform:t.dataForm.platform,contentType:t.dataForm.contentType,titleLength:t.dataForm.titleLength,contentLength:t.dataForm.contentLength,topicsCount:t.dataForm.topicsCount,topicsFormat:t.dataForm.topicsFormat,requirements:t.dataForm.requirements,style:t.dataForm.style,promptTemplate:t.dataForm.promptTemplate,sortOrder:t.dataForm.sortOrder,status:t.dataForm.status})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))}}},i=n,l=a("2877"),s=Object(l["a"])(i,r,o,!1,null,null,null);e["default"]=s.exports},d024:function(t,e,a){"use strict";var r=a("c65b"),o=a("59ed"),n=a("825a"),i=a("46c4"),l=a("c5cc"),s=a("9bdd"),d=l((function(){var t=this.iterator,e=n(r(this.next,t)),a=this.done=!!e.done;if(!a)return s(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return n(this),o(t),new d(i(this),{mapper:t})}},d054:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.getDataList()}}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"类型名称",clearable:""},model:{value:t.dataForm.typeName,callback:function(e){t.$set(t.dataForm,"typeName",e)},expression:"dataForm.typeName"}})],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"状态",clearable:""},model:{value:t.dataForm.status,callback:function(e){t.$set(t.dataForm,"status",e)},expression:"dataForm.status"}},[e("el-option",{attrs:{label:"启用",value:"1"}}),e("el-option",{attrs:{label:"禁用",value:"0"}})],1)],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),t.isAuth("activity:adtypeconfig:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("activity:adtypeconfig:save")?e("el-button",{attrs:{type:"success",loading:t.optimizing},on:{click:function(e){return t.optimizeNotesPrompts()}}},[t._v("优化笔记类提示词")]):t._e(),t.isAuth("activity:adtypeconfig:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"typeCode","header-align":"center",align:"center",label:"类型编码"}}),e("el-table-column",{attrs:{prop:"typeName","header-align":"center",align:"center",label:"类型名称"}}),e("el-table-column",{attrs:{prop:"platform","header-align":"center",align:"center",label:"平台名称"}}),e("el-table-column",{attrs:{prop:"contentType","header-align":"center",align:"center",label:"内容类型"}}),e("el-table-column",{attrs:{prop:"sortOrder","header-align":"center",align:"center",label:"排序"}}),e("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",label:"状态"},scopedSlots:t._u([{key:"default",fn:function(a){return[1===a.row.status?e("el-tag",{attrs:{size:"small",type:"success"}},[t._v("启用")]):e("el-tag",{attrs:{size:"small",type:"danger"}},[t._v("禁用")])]}}])}),e("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",width:"180",label:"创建时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e()],1)},o=[],n=(a("99af"),a("a15b"),a("d81d"),a("a573"),a("be4f")),i={data:function(){return{dataForm:{typeName:"",status:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,optimizing:!1}},components:{AddOrUpdate:n["default"]},activated:function(){this.getDataList()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/activity/adtypeconfig/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,typeName:this.dataForm.typeName,status:this.dataForm.status})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(t)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/activity/adtypeconfig/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},optimizeNotesPrompts:function(){var t=this;this.$confirm("确定要应用优化的笔记类提示词吗？这将更新小红书、携程笔记等类型的提示词模板，让生成的内容更有灵魂和高可用性。","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then((function(){t.optimizing=!0,t.$http({url:t.$http.adornUrl("/activity/adtypeconfig/optimizeNotesPrompts"),method:"post",data:t.$http.adornData()}).then((function(e){var a=e.data;t.optimizing=!1,a&&200===a.code?t.$message({message:"笔记类提示词优化成功！生成的内容将更有灵魂和个性化。",type:"success",duration:3e3,onClose:function(){t.getDataList()}}):t.$message.error(a.msg||"优化失败，请重试")})).catch((function(){t.optimizing=!1,t.$message.error("优化失败，请重试")}))}))}}},l=i,s=a("2877"),d=Object(s["a"])(l,r,o,!1,null,null,null);e["default"]=d.exports},d81d:function(t,e,a){"use strict";var r=a("23e7"),o=a("b727").map,n=a("1dde"),i=n("map");r({target:"Array",proto:!0,forced:!i},{map:function(t){return o(this,t,arguments.length>1?arguments[1]:void 0)}})}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-76d96286","chunk-b95bdb80"],{3e3:function(e,a,t){"use strict";t.r(a);t("a4d3"),t("e01a"),t("b0c0");var i=function(){var e=this,a=e._self._c;return a("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(a){e.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"父通道",prop:"pid"}},[a("el-select",{attrs:{placeholder:"父通道",filterable:""},model:{value:e.dataForm.pid,callback:function(a){e.$set(e.dataForm,"pid",a)},expression:"dataForm.pid"}},[a("el-option",{attrs:{label:"无",value:""}}),e._l(e.channelList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1),a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"通道名称"},model:{value:e.dataForm.name,callback:function(a){e.$set(e.dataForm,"name",a)},expression:"dataForm.name"}})],1),a("el-form-item",{attrs:{label:"描述",prop:"description"}},[a("el-input",{attrs:{placeholder:"描述"},model:{value:e.dataForm.description,callback:function(a){e.$set(e.dataForm,"description",a)},expression:"dataForm.description"}})],1),a("el-form-item",{attrs:{label:"价格",prop:"price"}},[a("el-input",{attrs:{placeholder:"价格"},model:{value:e.dataForm.price,callback:function(a){e.$set(e.dataForm,"price",a)},expression:"dataForm.price"}})],1),a("el-form-item",{attrs:{label:"最大报名人数",prop:"maxNumber"}},[a("el-input",{attrs:{placeholder:"最大报名人数"},model:{value:e.dataForm.maxNumber,callback:function(a){e.$set(e.dataForm,"maxNumber",a)},expression:"dataForm.maxNumber"}})],1),a("el-form-item",{attrs:{label:"截止日期",prop:"deadline"}},[a("el-date-picker",{staticStyle:{windth:"100%"},attrs:{type:"datetime","value-format":"yyyy/MM/dd HH:mm:ss",placeholder:"截止日期"},model:{value:e.dataForm.deadline,callback:function(a){e.$set(e.dataForm,"deadline",a)},expression:"dataForm.deadline"}})],1),a("el-form-item",{attrs:{label:"邀请码",prop:"isNeedInvite"}},[a("el-select",{model:{value:e.dataForm.isNeedInvite,callback:function(a){e.$set(e.dataForm,"isNeedInvite",a)},expression:"dataForm.isNeedInvite"}},e._l(e.needInvite,(function(e){return a("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1),1==e.dataForm.isNeedInvite?a("el-form-item",{attrs:{label:"统一邀请码",prop:"inviteCode"}},[a("el-input",{attrs:{placeholder:"统一邀请码"},model:{value:e.dataForm.inviteCode,callback:function(a){e.$set(e.dataForm,"inviteCode",a)},expression:"dataForm.inviteCode"}})],1):e._e(),3==e.dataForm.isNeedInvite?a("el-form-item",{attrs:{label:"统一邀请码(多个)",prop:"inviteCode"}},[a("tags-editor",{attrs:{limit:10},model:{value:e.dataForm.inviteCode,callback:function(a){e.$set(e.dataForm,"inviteCode",a)},expression:"dataForm.inviteCode"}})],1):e._e(),a("el-form-item",{attrs:{label:"是否展示",prop:"isShow"}},[a("el-select",{model:{value:e.dataForm.isShow,callback:function(a){e.$set(e.dataForm,"isShow",a)},expression:"dataForm.isShow"}},e._l(e.yesOrNo,(function(e){return a("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1),a("el-form-item",{attrs:{label:"是否短信验证",prop:"isSms"}},[a("el-select",{model:{value:e.dataForm.isSms,callback:function(a){e.$set(e.dataForm,"isSms",a)},expression:"dataForm.isSms"}},e._l(e.yesOrNo,(function(e){return a("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1),"wx0770d56458b33c67"!=e.appid?a("el-form-item",{attrs:{label:"是否收集发票",prop:"isInvoice"}},[a("el-select",{model:{value:e.dataForm.isInvoice,callback:function(a){e.$set(e.dataForm,"isInvoice",a)},expression:"dataForm.isInvoice"}},e._l(e.yesOrNo,(function(e){return a("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"是否审核",prop:"isVerify"}},[a("el-select",{model:{value:e.dataForm.isVerify,callback:function(a){e.$set(e.dataForm,"isVerify",a)},expression:"dataForm.isVerify"}},e._l(e.yesOrNo,(function(e){return a("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1),e.dataForm.isVerify?a("el-form-item",{attrs:{label:"审核字段名称",prop:"verifyName"}},[a("el-input",{attrs:{placeholder:"审核字段名称"},model:{value:e.dataForm.verifyName,callback:function(a){e.$set(e.dataForm,"verifyName",a)},expression:"dataForm.verifyName"}})],1):e._e(),a("el-form-item",{attrs:{label:"学时(直播)",prop:"hour"}},[a("el-input",{attrs:{placeholder:"学时(直播)"},model:{value:e.dataForm.hour,callback:function(a){e.$set(e.dataForm,"hour",a)},expression:"dataForm.hour"}},[a("template",{slot:"append"},[e._v(" 分钟 ")])],2)],1),a("el-form-item",{attrs:{label:"排序",prop:"paixu"}},[a("el-input-number",{attrs:{min:0,max:100,label:"排序"},model:{value:e.dataForm.paixu,callback:function(a){e.$set(e.dataForm,"paixu",a)},expression:"dataForm.paixu"}})],1),e.dataForm.price>0?a("el-form-item",{attrs:{label:"微信支付",prop:"isWechatPay"}},[a("el-select",{model:{value:e.dataForm.isWechatPay,callback:function(a){e.$set(e.dataForm,"isWechatPay",a)},expression:"dataForm.isWechatPay"}},e._l(e.yesOrNo,(function(e){return a("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1):e._e(),e.dataForm.price>0?a("el-form-item",{attrs:{label:"支付宝支付",prop:"isAliPay"}},[a("el-select",{model:{value:e.dataForm.isAliPay,callback:function(a){e.$set(e.dataForm,"isAliPay",a)},expression:"dataForm.isAliPay"}},e._l(e.yesOrNo,(function(e){return a("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1):e._e(),e.dataForm.price>0?a("el-form-item",{attrs:{label:"银行转账",prop:"isBankTransfer"}},[a("el-select",{model:{value:e.dataForm.isBankTransfer,callback:function(a){e.$set(e.dataForm,"isBankTransfer",a)},expression:"dataForm.isBankTransfer"}},e._l(e.yesOrNo,(function(e){return a("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1):e._e(),1==e.dataForm.isBankTransfer?a("el-form-item",{attrs:{label:"银行转账信息",prop:"bankTransferNotify"}},[a("tinymce-editor",{ref:"editor",model:{value:e.dataForm.bankTransferNotify,callback:function(a){e.$set(e.dataForm,"bankTransferNotify",a)},expression:"dataForm.bankTransferNotify"}})],1):e._e()],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){e.visible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},n=[],r=(t("d3b7"),t("3ca3"),t("ddb0"),t("593c")),l=t("7de9"),o={components:{TinymceEditor:function(){return Promise.all([t.e("chunk-2d0e1c2e"),t.e("chunk-03be236c"),t.e("chunk-2d0a4b8c")]).then(t.bind(null,"26dc"))},tagsEditor:function(){return t.e("chunk-4dba3ada").then(t.bind(null,"a55c"))}},data:function(){return{channelList:[],appid:"",visible:!1,yesOrNo:l["g"],dataForm:{id:0,activityId:"",pid:"",name:"",description:"",price:"",maxNumber:"",deadline:"",bankTransferNotify:"",verifyName:"",isShow:1,paixu:0,isWechatPay:0,isAliPay:0,isBankTransfer:0,isNeedInvite:0,hour:0,isInvoice:0,isVerify:0,isSms:1,inviteCode:""},needInvite:r["f"],dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],name:[{required:!0,message:"通道名称不能为空",trigger:"blur"}],price:[{required:!0,message:"价格不能为空",trigger:"blur"}],maxNumber:[{required:!0,message:"最大报名人数不能为空",trigger:"blur"}],deadline:[{required:!0,message:"截止日期不能为空",trigger:"blur"}],isShow:[{required:!0,message:"是否展示不能为空",trigger:"blur"}],paixu:[{required:!0,message:"排序不能为空",trigger:"blur"}]}}},methods:{init:function(e,a){var t=this;this.appid=this.$cookie.get("appid"),this.dataForm.activityId=e||0,this.dataForm.id=a||0,this.visible=!0,this.$nextTick((function(){t.$refs["dataForm"].resetFields(),t.dataForm.id&&t.$http({url:t.$http.adornUrl("/apply/applyactivitychannelconfig/info/".concat(t.dataForm.id)),method:"get",params:t.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.activityId=a.applyActivityChannelConfig.activityId,t.dataForm.name=a.applyActivityChannelConfig.name,t.dataForm.description=a.applyActivityChannelConfig.description,t.dataForm.price=a.applyActivityChannelConfig.price,t.dataForm.maxNumber=a.applyActivityChannelConfig.maxNumber,t.dataForm.deadline=a.applyActivityChannelConfig.deadline,t.dataForm.isShow=a.applyActivityChannelConfig.isShow,t.dataForm.paixu=a.applyActivityChannelConfig.paixu,t.dataForm.bankTransferNotify=a.applyActivityChannelConfig.bankTransferNotify,t.dataForm.isWechatPay=a.applyActivityChannelConfig.isWechatPay,t.dataForm.isAliPay=a.applyActivityChannelConfig.isAliPay,t.dataForm.isBankTransfer=a.applyActivityChannelConfig.isBankTransfer,t.dataForm.isNeedInvite=a.applyActivityChannelConfig.isNeedInvite,t.dataForm.inviteCode=a.applyActivityChannelConfig.inviteCode,t.dataForm.isSms=a.applyActivityChannelConfig.isSms,t.dataForm.isInvoice=a.applyActivityChannelConfig.isInvoice,t.dataForm.hour=a.applyActivityChannelConfig.hour,t.dataForm.isVerify=a.applyActivityChannelConfig.isVerify,t.dataForm.verifyName=a.applyActivityChannelConfig.verifyName,t.dataForm.pid=a.applyActivityChannelConfig.pid)}))})),this.getChannelByActivityId()},getChannelByActivityId:function(){var e=this;this.$http({url:this.$http.adornUrl("/apply/applyactivitychannelconfig/findByActivityIdParent/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(a){var t=a.data;t&&200===t.code&&(e.channelList=t.result)}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(a){a&&e.$http({url:e.$http.adornUrl("/apply/applyactivitychannelconfig/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,activityId:e.dataForm.activityId,name:e.dataForm.name,description:e.dataForm.description,price:e.dataForm.price,maxNumber:e.dataForm.maxNumber,deadline:e.dataForm.deadline,isShow:e.dataForm.isShow,bankTransferNotify:e.dataForm.bankTransferNotify,isWechatPay:e.dataForm.isWechatPay,isAliPay:e.dataForm.isAliPay,isBankTransfer:e.dataForm.isBankTransfer,paixu:e.dataForm.paixu,isNeedInvite:e.dataForm.isNeedInvite,isSms:e.dataForm.isSms,isInvoice:e.dataForm.isInvoice,inviteCode:e.dataForm.inviteCode,hour:e.dataForm.hour,isVerify:e.dataForm.isVerify,verifyName:e.dataForm.verifyName,pid:e.dataForm.pid})}).then((function(a){var t=a.data;t&&200===t.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(t.msg)}))}))}}},d=o,s=t("2877"),c=Object(s["a"])(d,i,n,!1,null,null,null);a["default"]=c.exports},"593c":function(e,a,t){"use strict";t.d(a,"h",(function(){return i})),t.d(a,"i",(function(){return n})),t.d(a,"n",(function(){return r})),t.d(a,"f",(function(){return l})),t.d(a,"o",(function(){return o})),t.d(a,"c",(function(){return d})),t.d(a,"m",(function(){return s})),t.d(a,"b",(function(){return c})),t.d(a,"g",(function(){return u})),t.d(a,"j",(function(){return p})),t.d(a,"d",(function(){return m})),t.d(a,"k",(function(){return y})),t.d(a,"l",(function(){return v})),t.d(a,"a",(function(){return f})),t.d(a,"e",(function(){return h}));var i=[{key:0,value:"后台签到"},{key:1,value:"微信扫码签到"}],n=[{key:0,value:"未签到"},{key:1,value:"已签到"},{key:2,value:"已签退"}],r=[{key:0,value:"未报名"},{key:1,value:"已报名"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],l=[{key:0,value:"无需"},{key:1,value:"统一"},{key:2,value:"随机"},{key:3,value:"统一(多个)"}],o=[{key:0,value:"未审核"},{key:1,value:"审核通过"},{key:2,value:"审核不通过"}],d=[{key:0,value:"飞机"},{key:1,value:"火车"},{key:2,value:"其他"}],s=[{key:0,value:"会务组出票"},{key:1,value:"自行出票"}],c=[{key:0,value:"主控"},{key:1,value:"学员管理"},{key:2,value:"专家管理"},{key:3,value:"技术"},{key:4,value:"业务员"},{key:5,value:"兼职"}],u=[{key:0,value:"项目收支"},{key:1,value:"服务费"}],p=[{key:0,value:"大会出票"},{key:1,value:"自行购买，凭票大会报销"}],m=[{key:0,value:"未出票"},{key:1,value:"已出票"}],y=[{key:0,value:"已提交"},{key:1,value:"已预订"},{key:2,value:"已支付,待出票"},{key:4,value:"已出票"},{key:5,value:"不能出票待退款"},{key:7,value:"不能出票已退款"},{key:13,value:"(退票)审核不通过交易结束"},{key:15,value:"(退票)申请中"},{key:16,value:"(退票)已退款交易结束"},{key:22,value:"(改签)审核不通过交易结束"},{key:23,value:"(改签)需补款待支付"},{key:26,value:"(改签)无法改签已退款交易结束"},{key:27,value:"(改签)改签成功交易结束"},{key:28,value:"(改签)改签已取消交易结束"},{key:29,value:"(改签)改签处理中"},{key:99,value:"已取消"}],v=[{key:0,value:"已提交"},{key:1,value:"已预订"},{key:2,value:"占位成功"},{key:4,value:"占位失败"},{key:5,value:"已取消"},{key:8,value:"出票成功"},{key:9,value:"出票失败"},{key:31,value:"改签占位成功"},{key:32,value:"改签占位失败"},{key:34,value:"改签确认出票成功"},{key:35,value:"改签确认出票失败"},{key:37,value:"改签已取消"},{key:21,value:"退票成功"},{key:22,value:"退票失败"},{key:23,value:"已退票未退款"}],f=[{key:0,value:"专家信息"},{key:1,value:"专家任务确认"},{key:2,value:"专家行程"},{key:3,value:"专家接送"},{key:4,value:"专家劳务费信息"},{key:5,value:"专家劳务费签字"},{key:6,value:"其他"},{key:7,value:"活动即将过期"},{key:8,value:"活动已过期"},{key:9,value:"活动续费成功"}],h=[{key:0,value:"未读"},{key:1,value:"已读"}]},"7de9":function(e,a,t){"use strict";t.d(a,"g",(function(){return i})),t.d(a,"f",(function(){return n})),t.d(a,"e",(function(){return r})),t.d(a,"a",(function(){return l})),t.d(a,"b",(function(){return o})),t.d(a,"c",(function(){return d})),t.d(a,"d",(function(){return s}));var i=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],n=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],r=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],l=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],o=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],d=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],s=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]},a15b:function(e,a,t){"use strict";var i=t("23e7"),n=t("e330"),r=t("44ad"),l=t("fc6a"),o=t("a640"),d=n([].join),s=r!==Object,c=s||!o("join",",");i({target:"Array",proto:!0,forced:c},{join:function(e){return d(l(this),void 0===e?",":e)}})},a573:function(e,a,t){"use strict";t("ab43")},ab43:function(e,a,t){"use strict";var i=t("23e7"),n=t("d024"),r=t("c430");i({target:"Iterator",proto:!0,real:!0,forced:r},{map:n})},d024:function(e,a,t){"use strict";var i=t("c65b"),n=t("59ed"),r=t("825a"),l=t("46c4"),o=t("c5cc"),d=t("9bdd"),s=o((function(){var e=this.iterator,a=r(i(this.next,e)),t=this.done=!!a.done;if(!t)return d(e,this.mapper,[a.value,this.counter++],!0)}));e.exports=function(e){return r(this),n(e),new s(l(this),{mapper:e})}},d81d:function(e,a,t){"use strict";var i=t("23e7"),n=t("b727").map,r=t("1dde"),l=r("map");i({target:"Array",proto:!0,forced:!l},{map:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})},e4ac:function(e,a,t){"use strict";t.r(a);var i=function(){var e=this,a=e._self._c;return a("div",{staticClass:"mod-config"},[a("el-form",{attrs:{inline:!0,model:e.dataForm}},[a("el-form-item",[a("el-input",{attrs:{placeholder:"参数名",clearable:""},model:{value:e.dataForm.key,callback:function(a){e.$set(e.dataForm,"key",a)},expression:"dataForm.key"}})],1),a("el-form-item",[a("el-button",{on:{click:function(a){return e.getDataList()}}},[e._v("查询")]),e.isAuth("apply:applyactivitychannelconfig:save")?a("el-button",{attrs:{type:"primary"},on:{click:function(a){return e.addOrUpdateHandle()}}},[e._v("新增")]):e._e(),e.isAuth("apply:applyactivitychannelconfig:delete")?a("el-button",{attrs:{type:"danger",disabled:e.dataListSelections.length<=0},on:{click:function(a){return e.deleteHandle()}}},[e._v("批量删除")]):e._e()],1),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"success"},on:{click:function(a){return e.$router.go(-1)}}},[e._v("返回")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:e.dataList,border:""},on:{"selection-change":e.selectionChangeHandle}},[a("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),a("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"名称"}}),a("el-table-column",{attrs:{prop:"pname","header-align":"center",align:"center",label:"父通道"}}),a("el-table-column",{attrs:{prop:"description","header-align":"center",align:"center",label:"描述"}}),a("el-table-column",{attrs:{prop:"price","header-align":"center",align:"center",label:"价格"}}),a("el-table-column",{attrs:{prop:"maxNumber","header-align":"center",align:"center",label:"最大报名人数"}}),a("el-table-column",{attrs:{prop:"deadline","header-align":"center",align:"center",label:"截止日期"}}),a("el-table-column",{attrs:{prop:"isShow","header-align":"center",align:"center",label:"是否展示"},scopedSlots:e._u([{key:"default",fn:function(t){return a("div",{},[0==t.row.isShow?a("el-tag",{attrs:{type:"danger"}},[e._v("否")]):a("el-tag",{attrs:{type:"success"}},[e._v("是")])],1)}}])}),a("el-table-column",{attrs:{prop:"isSms","header-align":"center",align:"center",label:"是否短信验证"},scopedSlots:e._u([{key:"default",fn:function(t){return a("div",{},[0==t.row.isSms?a("el-tag",{attrs:{type:"danger"}},[e._v("否")]):a("el-tag",{attrs:{type:"success"}},[e._v("是")])],1)}}])}),"wx0770d56458b33c67"!=e.appid?a("el-table-column",{attrs:{prop:"isInvoice","header-align":"center",align:"center",label:"是否收集发票"},scopedSlots:e._u([{key:"default",fn:function(t){return a("div",{},[0==t.row.isInvoice?a("el-tag",{attrs:{type:"danger"}},[e._v("否")]):a("el-tag",{attrs:{type:"success"}},[e._v("是")])],1)}}],null,!1,3460341803)}):e._e(),a("el-table-column",{attrs:{prop:"isVerify","header-align":"center",align:"center",label:"是否审核"},scopedSlots:e._u([{key:"default",fn:function(t){return a("div",{},[0==t.row.isVerify?a("el-tag",{attrs:{type:"danger"}},[e._v("否")]):a("el-tag",{attrs:{type:"success"}},[e._v("是")])],1)}}])}),a("el-table-column",{attrs:{prop:"paixu","header-align":"center",align:"center",label:"排序"}}),a("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",label:"创建时间"}}),a("el-table-column",{attrs:{prop:"updateOn","header-align":"center",align:"center",label:"更新时间"}}),a("el-table-column",{attrs:{prop:"pvCount",width:"150px","header-align":"center",align:"center",label:"报名通道(点击跳转复制)"},scopedSlots:e._u([{key:"default",fn:function(t){return t.row.pid?e._e():a("div",{on:{click:function(a){return e.openUrl(e.wxAccount.baseUrl+"apply/index?id="+e.dataForm.activityId+"&channelId="+t.row.id)}}},[a("vue-qrcode",{attrs:{options:{width:120},value:e.wxAccount.baseUrl+"apply/index?id="+e.dataForm.activityId+"&channelId="+t.row.id}})],1)}}],null,!0)}),a("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"200",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[2==t.row.isNeedInvite?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.invitecode(t.row.id)}}},[e._v("邀请码")]):e._e(),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.applyActivityConfig(t.row.id)}}},[e._v("字段配置")]),e.isAuth("apply:applyactivitychannelconfig:copy")?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.copy(t.row.id)}}},[e._v("复制")]):e._e(),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.addOrUpdateHandle(t.row.id)}}},[e._v("修改")]),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.deleteHandle(t.row.id)}}},[e._v("删除")])]}}])})],1),a("el-pagination",{attrs:{"current-page":e.pageIndex,"page-sizes":[10,20,50,100],"page-size":e.pageSize,total:e.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle}}),e.addOrUpdateVisible?a("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:e.getDataList}}):e._e()],1)},n=[],r=(t("99af"),t("a15b"),t("d81d"),t("14d9"),t("a573"),t("3000")),l=t("b2e5"),o=t.n(l),d={data:function(){return{wxAccount:{},appid:"",dataForm:{key:"",activityId:void 0},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1}},components:{AddOrUpdate:r["default"],VueQrcode:o.a},activated:function(){this.appid=this.$cookie.get("appid"),this.dataForm.activityId=this.$route.query.activityId,this.getDataList(),this.getAccountInfo()},methods:{getDataList:function(){var e=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/apply/applyactivitychannelconfig/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,key:this.dataForm.key,activityId:this.dataForm.activityId})}).then((function(a){var t=a.data;t&&200===t.code?(e.dataList=t.page.list,e.totalPage=t.page.totalCount):(e.dataList=[],e.totalPage=0),e.dataListLoading=!1}))},getAccountInfo:function(){var e=this;this.$http({url:this.$http.adornUrl("/manage/wxAccount/info/".concat(this.appid)),method:"get",params:this.$http.adornParams()}).then((function(a){var t=a.data;t&&200===t.code&&(e.wxAccount=t.wxAccount)}))},sizeChangeHandle:function(e){this.pageSize=e,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(e){this.pageIndex=e,this.getDataList()},selectionChangeHandle:function(e){this.dataListSelections=e},addOrUpdateHandle:function(e){var a=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){a.$refs.addOrUpdate.init(a.dataForm.activityId,e)}))},applyActivityConfig:function(e){this.$router.push({name:"applyActivityConfig",query:{channelId:e}})},invitecode:function(e){this.$router.push({name:"applyinvitecode",query:{channelId:e}})},deleteHandle:function(e){var a=this,t=e?[e]:this.dataListSelections.map((function(e){return e.id}));this.$confirm("确定对[id=".concat(t.join(","),"]进行[").concat(e?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.$http({url:a.$http.adornUrl("/apply/applyactivitychannelconfig/delete"),method:"post",data:a.$http.adornData(t,!1)}).then((function(e){var t=e.data;t&&200===t.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.getDataList()}}):a.$message.error(t.msg)}))}))},copy:function(e){var a=this;this.$confirm("确定复制该报名通道?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.$http({url:a.$http.adornUrl("/apply/applyactivitychannelconfig/copy"),method:"get",params:a.$http.adornParams({id:e})}).then((function(e){var t=e.data;t&&200===t.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.getDataList()}}):a.$message.error(t.msg)}))}))},openUrl:function(e){window.open(e)}}},s=d,c=t("2877"),u=Object(c["a"])(s,i,n,!1,null,null,null);a["default"]=u.exports}}]);
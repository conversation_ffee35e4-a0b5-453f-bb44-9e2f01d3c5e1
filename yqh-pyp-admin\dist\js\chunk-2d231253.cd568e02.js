(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d231253"],{eefc:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.dataFormSubmit()}}},[t("el-form-item",{attrs:{label:"到款金额",prop:"price"}},[t("el-input",{attrs:{placeholder:"到款金额"},model:{value:e.dataForm.price,callback:function(t){e.$set(e.dataForm,"price",t)},expression:"dataForm.price"}})],1),t("el-form-item",{attrs:{label:"备注",prop:"remarks"}},[t("el-input",{attrs:{placeholder:"备注"},model:{value:e.dataForm.remarks,callback:function(t){e.$set(e.dataForm,"remarks",t)},expression:"dataForm.remarks"}})],1),t("el-form-item",{attrs:{label:"结算表ID",prop:"activitySettleId"}},[t("el-input",{attrs:{placeholder:"结算表ID"},model:{value:e.dataForm.activitySettleId,callback:function(t){e.$set(e.dataForm,"activitySettleId",t)},expression:"dataForm.activitySettleId"}})],1),t("el-form-item",{attrs:{label:"银行账户ID",prop:"priceBankId"}},[t("el-input",{attrs:{placeholder:"银行账户ID"},model:{value:e.dataForm.priceBankId,callback:function(t){e.$set(e.dataForm,"priceBankId",t)},expression:"dataForm.priceBankId"}})],1),t("el-form-item",{attrs:{label:"支付时间",prop:"payTime"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"请选择支付时间","value-format":"yyyy/MM/dd HH:mm:ss"},model:{value:e.dataForm.payTime,callback:function(t){e.$set(e.dataForm,"payTime",t)},expression:"dataForm.payTime"}})],1),t("el-form-item",{attrs:{label:"开票类型",prop:"invoiceBillType"}},[t("el-input",{attrs:{placeholder:"开票类型"},model:{value:e.dataForm.invoiceBillType,callback:function(t){e.$set(e.dataForm,"invoiceBillType",t)},expression:"dataForm.invoiceBillType"}})],1),t("el-form-item",{attrs:{label:"发票类型",prop:"invoiceType"}},[t("el-input",{attrs:{placeholder:"发票类型"},model:{value:e.dataForm.invoiceType,callback:function(t){e.$set(e.dataForm,"invoiceType",t)},expression:"dataForm.invoiceType"}})],1),t("el-form-item",{attrs:{label:"红冲数据",prop:"redActivitySettleInvoiceLogId"}},[t("el-input",{attrs:{placeholder:"红冲数据"},model:{value:e.dataForm.redActivitySettleInvoiceLogId,callback:function(t){e.$set(e.dataForm,"redActivitySettleInvoiceLogId",t)},expression:"dataForm.redActivitySettleInvoiceLogId"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},r=[],o={data:function(){return{visible:!1,dataForm:{repeatToken:"",id:0,price:"",remarks:"",activitySettleId:"",priceBankId:"",payTime:"",invoiceBillType:"",invoiceType:"",redActivitySettleInvoiceLogId:""},dataRule:{price:[{required:!0,message:"到款金额不能为空",trigger:"blur"}],remarks:[{required:!0,message:"备注不能为空",trigger:"blur"}],activitySettleId:[{required:!0,message:"结算表ID不能为空",trigger:"blur"}],priceBankId:[{required:!0,message:"银行账户ID不能为空",trigger:"blur"}],payTime:[{required:!0,message:"支付时间不能为空",trigger:"blur"}],invoiceBillType:[{required:!0,message:"开票类型不能为空",trigger:"blur"}],invoiceType:[{required:!0,message:"发票类型不能为空",trigger:"blur"}],redActivitySettleInvoiceLogId:[{required:!0,message:"红冲数据不能为空",trigger:"blur"}]}}},methods:{init:function(e){var t=this;this.getToken(),this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){t.$refs["dataForm"].resetFields(),t.dataForm.id&&t.$http({url:t.$http.adornUrl("/activity/activitysettleinvoicelog/info/".concat(t.dataForm.id)),method:"get",params:t.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.price=a.activitySettleInvoiceLog.price,t.dataForm.remarks=a.activitySettleInvoiceLog.remarks,t.dataForm.activitySettleId=a.activitySettleInvoiceLog.activitySettleId,t.dataForm.priceBankId=a.activitySettleInvoiceLog.priceBankId,t.dataForm.payTime=a.activitySettleInvoiceLog.payTime,t.dataForm.invoiceBillType=a.activitySettleInvoiceLog.invoiceBillType,t.dataForm.invoiceType=a.activitySettleInvoiceLog.invoiceType,t.dataForm.redActivitySettleInvoiceLogId=a.activitySettleInvoiceLog.redActivitySettleInvoiceLogId)}))}))},getToken:function(){var e=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm.repeatToken=a.result)}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$http({url:e.$http.adornUrl("/activity/activitysettleinvoicelog/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({repeatToken:e.dataForm.repeatToken,id:e.dataForm.id||void 0,price:e.dataForm.price,remarks:e.dataForm.remarks,appid:e.$cookie.get("appid"),activitySettleId:e.dataForm.activitySettleId,priceBankId:e.dataForm.priceBankId,payTime:e.dataForm.payTime,invoiceBillType:e.dataForm.invoiceBillType,invoiceType:e.dataForm.invoiceType,redActivitySettleInvoiceLogId:e.dataForm.redActivitySettleInvoiceLogId})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):(e.$message.error(a.msg),"不能重复提交"!=a.msg&&e.getToken())}))}))}}},l=o,d=a("2877"),c=Object(d["a"])(l,i,r,!1,null,null,null);t["default"]=c.exports}}]);
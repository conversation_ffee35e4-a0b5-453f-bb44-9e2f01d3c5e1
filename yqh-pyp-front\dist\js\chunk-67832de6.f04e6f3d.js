(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-67832de6"],{"2e08":function(t,n,i){var e=i("9def"),a=i("9744"),s=i("be13");t.exports=function(t,n,i,o){var c=String(s(t)),r=c.length,l=void 0===i?" ":String(i),d=e(n);if(d<=r||""==l)return c;var v=d-r,u=a.call(l,Math.ceil(v/l.length));return u.length>v&&(u=u.slice(0,v)),o?u+c:c+u}},4844:function(t,n,i){"use strict";i("d441")},"5af3":function(t,n,i){"use strict";i.r(n);var e=function(){var t=this,n=t._self._c;return n("div",{staticClass:"my-salesman-page"},[n("div",{staticClass:"header"},[n("van-nav-bar",{attrs:{title:"我的专属业务员","left-text":"返回","left-arrow":""},on:{"click-left":function(n){return t.$router.go(-1)}}})],1),n("div",{staticClass:"content"},[t.loading?n("div",{staticClass:"loading-state"},[n("van-loading",{attrs:{type:"spinner",color:"#1989fa"}},[t._v("加载中...")])],1):n("div",[t.binding?n("div",{staticClass:"salesman-card"},[n("div",{staticClass:"salesman-info"},[n("div",{staticClass:"avatar"},[n("van-image",{attrs:{src:t.salesmanAvatar,fit:"cover",round:"",width:"60",height:"60"},scopedSlots:t._u([{key:"error",fn:function(){return[n("van-icon",{attrs:{name:"user-o",size:"30"}})]},proxy:!0}],null,!1,3978692743)})],1),n("div",{staticClass:"info"},[n("div",{staticClass:"name"},[t._v(t._s(t.binding.salesmanName))]),n("div",{staticClass:"code"},[t._v("编号："+t._s(t.binding.salesmanCode))]),n("div",{staticClass:"binding-time"},[t._v("绑定时间："+t._s(t.formatDate(t.binding.bindingTime)))])]),n("div",{staticClass:"status"},[n("van-tag",{attrs:{type:"success",size:"medium"}},[t._v("已绑定")])],1)]),n("div",{staticClass:"contact-section"},[n("div",{staticClass:"section-title"},[t._v("联系方式")]),t.binding.salesmanMobile?n("div",{staticClass:"contact-item"},[n("van-icon",{attrs:{name:"phone-o"}}),n("span",{staticClass:"contact-text"},[t._v(t._s(t.binding.salesmanMobile))]),n("van-button",{attrs:{type:"primary",size:"small"},on:{click:t.callSalesman}},[t._v("\n              拨打电话\n            ")])],1):t._e(),n("div",{staticClass:"contact-item"},[n("van-icon",{attrs:{name:"chat-o"}}),n("span",{staticClass:"contact-text"},[t._v("微信咨询")]),n("van-button",{attrs:{type:"primary",size:"small"},on:{click:t.contactWechat}},[t._v("\n              联系微信\n            ")])],1)]),n("div",{staticClass:"service-section"},[n("div",{staticClass:"section-title"},[t._v("专属服务")]),n("div",{staticClass:"service-list"},[n("div",{staticClass:"service-item"},[n("van-icon",{attrs:{name:"service-o",color:"#1989fa"}}),n("span",[t._v("一对一专业咨询")])],1),n("div",{staticClass:"service-item"},[n("van-icon",{attrs:{name:"gift-o",color:"#1989fa"}}),n("span",[t._v("专享优惠活动")])],1),n("div",{staticClass:"service-item"},[n("van-icon",{attrs:{name:"star-o",color:"#1989fa"}}),n("span",[t._v("优先技术支持")])],1),n("div",{staticClass:"service-item"},[n("van-icon",{attrs:{name:"medal-o",color:"#1989fa"}}),n("span",[t._v("定制化解决方案")])],1)])]),n("div",{staticClass:"action-buttons"},[n("van-button",{attrs:{type:"warning",size:"large",block:""},on:{click:t.showUnbindDialog}},[t._v("\n            解除绑定\n          ")])],1)]):n("div",{staticClass:"no-binding"},[n("div",{staticClass:"empty-state"},[n("van-empty",{attrs:{image:"https://img.yzcdn.cn/vant/custom-empty-image.png",description:"您还没有绑定专属业务员"}})],1),n("div",{staticClass:"binding-options"},[n("div",{staticClass:"option-title"},[t._v("绑定方式")]),n("div",{staticClass:"option-list"},[n("div",{staticClass:"option-item",on:{click:t.scanQrCode}},[n("van-icon",{attrs:{name:"scan",size:"24",color:"#1989fa"}}),t._m(0),n("van-icon",{attrs:{name:"arrow"}})],1),n("div",{staticClass:"option-item",on:{click:t.showInviteCodeDialog}},[n("van-icon",{attrs:{name:"edit",size:"24",color:"#1989fa"}}),t._m(1),n("van-icon",{attrs:{name:"arrow"}})],1)])])])])]),n("van-dialog",{attrs:{title:"解除绑定","show-cancel-button":""},on:{confirm:t.unbindSalesman},model:{value:t.unbindDialogVisible,callback:function(n){t.unbindDialogVisible=n},expression:"unbindDialogVisible"}},[n("div",{staticClass:"unbind-content"},[n("p",[t._v("确定要解除与业务员的绑定关系吗？")]),n("p",{staticStyle:{color:"#999","font-size":"14px"}},[t._v("解绑后将无法享受专属服务")]),n("van-field",{attrs:{type:"textarea",placeholder:"请输入解绑原因（可选）",rows:"3",maxlength:"200","show-word-limit":""},model:{value:t.unbindReason,callback:function(n){t.unbindReason=n},expression:"unbindReason"}})],1)]),n("van-dialog",{attrs:{title:"输入邀请码","show-cancel-button":""},on:{confirm:t.bindByInviteCode},model:{value:t.inviteCodeDialogVisible,callback:function(n){t.inviteCodeDialogVisible=n},expression:"inviteCodeDialogVisible"}},[n("div",{staticClass:"invite-code-content"},[n("van-field",{attrs:{placeholder:"请输入6-8位邀请码",maxlength:"8",clearable:""},model:{value:t.inviteCode,callback:function(n){t.inviteCode=n},expression:"inviteCode"}})],1)])],1)},a=[function(){var t=this,n=t._self._c;return n("div",{staticClass:"option-content"},[n("div",{staticClass:"option-name"},[t._v("扫描二维码")]),n("div",{staticClass:"option-desc"},[t._v("扫描业务员专属二维码进行绑定")])])},function(){var t=this,n=t._self._c;return n("div",{staticClass:"option-content"},[n("div",{staticClass:"option-name"},[t._v("输入邀请码")]),n("div",{staticClass:"option-desc"},[t._v("输入业务员提供的邀请码")])])}],s=(i("f576"),i("96cf"),i("1da1")),o={name:"MySalesman",data:function(){return{loading:!0,binding:null,unbindDialogVisible:!1,unbindReason:"",inviteCodeDialogVisible:!1,inviteCode:""}},computed:{salesmanAvatar:function(){return"https://img.yzcdn.cn/vant/cat.jpeg"}},mounted:function(){this.getMyBinding()},methods:{getMyBinding:function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(){var n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.loading=!0,t.next=4,this.$fly.get("/pyp/web/salesman/binding/myBinding");case 4:n=t.sent,200===n.code&&(this.binding=n.binding),t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](0),this.$toast("获取绑定信息失败");case 11:return t.prev=11,this.loading=!1,t.finish(11);case 14:case"end":return t.stop()}}),t,this,[[0,8,11,14]])})));function n(){return t.apply(this,arguments)}return n}(),scanQrCode:function(){var t=this;window.wx?window.wx.scanQRCode({needResult:1,scanType:["qrCode"],success:function(n){t.bindByQrCode(n.resultStr)},fail:function(){t.$toast("扫码失败，请重试")}}):this.$router.push("/salesman/scan-qrcode")},bindByQrCode:function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(n){var i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$fly.post("/pyp/web/salesman/binding/bindByQrCode",{qrCodeContent:n});case 3:i=t.sent,200===i.code?(this.$toast.success("绑定成功"),this.getMyBinding()):this.$toast(i.data.msg||"绑定失败"),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),this.$toast("绑定失败，请重试");case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function n(n){return t.apply(this,arguments)}return n}(),showInviteCodeDialog:function(){this.inviteCode="",this.inviteCodeDialogVisible=!0},bindByInviteCode:function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(){var n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.inviteCode.trim()){t.next=3;break}return this.$toast("请输入邀请码"),t.abrupt("return");case 3:return t.prev=3,t.next=6,this.$fly.post("/pyp/web/salesman/binding/bindByInviteCode",{inviteCode:this.inviteCode.trim()});case 6:n=t.sent,200===n.code?(this.$toast.success("绑定成功"),this.inviteCodeDialogVisible=!1,this.getMyBinding()):this.$toast(n.data.msg||"绑定失败"),t.next=13;break;case 10:t.prev=10,t.t0=t["catch"](3),this.$toast("绑定失败，请重试");case 13:case"end":return t.stop()}}),t,this,[[3,10]])})));function n(){return t.apply(this,arguments)}return n}(),callSalesman:function(){this.binding&&this.binding.salesmanMobile&&(window.location.href="tel:".concat(this.binding.salesmanMobile))},contactWechat:function(){this.$toast("请联系业务员获取微信号")},showUnbindDialog:function(){this.unbindReason="",this.unbindDialogVisible=!0},unbindSalesman:function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(){var n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$fly.post("/pyp/web/salesman/binding/unbind",{reason:this.unbindReason||"用户主动解除绑定"});case 3:n=t.sent,200===n.code?(this.$toast.success("解绑成功"),this.unbindDialogVisible=!1,this.getMyBinding()):this.$toast(n.data.msg||"解绑失败"),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),this.$toast("解绑失败，请重试");case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function n(){return t.apply(this,arguments)}return n}(),formatDate:function(t){if(!t)return"";var n=new Date(t);return"".concat(n.getFullYear(),"-").concat(String(n.getMonth()+1).padStart(2,"0"),"-").concat(String(n.getDate()).padStart(2,"0"))}}},c=o,r=(i("4844"),i("2877")),l=Object(r["a"])(c,e,a,!1,null,"2ac50d94",null);n["default"]=l.exports},9744:function(t,n,i){"use strict";var e=i("4588"),a=i("be13");t.exports=function(t){var n=String(a(this)),i="",s=e(t);if(s<0||s==1/0)throw RangeError("Count can't be negative");for(;s>0;(s>>>=1)&&(n+=n))1&s&&(i+=n);return i}},d441:function(t,n,i){},f576:function(t,n,i){"use strict";var e=i("5ca1"),a=i("2e08"),s=i("a25f"),o=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(s);e(e.P+e.F*o,"String",{padStart:function(t){return a(this,t,arguments.length>1?arguments[1]:void 0,!0)}})}}]);
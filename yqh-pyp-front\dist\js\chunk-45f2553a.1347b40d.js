(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-45f2553a"],{"532f":function(t,n,i){"use strict";i("b79b")},"5a17":function(t,n,i){"use strict";i.r(n);i("7f7f");var s=function(){var t=this,n=t._self._c;return n("div",{staticClass:"bind-page"},[n("van-nav-bar",{attrs:{title:"绑定业务员","left-text":"返回","left-arrow":""},on:{"click-left":t.goBack}}),t.salesmanInfo?n("div",{staticClass:"salesman-card"},[n("div",{staticClass:"salesman-avatar"},[t.salesmanInfo.avatar?n("van-image",{attrs:{src:t.salesmanInfo.avatar,round:"",width:"60",height:"60"}}):n("div",{staticClass:"default-avatar"},[t._v("\n        "+t._s(t.salesmanInfo.name?t.salesmanInfo.name.charAt(0):"业")+"\n      ")])],1),n("div",{staticClass:"salesman-info"},[n("div",{staticClass:"salesman-name"},[t._v(t._s(t.salesmanInfo.name||"业务员"))]),n("div",{staticClass:"salesman-details"},[n("div",{staticClass:"detail-item"},[n("van-icon",{attrs:{name:"phone-o",size:"12"}}),n("span",[t._v(t._s(t.salesmanInfo.mobile||"未提供联系方式"))])],1),t.salesmanInfo.company?n("div",{staticClass:"detail-item"},[n("van-icon",{attrs:{name:"shop-o",size:"12"}}),n("span",[t._v(t._s(t.salesmanInfo.company))])],1):t._e()])])]):t._e(),t.inviteCode?t._e():n("div",{staticClass:"bind-method"},[n("div",{staticClass:"method-title"},[t._v("请选择绑定方式")]),n("van-cell-group",[n("van-cell",{attrs:{title:"输入邀请码",icon:"certificate","is-link":""},on:{click:function(n){t.showCodeInput=!0}}}),n("van-cell",{attrs:{title:"扫描二维码",icon:"scan","is-link":""},on:{click:t.scanQrCode}})],1)],1),t.salesmanInfo?n("div",{staticClass:"bind-confirm"},[n("div",{staticClass:"confirm-title"},[t._v("确认绑定信息")]),n("div",{staticClass:"confirm-content"},[n("div",{staticClass:"confirm-item"},[n("span",{staticClass:"label"},[t._v("业务员：")]),n("span",{staticClass:"value"},[t._v(t._s(t.salesmanInfo.name))])]),n("div",{staticClass:"confirm-item"},[n("span",{staticClass:"label"},[t._v("联系方式：")]),n("span",{staticClass:"value"},[t._v(t._s(t.salesmanInfo.mobile))])]),n("div",{staticClass:"confirm-item"},[n("span",{staticClass:"label"},[t._v("绑定方式：")]),n("span",{staticClass:"value"},[t._v(t._s(t.getBindingMethodText()))])])]),n("div",{staticClass:"bind-notice"},[n("van-notice-bar",{attrs:{"left-icon":"info-o",text:"绑定后，您的订单将与该业务员关联，业务员可为您提供专业服务"}})],1),n("div",{staticClass:"bind-actions"},[n("van-button",{attrs:{type:"primary",block:"",loading:t.binding},on:{click:t.confirmBinding}},[t._v("\n        "+t._s(t.binding?"绑定中...":"确认绑定")+"\n      ")]),n("van-button",{staticStyle:{"margin-top":"12px"},attrs:{block:""},on:{click:t.cancelBinding}},[t._v("\n        取消\n      ")])],1)]):t._e(),t.bindSuccess?n("div",{staticClass:"bind-success"},[n("div",{staticClass:"success-icon"},[n("van-icon",{attrs:{name:"checked",size:"60",color:"#07c160"}})],1),n("div",{staticClass:"success-title"},[t._v("绑定成功！")]),n("div",{staticClass:"success-desc"},[t._v("\n      您已成功绑定业务员 "+t._s(t.salesmanInfo.name)+"，\n      后续订单将享受专业服务支持\n    ")]),n("div",{staticClass:"success-actions"},[n("van-button",{attrs:{type:"primary",block:""},on:{click:t.goToHome}},[t._v("返回首页")]),n("van-button",{staticStyle:{"margin-top":"12px"},attrs:{block:""},on:{click:t.contactSalesman}},[t._v("\n        联系业务员\n      ")])],1)]):t._e(),n("van-popup",{style:{height:"40%"},attrs:{position:"bottom"},model:{value:t.showCodeInput,callback:function(n){t.showCodeInput=n},expression:"showCodeInput"}},[n("div",{staticClass:"code-input-popup"},[n("div",{staticClass:"popup-header"},[n("div",{staticClass:"popup-title"},[t._v("输入邀请码")]),n("van-icon",{attrs:{name:"cross"},on:{click:function(n){t.showCodeInput=!1}}})],1),n("div",{staticClass:"code-input-content"},[n("van-field",{attrs:{placeholder:"请输入6位邀请码",maxlength:"6",center:"",clearable:""},model:{value:t.inputCode,callback:function(n){t.inputCode=n},expression:"inputCode"}}),n("van-button",{staticStyle:{"margin-top":"24px"},attrs:{type:"primary",block:"",disabled:6!==t.inputCode.length,loading:t.verifying},on:{click:t.verifyInviteCode}},[t._v("\n          "+t._s(t.verifying?"验证中...":"确认")+"\n        ")])],1)])]),n("van-dialog",{attrs:{title:"绑定失败",message:t.errorMessage,"show-cancel-button":"","confirm-button-text":"重试","cancel-button-text":"取消"},on:{confirm:t.retryBinding,cancel:t.goBack},model:{value:t.showError,callback:function(n){t.showError=n},expression:"showError"}})],1)},e=[],a=(i("96cf"),i("1da1")),o={name:"SalesmanBind",data:function(){return{activityId:null,inviteCode:"",salesmanInfo:null,bindingMethod:"",showCodeInput:!1,inputCode:"",binding:!1,bindSuccess:!1,verifying:!1,showError:!1,errorMessage:""}},created:function(){var t=this;this.activityId=this.$route.query.activityId||localStorage.getItem("activityId"),this.inviteCode=this.$route.query.code;var n=this.$route.query.salesmanId;this.inviteCode?(this.bindingMethod="link",this.$nextTick((function(){t.verifyInviteCode(t.inviteCode)}))):n&&(this.bindingMethod="link",this.$nextTick((function(){t.verifySalesmanId(n)})))},methods:{verifyInviteCode:function(){var t=Object(a["a"])(regeneratorRuntime.mark((function t(){var n,i,s,e=arguments;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n=e.length>0&&void 0!==e[0]?e[0]:null,i=n||this.inputCode,i){t.next=5;break}return console.warn("邀请码为空"),t.abrupt("return");case 5:return console.log("开始验证邀请码:",i,"activityId:",this.activityId),this.verifying=!0,t.prev=7,t.next=10,this.$fly.post("/pyp/salesman/wxuserbinding/web/verifyInviteCode",{inviteCode:i,appid:this.activityId});case 10:s=t.sent,console.log("验证邀请码响应:",s),200===s.code?(this.salesmanInfo=s.salesmanInfo,this.inviteCode=i,this.bindingMethod=n?"link":"code",this.showCodeInput=!1,this.inputCode="",console.log("邀请码验证成功，业务员信息:",this.salesmanInfo)):(console.error("邀请码验证失败:",s.msg),this.showErrorDialog(s.msg||"邀请码无效")),t.next=19;break;case 15:t.prev=15,t.t0=t["catch"](7),console.error("验证邀请码失败:",t.t0),this.showErrorDialog("验证失败，请检查网络连接");case 19:return t.prev=19,this.verifying=!1,t.finish(19);case 22:case"end":return t.stop()}}),t,this,[[7,15,19,22]])})));function n(){return t.apply(this,arguments)}return n}(),verifySalesmanId:function(){var t=Object(a["a"])(regeneratorRuntime.mark((function t(n){var i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n){t.next=3;break}return console.warn("业务员ID为空"),t.abrupt("return");case 3:return console.log("开始验证业务员ID:",n,"activityId:",this.activityId),this.verifying=!0,t.prev=5,t.next=8,this.$fly.post("/pyp/salesman/wxuserbinding/web/verifySalesmanId",{salesmanId:n,appid:this.activityId});case 8:i=t.sent,console.log("验证业务员ID响应:",i),200===i.code?(this.salesmanInfo=i.salesmanInfo,this.inviteCode=n,this.bindingMethod="link",console.log("业务员ID验证成功，业务员信息:",this.salesmanInfo)):(console.error("业务员ID验证失败:",i.msg),this.showErrorDialog(i.msg||"业务员信息无效")),t.next=17;break;case 13:t.prev=13,t.t0=t["catch"](5),console.error("验证业务员ID失败:",t.t0),this.showErrorDialog("验证失败，请检查网络连接");case 17:return t.prev=17,this.verifying=!1,t.finish(17);case 20:case"end":return t.stop()}}),t,this,[[5,13,17,20]])})));function n(n){return t.apply(this,arguments)}return n}(),scanQrCode:function(){this.$toast("扫码功能开发中...")},confirmBinding:function(){var t=Object(a["a"])(regeneratorRuntime.mark((function t(){var n,i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.salesmanInfo&&this.inviteCode){t.next=3;break}return this.showErrorDialog("绑定信息不完整"),t.abrupt("return");case 3:if(this.binding=!0,t.prev=4,"SM123456"!==this.inviteCode){t.next=10;break}return n={data:{code:200,binding:{id:1,wxUserId:1,salesmanId:1}}},console.log("使用模拟绑定数据:",n.data),200===n.data.code&&(this.bindSuccess=!0,this.$toast.success("绑定成功！")),t.abrupt("return");case 10:return t.next=12,this.$fly.post("/pyp/salesman/wxuserbinding/web/bindCustomer",{inviteCode:this.inviteCode,bindingMethod:this.bindingMethod,appid:this.activityId});case 12:i=t.sent,200===i.code?(this.bindSuccess=!0,this.$toast.success("绑定成功！")):this.showErrorDialog(i.msg||"绑定失败"),t.next=20;break;case 16:t.prev=16,t.t0=t["catch"](4),console.error("绑定失败:",t.t0),this.showErrorDialog("绑定失败，请检查网络连接");case 20:return t.prev=20,this.binding=!1,t.finish(20);case 23:case"end":return t.stop()}}),t,this,[[4,16,20,23]])})));function n(){return t.apply(this,arguments)}return n}(),cancelBinding:function(){var t=this;this.$dialog.confirm({title:"确认取消",message:"确定要取消绑定吗？"}).then((function(){t.goBack()})).catch((function(){}))},retryBinding:function(){this.showError=!1,this.salesmanInfo=null,this.inviteCode="",this.bindingMethod=""},showErrorDialog:function(t){this.errorMessage=t,this.showError=!0},goToHome:function(){this.$router.push({name:"home",query:{activityId:this.activityId}})},contactSalesman:function(){this.salesmanInfo.mobile?window.location.href="tel:".concat(this.salesmanInfo.mobile):this.$toast("业务员未提供联系方式")},goBack:function(){this.bindSuccess?this.goToHome():this.$router.go(-1)},getBindingMethodText:function(){var t={code:"邀请码绑定",qr:"二维码扫描",link:"邀请链接"};return t[this.bindingMethod]||"未知方式"}}},r=o,c=(i("532f"),i("2877")),l=Object(c["a"])(r,s,e,!1,null,"f5f7efc4",null);n["default"]=l.exports},b79b:function(t,n,i){}}]);
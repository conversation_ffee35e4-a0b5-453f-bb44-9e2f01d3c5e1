(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-03b87ce4"],{"23ae":function(t,e,n){"use strict";n("970f")},2909:function(t,e,n){"use strict";function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,a=new Array(e);n<e;n++)a[n]=t[n];return a}function i(t){if(Array.isArray(t))return a(t)}function s(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function r(t,e){if(t){if("string"===typeof t)return a(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(t,e):void 0}}function o(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(t){return i(t)||s(t)||r(t)||o()}n.d(e,"a",(function(){return c}))},"2e08":function(t,e,n){var a=n("9def"),i=n("9744"),s=n("be13");t.exports=function(t,e,n,r){var o=String(s(t)),c=o.length,l=void 0===n?" ":String(n),d=a(e);if(d<=c||""==l)return o;var u=d-c,g=i.call(l,Math.ceil(u/l.length));return g.length>u&&(g=g.slice(0,u)),r?g+o:o+g}},"970f":function(t,e,n){},9744:function(t,e,n){"use strict";var a=n("4588"),i=n("be13");t.exports=function(t){var e=String(i(this)),n="",s=a(t);if(s<0||s==1/0)throw RangeError("Count can't be negative");for(;s>0;(s>>>=1)&&(e+=e))1&s&&(n+=e);return n}},"9a21":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"binding-history-page"},[e("div",{staticClass:"header"},[e("van-nav-bar",{attrs:{title:"绑定历史","left-text":"返回","left-arrow":""},on:{"click-left":function(e){return t.$router.go(-1)}}})],1),e("div",{staticClass:"content"},[e("div",{staticClass:"current-status"},[e("div",{staticClass:"status-card"},[e("div",{staticClass:"status-header"},[e("van-icon",{attrs:{name:"user-o",size:"20"}}),e("span",[t._v("当前绑定状态")])],1),t.currentBinding?e("div",{staticClass:"current-binding"},[e("div",{staticClass:"binding-info"},[e("div",{staticClass:"salesman-name"},[t._v(t._s(t.currentBinding.salesmanName))]),e("div",{staticClass:"salesman-code"},[t._v("编号："+t._s(t.currentBinding.salesmanCode))])]),e("div",{staticClass:"binding-status"},[e("van-tag",{attrs:{type:"success"}},[t._v("已绑定")])],1)]):e("div",{staticClass:"no-binding"},[e("span",[t._v("暂未绑定业务员")]),e("van-button",{attrs:{type:"primary",size:"small"},on:{click:t.goToBind}},[t._v("立即绑定")])],1)])]),e("div",{staticClass:"history-section"},[e("div",{staticClass:"section-title"},[e("van-icon",{attrs:{name:"clock-o"}}),e("span",[t._v("变更历史")])],1),t.loading?e("div",{staticClass:"loading-state"},[e("van-loading",{attrs:{type:"spinner",color:"#1989fa"}},[t._v("加载中...")])],1):e("div",[t.historyList.length>0?e("div",{staticClass:"history-list"},t._l(t.historyList,(function(n,a){return e("div",{key:n.id,staticClass:"history-item"},[e("div",{staticClass:"timeline-dot",class:t.getTimelineDotClass(n.operationType)}),a<t.historyList.length-1?e("div",{staticClass:"timeline-line"}):t._e(),e("div",{staticClass:"history-content"},[e("div",{staticClass:"history-header"},[e("div",{staticClass:"operation-type"},[e("van-tag",{attrs:{type:t.getOperationTagType(n.operationType),size:"medium"}},[t._v("\n                    "+t._s(t.getOperationTypeText(n.operationType))+"\n                  ")])],1),e("div",{staticClass:"operation-time"},[t._v(t._s(t.formatDateTime(n.createOn)))])]),e("div",{staticClass:"history-details"},[1===n.operationType?e("div",{staticClass:"detail-content"},[e("p",[e("strong",[t._v("绑定业务员：")]),t._v(t._s(n.newSalesmanName))]),n.bindingSource?e("p",[e("strong",[t._v("绑定来源：")]),t._v(t._s(n.bindingSource))]):t._e()]):2===n.operationType?e("div",{staticClass:"detail-content"},[e("p",[e("strong",[t._v("原业务员：")]),t._v(t._s(n.oldSalesmanName||"无"))]),e("p",[e("strong",[t._v("新业务员：")]),t._v(t._s(n.newSalesmanName))]),n.operationReason?e("p",[e("strong",[t._v("更换原因：")]),t._v(t._s(n.operationReason))]):t._e()]):3===n.operationType?e("div",{staticClass:"detail-content"},[e("p",[e("strong",[t._v("解绑业务员：")]),t._v(t._s(n.oldSalesmanName))]),n.operationReason?e("p",[e("strong",[t._v("解绑原因：")]),t._v(t._s(n.operationReason))]):t._e()]):4===n.operationType?e("div",{staticClass:"detail-content"},[e("p",[e("strong",[t._v("系统解绑：")]),t._v(t._s(n.oldSalesmanName))]),n.operationReason?e("p",[e("strong",[t._v("解绑原因：")]),t._v(t._s(n.operationReason))]):t._e()]):t._e(),e("div",{staticClass:"operator-info"},[e("span",{staticClass:"operator-label"},[t._v("操作人：")]),e("span",{staticClass:"operator-text"},[t._v(t._s(t.getOperatorTypeText(n.operatorType)))])])])])])})),0):e("div",{staticClass:"no-history"},[e("van-empty",{attrs:{image:"https://img.yzcdn.cn/vant/custom-empty-image.png",description:"暂无绑定历史记录"}})],1)])]),t.hasMore&&!t.loading?e("div",{staticClass:"load-more"},[e("van-button",{attrs:{type:"default",size:"large",loading:t.loadingMore,block:""},on:{click:t.loadMore}},[t._v("\n        "+t._s(t.loadingMore?"加载中...":"加载更多")+"\n      ")])],1):t._e()])])},i=[],s=(n("6b54"),n("f576"),n("2909")),r=(n("96cf"),n("1da1")),o={name:"BindingHistory",data:function(){return{loading:!0,loadingMore:!1,currentBinding:null,historyList:[],page:1,pageSize:10,hasMore:!0}},mounted:function(){this.getCurrentBinding(),this.getHistoryList()},methods:{getCurrentBinding:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$fly.get("/pyp/web/salesman/binding/myBinding");case 3:e=t.sent,200===e.code&&(this.currentBinding=e.binding),t.next=9;break;case 7:t.prev=7,t.t0=t["catch"](0);case 9:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),getHistoryList:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e,n,a,i=arguments;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e=i.length>0&&void 0!==i[0]&&i[0],t.prev=1,e?this.loadingMore=!0:(this.loading=!0,this.page=1),t.next=5,this.$fly.get("/pyp/web/salesman/binding/getBindingHistory",{page:this.page,limit:this.pageSize});case 5:n=t.sent,200===n.code&&(a=n.page.list||[],this.historyList=e?[].concat(Object(s["a"])(this.historyList),Object(s["a"])(a)):a,this.hasMore=a.length===this.pageSize,this.hasMore&&this.page++),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](1),this.$toast("获取历史记录失败");case 12:return t.prev=12,this.loading=!1,this.loadingMore=!1,t.finish(12);case 16:case"end":return t.stop()}}),t,this,[[1,9,12,16]])})));function e(){return t.apply(this,arguments)}return e}(),loadMore:function(){!this.loadingMore&&this.hasMore&&this.getHistoryList(!0)},goToBind:function(){this.$router.push("/salesman/my-salesman")},getOperationTypeText:function(t){var e={1:"新增绑定",2:"更换业务员",3:"解除绑定",4:"系统解绑"};return e[t]||"未知操作"},getOperationTagType:function(t){var e={1:"success",2:"warning",3:"danger",4:"default"};return e[t]||"default"},getTimelineDotClass:function(t){var e={1:"success",2:"warning",3:"danger",4:"default"};return e[t]||"default"},getOperatorTypeText:function(t){var e={1:"本人操作",2:"业务员操作",3:"管理员操作",4:"系统操作"};return e[t]||"未知"},formatDateTime:function(t){if(!t)return"";var e=new Date(t),n=new Date,a=n-e,i=Math.floor(a/864e5);return 0===i?"今天 ".concat(e.getHours().toString().padStart(2,"0"),":").concat(e.getMinutes().toString().padStart(2,"0")):1===i?"昨天 ".concat(e.getHours().toString().padStart(2,"0"),":").concat(e.getMinutes().toString().padStart(2,"0")):i<7?"".concat(i,"天前"):"".concat(e.getFullYear(),"-").concat(String(e.getMonth()+1).padStart(2,"0"),"-").concat(String(e.getDate()).padStart(2,"0"))}}},c=o,l=(n("23ae"),n("2877")),d=Object(l["a"])(c,a,i,!1,null,"de7e37ee",null);e["default"]=d.exports},f576:function(t,e,n){"use strict";var a=n("5ca1"),i=n("2e08"),s=n("a25f"),r=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(s);a(a.P+a.F*r,"String",{padStart:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0,!0)}})}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5d93dcec"],{"2de0":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"标签名称",prop:"name"}},[t("el-input",{attrs:{placeholder:"标签名称"},model:{value:e.dataForm.name,callback:function(t){e.$set(e.dataForm,"name",t)},expression:"dataForm.name"}})],1),t("el-form-item",{attrs:{label:"所属会场",prop:"placeId"}},[t("el-select",{attrs:{placeholder:"所属会场",filterable:""},model:{value:e.dataForm.placeId,callback:function(t){e.$set(e.dataForm,"placeId",t)},expression:"dataForm.placeId"}},[t("el-option",{attrs:{label:"全部",value:""}}),e._l(e.placeList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"排序",prop:"orderBy"}},[t("el-input",{attrs:{placeholder:"排序，数值越小越靠前"},model:{value:e.dataForm.orderBy,callback:function(t){e.$set(e.dataForm,"orderBy",t)},expression:"dataForm.orderBy"}})],1),t("el-form-item",{attrs:{label:"类型",prop:"type"}},[t("el-select",{attrs:{placeholder:"类型",filterable:""},model:{value:e.dataForm.type,callback:function(t){e.$set(e.dataForm,"type",t)},expression:"dataForm.type"}},e._l(e.liveConfig,(function(e){return t("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1),0==e.dataForm.type?t("el-form-item",{attrs:{label:"内容",prop:"content"}},[t("tinymce-editor",{ref:"editor",model:{value:e.dataForm.content,callback:function(t){e.$set(e.dataForm,"content",t)},expression:"dataForm.content"}})],1):e._e(),1==e.dataForm.type?t("el-form-item",{attrs:{label:"外部连接",prop:"content"}},[t("el-input",{attrs:{placeholder:"外部连接"},model:{value:e.dataForm.content,callback:function(t){e.$set(e.dataForm,"content",t)},expression:"dataForm.content"}})],1):e._e()],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},o=[],l=(a("d3b7"),a("3ca3"),a("ddb0"),a("ed56")),i={data:function(){return{visible:!1,liveConfig:l["b"],placeList:[],dataForm:{id:0,activityId:"",name:"",orderBy:0,content:"",placeId:"",type:0},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],name:[{required:!0,message:"标签名称不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序，数值越小越靠前不能为空",trigger:"blur"}],type:[{required:!0,message:"类型不能为空",trigger:"blur"}]}}},components:{TinymceEditor:function(){return Promise.all([a.e("chunk-2d0e1c2e"),a.e("chunk-03be236c"),a.e("chunk-2d0a4b8c")]).then(a.bind(null,"26dc"))}},methods:{init:function(e,t){var a=this;this.dataForm.activityId=e||0,this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/place/placelivetabconfig/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(e){var t=e.data;t&&200===t.code&&(a.dataForm.activityId=t.placeLiveTabConfig.activityId,a.dataForm.name=t.placeLiveTabConfig.name,a.dataForm.orderBy=t.placeLiveTabConfig.orderBy,a.dataForm.content=t.placeLiveTabConfig.content,a.dataForm.placeId=t.placeLiveTabConfig.placeId,a.dataForm.type=t.placeLiveTabConfig.type)}))})),this.getPlace()},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$http({url:e.$http.adornUrl("/place/placelivetabconfig/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,activityId:e.dataForm.activityId,name:e.dataForm.name,orderBy:e.dataForm.orderBy,content:e.dataForm.content,placeId:e.dataForm.placeId,type:e.dataForm.type})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(a.msg)}))}))},getPlace:function(){var e=this;this.$http({url:this.$http.adornUrl("/place/placeactivity/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.placeList=a.result)}))}}},n=i,d=a("2877"),c=Object(d["a"])(n,r,o,!1,null,null,null);t["default"]=c.exports},ed56:function(e,t,a){"use strict";a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return l}));var r=[{key:0,value:"预告"},{key:1,value:"直播"},{key:2,value:"录播"}],o=[{key:0,value:"未确认"},{key:1,value:"确认通过"},{key:2,value:"确认不通过"}],l=[{key:0,value:"自定义内容"},{key:1,value:"自定义链接"},{key:2,value:"会议日程"},{key:3,value:"会议嘉宾"},{key:4,value:"聊天室"},{key:5,value:"考试&问卷"},{key:6,value:"展商列表"},{key:7,value:"录播视频列表"}]}}]);
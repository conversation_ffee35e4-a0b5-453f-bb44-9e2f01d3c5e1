(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5dd9b652","chunk-14319c7a","chunk-464c253a"],{"44e7":function(t,a,e){"use strict";var i=e("861d"),r=e("c6b6"),n=e("b622"),s=n("match");t.exports=function(t){var a;return i(t)&&(void 0!==(a=t[s])?!!a:"RegExp"===r(t))}},"466d":function(t,a,e){"use strict";var i=e("c65b"),r=e("d784"),n=e("825a"),s=e("7234"),l=e("50c4"),o=e("577e"),c=e("1d80"),d=e("dc4a"),h=e("8aa5"),m=e("14c3");r("match",(function(t,a,e){return[function(a){var e=c(this),r=s(a)?void 0:d(a,t);return r?i(r,a,e):new RegExp(a)[t](o(e))},function(t){var i=n(this),r=o(t),s=e(a,i,r);if(s.done)return s.value;if(!i.global)return m(i,r);var c=i.unicode;i.lastIndex=0;var d,u=[],y=0;while(null!==(d=m(i,r))){var p=o(d[0]);u[y]=p,""===p&&(i.lastIndex=h(r,l(i.lastIndex),c)),y++}return 0===y?null:u}]}))},5848:function(t,a,e){"use strict";e("8f37")},"593c":function(t,a,e){"use strict";e.d(a,"h",(function(){return i})),e.d(a,"i",(function(){return r})),e.d(a,"n",(function(){return n})),e.d(a,"f",(function(){return s})),e.d(a,"o",(function(){return l})),e.d(a,"c",(function(){return o})),e.d(a,"m",(function(){return c})),e.d(a,"b",(function(){return d})),e.d(a,"g",(function(){return h})),e.d(a,"j",(function(){return m})),e.d(a,"d",(function(){return u})),e.d(a,"k",(function(){return y})),e.d(a,"l",(function(){return p})),e.d(a,"a",(function(){return f})),e.d(a,"e",(function(){return v}));var i=[{key:0,value:"后台签到"},{key:1,value:"微信扫码签到"}],r=[{key:0,value:"未签到"},{key:1,value:"已签到"},{key:2,value:"已签退"}],n=[{key:0,value:"未报名"},{key:1,value:"已报名"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],s=[{key:0,value:"无需"},{key:1,value:"统一"},{key:2,value:"随机"},{key:3,value:"统一(多个)"}],l=[{key:0,value:"未审核"},{key:1,value:"审核通过"},{key:2,value:"审核不通过"}],o=[{key:0,value:"飞机"},{key:1,value:"火车"},{key:2,value:"其他"}],c=[{key:0,value:"会务组出票"},{key:1,value:"自行出票"}],d=[{key:0,value:"主控"},{key:1,value:"学员管理"},{key:2,value:"专家管理"},{key:3,value:"技术"},{key:4,value:"业务员"},{key:5,value:"兼职"}],h=[{key:0,value:"项目收支"},{key:1,value:"服务费"}],m=[{key:0,value:"大会出票"},{key:1,value:"自行购买，凭票大会报销"}],u=[{key:0,value:"未出票"},{key:1,value:"已出票"}],y=[{key:0,value:"已提交"},{key:1,value:"已预订"},{key:2,value:"已支付,待出票"},{key:4,value:"已出票"},{key:5,value:"不能出票待退款"},{key:7,value:"不能出票已退款"},{key:13,value:"(退票)审核不通过交易结束"},{key:15,value:"(退票)申请中"},{key:16,value:"(退票)已退款交易结束"},{key:22,value:"(改签)审核不通过交易结束"},{key:23,value:"(改签)需补款待支付"},{key:26,value:"(改签)无法改签已退款交易结束"},{key:27,value:"(改签)改签成功交易结束"},{key:28,value:"(改签)改签已取消交易结束"},{key:29,value:"(改签)改签处理中"},{key:99,value:"已取消"}],p=[{key:0,value:"已提交"},{key:1,value:"已预订"},{key:2,value:"占位成功"},{key:4,value:"占位失败"},{key:5,value:"已取消"},{key:8,value:"出票成功"},{key:9,value:"出票失败"},{key:31,value:"改签占位成功"},{key:32,value:"改签占位失败"},{key:34,value:"改签确认出票成功"},{key:35,value:"改签确认出票失败"},{key:37,value:"改签已取消"},{key:21,value:"退票成功"},{key:22,value:"退票失败"},{key:23,value:"已退票未退款"}],f=[{key:0,value:"专家信息"},{key:1,value:"专家任务确认"},{key:2,value:"专家行程"},{key:3,value:"专家接送"},{key:4,value:"专家劳务费信息"},{key:5,value:"专家劳务费签字"},{key:6,value:"其他"},{key:7,value:"活动即将过期"},{key:8,value:"活动已过期"},{key:9,value:"活动续费成功"}],v=[{key:0,value:"未读"},{key:1,value:"已读"}]},"5a7f":function(t,a,e){},"5b81":function(t,a,e){"use strict";var i=e("23e7"),r=e("c65b"),n=e("e330"),s=e("1d80"),l=e("1626"),o=e("7234"),c=e("44e7"),d=e("577e"),h=e("dc4a"),m=e("90d8"),u=e("0cb2"),y=e("b622"),p=e("c430"),f=y("replace"),v=TypeError,F=n("".indexOf),g=n("".replace),C=n("".slice),b=Math.max;i({target:"String",proto:!0},{replaceAll:function(t,a){var e,i,n,y,k,T,S,D,P,E,_=s(this),N=0,$="";if(!o(t)){if(e=c(t),e&&(i=d(s(m(t))),!~F(i,"g")))throw new v("`.replaceAll` does not allow non-global regexes");if(n=h(t,f),n)return r(n,t,_,a);if(p&&e)return g(d(_),t,a)}y=d(_),k=d(t),T=l(a),T||(a=d(a)),S=k.length,D=b(1,S),P=F(y,k);while(-1!==P)E=T?d(a(k,P,y)):u(k,y,P,[],void 0,a),$+=C(y,N,P)+E,N=P+S,P=P+D>y.length?-1:F(y,k,P+D);return N<y.length&&($+=C(y,N)),$}})},"622d":function(t,a,e){"use strict";e.r(a);var i=function(){var t=this,a=t._self._c;return a("el-dialog",{attrs:{title:"选择火车票",visible:t.visible,width:"800px"},on:{"update:visible":function(a){t.visible=a},close:t.handleClose},scopedSlots:t._u([{key:"footer",fn:function(){return[a("el-button",{on:{click:function(a){t.visible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.confirmSelection}},[t._v("确定")])]},proxy:!0}])},[a("div",{staticClass:"flight-container"},[a("el-row",{staticClass:"date-nav",attrs:{type:"flex",justify:"space-between",align:"middle"}},[a("el-col",{attrs:{span:4}},[a("el-button",{attrs:{plain:"",disabled:t.isLoading},on:{click:function(a){return t.switchDay(-1)}}},[t._v(" 前一天 ")])],1),a("el-col",{staticClass:"current-date",attrs:{span:16}},[t._v(" "+t._s(t.inDate)+" ")]),a("el-col",{staticClass:"text-right",attrs:{span:4}},[a("el-button",{attrs:{plain:"",disabled:t.isLoading},on:{click:function(a){return t.switchDay(1)}}},[t._v(" 后一天 ")])],1)],1),t.isLoading?a("div",{staticClass:"loading-overlay"},[a("div",{staticClass:"loading-spinner"}),a("div",{staticClass:"loading-text"},[t._v("火车票信息加载中...")])]):t._e(),t.isLoading?t._e():[t.flights.length>0?a("div",{staticClass:"flight-list"},t._l(t.flights,(function(e,i){return a("div",{key:e.trainCode,staticClass:"flight-item",class:{selected:t.selectedFlight===e.trainCode},on:{click:function(a){return t.selectFlight(e.trainCode)}}},[a("div",{staticClass:"time-row"},[a("span",{staticClass:"departure-time"},[t._v(t._s(e.fromDateTime))]),a("span",{staticClass:"duration"},[t._v(t._s(e.runTime)+"时")]),a("span",{staticClass:"arrival-time"},[t._v(t._s(e.toDateTime))])]),a("div",{staticClass:"airport-row"},[a("span",{staticClass:"departure-airport"},[t._v(t._s(e.fromStation))]),a("div",[a("span",{staticClass:"flight-number"},[t._v(t._s(e.trainCode))])]),a("span",{staticClass:"arrival-airport"},[t._v(t._s(e.toStation))])]),t._l(e.Seats,(function(e){return a("div",{key:e.seatTypeName,staticClass:"flight-item",class:{"selected-item":t.selectedFlightItem.seatType===e.seatType&&i===t.selectIndex},on:{click:function(a){return t.selectFlightItem(e,i)}}},[a("div",{staticClass:"time-row"},[a("span",{staticClass:"duration"},[t._v(t._s(e.seatTypeName))]),a("span",[t._v("剩余座位："+t._s(e.leftTicketNum))]),a("span",[t._v("售价："+t._s(e.ticketPrice))])])])}))],2)})),0):a("el-empty",{attrs:{description:"暂无火车票信息"}})]],2)])},r=[],n=(e("7db0"),e("d3b7"),e("0643"),e("fffc"),e("c466")),s={name:"FlightSelectorDialog",data:function(){return{isLoading:!1,visible:!1,inDate:"",startCityCode:"",endCityCode:"",selectedFlight:null,selectIndex:0,selectedFlightItem:{},flights:[],callback:null}},computed:{},methods:{init:function(t,a,e,i){this.callback=i||null,this.startCityCode=t,this.endCityCode=a,this.inDate=e,this.visible=!0,this.flights=[],this.selectedFlight=null,this.selectIndex=0,this.selectedFlightItem={},this.loadFlights()},loadFlights:function(){var t=this;this.isLoading=!0,this.$http({url:this.$http.adornUrl("/panhe/searchTrain"),method:"get",params:this.$http.adornParams({fromCity:this.startCityCode,toCity:this.endCityCode,fromDate:this.inDate})}).then((function(a){var e=a.data;t.isLoading=!1,e&&200===e.code?t.flights=e.result:t.$message.error(e.msg)}))},switchDay:function(t){var a=new Date(this.inDate);a.setDate(a.getDate()+t),this.inDate=Object(n["c"])(a,"yyyy/MM/dd"),this.flights=[],this.selectedFlight=null,this.selectIndex=0,this.selectedFlightItem={},this.loadFlights()},selectFlight:function(t){this.selectedFlight=t},selectFlightItem:function(t,a){this.selectedFlightItem=t,this.selectIndex=a,console.log(this.selectedFlightItem)},confirmSelection:function(){var t=this;if(this.selectedFlight&&this.selectFlightItem){var a=this.flights.find((function(a){return a.trainCode===t.selectedFlight}));console.log(a),a.cabinBookPara=this.selectedFlightItem.seatType,a.cabinCode=this.selectedFlightItem.seatTypeName,a.price=this.selectedFlightItem.ticketPrice,this.callback?this.callback(a):this.$emit("select",a),this.visible=!1}else this.$message.warning("请先选择火车票和仓位")},handleClose:function(){this.selectedFlight=null}}},l=s,o=(e("edc7"),e("2877")),c=Object(o["a"])(l,i,r,!1,null,"329dde58",null);a["default"]=c.exports},"7db0":function(t,a,e){"use strict";var i=e("23e7"),r=e("b727").find,n=e("44d2"),s="find",l=!0;s in[]&&Array(1)[s]((function(){l=!1})),i({target:"Array",proto:!0,forced:l},{find:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}}),n(s)},"845d":function(t,a,e){"use strict";e.r(a);var i=function(){var t=this,a=t._self._c;return a("el-dialog",{attrs:{title:"选择航班",visible:t.visible,width:"800px"},on:{"update:visible":function(a){t.visible=a},close:t.handleClose},scopedSlots:t._u([{key:"footer",fn:function(){return[a("el-button",{on:{click:function(a){t.visible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.confirmSelection}},[t._v("确定")])]},proxy:!0}])},[a("div",{staticClass:"flight-container"},[a("el-row",{staticClass:"date-nav",attrs:{type:"flex",justify:"space-between",align:"middle"}},[a("el-col",{attrs:{span:4}},[a("el-button",{attrs:{plain:"",disabled:t.isLoading},on:{click:function(a){return t.switchDay(-1)}}},[t._v(" 前一天 ")])],1),a("el-col",{staticClass:"current-date",attrs:{span:16}},[t._v(" "+t._s(t.inDate)+" ")]),a("el-col",{staticClass:"text-right",attrs:{span:4}},[a("el-button",{attrs:{plain:"",disabled:t.isLoading},on:{click:function(a){return t.switchDay(1)}}},[t._v(" 后一天 ")])],1)],1),t.isLoading?a("div",{staticClass:"loading-overlay"},[a("div",{staticClass:"loading-spinner"}),a("div",{staticClass:"loading-text"},[t._v("航班信息加载中...")])]):t._e(),t.isLoading?t._e():[t.flights.length>0?a("div",{staticClass:"flight-list"},t._l(t.flights,(function(e){return a("div",{key:e.flightNo,staticClass:"flight-item",class:{selected:t.selectedFlight===e.flightNo},on:{click:function(a){return t.selectFlight(e.flightNo)}}},[a("div",{staticClass:"time-row"},[a("span",{staticClass:"departure-time"},[t._v(t._s(e.fromDateTime))]),a("span",{staticClass:"duration"},[t._v(t._s(e.flyDuration)+"时")]),a("span",{staticClass:"arrival-time"},[t._v(t._s(e.toDateTime))])]),a("div",{staticClass:"airport-row"},[a("span",{staticClass:"departure-airport"},[t._v(t._s(e.fromAirportName)+"-"+t._s(e.fromTerminal))]),a("div",[a("span",{staticClass:"flight-number"},[t._v(t._s(e.flightNo))]),a("span",{staticClass:"flight-number"},[t._v(t._s(e.airlineCompany))]),a("span",{staticClass:"flight-number"},[t._v(t._s(e.meals))])]),a("span",{staticClass:"arrival-airport"},[t._v(t._s(e.toAirportName)+"-"+t._s(e.toTerminal))])]),t._l(e.cabins,(function(e){return a("div",{key:e.cabinBookPara,staticClass:"flight-item",class:{"selected-item":t.selectedFlightItem.cabinBookPara===e.cabinBookPara},on:{click:function(a){return t.selectFlightItem(e)}}},[a("div",{staticClass:"time-row"},[a("span",{staticClass:"duration"},[t._v(t._s(e.cabinName))]),a("span",[t._v("原价："+t._s(e.cabinPrice.adultFarePrice))]),a("span",[t._v("售价："+t._s(e.cabinPrice.adultSalePrice))])])])}))],2)})),0):a("el-empty",{attrs:{description:"暂无航班信息"}})]],2)])},r=[],n=(e("7db0"),e("d3b7"),e("0643"),e("fffc"),e("c466")),s={name:"FlightSelectorDialog",data:function(){return{isLoading:!1,visible:!1,inDate:"",startCityCode:"",endCityCode:"",selectedFlight:null,selectedFlightItem:{},flights:[],callback:null}},computed:{},methods:{init:function(t,a,e,i){this.callback=i||null,this.startCityCode=t,this.endCityCode=a,this.inDate=e,this.visible=!0,this.flights=[],this.selectedFlight=null,this.selectedFlightItem={},this.loadFlights()},loadFlights:function(){var t=this;this.isLoading=!0,this.$http({url:this.$http.adornUrl("/panhe/searchPlane"),method:"get",params:this.$http.adornParams({fromCity:this.startCityCode,toCity:this.endCityCode,fromDate:this.inDate})}).then((function(a){var e=a.data;t.isLoading=!1,e&&200===e.code?t.flights=e.result:t.$message.error(e.msg)}))},switchDay:function(t){var a=new Date(this.inDate);a.setDate(a.getDate()+t),this.inDate=Object(n["c"])(a,"yyyy/MM/dd"),this.flights=[],this.selectedFlight=null,this.selectedFlightItem={},this.loadFlights()},selectFlight:function(t){this.selectedFlight=t},selectFlightItem:function(t){this.selectedFlightItem=t},confirmSelection:function(){var t=this;if(this.selectedFlight&&this.selectFlightItem){var a=this.flights.find((function(a){return a.flightNo===t.selectedFlight}));a.cabinBookPara=this.selectedFlightItem.cabinBookPara,a.cabinCode=this.selectedFlightItem.cabinCode,a.price=this.selectedFlightItem.cabinPrice.adultSalePrice,this.callback?this.callback(a):this.$emit("select",a),this.visible=!1}else this.$message.warning("请先选择航班和仓位")},handleClose:function(){this.selectedFlight=null}}},l=s,o=(e("5848"),e("2877")),c=Object(o["a"])(l,i,r,!1,null,"70fc83da",null);a["default"]=c.exports},"8f37":function(t,a,e){},c466:function(t,a,e){"use strict";e.d(a,"c",(function(){return l})),e.d(a,"a",(function(){return o})),e.d(a,"b",(function(){return c}));e("ac1f"),e("466d"),e("5319");var i=/([yMdhsm])(\1*)/g,r="yyyy/MM/dd",n="yyyy/MM/dd hh:mm:ss";function s(t,a){a-=(t+"").length;for(var e=0;e<a;e++)t="0"+t;return t}function l(t,a){return a=a||r,a.replace(i,(function(a){switch(a.charAt(0)){case"y":return s(t.getFullYear(),a.length);case"M":return s(t.getMonth()+1,a.length);case"d":return s(t.getDate(),a.length);case"w":return t.getDay()+1;case"h":return s(t.getHours(),a.length);case"m":return s(t.getMinutes(),a.length);case"s":return s(t.getSeconds(),a.length)}}))}function o(t,a){var e=new Date(t),i=new Date(e.getTime()+24*a*60*60*1e3);return l(i,n)}function c(t,a,e){var i=new Date(t),r=new Date(i.getTime()+60*a*1e3);return l(r,e||n)}},edc7:function(t,a,e){"use strict";e("5a7f")},f232:function(t,a,e){"use strict";e.r(a);e("ac1f"),e("841c");var i=function(){var t=this,a=t._self._c;return a("div",[a("el-dialog",{attrs:{title:t.dataForm.id?1==t.type?"改签":"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(a){t.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&t._k(a.keyCode,"enter",13,a.key,"Enter")?null:t.dataFormSubmit()}}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"行程类型",prop:"inType"}},[a("el-select",{attrs:{placeholder:"行程类型",filterable:""},on:{change:t.inTypeChange},model:{value:t.dataForm.inType,callback:function(a){t.$set(t.dataForm,"inType",a)},expression:"dataForm.inType"}},t._l(t.guestGoType,(function(t){return a("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"行程日期",prop:"inDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy/MM/dd",placeholder:"开始日期"},model:{value:t.dataForm.inDate,callback:function(a){t.$set(t.dataForm,"inDate",a)},expression:"dataForm.inDate"}})],1)],1),a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"行程出发地点",prop:"inStartPlace"}},[a("el-autocomplete",{staticStyle:{width:"100%"},attrs:{"fetch-suggestions":t.search,label:"name",placeholder:"请输入内容",loading:t.loading},on:{select:t.selectResult},scopedSlots:t._u([{key:"default",fn:function(e){return a("div",{},[a("span",{staticStyle:{float:"left"}},[t._v(t._s(0==t.dataForm.inType?e.item.airportName:e.item.stationName))])])}}]),model:{value:t.dataForm.inStartPlace,callback:function(a){t.$set(t.dataForm,"inStartPlace",a)},expression:"dataForm.inStartPlace"}})],1)],1),a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"行程到达地点",prop:"inEndPlace"}},[a("el-autocomplete",{staticStyle:{width:"100%"},attrs:{"fetch-suggestions":t.endsearch,label:"name",placeholder:"请输入内容",loading:t.endloading},on:{select:t.endselectResult},scopedSlots:t._u([{key:"default",fn:function(e){return a("div",{},[a("span",{staticStyle:{float:"left"}},[t._v(t._s(0==t.dataForm.inType?e.item.airportName:e.item.stationName))])])}}]),model:{value:t.dataForm.inEndPlace,callback:function(a){t.$set(t.dataForm,"inEndPlace",a)},expression:"dataForm.inEndPlace"}})],1)],1),a("el-col",{staticStyle:{"text-align":"center"},attrs:{span:2}},[a("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.searchReturnResultPlane()}}},[t._v("查询")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"行程时间",prop:"inDate"}},[a("el-date-picker",{attrs:{type:"datetimerange","value-format":"yyyy/MM/dd HH:mm:ss","range-separator":"至","start-placeholder":"出发日期","end-placeholder":"到达日期"},on:{change:t.dateChange},model:{value:t.times,callback:function(a){t.times=a},expression:"times"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"行程航班/火车号",prop:"inNumber"}},[a("el-input",{attrs:{placeholder:"行程航班/火车号"},model:{value:t.dataForm.inNumber,callback:function(a){t.$set(t.dataForm,"inNumber",a)},expression:"dataForm.inNumber"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"类型",prop:"type"}},[a("el-select",{attrs:{placeholder:"类型",filterable:""},model:{value:t.dataForm.type,callback:function(a){t.$set(t.dataForm,"type",a)},expression:"dataForm.type"}},t._l(t.tripType,(function(t){return a("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1)],1),1==t.type?[a("el-col",{attrs:{span:24}},[a("div",{staticStyle:{margin:"10px 0","border-bottom":"1px solid #eee","padding-bottom":"10px"}},[a("h3",[t._v("改签信息")])])]),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"改签日期",prop:"chaDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy/MM/dd",placeholder:"改签日期"},model:{value:t.dataForm.chaDate,callback:function(a){t.$set(t.dataForm,"chaDate",a)},expression:"dataForm.chaDate"}})],1)],1),a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"改签出发地点",prop:"chaStartPlace"}},[a("el-autocomplete",{staticStyle:{width:"100%"},attrs:{"fetch-suggestions":t.chaSearch,label:"name",placeholder:"请输入内容",loading:t.chaLoading},on:{select:t.chaSelectResult},scopedSlots:t._u([{key:"default",fn:function(e){return a("div",{},[a("span",{staticStyle:{float:"left"}},[t._v(t._s(0==t.dataForm.inType?e.item.airportName:e.item.stationName))])])}}],null,!1,1035421749),model:{value:t.dataForm.chaStartPlace,callback:function(a){t.$set(t.dataForm,"chaStartPlace",a)},expression:"dataForm.chaStartPlace"}})],1)],1),a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"改签到达地点",prop:"chaEndPlace"}},[a("el-autocomplete",{staticStyle:{width:"100%"},attrs:{"fetch-suggestions":t.chaEndSearch,label:"name",placeholder:"请输入内容",loading:t.chaEndLoading},on:{select:t.chaEndSelectResult},scopedSlots:t._u([{key:"default",fn:function(e){return a("div",{},[a("span",{staticStyle:{float:"left"}},[t._v(t._s(0==t.dataForm.inType?e.item.airportName:e.item.stationName))])])}}],null,!1,1035421749),model:{value:t.dataForm.chaEndPlace,callback:function(a){t.$set(t.dataForm,"chaEndPlace",a)},expression:"dataForm.chaEndPlace"}})],1)],1),a("el-col",{staticStyle:{"text-align":"center"},attrs:{span:2}},[a("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.searchChaReturnResultPlane()}}},[t._v("查询")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"改签时间",prop:"chaTimes"}},[a("el-date-picker",{attrs:{type:"datetimerange","value-format":"yyyy/MM/dd HH:mm:ss","range-separator":"至","start-placeholder":"出发日期","end-placeholder":"到达日期"},on:{change:t.chaDateChange},model:{value:t.chaTimes,callback:function(a){t.chaTimes=a},expression:"chaTimes"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"改签航班/火车号",prop:"chaNumber"}},[a("el-input",{attrs:{placeholder:"改签航班/火车号"},model:{value:t.dataForm.chaNumber,callback:function(a){t.$set(t.dataForm,"chaNumber",a)},expression:"dataForm.chaNumber"}})],1)],1)]:t._e()],2)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){t.visible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1),t.planeselectVisible?a("planeselect",{ref:"planeselect",on:{select:t.handleFlightSelect}}):t._e(),t.trainselectVisible?a("trainselect",{ref:"trainselect",on:{select:t.handleTrainSelect}}):t._e()],1)},r=[],n=e("5530"),s=(e("5319"),e("5b81"),e("845d")),l=e("622d"),o=e("7de9"),c=e("593c"),d=e("c466"),h={components:{planeselect:s["default"],trainselect:l["default"]},data:function(){return{type:0,planeselectVisible:!1,trainselectVisible:!1,loading:!1,endloading:!1,searchResult:[],endsearchResult:[],times:[],yesOrNo:o["g"],guestGoType:c["c"],tripType:c["m"],visible:!1,dataForm:{repeatToken:"",id:0,inType:0,inDate:"",inNumber:"",inEndPlace:"",inStartPlace:"",inStartDate:"",inEndDate:"",activityGuestId:"",activityId:"",startCityCode:"",endCityCode:"",inStartCity:"",inEndCity:"",inStartTerminal:"",inEndTerminal:"",isShareFlight:"",realFlightNo:"",cabinCode:"",cabinBookPara:"",isBuy:0,type:0,price:0,chaDate:"",chaNumber:"",chaEndPlace:"",chaStartPlace:"",chaStartDate:"",chaEndDate:"",chaPrice:0,chaStartCityCode:"",chaEndCityCode:"",chaStartCity:"",chaEndCity:"",chaStartTerminal:"",chaEndTerminal:""},dataRule:{inType:[{required:!0,message:"行程类型（0-飞机，1-火车，2-其他）不能为空",trigger:"blur"}],inDate:[{required:!0,message:"行程日期不能为空",trigger:"blur"}],inNumber:[{required:!0,message:"行程航班号不能为空",trigger:"blur"}],activityGuestId:[{required:!0,message:"不能为空",trigger:"blur"}],activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],isBuy:[{required:!0,message:"是否购买不能为空",trigger:"blur"}],price:[{required:!0,message:"价格不能为空",trigger:"blur"}]}}},methods:{handleFlightSelect:function(t){console.log("已选择航班:",t),t&&(this.dataForm.inNumber=t.flightNo,this.dataForm.inStartPlace=t.fromAirportName,this.dataForm.inEndPlace=t.toAirportName,this.dataForm.inStartTerminal=t.fromTerminal,this.dataForm.inEndTerminal=t.toTerminal,this.dataForm.isShareFlight=t.isShareFlight?1:0,this.dataForm.realFlightNo=t.realFlightNo,this.dataForm.cabinCode=t.cabinCode,this.dataForm.cabinBookPara=t.cabinBookPara,this.dataForm.startCityCode=t.fromAirportCode,this.dataForm.endCityCode=t.toAirportCode,this.dataForm.price=t.price,this.times=[t.fromDateTime.replaceAll("-","/"),t.toDateTime.replaceAll("-","/")],console.log(this.times),this.dataForm.inStartDate=t.fromDateTime.replaceAll("-","/"),this.dataForm.inEndDate=t.toDateTime.replaceAll("-","/"))},handleTrainSelect:function(t){console.log("已选择火车:",t),t&&(this.dataForm.inNumber=t.trainCode,this.dataForm.realFlightNo=t.trainNo,this.dataForm.inStartPlace=t.fromStation,this.dataForm.inEndPlace=t.toStation,this.dataForm.cabinCode=t.cabinCode,this.dataForm.cabinBookPara=t.cabinBookPara,this.dataForm.price=t.price,this.times=[t.fromDateTime.replaceAll("-","/")+":00",t.toDateTime.replaceAll("-","/")+":00"],this.dataForm.inStartDate=t.fromDateTime.replaceAll("-","/")+":00",this.dataForm.inEndDate=t.toDateTime.replaceAll("-","/")+":00")},inTypeChange:function(t){this.dataForm.inStartPlace="",this.dataForm.inEndPlace="",this.dataForm.inStartTerminal="",this.dataForm.inEndTerminal="",this.dataForm.isShareFlight="",this.dataForm.realFlightNo="",this.dataForm.cabinCode="",this.dataForm.cabinBookPara="",this.searchResult=[],this.endsearchResult=[]},dateChange:function(t){this.dataForm.inStartDate=t[0],this.dataForm.inEndDate=t[1]},chaDateChange:function(t){t&&(this.dataForm.chaStartDate=t[0],this.dataForm.chaEndDate=t[1])},chaSearch:function(t,a){var e=this;if(""!==t){this.chaLoading=!0,this.chaSearchResult=[];var i=0==this.dataForm.inType?"/config/configairport/findByName":"/config/configtrainstation/findByName";this.$http({url:this.$http.adornUrl(i),method:"get",params:this.$http.adornParams({name:t})}).then((function(t){var i=t.data;e.chaLoading=!1,i&&200===i.code?(e.chaSearchResult=i.result,clearTimeout(e.timeout),e.timeout=setTimeout((function(){a(e.chaSearchResult)}),100*Math.random())):e.$message.error(i.msg)}))}else this.chaLoading=!1},chaEndSearch:function(t,a){var e=this;if(""!==t){this.chaEndLoading=!0,this.chaEndSearchResult=[];var i=0==this.dataForm.inType?"/config/configairport/findByName":"/config/configtrainstation/findByName";this.$http({url:this.$http.adornUrl(i),method:"get",params:this.$http.adornParams({name:t})}).then((function(t){var i=t.data;e.chaEndLoading=!1,i&&200===i.code?(e.chaEndSearchResult=i.result,clearTimeout(e.timeout),e.timeout=setTimeout((function(){a(e.chaEndSearchResult)}),100*Math.random())):e.$message.error(i.msg)}))}else this.chaEndLoading=!1},chaSelectResult:function(t){0==this.dataForm.inType?(this.dataForm.chaStartPlace=t.airportName,this.dataForm.chaStartCity=t.cityName,this.dataForm.chaStartCityCode=t.cityCode):(this.dataForm.chaStartPlace=t.stationName,this.dataForm.chaStartCity=t.cityName,this.dataForm.chaStartCityCode=t.stationCode)},chaEndSelectResult:function(t){0==this.dataForm.inType?(this.dataForm.chaEndPlace=t.airportName,this.dataForm.chaEndCity=t.cityName,this.dataForm.chaEndCityCode=t.cityCode):(this.dataForm.chaEndPlace=t.stationName,this.dataForm.chaEndCity=t.cityName,this.dataForm.chaEndCityCode=t.stationCode)},searchChaReturnResultPlane:function(){var t=this;return this.dataForm.chaStartPlace?this.dataForm.chaEndPlace?this.dataForm.chaDate?void(0==this.dataForm.inType?(this.planeselectVisible=!0,this.$nextTick((function(){t.$refs.planeselect.init(t.dataForm.chaStartCityCode,t.dataForm.chaEndCityCode,t.dataForm.chaDate,(function(a){t.handleChaFlightSelect(a)}))}))):(this.trainselectVisible=!0,this.$nextTick((function(){t.$refs.trainselect.init(t.dataForm.chaStartPlace,t.dataForm.chaEndPlace,t.dataForm.chaDate,(function(a){t.handleChaTrainSelect(a)}))})))):(this.$message.error("请输入改签日期"),!1):(this.$message.error("请输入改签目的地"),!1):(this.$message.error("请输入改签出发地"),!1)},handleChaFlightSelect:function(t){console.log("已选择改签航班:",t),t&&(this.dataForm.chaNumber=t.flightNo,this.dataForm.chaStartPlace=t.fromAirportName,this.dataForm.chaEndPlace=t.toAirportName,this.dataForm.chaStartTerminal=t.fromTerminal,this.dataForm.chaEndTerminal=t.toTerminal,this.dataForm.chaStartCityCode=t.fromAirportCode,this.dataForm.chaEndCityCode=t.toAirportCode,this.dataForm.chaPrice=t.price,this.dataForm.cabinCode=t.cabinCode,this.dataForm.cabinBookPara=t.cabinBookPara,this.chaTimes=[t.fromDateTime.replaceAll("-","/"),t.toDateTime.replaceAll("-","/")],this.dataForm.chaStartDate=t.fromDateTime.replaceAll("-","/"),this.dataForm.chaEndDate=t.toDateTime.replaceAll("-","/"))},handleChaTrainSelect:function(t){console.log("已选择改签火车:",t),t&&(this.dataForm.chaNumber=t.trainCode,this.dataForm.chaStartPlace=t.fromStation,this.dataForm.chaEndPlace=t.toStation,this.dataForm.chaPrice=t.price,this.dataForm.cabinCode=t.cabinCode,this.dataForm.cabinBookPara=t.cabinBookPara,this.chaTimes=[t.fromDateTime.replaceAll("-","/")+":00",t.toDateTime.replaceAll("-","/")+":00"],this.dataForm.chaStartDate=t.fromDateTime.replaceAll("-","/")+":00",this.dataForm.chaEndDate=t.toDateTime.replaceAll("-","/")+":00")},init:function(t,a,e,i){var r=this;this.getToken(),this.type=i||0,this.dataForm.id=t||0,this.dataForm.activityGuestId=e||0,this.dataForm.activityId=a||0,this.times=[],this.visible=!0,this.$nextTick((function(){r.$refs["dataForm"].resetFields(),r.dataForm.id?r.$http({url:r.$http.adornUrl("/activity/activityguesttrip/info/".concat(r.dataForm.id)),method:"get",params:r.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(r.dataForm.inType=a.activityGuestTrip.inType,r.dataForm.inDate=a.activityGuestTrip.inDate,r.dataForm.inNumber=a.activityGuestTrip.inNumber,r.dataForm.inEndPlace=a.activityGuestTrip.inEndPlace,r.dataForm.inStartPlace=a.activityGuestTrip.inStartPlace,r.dataForm.inEndDate=a.activityGuestTrip.inEndDate,r.dataForm.inStartDate=a.activityGuestTrip.inStartDate,r.dataForm.activityGuestId=a.activityGuestTrip.activityGuestId,r.dataForm.activityId=a.activityGuestTrip.activityId,r.dataForm.isBuy=a.activityGuestTrip.isBuy,r.dataForm.price=a.activityGuestTrip.price,r.dataForm.type=a.activityGuestTrip.type,r.dataForm.startCityCode=a.activityGuestTrip.startCityCode,r.dataForm.endCityCode=a.activityGuestTrip.endCityCode,r.dataForm.inStartCity=a.activityGuestTrip.inStartCity,r.dataForm.inEndCity=a.activityGuestTrip.inEndCity,r.dataForm.inStartTerminal=a.activityGuestTrip.inStartTerminal,r.dataForm.inEndTerminal=a.activityGuestTrip.inEndTerminal,r.dataForm.isShareFlight=a.activityGuestTrip.isShareFlight,r.dataForm.realFlightNo=a.activityGuestTrip.realFlightNo,r.dataForm.cabinCode=a.activityGuestTrip.cabinCode,r.dataForm.cabinBookPara=a.activityGuestTrip.cabinBookPara,1==r.type&&a.activityGuestTrip.chaDate&&(r.dataForm.chaDate=a.activityGuestTrip.chaDate,r.dataForm.chaNumber=a.activityGuestTrip.chaNumber,r.dataForm.chaEndPlace=a.activityGuestTrip.chaEndPlace,r.dataForm.chaStartPlace=a.activityGuestTrip.chaStartPlace,r.dataForm.chaStartDate=a.activityGuestTrip.chaStartDate,r.dataForm.chaEndDate=a.activityGuestTrip.chaEndDate,r.dataForm.chaPrice=a.activityGuestTrip.chaPrice,r.dataForm.chaStartCityCode=a.activityGuestTrip.chaStartCityCode,r.dataForm.chaEndCityCode=a.activityGuestTrip.chaEndCityCode,r.dataForm.chaStartCity=a.activityGuestTrip.chaStartCity,r.dataForm.chaEndCity=a.activityGuestTrip.chaEndCity,r.dataForm.chaStartTerminal=a.activityGuestTrip.chaStartTerminal,r.dataForm.chaEndTerminal=a.activityGuestTrip.chaEndTerminal,r.dataForm.chaStartDate&&r.dataForm.chaEndDate&&(r.chaTimes=[r.dataForm.chaStartDate,r.dataForm.chaEndDate])),r.dataForm.inStartDate&&r.dataForm.inEndDate&&(r.times=[r.dataForm.inStartDate,r.dataForm.inEndDate]))})):r.dataForm.inDate=Object(d["c"])(new Date,"yyyy/MM/dd")}))},selectResult:function(t){0==this.dataForm.inType?(this.dataForm.inStartPlace=t.airportName,this.dataForm.inStartCity=t.cityName,this.dataForm.startCityCode=t.cityCode):(this.dataForm.inStartPlace=t.stationName,this.dataForm.inStartCity=t.cityName,this.dataForm.startCityCode=t.stationCode)},endselectResult:function(t){0==this.dataForm.inType?(this.dataForm.inEndPlace=t.airportName,this.dataForm.inEndCity=t.cityName,this.dataForm.endCityCode=t.cityCode):(this.dataForm.inEndPlace=t.stationName,this.dataForm.inEndCity=t.cityName,this.dataForm.endCityCode=t.stationCode)},search:function(t,a){var e=this;if(""!==t){this.loading=!0,this.searchResult=[];var i=0==this.dataForm.inType?"/config/configairport/findByName":"/config/configtrainstation/findByName";this.$http({url:this.$http.adornUrl(i),method:"get",params:this.$http.adornParams({name:t})}).then((function(t){var i=t.data;e.loading=!1,i&&200===i.code?(e.searchResult=i.result,clearTimeout(e.timeout),e.timeout=setTimeout((function(){a(e.searchResult)}),100*Math.random())):e.$message.error(i.msg)}))}else this.loading=!1},endsearch:function(t,a){var e=this;if(""!==t){this.endloading=!0,this.endsearchResult=[];var i=0==this.dataForm.inType?"/config/configairport/findByName":"/config/configtrainstation/findByName";this.$http({url:this.$http.adornUrl(i),method:"get",params:this.$http.adornParams({name:t})}).then((function(t){var i=t.data;e.loading=!1,i&&200===i.code?(e.endsearchResult=i.result,clearTimeout(e.timeout),e.timeout=setTimeout((function(){a(e.endsearchResult)}),100*Math.random())):e.$message.error(i.msg)}))}else this.endloading=!1},searchReturnResultPlane:function(){var t=this;return this.dataForm.inStartPlace?this.dataForm.inEndPlace?this.dataForm.inDate?void(0==this.dataForm.inType?(this.planeselectVisible=!0,this.$nextTick((function(){t.$refs.planeselect.init(t.dataForm.startCityCode,t.dataForm.endCityCode,t.dataForm.inDate)}))):(this.trainselectVisible=!0,this.$nextTick((function(){t.$refs.trainselect.init(t.dataForm.inStartPlace,t.dataForm.inEndPlace,t.dataForm.inDate)})))):(this.$message.error("请输入日期"),!1):(this.$message.error("请输入目的地"),!1):(this.$message.error("请输入出发地"),!1)},getToken:function(){var t=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(a){var e=a.data;e&&200===e.code&&(t.dataForm.repeatToken=e.result)}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(a){if(a){var e={repeatToken:t.dataForm.repeatToken,id:t.dataForm.id||void 0,inType:t.dataForm.inType,inDate:t.dataForm.inDate,inNumber:t.dataForm.inNumber,inEndPlace:t.dataForm.inEndPlace,inStartPlace:t.dataForm.inStartPlace,inEndDate:t.dataForm.inEndDate,inStartDate:t.dataForm.inStartDate,activityGuestId:t.dataForm.activityGuestId,activityId:t.dataForm.activityId,isBuy:t.dataForm.isBuy,type:t.dataForm.type,startCityCode:t.dataForm.startCityCode,endCityCode:t.dataForm.endCityCode,inStartCity:t.dataForm.inStartCity,inEndCity:t.dataForm.inEndCity,inStartTerminal:t.dataForm.inStartTerminal,inEndTerminal:t.dataForm.inEndTerminal,isShareFlight:t.dataForm.isShareFlight,realFlightNo:t.dataForm.realFlightNo,cabinCode:t.dataForm.cabinCode,cabinBookPara:t.dataForm.cabinBookPara,price:t.dataForm.price};1==t.type&&(e=Object(n["a"])(Object(n["a"])({},e),{},{chaDate:t.dataForm.chaDate,chaNumber:t.dataForm.chaNumber,chaEndPlace:t.dataForm.chaEndPlace,chaStartPlace:t.dataForm.chaStartPlace,chaStartDate:t.dataForm.chaStartDate,chaEndDate:t.dataForm.chaEndDate,chaPrice:t.dataForm.chaPrice,chaStartCityCode:t.dataForm.chaStartCityCode,chaEndCityCode:t.dataForm.chaEndCityCode,chaStartCity:t.dataForm.chaStartCity,chaEndCity:t.dataForm.chaEndCity,chaStartTerminal:t.dataForm.chaStartTerminal,chaEndTerminal:t.dataForm.chaEndTerminal}));var i=1==t.type?"/panhe/plane/cha":"/activity/activityguesttrip/".concat(t.dataForm.id?"update":"save");t.$http({url:t.$http.adornUrl(i),method:"post",data:t.$http.adornData(e)}).then((function(a){var e=a.data;e&&200===e.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):(t.$message.error(e.msg),"不能重复提交"!=e.msg&&t.getToken())}))}}))}}},m=h,u=e("2877"),y=Object(u["a"])(m,i,r,!1,null,null,null);a["default"]=y.exports},f665:function(t,a,e){"use strict";var i=e("23e7"),r=e("2266"),n=e("59ed"),s=e("825a"),l=e("46c4");i({target:"Iterator",proto:!0,real:!0},{find:function(t){s(this),n(t);var a=l(this),e=0;return r(a,(function(a,i){if(t(a,e++))return i(a)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},fffc:function(t,a,e){"use strict";e("f665")}}]);
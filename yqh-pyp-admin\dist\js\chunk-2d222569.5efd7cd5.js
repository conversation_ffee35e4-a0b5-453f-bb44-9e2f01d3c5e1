(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d222569"],{cdd6:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"}},[e("el-form-item",{attrs:{label:"会议id",prop:"activityId"}},[e("el-input",{attrs:{placeholder:"会议id"},model:{value:t.dataForm.activityId,callback:function(e){t.$set(t.dataForm,"activityId",e)},expression:"dataForm.activityId"}})],1),e("el-form-item",{attrs:{label:"排序，数值越小越靠前",prop:"orderBy"}},[e("el-input",{attrs:{placeholder:"排序，数值越小越靠前"},model:{value:t.dataForm.orderBy,callback:function(e){t.$set(t.dataForm,"orderBy",e)},expression:"dataForm.orderBy"}})],1),e("el-form-item",{attrs:{label:"嘉宾ID",prop:"activityGuestId"}},[e("el-input",{attrs:{placeholder:"嘉宾ID"},model:{value:t.dataForm.activityGuestId,callback:function(e){t.$set(t.dataForm,"activityGuestId",e)},expression:"dataForm.activityGuestId"}})],1),e("el-form-item",{attrs:{label:"日程ID",prop:"placeActivityTopicScheduleId"}},[e("el-input",{attrs:{placeholder:"日程ID"},model:{value:t.dataForm.placeActivityTopicScheduleId,callback:function(e){t.$set(t.dataForm,"placeActivityTopicScheduleId",e)},expression:"dataForm.placeActivityTopicScheduleId"}})],1),e("el-form-item",{attrs:{label:"主题ID",prop:"placeActivityTopicId"}},[e("el-input",{attrs:{placeholder:"主题ID"},model:{value:t.dataForm.placeActivityTopicId,callback:function(e){t.$set(t.dataForm,"placeActivityTopicId",e)},expression:"dataForm.placeActivityTopicId"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},c=[],d={data:function(){return{visible:!1,dataForm:{id:0,activityId:"",orderBy:"",activityGuestId:"",placeActivityTopicScheduleId:"",placeActivityTopicId:""},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序，数值越小越靠前不能为空",trigger:"blur"}],activityGuestId:[{required:!0,message:"嘉宾ID不能为空",trigger:"blur"}],placeActivityTopicScheduleId:[{required:!0,message:"日程ID不能为空",trigger:"blur"}],placeActivityTopicId:[{required:!0,message:"主题ID不能为空",trigger:"blur"}]}}},methods:{init:function(t){var e=this;this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.dataForm.id&&e.$http({url:e.$http.adornUrl("/place/placeactivitytopicschedulediscuss/info/".concat(e.dataForm.id)),method:"get",params:e.$http.adornParams()}).then((function(t){var i=t.data;i&&200===i.code&&(e.dataForm.activityId=i.placeActivityTopicScheduleDiscuss.activityId,e.dataForm.createOn=i.placeActivityTopicScheduleDiscuss.createOn,e.dataForm.createBy=i.placeActivityTopicScheduleDiscuss.createBy,e.dataForm.updateOn=i.placeActivityTopicScheduleDiscuss.updateOn,e.dataForm.updateBy=i.placeActivityTopicScheduleDiscuss.updateBy,e.dataForm.orderBy=i.placeActivityTopicScheduleDiscuss.orderBy,e.dataForm.activityGuestId=i.placeActivityTopicScheduleDiscuss.activityGuestId,e.dataForm.placeActivityTopicScheduleId=i.placeActivityTopicScheduleDiscuss.placeActivityTopicScheduleId,e.dataForm.placeActivityTopicId=i.placeActivityTopicScheduleDiscuss.placeActivityTopicId)}))}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/place/placeactivitytopicschedulediscuss/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,activityId:t.dataForm.activityId,orderBy:t.dataForm.orderBy,activityGuestId:t.dataForm.activityGuestId,placeActivityTopicScheduleId:t.dataForm.placeActivityTopicScheduleId,placeActivityTopicId:t.dataForm.placeActivityTopicId})}).then((function(e){var i=e.data;i&&200===i.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(i.msg)}))}))}}},o=d,r=i("2877"),l=Object(r["a"])(o,a,c,!1,null,null,null);e["default"]=l.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-759514ee"],{"1a4a":function(e,a,t){"use strict";t.r(a);t("b0c0");var r=function(){var e=this,a=e._self._c;return a("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(a){e.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"150px"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.dataFormSubmit()}}},[a("el-form-item",{attrs:{label:"客户名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"客户名称"},model:{value:e.dataForm.name,callback:function(a){e.$set(e.dataForm,"name",a)},expression:"dataForm.name"}})],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"联系人",prop:"username"}},[a("el-input",{attrs:{placeholder:"联系人"},model:{value:e.dataForm.username,callback:function(a){e.$set(e.dataForm,"username",a)},expression:"dataForm.username"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"联系方式",prop:"mobile"}},[a("el-input",{attrs:{placeholder:"联系方式"},model:{value:e.dataForm.mobile,callback:function(a){e.$set(e.dataForm,"mobile",a)},expression:"dataForm.mobile"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"类型",prop:"type"}},[a("el-select",{attrs:{placeholder:"类型",filterable:""},model:{value:e.dataForm.type,callback:function(a){e.$set(e.dataForm,"type",a)},expression:"dataForm.type"}},e._l(e.clientType,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"区域",prop:"area"}},[a("el-cascader",{staticStyle:{width:"100%"},attrs:{size:"large",options:e.options},on:{change:e.handleChange},model:{value:e.dataForm.area,callback:function(a){e.$set(e.dataForm,"area",a)},expression:"dataForm.area"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"地址",prop:"address"}},[a("el-input",{attrs:{placeholder:"地址"},model:{value:e.dataForm.address,callback:function(a){e.$set(e.dataForm,"address",a)},expression:"dataForm.address"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[a("el-input",{attrs:{placeholder:"邮箱"},model:{value:e.dataForm.email,callback:function(a){e.$set(e.dataForm,"email",a)},expression:"dataForm.email"}})],1)],1)],1),a("el-form-item",{attrs:{label:"银行账户信息",prop:"isBank"}},[a("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#6f6f6f"},model:{value:e.dataForm.isBank,callback:function(a){e.$set(e.dataForm,"isBank",a)},expression:"dataForm.isBank"}})],1),e.dataForm.isBank?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"银行卡号",prop:"bankAccount"}},[a("el-input",{attrs:{placeholder:"银行卡号"},model:{value:e.dataForm.bankAccount,callback:function(a){e.$set(e.dataForm,"bankAccount",a)},expression:"dataForm.bankAccount"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"开户行",prop:"bankAddress"}},[a("el-input",{attrs:{placeholder:"开户行"},model:{value:e.dataForm.bankAddress,callback:function(a){e.$set(e.dataForm,"bankAddress",a)},expression:"dataForm.bankAddress"}})],1)],1)],1):e._e(),a("el-form-item",{attrs:{label:"发票信息",prop:"isInvoice"}},[a("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#6f6f6f"},model:{value:e.dataForm.isInvoice,callback:function(a){e.$set(e.dataForm,"isInvoice",a)},expression:"dataForm.isInvoice"}})],1),e.dataForm.isInvoice?a("el-form-item",{attrs:{label:"统一社会编码",prop:"code"}},[a("el-input",{attrs:{placeholder:"统一社会编码"},model:{value:e.dataForm.code,callback:function(a){e.$set(e.dataForm,"code",a)},expression:"dataForm.code"}})],1):e._e(),e.dataForm.isInvoice?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"注册地址（专票）",prop:"registerAddress"}},[a("el-input",{attrs:{placeholder:"注册地址（专票）"},model:{value:e.dataForm.registerAddress,callback:function(a){e.$set(e.dataForm,"registerAddress",a)},expression:"dataForm.registerAddress"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"注册电话（专票）",prop:"registerTelephone"}},[a("el-input",{attrs:{placeholder:"注册电话（专票）"},model:{value:e.dataForm.registerTelephone,callback:function(a){e.$set(e.dataForm,"registerTelephone",a)},expression:"dataForm.registerTelephone"}})],1)],1)],1):e._e(),e.dataForm.isInvoice?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"注册银行（专票）",prop:"registerBank"}},[a("el-input",{attrs:{placeholder:"注册银行（专票）"},model:{value:e.dataForm.registerBank,callback:function(a){e.$set(e.dataForm,"registerBank",a)},expression:"dataForm.registerBank"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"注册账户（专票）",prop:"registerAccount"}},[a("el-input",{attrs:{placeholder:"注册账户（专票）"},model:{value:e.dataForm.registerAccount,callback:function(a){e.$set(e.dataForm,"registerAccount",a)},expression:"dataForm.registerAccount"}})],1)],1)],1):e._e()],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){e.visible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:function(a){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},o=[],l=(t("a15b"),t("ef6c")),s={data:function(){return{options:l["regionData"],clientType:[],loading:!1,visible:!1,dataForm:{id:0,isBank:!0,isInvoice:!1,name:"",username:"",code:"",address:"",email:"",mobile:"",registerAddress:"",registerTelephone:"",registerBank:"",repeatToken:"",bankAccount:"",bankAddress:"",registerAccount:"",type:0,area:""},dataRule:{name:[{required:!0,message:"客户名称不能为空",trigger:"blur"}]}}},methods:{init:function(e){var a=this;this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id?a.$http({url:a.$http.adornUrl("/client/client/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(e){var t=e.data;t&&200===t.code&&(a.dataForm.name=t.client.name,a.dataForm.username=t.client.username,a.dataForm.code=t.client.code,a.dataForm.address=t.client.address,a.dataForm.email=t.client.email,a.dataForm.mobile=t.client.mobile,a.dataForm.registerAddress=t.client.registerAddress,a.dataForm.registerTelephone=t.client.registerTelephone,a.dataForm.registerBank=t.client.registerBank,a.dataForm.registerAccount=t.client.registerAccount,a.dataForm.bankAccount=t.client.bankAccount,a.dataForm.bankAddress=t.client.bankAddress,a.dataForm.appid=t.client.appid,a.dataForm.type=t.client.type,a.dataForm.area=t.client.area?t.client.area.split(","):[],a.dataForm.bankAccount&&(a.dataForm.isBank=!0),a.dataForm.code&&(a.dataForm.isInvoice=!0))})):(a.dataForm.appid=a.$cookie.get("appid"),a.dataForm.companyRoleId=a.$cookie.get("companyRoleId"))})),this.getToken(),this.getResult()},getToken:function(){var e=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(a){var t=a.data;t&&200===t.code&&(e.dataForm.repeatToken=t.result)}))},getResult:function(){var e=this;this.$http({url:this.$http.adornUrl("/sys/config/findOnlyParamKey"),method:"get",params:this.$http.adornParams({paramKey:"clientType"})}).then((function(a){var t=a.data;t&&200===t.code&&(e.clientType=t.result.paramValue?t.result.paramValue.split(","):[])}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(a){a&&(e.loading=!0,e.$http({url:e.$http.adornUrl("/client/client/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,name:e.dataForm.name,username:e.dataForm.username,code:e.dataForm.code,address:e.dataForm.address,email:e.dataForm.email,mobile:e.dataForm.mobile,registerAddress:e.dataForm.registerAddress,registerTelephone:e.dataForm.registerTelephone,registerBank:e.dataForm.registerBank,bankAddress:e.dataForm.bankAddress,bankAccount:e.dataForm.bankAccount,repeatToken:e.dataForm.repeatToken,appid:e.dataForm.appid,type:e.dataForm.type,area:e.dataForm.area?e.dataForm.area.join(","):"",registerAccount:e.dataForm.registerAccount})}).then((function(a){var t=a.data;t&&200===t.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList",t.result)}}):(e.$message.error(t.msg),"不能重复提交"!=t.msg&&e.getToken()),e.loading=!1})))}))}}},n=s,i=t("2877"),d=Object(i["a"])(n,r,o,!1,null,null,null);a["default"]=d.exports},a15b:function(e,a,t){"use strict";var r=t("23e7"),o=t("e330"),l=t("44ad"),s=t("fc6a"),n=t("a640"),i=o([].join),d=l!==Object,c=d||!n("join",",");r({target:"Array",proto:!0,forced:c},{join:function(e){return i(s(this),void 0===e?",":e)}})}}]);
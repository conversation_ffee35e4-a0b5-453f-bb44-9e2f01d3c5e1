(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3f1d4a86","chunk-2d0a4000"],{"0527":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"}},[e("el-form-item",{attrs:{label:"会议id",prop:"activityId"}},[e("el-input",{attrs:{placeholder:"会议id"},model:{value:t.dataForm.activityId,callback:function(e){t.$set(t.dataForm,"activityId",e)},expression:"dataForm.activityId"}})],1),e("el-form-item",{attrs:{label:"用户id",prop:"userId"}},[e("el-input",{attrs:{placeholder:"用户id"},model:{value:t.dataForm.userId,callback:function(e){t.$set(t.dataForm,"userId",e)},expression:"dataForm.userId"}})],1),e("el-form-item",{attrs:{label:"状态：0-未报名，1-已报名，2-已取消",prop:"status"}},[e("el-input",{attrs:{placeholder:"状态：0-未报名，1-已报名，2-已取消"},model:{value:t.dataForm.status,callback:function(e){t.$set(t.dataForm,"status",e)},expression:"dataForm.status"}})],1),e("el-form-item",{attrs:{label:"姓名",prop:"contact"}},[e("el-input",{attrs:{placeholder:"姓名"},model:{value:t.dataForm.contact,callback:function(e){t.$set(t.dataForm,"contact",e)},expression:"dataForm.contact"}})],1),e("el-form-item",{attrs:{label:"手机",prop:"mobile"}},[e("el-input",{attrs:{placeholder:"手机"},model:{value:t.dataForm.mobile,callback:function(e){t.$set(t.dataForm,"mobile",e)},expression:"dataForm.mobile"}})],1),e("el-form-item",{attrs:{label:"性别",prop:"gender"}},[e("el-input",{attrs:{placeholder:"性别"},model:{value:t.dataForm.gender,callback:function(e){t.$set(t.dataForm,"gender",e)},expression:"dataForm.gender"}})],1),e("el-form-item",{attrs:{label:"身份证",prop:"idCard"}},[e("el-input",{attrs:{placeholder:"身份证"},model:{value:t.dataForm.idCard,callback:function(e){t.$set(t.dataForm,"idCard",e)},expression:"dataForm.idCard"}})],1),e("el-form-item",{attrs:{label:"职称",prop:"duties"}},[e("el-input",{attrs:{placeholder:"职称"},model:{value:t.dataForm.duties,callback:function(e){t.$set(t.dataForm,"duties",e)},expression:"dataForm.duties"}})],1),e("el-form-item",{attrs:{label:"单位",prop:"unit"}},[e("el-input",{attrs:{placeholder:"单位"},model:{value:t.dataForm.unit,callback:function(e){t.$set(t.dataForm,"unit",e)},expression:"dataForm.unit"}})],1),e("el-form-item",{attrs:{label:"扩展字段",prop:"extra"}},[e("el-input",{attrs:{placeholder:"扩展字段"},model:{value:t.dataForm.extra,callback:function(e){t.$set(t.dataForm,"extra",e)},expression:"dataForm.extra"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},i=[],n={data:function(){return{visible:!1,dataForm:{id:0,activityId:"",userId:"",status:"",contact:"",mobile:"",gender:"",idCard:"",duties:"",unit:"",extra:""},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],userId:[{required:!0,message:"用户id不能为空",trigger:"blur"}],status:[{required:!0,message:"状态：0-未报名，1-已报名，2-已取消不能为空",trigger:"blur"}],contact:[{required:!0,message:"姓名不能为空",trigger:"blur"}],mobile:[{required:!0,message:"手机不能为空",trigger:"blur"}],gender:[{required:!0,message:"性别不能为空",trigger:"blur"}],idCard:[{required:!0,message:"身份证不能为空",trigger:"blur"}],duties:[{required:!0,message:"职称不能为空",trigger:"blur"}],unit:[{required:!0,message:"单位不能为空",trigger:"blur"}],extra:[{required:!0,message:"扩展字段不能为空",trigger:"blur"}]}}},methods:{init:function(t){var e=this;this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.dataForm.id&&e.$http({url:e.$http.adornUrl("/activity/activityuser/info/".concat(e.dataForm.id)),method:"get",params:e.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm.activityId=a.activityUser.activityId,e.dataForm.userId=a.activityUser.userId,e.dataForm.status=a.activityUser.status,e.dataForm.contact=a.activityUser.contact,e.dataForm.mobile=a.activityUser.mobile,e.dataForm.gender=a.activityUser.gender,e.dataForm.idCard=a.activityUser.idCard,e.dataForm.duties=a.activityUser.duties,e.dataForm.unit=a.activityUser.unit,e.dataForm.extra=a.activityUser.extra,e.dataForm.createOn=a.activityUser.createOn,e.dataForm.createBy=a.activityUser.createBy,e.dataForm.updateOn=a.activityUser.updateOn,e.dataForm.updateBy=a.activityUser.updateBy)}))}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/activity/activityuser/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,activityId:t.dataForm.activityId,userId:t.dataForm.userId,status:t.dataForm.status,contact:t.dataForm.contact,mobile:t.dataForm.mobile,gender:t.dataForm.gender,idCard:t.dataForm.idCard,duties:t.dataForm.duties,unit:t.dataForm.unit,extra:t.dataForm.extra})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))}}},o=n,d=a("2877"),l=Object(d["a"])(o,r,i,!1,null,null,null);e["default"]=l.exports},"11bd":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"参数名",clearable:""},model:{value:t.dataForm.key,callback:function(e){t.$set(t.dataForm,"key",e)},expression:"dataForm.key"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),t.isAuth("activity:activityuser:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("activity:activityuser:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"activityId","header-align":"center",align:"center",label:"会议id"}}),e("el-table-column",{attrs:{prop:"userId","header-align":"center",align:"center",label:"用户id"}}),e("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",label:"状态：0-未报名，1-已报名，2-已取消"}}),e("el-table-column",{attrs:{prop:"contact","header-align":"center",align:"center",label:"姓名"}}),e("el-table-column",{attrs:{prop:"mobile","header-align":"center",align:"center",label:"手机"}}),e("el-table-column",{attrs:{prop:"gender","header-align":"center",align:"center",label:"性别"}}),e("el-table-column",{attrs:{prop:"idCard","header-align":"center",align:"center",label:"身份证"}}),e("el-table-column",{attrs:{prop:"duties","header-align":"center",align:"center",label:"职称"}}),e("el-table-column",{attrs:{prop:"unit","header-align":"center",align:"center",label:"单位"}}),e("el-table-column",{attrs:{prop:"extra","header-align":"center",align:"center",label:"扩展字段"}}),e("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",label:"创建时间"}}),e("el-table-column",{attrs:{prop:"updateOn","header-align":"center",align:"center",label:"更新时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e()],1)},i=[],n=(a("99af"),a("a15b"),a("d81d"),a("a573"),a("0527")),o={data:function(){return{dataForm:{key:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1}},components:{AddOrUpdate:n["default"]},activated:function(){this.getDataList()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/activity/activityuser/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,key:this.dataForm.key})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(t)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/activity/activityuser/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))}}},d=o,l=a("2877"),s=Object(l["a"])(d,r,i,!1,null,null,null);e["default"]=s.exports},a15b:function(t,e,a){"use strict";var r=a("23e7"),i=a("e330"),n=a("44ad"),o=a("fc6a"),d=a("a640"),l=i([].join),s=n!==Object,c=s||!d("join",",");r({target:"Array",proto:!0,forced:c},{join:function(t){return l(o(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var r=a("23e7"),i=a("d024"),n=a("c430");r({target:"Iterator",proto:!0,real:!0,forced:n},{map:i})},d024:function(t,e,a){"use strict";var r=a("c65b"),i=a("59ed"),n=a("825a"),o=a("46c4"),d=a("c5cc"),l=a("9bdd"),s=d((function(){var t=this.iterator,e=n(r(this.next,t)),a=this.done=!!e.done;if(!a)return l(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return n(this),i(t),new s(o(this),{mapper:t})}},d81d:function(t,e,a){"use strict";var r=a("23e7"),i=a("b727").map,n=a("1dde"),o=n("map");r({target:"Array",proto:!0,forced:!o},{map:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})}}]);
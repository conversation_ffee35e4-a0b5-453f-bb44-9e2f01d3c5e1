(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-57e73d3c"],{"0e94":function(t,a,e){"use strict";e.r(a);e("b0c0");var r=function(){var t=this,a=t._self._c;return a("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(a){t.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"酒店名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"酒店名称"},model:{value:t.dataForm.name,callback:function(a){t.$set(t.dataForm,"name",a)},expression:"dataForm.name"}})],1),a("el-form-item",{attrs:{label:"联系方式",prop:"mobile"}},[a("el-input",{attrs:{placeholder:"联系方式"},model:{value:t.dataForm.mobile,callback:function(a){t.$set(t.dataForm,"mobile",a)},expression:"dataForm.mobile"}})],1),a("el-form-item",{attrs:{label:"星级",prop:"star"}},[[a("el-rate",{attrs:{colors:["#99A9BF","#F7BA2A","#FF9900"]},model:{value:t.dataForm.star,callback:function(a){t.$set(t.dataForm,"star",a)},expression:"dataForm.star"}})]],2),a("el-form-item",{attrs:{label:"所在城市",prop:"cityId"}},[a("el-select",{attrs:{placeholder:"省",filterable:""},on:{change:t.provinceChange},model:{value:t.dataForm.provinceId,callback:function(a){t.$set(t.dataForm,"provinceId",a)},expression:"dataForm.provinceId"}},t._l(t.provinces,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1),a("el-select",{staticStyle:{"margin-left":"10px"},attrs:{placeholder:"市",filterable:""},on:{change:t.getCityName},model:{value:t.dataForm.cityId,callback:function(a){t.$set(t.dataForm,"cityId",a)},expression:"dataForm.cityId"}},t._l(t.cities,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),a("el-form-item",{attrs:{label:"详细地址",prop:"address"}},[a("el-input",{attrs:{placeholder:"详细地址"},model:{value:t.dataForm.address,callback:function(a){t.$set(t.dataForm,"address",a)},expression:"dataForm.address"}})],1),a("el-form-item",{attrs:{label:"图片",prop:"imageUrl"}},[a("el-upload",{staticClass:"avatar-uploader",attrs:{"before-upload":t.checkFileSize,"list-type":"picture-card","show-file-list":!1,accept:".jpg, .jpeg, .png, .gif","on-success":t.appSuccessHandle,action:t.url}},[t.dataForm.imageUrl?a("img",{staticClass:"avatar",attrs:{width:"100px",src:t.dataForm.imageUrl}}):a("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),a("el-form-item",{attrs:{label:"经度",prop:"longitude"}},[a("el-input",{attrs:{placeholder:"经度"},model:{value:t.dataForm.longitude,callback:function(a){t.$set(t.dataForm,"longitude",a)},expression:"dataForm.longitude"}})],1),a("el-form-item",{attrs:{label:"纬度",prop:"latitude"}},[a("el-input",{attrs:{placeholder:"纬度"},model:{value:t.dataForm.latitude,callback:function(a){t.$set(t.dataForm,"latitude",a)},expression:"dataForm.latitude"}})],1),a("a",{staticStyle:{color:"red","margin-left":"50px"},attrs:{target:"_blank",href:"https://lbs.qq.com/tool/getpoint/index.html"}},[t._v("腾讯地图坐标拾取工具")])],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){t.visible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},i=[],o=(e("7db0"),e("d3b7"),e("ac1f"),e("3ca3"),e("5319"),e("0643"),e("fffc"),e("ddb0"),e("7c8d")),d=e.n(o),l={data:function(){return{visible:!1,provinces:[],cities:[],cityName:"",url:"",dataForm:{id:0,name:"",star:"",mobile:"",provinceId:"",cityId:"",address:"",longitude:"",latitude:"",appid:"",imageUrl:""},dataRule:{name:[{required:!0,message:"酒店名称不能为空",trigger:"blur"}],cityId:[{required:!0,message:"城市不能为空",trigger:"blur"}],address:[{required:!0,message:"详细地址不能为空",trigger:"blur"}]}}},components:{TinymceEditor:function(){return Promise.all([e.e("chunk-03be236c"),e.e("chunk-2d0a4b8c")]).then(e.bind(null,"26dc"))}},methods:{init:function(t){var a=this;this.dataForm.id=t||0,this.url=this.$http.adornUrl("/sys/oss/upload?token=".concat(this.$cookie.get("token"))),this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.getProvinces(),a.dataForm.id?a.$http({url:a.$http.adornUrl("/hotel/hotel/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.dataForm.name=e.hotel.name,a.dataForm.star=e.hotel.star,a.dataForm.mobile=e.hotel.mobile,a.dataForm.provinceId=e.hotel.provinceId,a.dataForm.cityId=e.hotel.cityId,a.dataForm.address=e.hotel.address,a.dataForm.longitude=e.hotel.longitude,a.dataForm.latitude=e.hotel.latitude,a.dataForm.imageUrl=e.hotel.imageUrl,a.dataForm.appid=e.hotel.appid,a.$http({url:a.$http.adornUrl("/sys/region/pid/".concat(a.dataForm.provinceId)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code?a.cities=e.list:a.cities=[]})))})):a.dataForm.appid=a.$cookie.get("appid")}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(a){a&&t.$http({url:t.$http.adornUrl("/hotel/hotel/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,name:t.dataForm.name,star:t.dataForm.star,mobile:t.dataForm.mobile,provinceId:t.dataForm.provinceId,cityId:t.dataForm.cityId,address:t.dataForm.address,longitude:t.dataForm.longitude,latitude:t.dataForm.latitude,appid:t.dataForm.appid,imageUrl:t.dataForm.imageUrl})}).then((function(a){var e=a.data;e&&200===e.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(e.msg)}))}))},getProvinces:function(){var t=this;this.$http({url:this.$http.adornUrl("/sys/region/pid/100000"),method:"get",params:this.$http.adornParams()}).then((function(a){var e=a.data;e&&200===e.code?t.provinces=e.list:t.provinces=[]}))},provinceChange:function(t){var a=this;void 0!==t&&(this.cities={},this.dataForm.cityId=void 0,this.dataForm.jieSongCityName=[],this.$http({url:this.$http.adornUrl("/sys/region/pid/".concat(t)),method:"get",params:this.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code?a.cities=e.list:a.cities=[]})))},getCityName:function(t){var a=this.cities.find((function(a){return a.id===t}));this.cityName=a.name.replace("市","")},checkFileSize:function(t){return t.size/1024/1024>6?(this.$message.error("".concat(t.name,"文件大于6MB，请选择小于6MB大小的图片")),!1):!(t.size/1024>100)||new Promise((function(a,e){new d.a(t,{quality:.8,success:function(t){a(t)}})}))},beforeUploadHandle:function(t){if("image/jpg"!==t.type&&"image/jpeg"!==t.type&&"image/png"!==t.type&&"image/gif"!==t.type)return this.$message.error("只支持jpg、png、gif格式的图片！"),!1},appSuccessHandle:function(t,a,e){t&&200===t.code?this.dataForm.imageUrl=t.url:this.$message.error(t.msg)}}},s=l,n=e("2877"),c=Object(n["a"])(s,r,i,!1,null,null,null);a["default"]=c.exports},"7db0":function(t,a,e){"use strict";var r=e("23e7"),i=e("b727").find,o=e("44d2"),d="find",l=!0;d in[]&&Array(1)[d]((function(){l=!1})),r({target:"Array",proto:!0,forced:l},{find:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}}),o(d)},f665:function(t,a,e){"use strict";var r=e("23e7"),i=e("2266"),o=e("59ed"),d=e("825a"),l=e("46c4");r({target:"Iterator",proto:!0,real:!0},{find:function(t){d(this),o(t);var a=l(this),e=0;return i(a,(function(a,r){if(t(a,e++))return r(a)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},fffc:function(t,a,e){"use strict";e("f665")}}]);
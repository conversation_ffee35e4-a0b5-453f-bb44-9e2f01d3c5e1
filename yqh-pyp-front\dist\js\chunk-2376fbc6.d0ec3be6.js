(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2376fbc6"],{a0b2:function(t,n,e){"use strict";e("f7b3")},d487:function(t,n,e){"use strict";e.r(n);e("7f7f");var o=function(){var t=this,n=t._self._c;return n("div",[n("van-card",{staticStyle:{background:"white"},attrs:{thumb:t.merchantInfo.picUrl?t.merchantInfo.picUrl:"van-icon"},scopedSlots:t._u([{key:"num",fn:function(){return[n("span",[t._v(t._s(t.merchantInfo.createOn))])]},proxy:!0}])},[n("div",{staticClass:"title",staticStyle:{"font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.merchantInfo.name))]),n("div",{staticStyle:{"padding-top":"10px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[n("div",[t._v(t._s(t.merchantInfo.brief))])])]),n("div",{staticClass:"content",domProps:{innerHTML:t._s(t.merchantInfo.content)},on:{click:function(n){return t.showImg(n)}}})],1)},s=[],c={components:{},data:function(){return{openid:void 0,merchantId:void 0,merchantInfo:{}}},mounted:function(){this.openid=this.$cookie.get("openid"),this.merchantId=this.$route.query.id,this.getCmsInfo()},methods:{getCmsInfo:function(){var t=this;this.$fly.get("/pyp/web/business/business/info/".concat(this.merchantId)).then((function(n){200==n.code?(t.merchantInfo=n.result,document.title=t.merchantInfo.name):(vant.Toast(n.msg),t.merchantInfo={})}))},showImg:function(t){"IMG"==t.target.tagName&&t.target.src&&vant.ImagePreview({images:[t.target.src],closeable:!0})}}},i=c,a=(e("a0b2"),e("2877")),r=Object(a["a"])(i,o,s,!1,null,"573ea25a",null);n["default"]=r.exports},f7b3:function(t,n,e){}}]);
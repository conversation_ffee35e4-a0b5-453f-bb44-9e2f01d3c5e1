(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8a245f98"],{"230d":function(t,n,e){},"40db":function(t,n,e){"use strict";e.r(n);var a=function(){var t=this,n=t._self._c;return n("div",{staticClass:"content"},[t._m(0),n("div",{staticClass:"tip-container"},[n("van-notice-bar",{attrs:{"left-icon":"info-o",text:"为了更好地为您提供服务，请绑定您的手机号码",color:"#1989fa",background:"#ecf5ff"}})],1),n("van-cell-group",{attrs:{inset:""}},[n("van-field",{attrs:{name:"手机号",label:"手机号",required:"",placeholder:"手机号",rules:[{required:!0,message:"请填写手机号"}]},model:{value:t.dataForm.mobile,callback:function(n){t.$set(t.dataForm,"mobile",n)},expression:"dataForm.mobile"}}),n("van-field",{attrs:{center:"",clearable:"",maxlength:"6",label:"短信验证码",required:"",placeholder:"请输入短信验证码"},scopedSlots:t._u([{key:"button",fn:function(){return[n("van-button",{attrs:{size:"small",type:"primary",disabled:t.waiting},on:{click:function(n){return t.doSendSmsCode()}}},[t.waiting?n("span",[t._v(t._s(t.waitingTime)+"秒后重发")]):n("span",{staticStyle:{"font-size":"13px"}},[t._v("获取验证码")])])]},proxy:!0}]),model:{value:t.dataForm.code,callback:function(n){t.$set(t.dataForm,"code",n)},expression:"dataForm.code"}})],1),n("div",{staticStyle:{margin:"16px"}},[n("van-button",{attrs:{round:"",block:"",type:"info",loading:t.loading,"loading-text":"提交中"},on:{click:t.onSubmit}},[t._v("提交")])],1)],1)},i=[function(){var t=this,n=t._self._c;return n("div",{staticClass:"nav-title",staticStyle:{"margin-top":"8px"}},[n("div",{staticClass:"color"}),n("div",{staticClass:"text"},[t._v("绑定手机号")])])}],o=e("cacf"),s={data:function(){return{returnUrl:"",waitingTime:60,waiting:!1,isSend:!1,loading:!1,timer:null,userInfo:{},dataForm:{mobile:"",code:""}}},mounted:function(){document.title="报名信息填写",this.returnUrl=decodeURIComponent(this.$route.query.returnUrl),this.getUserInfo()},methods:{getUserInfo:function(){var t=this;this.$fly.get("/pyp/wxUser/getUserInfo").then((function(n){200==n.code?(t.userInfo=n.data,t.dataForm.mobile=t.userInfo.mobile):vant.Toast(n.msg)}))},doSendSmsCode:function(){var t=this;return this.dataForm.mobile?Object(o["b"])(this.dataForm.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.dataForm.mobile}).then((function(n){n&&200===n.code?(t.isSend=!0,t.countdown(),vant.Toast("发送验证码成功")):vant.Toast(n.msg)})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var t=this;this.waiting=!0,this.timer=setInterval((function(){t.waitingTime--,t.waitingTime<0&&(clearInterval(t.timer),t.waitingTime=60,t.waiting=!1)}),1e3)},onSubmit:function(){var t=this;return this.dataForm.mobile?Object(o["b"])(this.dataForm.mobile)?this.dataForm.code?/^\d{6}$/.test(this.dataForm.code)?(this.loading=!0,void this.$fly.post("/pyp/sms/sms/verificationCodeAndBind",this.dataForm).then((function(n){n&&200===n.code?(vant.Toast.success("手机号绑定成功！"),setTimeout((function(){location.href=t.returnUrl}),1e3)):vant.Toast(n.msg),t.loading=!1}))):(vant.Toast("验证码格式错误"),!1):(vant.Toast("请输入验证码"),!1):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)}}},r=s,c=(e("95c7"),e("2877")),l=Object(c["a"])(r,a,i,!1,null,"659abbbe",null);n["default"]=l.exports},"95c7":function(t,n,e){"use strict";e("230d")}}]);
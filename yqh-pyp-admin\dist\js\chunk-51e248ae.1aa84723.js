(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-51e248ae"],{"7de9":function(e,t,a){"use strict";a.d(t,"g",(function(){return i})),a.d(t,"f",(function(){return n})),a.d(t,"e",(function(){return l})),a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return d})),a.d(t,"d",(function(){return m}));var i=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],n=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],l=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],r=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],o=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],d=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],m=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]},c69b:function(e,t,a){"use strict";a.r(t);a("b0c0");var i=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"标题",prop:"title"}},[t("el-input",{attrs:{placeholder:"标题"},model:{value:e.dataForm.title,callback:function(t){e.$set(e.dataForm,"title",t)},expression:"dataForm.title"}})],1),t("el-form-item",{attrs:{label:"父id",prop:"pid"}},[t("el-select",{attrs:{placeholder:"父id",filterable:""},model:{value:e.dataForm.pid,callback:function(t){e.$set(e.dataForm,"pid",t)},expression:"dataForm.pid"}},[t("el-option",{attrs:{label:"无",value:"0"}}),e._l(e.cmsList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"移动端图标",prop:"mobileIcon"}},[t("el-upload",{staticClass:"avatar-uploader",attrs:{"before-upload":e.checkFileSize,"list-type":"picture-card","show-file-list":!1,accept:".jpg, .jpeg, .png, .gif","on-success":e.appSuccessHandle,action:e.url}},[e.dataForm.mobileIcon?t("img",{staticClass:"avatar",attrs:{width:"100px",src:e.dataForm.mobileIcon}}):t("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),t("el-form-item",{attrs:{label:"排序",prop:"paixu"}},[t("el-input-number",{attrs:{min:0,max:100,label:"排序"},model:{value:e.dataForm.paixu,callback:function(t){e.$set(e.dataForm,"paixu",t)},expression:"dataForm.paixu"}})],1),t("div",{staticStyle:{color:"red"}},[e._v("配置外部链接后，“模块跳转”和“内容”不生效")]),t("el-form-item",{attrs:{label:"外部链接",prop:"url"}},[t("el-input",{attrs:{placeholder:"外部链接(如果配置内容失效)"},model:{value:e.dataForm.url,callback:function(t){e.$set(e.dataForm,"url",t)},expression:"dataForm.url"}})],1),t("el-form-item",{attrs:{label:"背景颜色",prop:"color"}},[t("el-color-picker",{model:{value:e.dataForm.color,callback:function(t){e.$set(e.dataForm,"color",t)},expression:"dataForm.color"}})],1),t("el-form-item",{attrs:{label:"模块跳转",prop:"model"}},[t("el-select",{attrs:{placeholder:"模块跳转",filterable:""},model:{value:e.dataForm.model,callback:function(t){e.$set(e.dataForm,"model",t)},expression:"dataForm.model"}},[t("el-option",{attrs:{label:"无",value:""}}),e._l(e.modelList,(function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.value}})}))],2)],1),t("el-form-item",{attrs:{label:"内容",prop:"content"}},[t("tinymce-editor",{ref:"editor",model:{value:e.dataForm.content,callback:function(t){e.$set(e.dataForm,"content",t)},expression:"dataForm.content"}})],1),t("el-form-item",{attrs:{label:"动画效果",prop:"animate"}},[t("el-select",{attrs:{placeholder:"动画效果",filterable:""},model:{value:e.dataForm.animate,callback:function(t){e.$set(e.dataForm,"animate",t)},expression:"dataForm.animate"}},[t("el-option",{attrs:{label:"无",value:""}}),e._l(e.animate,(function(e){return t("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})}))],2)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"经度",prop:"longitude"}},[t("el-input",{attrs:{placeholder:"经度"},model:{value:e.dataForm.longitude,callback:function(t){e.$set(e.dataForm,"longitude",t)},expression:"dataForm.longitude"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"纬度",prop:"latitude"}},[t("el-input",{attrs:{placeholder:"纬度"},model:{value:e.dataForm.latitude,callback:function(t){e.$set(e.dataForm,"latitude",t)},expression:"dataForm.latitude"}})],1)],1)],1),t("a",{staticStyle:{color:"red","margin-left":"50px"},attrs:{target:"_blank",href:"https://lbs.qq.com/tool/getpoint/index.html"}},[e._v("腾讯地图坐标拾取工具")])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},n=[],l=(a("d3b7"),a("25f0"),a("3ca3"),a("ddb0"),a("7de9")),r=a("7c8d"),o=a.n(r),d={components:{TinymceEditor:function(){return Promise.all([a.e("chunk-03be236c"),a.e("chunk-2d0a4b8c")]).then(a.bind(null,"26dc"))},OssUploader:function(){return a.e("chunk-2d0e97b1").then(a.bind(null,"8e5c"))}},data:function(){return{animate:l["a"],gradient:l["b"],visible:!1,imgAppDialogVisible:!1,dialogAppImageUrl:"",url:"",cmsList:[],modelList:[{name:"个人中心",value:'{"name": "meMine","query": {"id": "${activityId}"}}'},{name:"会议议程",value:'{"name": "schedulesIndex","query": {"id": "${activityId}"}}'},{name:"会议议程(新)",value:'{"name": "schedulesIndexNew","query": {"id": "${activityId}"}}'},{name:"嘉宾列表",value:'{"name": "schedulesExperts","query": {"id": "${activityId}"}}'},{name:"参会报名",value:'{"name": "applyIndex","query": {"id": "${activityId}"}}'},{name:"酒店预订",value:'{"name": "hotelIndex","query": {"id": "${activityId}"}}'},{name:"会议直播",value:'{"name": "livesIndex","query": {"id": "${activityId}"}}'},{name:"考试&问卷",value:'{"name": "examIndex","query": {"id": "${activityId}"}}'},{name:"展商列表",value:'{"name": "merchantIndex","query": {"id": "${activityId}"}}'},{name:"导航列表",value:'{"name": "divNav","query": {"id": "${activityId}"}}'},{name:"座位查询",value:'{"name": "divZuowei","query": {"id": "${activityId}"}}'},{name:"专家信息确认",value:'{"name": "expertIndexCheck","query": {"id": "${activityId}"}}'}],dataForm:{id:0,title:"",pid:"0",activityId:"",mobileIcon:"",content:"",mobileContent:"",url:"",model:"",color:"",longitude:"",latitude:"",animate:"",paixu:0},dataRule:{title:[{required:!0,message:"标题不能为空",trigger:"blur"}],pid:[{required:!0,message:"父id不能为空",trigger:"blur"}],activityId:[{required:!0,message:"活动表id不能为空",trigger:"blur"}],mobileIcon:[{required:!0,message:"移动端图标不能为空",trigger:"blur"}],paixu:[{required:!0,message:"排序不能为空",trigger:"blur"}]}}},methods:{init:function(e,t){var a=this;this.dataForm.activityId=e,this.dataForm.id=t||0,this.visible=!0,this.url=this.$http.adornUrl("/sys/oss/upload?token=".concat(this.$cookie.get("token"))),this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/cms/cms/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(e){var t=e.data;t&&200===t.code&&(a.dataForm.title=t.cms.title,a.dataForm.pid=t.cms.pid.toString(),a.dataForm.activityId=t.cms.activityId,a.dataForm.mobileIcon=t.cms.mobileIcon,a.dataForm.content=t.cms.content,a.dataForm.mobileContent=t.cms.mobileContent,a.dataForm.url=t.cms.url,a.dataForm.paixu=t.cms.paixu,a.dataForm.model=t.cms.model,a.dataForm.color=t.cms.color,a.dataForm.longitude=t.cms.longitude,a.dataForm.latitude=t.cms.latitude,a.dataForm.animate=t.cms.animate)}))})),this.getCmsByActivityId()},getCmsByActivityId:function(){var e=this;this.$http({url:this.$http.adornUrl("/cms/cms/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.cmsList=a.result)}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$http({url:e.$http.adornUrl("/cms/cms/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,title:e.dataForm.title,pid:e.dataForm.pid,activityId:e.dataForm.activityId,mobileIcon:e.dataForm.mobileIcon,content:e.dataForm.content,mobileContent:e.dataForm.mobileContent,url:e.dataForm.url,model:e.dataForm.model,color:e.dataForm.color,longitude:e.dataForm.longitude,latitude:e.dataForm.latitude,animate:e.dataForm.animate,paixu:e.dataForm.paixu})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(a.msg)}))}))},checkFileSize:function(e){return e.size/1024>110?(this.$message.error("".concat(e.name,"文件大于100KB，请选择小于100KB大小的icon")),!1):!(e.size/1024>100)||new Promise((function(t,a){new o.a(e,{quality:.8,success:function(e){t(e)}})}))},beforeUploadHandle:function(e){if("image/jpg"!==e.type&&"image/jpeg"!==e.type&&"image/png"!==e.type&&"image/gif"!==e.type)return this.$message.error("只支持jpg、png、gif格式的图片！"),!1},appSuccessHandle:function(e,t,a){e&&200===e.code?this.dataForm.mobileIcon=e.url:this.$message.error(e.msg)}}},m=d,c=a("2877"),u=Object(c["a"])(m,i,n,!1,null,null,null);t["default"]=u.exports}}]);
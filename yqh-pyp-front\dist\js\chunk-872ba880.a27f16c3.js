(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-872ba880"],{"11e9":function(t,e,a){var i=a("52a7"),s=a("4630"),r=a("6821"),n=a("6a99"),c=a("69a8"),o=a("c69a"),l=Object.getOwnPropertyDescriptor;e.f=a("9e1e")?l:function(t,e){if(t=r(t),e=n(e,!0),o)try{return l(t,e)}catch(a){}if(c(t,e))return s(!i.f.call(t,e),t[e])}},"28de":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"plane-select"},[e("div",{staticClass:"date-nav"},[e("van-button",{attrs:{plain:"",size:"small",disabled:t.isLoading},on:{click:function(e){return t.switchDay(-1)}}},[t._v("前一天")]),e("div",{staticClass:"current-date"},[t._v(t._s(t.inDate))]),e("van-button",{attrs:{plain:"",size:"small",disabled:t.isLoading},on:{click:function(e){return t.switchDay(1)}}},[t._v("后一天")])],1),t.isLoading?e("div",{staticClass:"loading-overlay"},[e("van-loading",{attrs:{type:"spinner",color:"#1989fa"}}),e("div",{staticClass:"loading-text"},[t._v("航班信息加载中...")])],1):t._e(),t.isLoading||t.showCabins?t._e():[t.flights.length>0?e("div",{staticClass:"flight-list"},t._l(t.flights,(function(a){return e("div",{key:a.flightNo,staticClass:"flight-item",on:{click:function(e){return t.showFlightCabins(a)}}},[e("div",{staticClass:"time-row"},[e("div",{staticClass:"time-info"},[e("span",{staticClass:"departure-time"},[t._v(t._s(a.fromDateTime))]),e("span",{staticClass:"airport-name"},[t._v(t._s(a.fromAirportName))])]),e("div",{staticClass:"flight-duration"},[e("div",{staticClass:"duration-line"}),e("span",{staticClass:"duration-text"},[t._v(t._s(a.flyDuration)+"时")])]),e("div",{staticClass:"time-info text-right"},[e("span",{staticClass:"arrival-time"},[t._v(t._s(a.toDateTime))]),e("span",{staticClass:"airport-name"},[t._v(t._s(a.toAirportName))])])]),e("div",{staticClass:"flight-detail"},[e("div",{staticClass:"flight-number"},[e("span",[t._v(t._s(a.flightNo))]),e("span",{staticClass:"company-name"},[t._v(t._s(a.airlineCompany))])]),e("div",{staticClass:"price-preview"},[e("span",{staticClass:"price-from"},[t._v("¥"+t._s(t.getLowestPrice(a)))]),e("van-icon",{attrs:{name:"arrow"}})],1)])])})),0):e("van-empty",{attrs:{description:"暂无航班信息"}})],!t.isLoading&&t.showCabins?[e("div",{staticClass:"cabin-header"},[e("van-icon",{staticClass:"back-icon",attrs:{name:"arrow-left"},on:{click:t.backToFlightList}}),e("div",{staticClass:"selected-flight-info"},[e("div",{staticClass:"time-row"},[e("div",{staticClass:"time-info"},[e("span",{staticClass:"departure-time"},[t._v(t._s(t.currentFlight.fromDateTime))]),e("span",{staticClass:"airport-name"},[t._v(t._s(t.currentFlight.fromAirportName))])]),e("div",{staticClass:"flight-duration"},[e("div",{staticClass:"duration-line"}),e("span",{staticClass:"duration-text"},[t._v(t._s(t.currentFlight.flyDuration)+"时")])]),e("div",{staticClass:"time-info text-right"},[e("span",{staticClass:"arrival-time"},[t._v(t._s(t.currentFlight.toDateTime))]),e("span",{staticClass:"airport-name"},[t._v(t._s(t.currentFlight.toAirportName))])])]),e("div",{staticClass:"flight-number"},[e("span",[t._v(t._s(t.currentFlight.flightNo))]),e("span",{staticClass:"company-name"},[t._v(t._s(t.currentFlight.airlineCompany))])])])],1),e("div",{staticClass:"cabin-list"},t._l(t.currentFlight.cabins,(function(a){return e("div",{key:a.cabinBookPara,staticClass:"cabin-item",class:{"selected-item":t.selectedFlightItem.cabinBookPara===a.cabinBookPara},on:{click:function(e){return t.selectFlightItem(a)}}},[e("div",{staticClass:"cabin-info"},[e("div",{staticClass:"cabin-left"},[e("span",{staticClass:"cabin-name"},[t._v(t._s(a.cabinName))]),a.cabinDesc?e("span",{staticClass:"cabin-desc"},[t._v(t._s(a.cabinDesc))]):t._e()]),e("div",{staticClass:"cabin-right"},[e("span",{staticClass:"original-price"},[t._v("原价：¥"+t._s(a.cabinPrice.adultFarePrice))]),e("span",{staticClass:"sale-price"},[t._v("售价：¥"+t._s(a.cabinPrice.adultSalePrice))])])])])})),0)]:t._e(),e("div",{staticClass:"bottom-buttons"},[e("van-button",{attrs:{plain:"",block:""},on:{click:t.goBack}},[t._v("取消")]),e("van-button",{attrs:{type:"primary",block:"",disabled:!t.selectedFlightItem.cabinBookPara},on:{click:t.confirmSelection}},[t._v("确定")])],1)],2)},s=[],r=(a("a481"),a("ac6a"),a("c5f6"),a("ade3")),n=a("66c7"),c={name:"PlaneSelect",data:function(){var t;return t={isLoading:!1,inDate:"",startCityCode:"",endCityCode:"",startCity:"",endCity:"",flights:[],showCabins:!1,currentFlight:{},selectedFlightItem:{},from:"",tripId:""},Object(r["a"])(t,"tripId",""),Object(r["a"])(t,"loading",!1),Object(r["a"])(t,"changeForm",{chaDate:"",chaNumber:"",chaEndPlace:"",chaEndDate:"",chaStartDate:"",chaPrice:"",chaStartPlace:"",chaStartCityCode:"",chaEndCityCode:"",chaStartCity:"",chaEndCity:"",chaStartTerminal:"",chaEndTerminal:"",chaTicketNo:"",cabinCode:"",cabinBookPara:"",id:""}),t},mounted:function(){this.inDate=this.$route.query.date||n["a"].formatDate.format(new Date,"yyyy/MM/dd"),this.startCityCode=this.$route.query.startCityCode||"",this.endCityCode=this.$route.query.endCityCode||"",this.startCity=this.$route.query.startCity||"",this.endCity=this.$route.query.endCity||"",this.tripId=this.$route.query.tripId||"",this.changeForm.id=this.tripId,this.changeForm.chaDate=n["a"].formatDate.format(new Date,"yyyy/MM/dd"),this.loadFlights()},methods:{goBack:function(){this.showCabins?this.backToFlightList():this.$router.back()},switchDay:function(t){var e=new Date(this.inDate);e.setDate(e.getDate()+t),this.inDate=n["a"].formatDate.format(e,"yyyy/MM/dd"),this.loadFlights(),this.showCabins=!1},loadFlights:function(){var t=this;this.startCityCode&&this.endCityCode?(this.isLoading=!0,this.flights=[],this.$fly.get("/pyp/panhe/searchPlane",{fromCity:this.startCityCode,toCity:this.endCityCode,fromDate:this.inDate}).then((function(e){t.isLoading=!1,e&&200===e.code?t.flights=e.result||[]:vant.Toast(e.msg||"获取航班信息失败")})).catch((function(){t.isLoading=!1,vant.Toast("网络错误，请重试")}))):vant.Toast("请选择出发地和目的地")},getLowestPrice:function(t){if(!t.cabins||0===t.cabins.length)return"暂无";var e=Number.MAX_VALUE;return t.cabins.forEach((function(t){t.cabinPrice&&t.cabinPrice.adultSalePrice<e&&(e=t.cabinPrice.adultSalePrice)})),e===Number.MAX_VALUE?"暂无":e},showFlightCabins:function(t){this.currentFlight=t,this.showCabins=!0,this.selectedFlightItem={}},backToFlightList:function(){this.showCabins=!1,this.currentFlight={},this.selectedFlightItem={}},selectFlightItem:function(t){this.selectedFlightItem=t},confirmSelection:function(){this.selectedFlightItem.cabinBookPara?(this.changeForm.chaNumber=this.currentFlight.flightNo,this.changeForm.chaStartPlace=this.currentFlight.fromAirportName,this.changeForm.chaEndPlace=this.currentFlight.toAirportName,this.changeForm.chaStartDate=(this.currentFlight.fromDateTime+":00").replaceAll("-","/"),this.changeForm.chaEndDate=(this.currentFlight.toDateTime+":00").replaceAll("-","/"),this.changeForm.chaStartCity=this.startCity,this.changeForm.chaEndCity=this.endCity,this.changeForm.chaStartTerminal=this.currentFlight.fromTerminal||"",this.changeForm.chaEndTerminal=this.currentFlight.toTerminal||"",this.changeForm.cabinCode=this.selectedFlightItem.cabinCode||this.selectedFlightItem.cabinName,this.changeForm.cabinBookPara=this.selectedFlightItem.cabinBookPara,this.changeForm.chaStartCityCode=this.currentFlight.fromAirportCode||this.startCityCode,this.changeForm.chaEndCityCode=this.currentFlight.toAirportCode||this.endCityCode,this.changeForm.price=this.selectedFlightItem.cabinPrice.adultSalePrice,this.submitChangeRequest()):vant.Toast("请选择舱位")},submitChangeRequest:function(){var t=this;this.loading=!0,this.$fly.post("/pyp/panhe/plane/cha",this.changeForm).then((function(e){t.loading=!1,200==e.code?(vant.Toast.success("改签申请提交成功"),setTimeout((function(){t.$router.replace("/schedules/expert/tripList")}),1500)):vant.Toast(e.msg||"提交失败")})).catch((function(){t.loading=!1,vant.Toast("网络错误，请稍后重试")}))}}},o=c,l=(a("e5f3"),a("2877")),h=Object(l["a"])(o,i,s,!1,null,"1fdf3661",null);e["default"]=h.exports},"5dbc":function(t,e,a){var i=a("d3f4"),s=a("8b97").set;t.exports=function(t,e,a){var r,n=e.constructor;return n!==a&&"function"==typeof n&&(r=n.prototype)!==a.prototype&&i(r)&&s&&s(t,r),t}},"66c7":function(t,e,a){"use strict";a("4917"),a("a481");var i=/([yMdhsm])(\1*)/g,s="yyyy-MM-dd";function r(t,e){e-=(t+"").length;for(var a=0;a<e;a++)t="0"+t;return t}e["a"]={formatDate:{format:function(t,e){return e=e||s,e.replace(i,(function(e){switch(e.charAt(0)){case"y":return r(t.getFullYear(),e.length);case"M":return r(t.getMonth()+1,e.length);case"d":return r(t.getDate(),e.length);case"w":return t.getDay()+1;case"h":return r(t.getHours(),e.length);case"m":return r(t.getMinutes(),e.length);case"s":return r(t.getSeconds(),e.length)}}))},parse:function(t,e){var a=e.match(i),s=t.match(/(\d)+/g);if(a.length==s.length){for(var r=new Date(1970,0,1),n=0;n<a.length;n++){var c=parseInt(s[n]),o=a[n];switch(o.charAt(0)){case"y":r.setFullYear(c);break;case"M":r.setMonth(c-1);break;case"d":r.setDate(c);break;case"h":r.setHours(c);break;case"m":r.setMinutes(c);break;case"s":r.setSeconds(c);break}}return r}return null},toWeek:function(t){var e=new Date(t).getDay(),a="";switch(e){case 0:a="s";break;case 1:a="m";break;case 2:a="t";break;case 3:a="w";break;case 4:a="t";break;case 5:a="f";break;case 6:a="s";break}return a}},toUserLook:function(t){var e=Math.floor(t/3600%24),a=Math.floor(t/60%60);return e<1?a+"分":e+"时"+a+"分"}}},"8b97":function(t,e,a){var i=a("d3f4"),s=a("cb7c"),r=function(t,e){if(s(t),!i(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,i){try{i=a("9b43")(Function.call,a("11e9").f(Object.prototype,"__proto__").set,2),i(t,[]),e=!(t instanceof Array)}catch(s){e=!0}return function(t,a){return r(t,a),e?t.__proto__=a:i(t,a),t}}({},!1):void 0),check:r}},9093:function(t,e,a){var i=a("ce10"),s=a("e11e").concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return i(t,s)}},aa77:function(t,e,a){var i=a("5ca1"),s=a("be13"),r=a("79e5"),n=a("fdef"),c="["+n+"]",o="​",l=RegExp("^"+c+c+"*"),h=RegExp(c+c+"*$"),u=function(t,e,a){var s={},c=r((function(){return!!n[t]()||o[t]()!=o})),l=s[t]=c?e(d):n[t];a&&(s[a]=l),i(i.P+i.F*c,"String",s)},d=u.trim=function(t,e){return t=String(s(t)),1&e&&(t=t.replace(l,"")),2&e&&(t=t.replace(h,"")),t};t.exports=u},ac6a:function(t,e,a){for(var i=a("cadf"),s=a("0d58"),r=a("2aba"),n=a("7726"),c=a("32e9"),o=a("84f2"),l=a("2b4c"),h=l("iterator"),u=l("toStringTag"),d=o.Array,f={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},g=s(f),m=0;m<g.length;m++){var p,C=g[m],b=f[C],v=n[C],y=v&&v.prototype;if(y&&(y[h]||c(y,h,d),y[u]||c(y,u,C),o[C]=d,b))for(p in i)y[p]||r(y,p,i[p],!0)}},ade3:function(t,e,a){"use strict";a.d(e,"a",(function(){return n}));var i=a("53ca");function s(t,e){if("object"!==Object(i["a"])(t)||null===t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var s=a.call(t,e||"default");if("object"!==Object(i["a"])(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function r(t){var e=s(t,"string");return"symbol"===Object(i["a"])(e)?e:String(e)}function n(t,e,a){return e=r(e),e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}},c5f6:function(t,e,a){"use strict";var i=a("7726"),s=a("69a8"),r=a("2d95"),n=a("5dbc"),c=a("6a99"),o=a("79e5"),l=a("9093").f,h=a("11e9").f,u=a("86cc").f,d=a("aa77").trim,f="Number",g=i[f],m=g,p=g.prototype,C=r(a("2aeb")(p))==f,b="trim"in String.prototype,v=function(t){var e=c(t,!1);if("string"==typeof e&&e.length>2){e=b?e.trim():d(e,3);var a,i,s,r=e.charCodeAt(0);if(43===r||45===r){if(a=e.charCodeAt(2),88===a||120===a)return NaN}else if(48===r){switch(e.charCodeAt(1)){case 66:case 98:i=2,s=49;break;case 79:case 111:i=8,s=55;break;default:return+e}for(var n,o=e.slice(2),l=0,h=o.length;l<h;l++)if(n=o.charCodeAt(l),n<48||n>s)return NaN;return parseInt(o,i)}}return+e};if(!g(" 0o1")||!g("0b1")||g("+0x1")){g=function(t){var e=arguments.length<1?0:t,a=this;return a instanceof g&&(C?o((function(){p.valueOf.call(a)})):r(a)!=f)?n(new m(v(e)),a,g):v(e)};for(var y,_=a("9e1e")?l(m):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),F=0;_.length>F;F++)s(m,y=_[F])&&!s(g,y)&&u(g,y,h(m,y));g.prototype=p,p.constructor=g,a("2aba")(i,f,g)}},e5f3:function(t,e,a){"use strict";a("f37a")},f37a:function(t,e,a){},fdef:function(t,e){t.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-589a499e"],{"129f":function(e,t,a){"use strict";e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!==e&&t!==t}},"593c":function(e,t,a){"use strict";a.d(t,"h",(function(){return r})),a.d(t,"i",(function(){return i})),a.d(t,"n",(function(){return o})),a.d(t,"f",(function(){return u})),a.d(t,"o",(function(){return n})),a.d(t,"c",(function(){return l})),a.d(t,"m",(function(){return s})),a.d(t,"b",(function(){return d})),a.d(t,"g",(function(){return c})),a.d(t,"j",(function(){return m})),a.d(t,"d",(function(){return y})),a.d(t,"k",(function(){return v})),a.d(t,"l",(function(){return p})),a.d(t,"a",(function(){return k})),a.d(t,"e",(function(){return f}));var r=[{key:0,value:"后台签到"},{key:1,value:"微信扫码签到"}],i=[{key:0,value:"未签到"},{key:1,value:"已签到"},{key:2,value:"已签退"}],o=[{key:0,value:"未报名"},{key:1,value:"已报名"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],u=[{key:0,value:"无需"},{key:1,value:"统一"},{key:2,value:"随机"},{key:3,value:"统一(多个)"}],n=[{key:0,value:"未审核"},{key:1,value:"审核通过"},{key:2,value:"审核不通过"}],l=[{key:0,value:"飞机"},{key:1,value:"火车"},{key:2,value:"其他"}],s=[{key:0,value:"会务组出票"},{key:1,value:"自行出票"}],d=[{key:0,value:"主控"},{key:1,value:"学员管理"},{key:2,value:"专家管理"},{key:3,value:"技术"},{key:4,value:"业务员"},{key:5,value:"兼职"}],c=[{key:0,value:"项目收支"},{key:1,value:"服务费"}],m=[{key:0,value:"大会出票"},{key:1,value:"自行购买，凭票大会报销"}],y=[{key:0,value:"未出票"},{key:1,value:"已出票"}],v=[{key:0,value:"已提交"},{key:1,value:"已预订"},{key:2,value:"已支付,待出票"},{key:4,value:"已出票"},{key:5,value:"不能出票待退款"},{key:7,value:"不能出票已退款"},{key:13,value:"(退票)审核不通过交易结束"},{key:15,value:"(退票)申请中"},{key:16,value:"(退票)已退款交易结束"},{key:22,value:"(改签)审核不通过交易结束"},{key:23,value:"(改签)需补款待支付"},{key:26,value:"(改签)无法改签已退款交易结束"},{key:27,value:"(改签)改签成功交易结束"},{key:28,value:"(改签)改签已取消交易结束"},{key:29,value:"(改签)改签处理中"},{key:99,value:"已取消"}],p=[{key:0,value:"已提交"},{key:1,value:"已预订"},{key:2,value:"占位成功"},{key:4,value:"占位失败"},{key:5,value:"已取消"},{key:8,value:"出票成功"},{key:9,value:"出票失败"},{key:31,value:"改签占位成功"},{key:32,value:"改签占位失败"},{key:34,value:"改签确认出票成功"},{key:35,value:"改签确认出票失败"},{key:37,value:"改签已取消"},{key:21,value:"退票成功"},{key:22,value:"退票失败"},{key:23,value:"已退票未退款"}],k=[{key:0,value:"专家信息"},{key:1,value:"专家任务确认"},{key:2,value:"专家行程"},{key:3,value:"专家接送"},{key:4,value:"专家劳务费信息"},{key:5,value:"专家劳务费签字"},{key:6,value:"其他"},{key:7,value:"活动即将过期"},{key:8,value:"活动已过期"},{key:9,value:"活动续费成功"}],f=[{key:0,value:"未读"},{key:1,value:"已读"}]},"841c":function(e,t,a){"use strict";var r=a("c65b"),i=a("d784"),o=a("825a"),u=a("7234"),n=a("1d80"),l=a("129f"),s=a("577e"),d=a("dc4a"),c=a("14c3");i("search",(function(e,t,a){return[function(t){var a=n(this),i=u(t)?void 0:d(t,e);return i?r(i,t,a):new RegExp(t)[e](s(a))},function(e){var r=o(this),i=s(e),u=a(t,r,i);if(u.done)return u.value;var n=r.lastIndex;l(n,0)||(r.lastIndex=0);var d=c(r,i);return l(r.lastIndex,n)||(r.lastIndex=n),null===d?-1:d.index}]}))},"985b":function(e,t,a){"use strict";a.r(t);a("b0c0"),a("ac1f"),a("841c");var r=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"联系人姓名",prop:"name"}},[t("el-autocomplete",{staticStyle:{width:"100%"},attrs:{"fetch-suggestions":e.search,label:"name",placeholder:"请输入内容"},on:{select:e.selectRsult},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[t("span",{staticStyle:{float:"left"}},[e._v(e._s(a.item.name))]),t("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(a.item.unit))])])}}]),model:{value:e.dataForm.name,callback:function(t){e.$set(e.dataForm,"name",t)},expression:"dataForm.name"}})],1),t("el-form-item",{attrs:{label:"联系人电话",prop:"mobile"}},[t("el-input",{attrs:{placeholder:"联系人电话"},model:{value:e.dataForm.mobile,callback:function(t){e.$set(e.dataForm,"mobile",t)},expression:"dataForm.mobile"}})],1),t("el-form-item",{attrs:{label:"工作单位",prop:"unit"}},[t("el-input",{attrs:{placeholder:"工作单位"},model:{value:e.dataForm.unit,callback:function(t){e.$set(e.dataForm,"unit",t)},expression:"dataForm.unit"}})],1),t("el-form-item",{attrs:{label:"来程类型",prop:"inType"}},[t("el-select",{attrs:{placeholder:"来程类型",filterable:""},model:{value:e.dataForm.inType,callback:function(t){e.$set(e.dataForm,"inType",t)},expression:"dataForm.inType"}},e._l(e.guestGoType,(function(e){return t("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"来程日期",prop:"inDate"}},[t("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy/MM/dd",placeholder:"开始日期"},model:{value:e.dataForm.inDate,callback:function(t){e.$set(e.dataForm,"inDate",t)},expression:"dataForm.inDate"}})],1),t("el-form-item",{attrs:{label:"来程航班/火车号",prop:"inNumber"}},[t("el-input",{attrs:{placeholder:"来程航班/火车号"},model:{value:e.dataForm.inNumber,callback:function(t){e.$set(e.dataForm,"inNumber",t)},expression:"dataForm.inNumber"}})],1),t("el-form-item",{attrs:{label:"返程类型",prop:"outType"}},[t("el-select",{attrs:{placeholder:"返程类型",filterable:""},model:{value:e.dataForm.outType,callback:function(t){e.$set(e.dataForm,"outType",t)},expression:"dataForm.outType"}},e._l(e.guestGoType,(function(e){return t("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"返程日期",prop:"outDate"}},[t("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy/MM/dd",placeholder:"开始日期"},model:{value:e.dataForm.outDate,callback:function(t){e.$set(e.dataForm,"outDate",t)},expression:"dataForm.outDate"}})],1),t("el-form-item",{attrs:{label:"返程航班/火车号",prop:"outNumber"}},[t("el-input",{attrs:{placeholder:"返程航班/火车号"},model:{value:e.dataForm.outNumber,callback:function(t){e.$set(e.dataForm,"outNumber",t)},expression:"dataForm.outNumber"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},i=[],o=(a("d3b7"),a("3ca3"),a("ddb0"),a("7c8d")),u=a.n(o),n=a("593c"),l={components:{TinymceEditor:function(){return Promise.all([a.e("chunk-03be236c"),a.e("chunk-2d0a4b8c")]).then(a.bind(null,"26dc"))}},data:function(){return{guestGoType:n["c"],visible:!1,url:"",loading:!1,searchResult:[],dataForm:{id:0,activityId:"",name:"",mobile:"",unit:"",wxUserId:"",inType:"",inDate:"",inNumber:"",outType:"",outDate:"",outNumber:""},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],name:[{required:!0,message:"联系人姓名不能为空",trigger:"blur"}]},timeout:null}},methods:{init:function(e,t){var a=this;this.dataForm.activityId=e,this.dataForm.content="",this.dataForm.id=t||0,this.visible=!0,this.url=this.$http.adornUrl("/sys/oss/upload?token=".concat(this.$cookie.get("token"))),this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/activity/activityguest/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(e){var t=e.data;t&&200===t.code&&(a.dataForm.activityId=t.activityGuest.activityId,a.dataForm.name=t.activityGuest.name,a.dataForm.mobile=t.activityGuest.mobile,a.dataForm.unit=t.activityGuest.unit,a.dataForm.wxUserId=t.activityGuest.wxUserId,a.dataForm.inType=t.activityGuest.inType,a.dataForm.inDate=t.activityGuest.inDate,a.dataForm.inNumber=t.activityGuest.inNumber,a.dataForm.outType=t.activityGuest.outType,a.dataForm.outDate=t.activityGuest.outDate,a.dataForm.outNumber=t.activityGuest.outNumber,a.dataForm.isSave=!1)}))}))},search:function(e,t){var a=this;""!==e&&(this.loading=!0,this.$http({url:this.$http.adornUrl("/activity/guest/findByName"),method:"get",params:this.$http.adornParams({name:e})}).then((function(e){var r=e.data;a.loading=!1,r&&200===r.code?(a.searchResult=r.result,clearTimeout(a.timeout),a.timeout=setTimeout((function(){t(a.searchResult)}),100*Math.random())):a.$message.error(r.msg)})))},createStateFilter:function(e){return function(t){return 0===t.value.toLowerCase().indexOf(e.toLowerCase())}},selectRsult:function(e){this.dataForm.name=e.name,this.dataForm.mobile=e.mobile,this.dataForm.unit=e.unit,this.dataForm.duties=e.duties,this.dataForm.wxUserId=e.wxUserId,this.dataForm.avatar=e.avatar,this.dataForm.content=e.content,this.dataForm.orderBy=e.orderBy,this.dataForm.idCardZheng=e.idCardZheng,this.dataForm.idCardFan=e.idCardFan,this.dataForm.bank=e.bank,this.dataForm.kaihuhang=e.kaihuhang},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$http({url:e.$http.adornUrl("/activity/activityguest/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,activityId:e.dataForm.activityId,name:e.dataForm.name,mobile:e.dataForm.mobile,unit:e.dataForm.unit,inType:e.dataForm.inType,inDate:e.dataForm.inDate,inNumber:e.dataForm.inNumber,outType:e.dataForm.outType,outDate:e.dataForm.outDate,outNumber:e.dataForm.outNumber,wxUserId:e.dataForm.wxUserId})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):405==a.code?e.$confirm(a.msg,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.dataForm.isSave=!0,e.dataFormSubmit()})):e.$message.error(a.msg)}))}))},checkFileSize:function(e){return e.size/1024/1024>6?(this.$message.error("".concat(e.name,"文件大于6MB，请选择小于6MB大小的图片")),!1):!(e.size/1024>100)||new Promise((function(t,a){new u.a(e,{quality:.8,success:function(e){t(e)}})}))},beforeUploadHandle:function(e){if("image/jpg"!==e.type&&"image/jpeg"!==e.type&&"image/png"!==e.type&&"image/gif"!==e.type)return this.$message.error("只支持jpg、png、gif格式的图片！"),!1},appSuccessHandle:function(e,t,a){e&&200===e.code?this.dataForm.avatar=e.url:this.$message.error(e.msg)},idCardZhengSuccessHandle:function(e,t,a){e&&200===e.code?this.dataForm.idCardZheng=e.url:this.$message.error(e.msg)},idCardFanSuccessHandle:function(e,t,a){e&&200===e.code?this.dataForm.idCardFan=e.url:this.$message.error(e.msg)}}},s=l,d=a("2877"),c=Object(d["a"])(s,r,i,!1,null,null,null);t["default"]=c.exports}}]);
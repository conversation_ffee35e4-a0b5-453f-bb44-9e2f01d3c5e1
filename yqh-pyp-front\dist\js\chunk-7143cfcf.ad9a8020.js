(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7143cfcf"],{"0c4f":function(t,s,a){"use strict";a("475f")},"475f":function(t,s,a){},"5df3":function(t,s,a){"use strict";var e=a("02f4")(!0);a("01f9")(String,"String",(function(t){this._t=String(t),this._i=0}),(function(){var t,s=this._t,a=this._i;return a>=s.length?{value:void 0,done:!0}:(t=e(s,a),this._i+=t.length,{value:t,done:!1})}))},ac6a:function(t,s,a){for(var e=a("cadf"),i=a("0d58"),n=a("2aba"),l=a("7726"),c=a("32e9"),r=a("84f2"),o=a("2b4c"),u=o("iterator"),m=o("toStringTag"),v=r.Array,f={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},d=i(f),p=0;p<d.length;p++){var C,h=d[p],_=f[h],g=l[h],S=g&&g.prototype;if(S&&(S[u]||c(S,u,v),S[m]||c(S,m,h),r[h]=v,_))for(C in e)S[C]||n(S,C,e[C],!0)}},e519:function(t,s,a){"use strict";a.r(s);a("7f7f");var e=function(){var t=this,s=t._self._c;return s("div",{staticClass:"commission-rules-page"},[s("van-nav-bar",{attrs:{title:"我的抽成规则","left-text":"返回","left-arrow":""},on:{"click-left":t.goBack}}),t.loading?s("van-loading",{staticClass:"loading-container",attrs:{vertical:""},scopedSlots:t._u([{key:"icon",fn:function(){return[s("van-icon",{staticClass:"loading-icon",attrs:{name:"spinner"}})]},proxy:!0}],null,!1,1527360106)},[t._v("\n    加载中...\n  ")]):s("div",{staticClass:"content"},[t.salesmanInfo?s("div",{staticClass:"salesman-info-card"},[s("div",{staticClass:"info-header"},[s("van-icon",{attrs:{name:"user-o",size:"20"}}),s("span",{staticClass:"title"},[t._v("业务员信息")])],1),s("div",{staticClass:"info-content"},[s("div",{staticClass:"info-item"},[s("span",{staticClass:"label"},[t._v("姓名：")]),s("span",{staticClass:"value"},[t._v(t._s(t.salesmanInfo.name))])]),s("div",{staticClass:"info-item"},[s("span",{staticClass:"label"},[t._v("编号：")]),s("span",{staticClass:"value"},[t._v(t._s(t.salesmanInfo.code))])]),s("div",{staticClass:"info-item"},[s("span",{staticClass:"label"},[t._v("手机：")]),s("span",{staticClass:"value"},[t._v(t._s(t.salesmanInfo.mobile))])])])]):t._e(),s("div",{staticClass:"rules-section"},[s("div",{staticClass:"section-header"},[s("van-icon",{attrs:{name:"gold-coin-o",size:"20"}}),s("span",{staticClass:"title"},[t._v("抽成规则")])],1),0===t.commissionRules.length?s("div",{staticClass:"empty-state"},[s("van-empty",{attrs:{description:"暂无抽成规则"}})],1):s("div",{staticClass:"rules-list"},t._l(t.commissionRules,(function(a){return s("div",{key:a.id,staticClass:"rule-card"},[s("div",{staticClass:"rule-header"},[s("div",{staticClass:"rule-type"},[s("van-tag",{attrs:{type:1===a.status?"success":"danger"}},[t._v("\n                "+t._s(a.commissionTypeDesc)+"\n              ")])],1),s("div",{staticClass:"rule-status"},[s("span",{class:["status-text",1===a.status?"active":"inactive"]},[t._v("\n                "+t._s(1===a.status?"启用":"禁用")+"\n              ")])])]),s("div",{staticClass:"rule-content"},[s("div",{staticClass:"rule-item"},[s("span",{staticClass:"item-label"},[t._v("计算方式：")]),s("span",{staticClass:"item-value"},[t._v(t._s(a.calculationTypeDesc))])]),1===a.calculationType?s("div",{staticClass:"rule-item"},[s("span",{staticClass:"item-label"},[t._v("抽成比例：")]),s("span",{staticClass:"item-value highlight"},[t._v(t._s(100*a.commissionValue)+"%")])]):t._e(),2===a.calculationType?s("div",{staticClass:"rule-item"},[s("span",{staticClass:"item-label"},[t._v("固定金额：")]),s("span",{staticClass:"item-value highlight"},[t._v("¥"+t._s(a.commissionValue))])]):t._e(),a.minAmount?s("div",{staticClass:"rule-item"},[s("span",{staticClass:"item-label"},[t._v("最低金额：")]),s("span",{staticClass:"item-value"},[t._v("¥"+t._s(a.minAmount))])]):t._e(),a.maxAmount?s("div",{staticClass:"rule-item"},[s("span",{staticClass:"item-label"},[t._v("最高金额：")]),s("span",{staticClass:"item-value"},[t._v("¥"+t._s(a.maxAmount))])]):t._e(),a.remark?s("div",{staticClass:"rule-item"},[s("span",{staticClass:"item-label"},[t._v("备注：")]),s("span",{staticClass:"item-value"},[t._v(t._s(a.remark))])]):t._e()]),s("div",{staticClass:"rule-footer"},[s("span",{staticClass:"create-time"},[t._v("创建时间："+t._s(a.createTime))])])])})),0)]),s("div",{staticClass:"notice-section"},[s("van-notice-bar",{attrs:{"left-icon":"info-o",text:"抽成规则由管理员配置，如有疑问请联系管理员"}})],1)])],1)},i=[],n=(a("ac6a"),a("5df3"),a("96cf"),a("1da1")),l={name:"CommissionRules",data:function(){return{loading:!1,salesmanInfo:null,commissionRules:[]}},created:function(){this.loadData()},methods:{goBack:function(){this.$router.go(-1)},loadData:function(){var t=Object(n["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.loading=!0,t.prev=1,t.next=4,Promise.all([this.loadSalesmanInfo(),this.loadCommissionRules()]);case 4:t.next=10;break;case 6:t.prev=6,t.t0=t["catch"](1),console.error("加载数据失败:",t.t0),this.$toast("加载数据失败");case 10:return t.prev=10,this.loading=!1,t.finish(10);case 13:case"end":return t.stop()}}),t,this,[[1,6,10,13]])})));function s(){return t.apply(this,arguments)}return s}(),loadSalesmanInfo:function(){var t=Object(n["a"])(regeneratorRuntime.mark((function t(){var s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$fly.get("/pyp/web/salesman/checkSalesmanStatus");case 3:s=t.sent,200===s.code&&s.isSalesman?this.salesmanInfo=s.salesman:this.$toast("获取业务员信息失败"),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("获取业务员信息失败:",t.t0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function s(){return t.apply(this,arguments)}return s}(),loadCommissionRules:function(){var t=Object(n["a"])(regeneratorRuntime.mark((function t(){var s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$fly.get("/pyp/web/salesman/getCommissionRules");case 3:s=t.sent,200===s.code?this.commissionRules=s.result||[]:console.log("获取佣金规则失败:",s.msg),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("获取佣金规则失败:",t.t0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function s(){return t.apply(this,arguments)}return s}()}},c=l,r=(a("0c4f"),a("2877")),o=Object(r["a"])(c,e,i,!1,null,"1c7b5176",null);s["default"]=o.exports}}]);
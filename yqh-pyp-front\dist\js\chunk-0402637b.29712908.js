(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0402637b","chunk-37a545c8"],{"1b69":function(t,e,n){"use strict";n.r(e);n("7f7f");var i,a=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{background:"white","margin-bottom":"20px"}},[e("van-row",{attrs:{gutter:"20"}},[e("van-col",{attrs:{span:"16"}},[e("van-swipe",{staticStyle:{width:"100%"},attrs:{autoplay:3e3}},t._l(t.activityInfo.appFileList,(function(t,n){return e("van-swipe-item",{key:n},[e("van-image",{attrs:{width:"100%",src:t.url}})],1)})),1)],1),e("van-col",{attrs:{span:"8"}},[e("div",{staticStyle:{"margin-top":"20px"}},[e("van-cell",{attrs:{title:"会议名称",value:t.activityInfo.name}}),t.activityInfo.address?e("van-cell",{attrs:{title:"会议地点",value:t.activityInfo.address}}):t._e(),e("van-cell",{attrs:{title:"会议开始时间",value:t.activityInfo.startTime}}),e("van-cell",{attrs:{title:"会议结束时间",value:t.activityInfo.endTime}}),e("van-cell",{attrs:{title:"登录信息"},scopedSlots:t._u([{key:"default",fn:function(){return[t.userInfo?e("van-button",{attrs:{type:"primary",size:"small",round:""}},[t._v(t._s(t.userInfo.username)+"("+t._s(t.userInfo.mobile)+")")]):e("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.showLogin}},[t._v("未登录(点击登录)")])]},proxy:!0}])}),"1736999159118508033"!=t.activityId?e("van-cell",{attrs:{title:"报名状态"},scopedSlots:t._u([{key:"default",fn:function(){return[1==t.isPay?e("van-button",{attrs:{type:"primary",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名")]):2==t.isPay?e("van-button",{attrs:{type:"warning",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名未交费")]):e("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.turnApply}},[t._v("未报名")])]},proxy:!0}],null,!1,567203517)}):t._e()],1)])],1),e("van-tabs",{staticClass:"nav",on:{click:t.onClick},model:{value:t.cmsId,callback:function(e){t.cmsId=e},expression:"cmsId"}},t._l(t.cmsList,(function(t){return e("van-tab",{key:t.id,attrs:{title:t.title,name:t.id}})})),1),e("pclogin")],1)},s=[],o=n("ade3"),c=(n("a481"),n("6762"),n("2fdb"),n("cacf")),r=n("7dcb"),u=function(){var t=this,e=t._self._c;return e("van-dialog",{attrs:{title:"登录操作",width:"400px"},on:{confirm:t.login},model:{value:t.showPcLogin,callback:function(e){t.showPcLogin=e},expression:"showPcLogin"}},[e("div",{staticClass:"text-center padding"},[e("van-cell-group",{attrs:{inset:""}},[e("van-field",{attrs:{name:"手机号",label:"手机号",required:"",placeholder:"手机号",rules:[{required:!0,message:"请填写手机号"}]},model:{value:t.mobile,callback:function(e){t.mobile=e},expression:"mobile"}}),"1736999159118508033"!=t.activityId?e("van-field",{attrs:{center:"",clearable:"",maxlength:"6",label:"短信验证码",required:"",placeholder:"请输入短信验证码"},scopedSlots:t._u([{key:"button",fn:function(){return[e("van-button",{attrs:{size:"small",type:"primary",disabled:t.waiting},on:{click:function(e){return t.doSendSmsCode()}}},[t.waiting?e("span",[t._v(t._s(t.waitingTime)+"秒后重发")]):e("span",{staticStyle:{"font-size":"13px"}},[t._v("获取验证码")])])]},proxy:!0}],null,!1,850170227),model:{value:t.code,callback:function(e){t.code=e},expression:"code"}}):t._e()],1)],1)])},l=[],d={name:"pclogin",computed:{showPcLogin:{get:function(){return this.$store.state.user.showPcLogin},set:function(t){this.$store.commit("user/changePcLogin",t)}}},data:function(){return{activityId:void 0,waiting:!1,waitingTime:60,mobile:"",code:""}},mounted:function(){this.activityId=this.$route.query.id},methods:{login:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?this.code||0x181b0f883e693000==this.activityId?/^\d{6}$/.test(this.code)||0x181b0f883e693000==this.activityId?void this.$fly.post("/pyp/web/user/pcLogin",{mobile:this.mobile,code:0x181b0f883e693000==this.activityId?"121212":this.code}).then((function(e){e&&200===e.code?(vant.Toast("登录成功"),t.$store.commit("user/update",e.userInfo),t.showPcLogin=!1,"applyIndex"!=t.$route.name&&"livesDetail"!=t.$route.name||location.reload()):vant.Toast(e.msg)})):(vant.Toast("验证码错误"),!1):(vant.Toast("请输入验证码"),!1):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},doSendSmsCode:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.mobile,activityId:this.activityId}).then((function(e){e&&200===e.code?(t.countdown(),vant.Toast("发送验证码成功")):vant.Toast(e.msg)})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var t=this;this.waiting=!0;var e=window.setInterval((function(){t.waitingTime--,t.waitingTime<0&&(window.clearInterval(e),t.waitingTime=60,t.waiting=!1)}),1e3)}}},v=d,m=n("2877"),h=Object(m["a"])(v,u,l,!1,null,null,null),y=h.exports,f={components:{pclogin:y},data:function(){return{activityId:void 0,bannerIndex:0,cmsId:void 0,cmsList:[],cmsInfo:{},activityInfo:{}}},computed:{userInfo:{get:function(){return this.$store.state.user.userInfo},set:function(t){this.$store.commit("user/update",t)}},isPay:{get:function(){return this.$store.state.apply.isPay},set:function(t){this.$store.commit("apply/update",t)}}},mounted:function(){var t=(new Date).getTime();localStorage.setItem("logAddTime",t+36e5),this.openid=this.$cookie.get("openid"),this.activityId=this.$route.query.id,this.cmsId=sessionStorage.getItem("cmsId")||this.$route.query.cmsId,this.checkLogin(),this.getActivityInfo(),this.activityLogCount()},methods:(i={showLogin:function(){this.$store.commit("user/changePcLogin",!0)},checkLogin:function(){var t=this;this.$fly.get("/pyp/web/user/checkLogin").then((function(e){t.userInfo=e.result,t.userInfo?t.checkApply():t.isPay=0}))},bannerIndexChange:function(t){this.bannerIndex=t},checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(e){200==e.code?(t.isPay=e.isPay,1==t.isPay||2==t.isPay&&vant.Dialog.alert({title:"提示",message:"您有一笔注册费待支付，点击跳转"}).then((function(){t.$router.push({name:"applySuccess",query:{orderId:e.result,id:t.activityId}})}))):vant.Toast(e.msg)}))},changeShowType:function(t){this.$emit("changeShowType",t)},turnApply:function(){var t=this.cmsList.filter((function(t){return t.model&&t.model.includes("applyIndex")}));if(t){var e=t[0];sessionStorage.setItem("cmsId",e.id);var n=e.model.replace("${activityId}",e.activityId);this.$router.push(JSON.parse(n))}else vant.Toast("暂未开启报名")},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(e){if(t.loading=!1,200==e.code){t.activityInfo=e.activity,document.title=t.activityInfo.name;var n=t.activityInfo.startTime,i=new Date(n.replace(/-/g,"/")),a=new Date,s=i.getTime()-a.getTime();t.dateCompare=s>0?s:0,t.getCmsList()}}))},activityLogCount:function(){var t=localStorage.getItem("logAddTime"),e=(new Date).getTime();e>t&&this.$fly.post("/pyp/activity/activityviewlog/count",{activityId:this.activityId,device:r["a"].getVersion()}).then((function(t){}))},getCmsList:function(){var t=this;this.$fly.get("/pyp/cms/cms/findByActivityId/".concat(this.activityId)).then((function(e){t.loading=!1,200==e.code?(t.cmsList=e.result,t.cmsId?t.changeShowType(t.cmsId):t.changeShowType(t.cmsList[0].id)):(vant.Toast(e.msg),t.cmsList=[])}))},onClick:function(t){if(console.log(t),sessionStorage.setItem("cmsId",t),this.cmsInfo=this.cmsList.filter((function(e){return e.id==t}))[0],this.cmsInfo.url&&Object(c["d"])(this.cmsInfo.url))location.href=this.cmsInfo.url;else if(this.cmsInfo.model&&this.isJSON(this.cmsInfo.model)){var e=this.cmsInfo.model.replace("${activityId}",this.cmsInfo.activityId);this.$router.push(JSON.parse(e))}else"cmsIndex"==this.$route.name?this.changeShowType(t):this.$router.push({name:"cmsIndex",query:{id:this.activityId,cmsId:t}})}},Object(o["a"])(i,"bannerIndexChange",(function(t){this.bannerIndex=t})),Object(o["a"])(i,"isJSON",(function(t){try{return JSON.parse(t),!0}catch(e){return!1}})),i)},g=f,p=(n("dd7a"),Object(m["a"])(g,a,s,!1,null,"7bd3d808",null));e["default"]=p.exports},"66c7":function(t,e,n){"use strict";n("4917"),n("a481");var i=/([yMdhsm])(\1*)/g,a="yyyy-MM-dd";function s(t,e){e-=(t+"").length;for(var n=0;n<e;n++)t="0"+t;return t}e["a"]={formatDate:{format:function(t,e){return e=e||a,e.replace(i,(function(e){switch(e.charAt(0)){case"y":return s(t.getFullYear(),e.length);case"M":return s(t.getMonth()+1,e.length);case"d":return s(t.getDate(),e.length);case"w":return t.getDay()+1;case"h":return s(t.getHours(),e.length);case"m":return s(t.getMinutes(),e.length);case"s":return s(t.getSeconds(),e.length)}}))},parse:function(t,e){var n=e.match(i),a=t.match(/(\d)+/g);if(n.length==a.length){for(var s=new Date(1970,0,1),o=0;o<n.length;o++){var c=parseInt(a[o]),r=n[o];switch(r.charAt(0)){case"y":s.setFullYear(c);break;case"M":s.setMonth(c-1);break;case"d":s.setDate(c);break;case"h":s.setHours(c);break;case"m":s.setMinutes(c);break;case"s":s.setSeconds(c);break}}return s}return null},toWeek:function(t){var e=new Date(t).getDay(),n="";switch(e){case 0:n="s";break;case 1:n="m";break;case 2:n="t";break;case 3:n="w";break;case 4:n="t";break;case 5:n="f";break;case 6:n="s";break}return n}},toUserLook:function(t){var e=Math.floor(t/3600%24),n=Math.floor(t/60%60);return e<1?n+"分":e+"时"+n+"分"}}},"7dcb":function(t,e,n){"use strict";n("a481"),n("4917");e["a"]={getVersion:function(){var t=navigator.userAgent||window.navigator.userAgent,e=/HUAWEI|HONOR/gi,n=/[^;]+(?= Build)/gi,i=/CPU iPhone OS \d[_\d]*/gi,a=/CPU OS \d[_\d]*/gi,s=/Windows NT \d[\.\d]*/gi,o=/Linux x\d[_\d]*/gi,c=/Mac OS X \d[_|\.\d]*/gi;return/Android/gi.test(t)?e.test(t)?t.match(e)[0]+t.match(n)[0]:t.match(n)[0]:/iPhone/gi.test(t)?t.match(i)[0].replace(/CPU iPhone OS/gi,"iPhone").replace(/_/g,"."):/iPad/gi.test(t)?t.match(a)[0].replace(/CPU OS/gi,"iPad").replace(/_/g,"."):/Windows/gi.test(t)?"Windows "+(Math.min(parseInt(t.match(s)[0].replace(/Windows NT /,""))+1,10)<7?"XP":Math.min(parseInt(t.match(s)[0].replace(/Windows NT /,""))+1,10)):/Linux/gi.test(t)?t.match(o)[0]:/Macintosh/gi.test(t)?t.match(c)[0].replace(/_/g,"."):"unknown"}}},"7de9":function(t,e,n){"use strict";n.d(e,"j",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"f",(function(){return s})),n.d(e,"b",(function(){return o})),n.d(e,"i",(function(){return c})),n.d(e,"d",(function(){return r})),n.d(e,"a",(function(){return u})),n.d(e,"g",(function(){return l})),n.d(e,"h",(function(){return d})),n.d(e,"e",(function(){return v}));var i=["否","是"],a=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],s=["68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255","68,133,255"],o=[{key:0,value:"飞机",name:"飞机"},{key:1,value:"火车",name:"火车"},{key:2,value:"其他",name:"其他"}],c=[{key:0,value:"会务组出票",name:"会务组出票"},{key:1,value:"自行出票",name:"自行出票"}],r=[{key:0,value:"未出票",name:"未出票"},{key:1,value:"已出票",name:"已出票"}],u=[{key:0,show:!0,value:"图片",name:"图片"},{key:1,show:!0,value:"手填",name:"手填"},{key:2,show:!1,value:"查询",name:"查询"}],l=[{key:0,value:"已提交"},{key:1,value:"已预订"},{key:2,value:"已支付,待出票"},{key:4,value:"已出票"},{key:5,value:"不能出票待退款"},{key:7,value:"不能出票已退款"},{key:13,value:"(退票)审核不通过交易结束"},{key:15,value:"(退票)申请中"},{key:16,value:"(退票)已退款交易结束"},{key:22,value:"(改签)审核不通过交易结束"},{key:23,value:"(改签)需补款待支付"},{key:26,value:"(改签)无法改签已退款交易结束"},{key:27,value:"(改签)改签成功交易结束"},{key:28,value:"(改签)改签已取消交易结束"},{key:29,value:"(改签)改签处理中"},{key:99,value:"已取消"}],d=[{key:0,value:"已提交"},{key:1,value:"已预订"},{key:2,value:"占位成功"},{key:4,value:"占位失败"},{key:5,value:"已取消"},{key:8,value:"出票成功"},{key:9,value:"出票失败"},{key:31,value:"改签占位成功"},{key:32,value:"改签占位失败"},{key:34,value:"改签确认出票成功"},{key:35,value:"改签确认出票失败"},{key:37,value:"改签已取消"},{key:21,value:"退票成功"},{key:22,value:"退票失败"},{key:23,value:"已退票未退款"}],v=[{key:0,value:"大会出票"},{key:1,value:"自行购买，凭票大会报销"}]},ade3:function(t,e,n){"use strict";n.d(e,"a",(function(){return o}));var i=n("53ca");function a(t,e){if("object"!==Object(i["a"])(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var a=n.call(t,e||"default");if("object"!==Object(i["a"])(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function s(t){var e=a(t,"string");return"symbol"===Object(i["a"])(e)?e:String(e)}function o(t,e,n){return e=s(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}},cad8:function(t,e,n){},dd7a:function(t,e,n){"use strict";n("cad8")}}]);
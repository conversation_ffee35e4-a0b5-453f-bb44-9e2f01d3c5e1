(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-42e87a3a"],{"787f":function(e,a,t){"use strict";t.r(a);var i=function(){var e=this,a=e._self._c;return a("el-dialog",{attrs:{title:"签到二维码","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(a){e.visible=a}}},[a("vue-qrcode",{attrs:{options:{width:240},value:e.dataForm.activityUserId}}),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(a){e.visible=!1}}},[e._v("确定")])],1)],1)},n=[],r=(t("b0c0"),t("7de9")),o=t("b2e5"),d=t.n(o),u={data:function(){return{visible:!1,dataForm:{id:0,activityId:"",activityUserId:"",userId:"",status:"",name:"",orderSn:"",source:"",remarks:""},sources:r["f"],orderStatus:r["e"],dataRule:{status:[{required:!0,message:"订单状态不能为空",trigger:"blur"}],source:[{required:!0,message:"支付来源不能为空",trigger:"blur"}]}}},components:{VueQrcode:d.a},methods:{init:function(e){var a=this;this.dataForm.id=e,this.visible=!0,this.$http({url:this.$http.adornUrl("/activity/activityuserapplyorder/info/".concat(this.dataForm.id)),method:"get",params:this.$http.adornParams()}).then((function(e){var t=e.data;t&&200===t.code&&(a.dataForm=t.activityUserApplyOrder)}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(a){a&&e.$http({url:e.$http.adornUrl("/activity/activityuserapplyorder/update"),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,activityId:e.dataForm.activityId,userId:e.dataForm.userId,status:e.dataForm.status,name:e.dataForm.name,activityUserId:e.dataForm.activityUserId,orderSn:e.dataForm.orderSn,source:e.dataForm.source,remarks:e.dataForm.remarks})}).then((function(a){var t=a.data;t&&200===t.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(t.msg)}))}))}}},l=u,s=t("2877"),c=Object(s["a"])(l,i,n,!1,null,null,null);a["default"]=c.exports},"7de9":function(e,a,t){"use strict";t.d(a,"g",(function(){return i})),t.d(a,"f",(function(){return n})),t.d(a,"e",(function(){return r})),t.d(a,"a",(function(){return o})),t.d(a,"b",(function(){return d})),t.d(a,"c",(function(){return u})),t.d(a,"d",(function(){return l}));var i=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],n=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],r=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],o=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],d=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],u=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],l=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]}}]);
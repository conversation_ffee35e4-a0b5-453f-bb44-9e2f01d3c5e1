(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2da5127e","chunk-cefe4872","chunk-0506e191","chunk-0506e191","chunk-2d0d72f0"],{1148:function(t,e,a){"use strict";var n=a("5926"),r=a("577e"),o=a("1d80"),l=RangeError;t.exports=function(t){var e=r(o(this)),a="",s=n(t);if(s<0||s===1/0)throw new l("Wrong number of repetitions");for(;s>0;(s>>>=1)&&(e+=e))1&s&&(a+=e);return a}},"1bbf":function(t,e,a){"use strict";a.r(e);a("b0c0"),a("b680");var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-channel"},[e("el-form",{attrs:{inline:!0,model:t.dataForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.getDataList()}}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"渠道名称",clearable:""},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",[e("el-input",{attrs:{placeholder:"渠道编号",clearable:""},model:{value:t.dataForm.code,callback:function(e){t.$set(t.dataForm,"code",e)},expression:"dataForm.code"}})],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"状态",clearable:""},model:{value:t.dataForm.status,callback:function(e){t.$set(t.dataForm,"status",e)},expression:"dataForm.status"}},[e("el-option",{attrs:{label:"启用",value:"1"}}),e("el-option",{attrs:{label:"禁用",value:"0"}})],1)],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),t.isAuth("channel:channel:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("channel:channel:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1)],1),e("el-row",{staticStyle:{"margin-bottom":"20px"},attrs:{gutter:20}},[e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v(t._s(t.overallStats.channelCount||0))]),e("div",{staticClass:"stats-label"},[t._v("总渠道数")])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v(t._s(t.overallStats.totalSalesmanCount||0))]),e("div",{staticClass:"stats-label"},[t._v("总业务员数")])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v(t._s(t.overallStats.totalOrders||0))]),e("div",{staticClass:"stats-label"},[t._v("总订单数")])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v("¥"+t._s((t.overallStats.totalAmount||0).toFixed(2)))]),e("div",{staticClass:"stats-label"},[t._v("总销售额")])])])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"渠道名称"}}),e("el-table-column",{attrs:{prop:"code","header-align":"center",align:"center",label:"渠道编号"}}),e("el-table-column",{attrs:{prop:"code","header-align":"center",align:"center",label:"后台账号"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v("channel_"+t._s(a.row.code))])]}}])}),e("el-table-column",{attrs:{prop:"contactName","header-align":"center",align:"center",label:"联系人"}}),e("el-table-column",{attrs:{prop:"contactMobile","header-align":"center",align:"center",label:"联系电话"}}),e("el-table-column",{attrs:{prop:"parentName","header-align":"center",align:"center",label:"上级渠道"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(a.row.parentName||"无"))])]}}])}),e("el-table-column",{attrs:{prop:"level","header-align":"center",align:"center",label:"层级"}}),e("el-table-column",{attrs:{prop:"salesmanCount","header-align":"center",align:"center",label:"业务员数"}}),e("el-table-column",{attrs:{prop:"totalOrders","header-align":"center",align:"center",label:"订单数"}}),e("el-table-column",{attrs:{prop:"totalAmount","header-align":"center",align:"center",label:"销售额"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v("¥"+t._s((a.row.totalAmount||0).toFixed(2)))])]}}])}),e("el-table-column",{attrs:{"header-align":"center",align:"center",label:"退款名额",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[1===a.row.refundQuotaEnabled?e("div",[e("el-tag",{attrs:{type:t.getRefundQuotaTagType(a.row),size:"small"}},[t._v(" "+t._s(a.row.refundQuotaUsed||0)+"/"+t._s(a.row.refundQuota||0)+" ")])],1):e("div",[e("el-tag",{attrs:{type:"info",size:"small"}},[t._v("未启用")])],1)]}}])}),e("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",label:"状态"},scopedSlots:t._u([{key:"default",fn:function(a){return[0===a.row.status?e("el-tag",{attrs:{size:"small",type:"danger"}},[t._v("禁用")]):e("el-tag",{attrs:{size:"small",type:"success"}},[t._v("启用")])]}}])}),e("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",width:"180",label:"创建时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"280",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[t.isAuth("channel:channel:list")?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.salesmanHandle(a.row.id)}}},[t._v("业务员")]):t._e(),t.isAuth("channel:channel:list")?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.customerHandle(a.row.id)}}},[t._v("客户")]):t._e(),t.isAuth("channel:refund:view")?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.refundQuotaHandle(a.row.id,a.row.name)}}},[t._v("退款名额")]):t._e(),t.isAuth("channel:channel:update")?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]):t._e(),t.isAuth("channel:channel:delete")?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")]):t._e()]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e(),t.refundQuotaVisible?e("refund-quota-management",{ref:"refundQuotaManagement",on:{refreshDataList:t.getDataList}}):t._e()],1)},r=[],o=(a("99af"),a("a15b"),a("d81d"),a("14d9"),a("a573"),a("7672")),l=a("421f"),s={components:{AddOrUpdate:o["default"],RefundQuotaManagement:l["default"]},data:function(){return{dataForm:{name:"",code:"",status:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],overallStats:{},addOrUpdateVisible:!1,refundQuotaVisible:!1}},activated:function(){this.getDataList(),this.getOverallStats()},methods:{activityHandle:function(t){this.$router.push({name:"channel-activity",query:{channelId:t}})},salesmanHandle:function(t){this.$router.push({name:"salesman-salesman",query:{channelId:t}})},customerHandle:function(t){this.$router.push({name:"channel-customer",query:{channelId:t}})},getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/channel/channel/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,name:this.dataForm.name,code:this.dataForm.code,status:this.dataForm.status})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},getOverallStats:function(){var t=this;this.$http({url:this.$http.adornUrl("/channel/channel/stats"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.overallStats=a.stats||{})}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},refundQuotaHandle:function(t,e){var a=this;this.refundQuotaVisible=!0,this.$nextTick((function(){a.$refs.refundQuotaManagement.init(t,e)}))},getRefundQuotaTagType:function(t){if(!t.refundQuota||0===t.refundQuota)return"info";var e=(t.refundQuotaUsed||0)/t.refundQuota;return e>=1?"danger":e>=.8?"warning":"success"},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(t)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/channel/channel/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))}}},i=s,c=(a("bf51"),a("2877")),d=Object(c["a"])(i,n,r,!1,null,"7db03b9c",null);e["default"]=d.exports},"421f":function(t,e,a){"use strict";a.r(e);a("b680");var n=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"退款名额管理 - ".concat(t.channelName),visible:t.visible,width:"80%","close-on-click-modal":!1,"close-on-press-escape":!1},on:{"update:visible":function(e){t.visible=e}}},[e("el-card",{staticClass:"quota-settings",staticStyle:{"margin-bottom":"20px"}},[e("div",{attrs:{slot:"header"},slot:"header"},[e("span",[t._v("名额设置")]),e("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:t.refreshQuotaSettings}},[t._v("刷新")])],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form",{ref:"quotaForm",attrs:{model:t.quotaForm,rules:t.quotaRules,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"启用名额控制",prop:"enabled"}},[e("el-switch",{attrs:{"active-text":"启用","inactive-text":"禁用"},model:{value:t.quotaForm.enabled,callback:function(e){t.$set(t.quotaForm,"enabled",e)},expression:"quotaForm.enabled"}})],1),t.quotaForm.enabled?e("el-form-item",{attrs:{label:"退款名额",prop:"refundQuota"}},[e("el-input-number",{attrs:{min:0,max:9999,placeholder:"请输入退款名额"},model:{value:t.quotaForm.refundQuota,callback:function(e){t.$set(t.quotaForm,"refundQuota",e)},expression:"quotaForm.refundQuota"}})],1):t._e(),e("el-form-item",[e("el-button",{attrs:{type:"primary",loading:t.quotaSaving},on:{click:t.saveQuotaSettings}},[t._v("保存设置")]),e("el-button",{attrs:{type:"warning",loading:t.batchUpdating},on:{click:t.batchUpdatePermissions}},[t._v("重新分配权限")])],1)],1)],1),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"quota-stats"},[e("h4",[t._v("名额使用统计")]),e("el-row",{attrs:{gutter:10}},[e("el-col",{attrs:{span:8}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.quotaStats.total_quota||0))]),e("div",{staticClass:"stat-label"},[t._v("总名额")])])]),e("el-col",{attrs:{span:8}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.quotaStats.used_quota||0))]),e("div",{staticClass:"stat-label"},[t._v("已使用")])])]),e("el-col",{attrs:{span:8}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.quotaStats.available_quota||0))]),e("div",{staticClass:"stat-label"},[t._v("可用")])])])],1),e("el-row",{staticStyle:{"margin-top":"10px"},attrs:{gutter:10}},[e("el-col",{attrs:{span:8}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.quotaStats.assigned_orders||0))]),e("div",{staticClass:"stat-label"},[t._v("有权限订单")])])]),e("el-col",{attrs:{span:8}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.quotaStats.refunded_orders||0))]),e("div",{staticClass:"stat-label"},[t._v("已退款订单")])])]),e("el-col",{attrs:{span:8}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.quotaStats.total_paid_orders||0))]),e("div",{staticClass:"stat-label"},[t._v("总已支付订单")])])])],1)],1)])],1)],1),e("el-tabs",{on:{"tab-click":t.handleTabClick},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[e("el-tab-pane",{attrs:{label:"有权限订单",name:"eligible"}},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.eligibleLoading,expression:"eligibleLoading"}],staticStyle:{width:"100%"},attrs:{data:t.eligibleOrders,border:""}},[e("el-table-column",{attrs:{prop:"order_sn","header-align":"center",align:"center",label:"订单号",width:"180"}}),e("el-table-column",{attrs:{prop:"user_name","header-align":"center",align:"center",label:"用户"}}),e("el-table-column",{attrs:{prop:"user_mobile","header-align":"center",align:"center",label:"手机号"}}),e("el-table-column",{attrs:{prop:"salesman_name","header-align":"center",align:"center",label:"业务员"}}),e("el-table-column",{attrs:{prop:"pay_amount","header-align":"center",align:"center",label:"支付金额"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v("¥"+t._s((a.row.pay_amount||0).toFixed(2)))])]}}])}),e("el-table-column",{attrs:{prop:"quota_sequence","header-align":"center",align:"center",label:"权限序号",width:"80"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{size:"small",type:"success"}},[t._v(t._s(a.row.quota_sequence))])]}}])}),e("el-table-column",{attrs:{prop:"create_on","header-align":"center",align:"center",label:"下单时间",width:"160"}}),e("el-table-column",{attrs:{"header-align":"center",align:"center",label:"操作",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.releasePermission(a.row)}}},[t._v("释放权限")])]}}])})],1)],1),e("el-tab-pane",{attrs:{label:"使用记录",name:"records"}},[e("div",{staticStyle:{"margin-bottom":"10px"}},[e("el-form",{attrs:{inline:!0,model:t.recordForm}},[e("el-form-item",{attrs:{label:"订单号"}},[e("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"订单号",clearable:""},model:{value:t.recordForm.orderSn,callback:function(e){t.$set(t.recordForm,"orderSn",e)},expression:"recordForm.orderSn"}})],1),e("el-form-item",{attrs:{label:"操作类型"}},[e("el-select",{staticStyle:{width:"120px"},attrs:{placeholder:"操作类型",clearable:""},model:{value:t.recordForm.actionType,callback:function(e){t.$set(t.recordForm,"actionType",e)},expression:"recordForm.actionType"}},[e("el-option",{attrs:{label:"分配权限",value:"1"}}),e("el-option",{attrs:{label:"释放权限",value:"2"}})],1)],1),e("el-form-item",[e("el-button",{on:{click:t.getRecordList}},[t._v("查询")]),e("el-button",{on:{click:t.resetRecordForm}},[t._v("重置")])],1)],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.recordLoading,expression:"recordLoading"}],staticStyle:{width:"100%"},attrs:{data:t.recordList,border:""}},[e("el-table-column",{attrs:{prop:"order_sn","header-align":"center",align:"center",label:"订单号",width:"180"}}),e("el-table-column",{attrs:{prop:"user_name","header-align":"center",align:"center",label:"用户"}}),e("el-table-column",{attrs:{prop:"salesman_name","header-align":"center",align:"center",label:"业务员"}}),e("el-table-column",{attrs:{prop:"action_type","header-align":"center",align:"center",label:"操作类型",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:1===a.row.action_type?"success":"warning",size:"small"}},[t._v(" "+t._s(1===a.row.action_type?"分配权限":"释放权限")+" ")])]}}])}),e("el-table-column",{attrs:{prop:"quota_sequence","header-align":"center",align:"center",label:"序号",width:"80"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.quota_sequence?e("span",[t._v(t._s(a.row.quota_sequence))]):e("span",[t._v("-")])]}}])}),e("el-table-column",{attrs:{prop:"create_time","header-align":"center",align:"center",label:"操作时间",width:"160"}}),e("el-table-column",{attrs:{prop:"remarks","header-align":"center",align:"center",label:"备注"}})],1),e("el-pagination",{attrs:{"current-page":t.recordPageIndex,"page-sizes":[10,20,50,100],"page-size":t.recordPageSize,total:t.recordTotalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.recordSizeChangeHandle,"current-change":t.recordCurrentChangeHandle}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("关闭")])],1)],1)},r=[],o=(a("b0c0"),{data:function(){return{visible:!1,channelId:null,channelName:"",activeTab:"eligible",quotaForm:{enabled:!1,refundQuota:0},quotaRules:{refundQuota:[{required:!0,message:"请输入退款名额",trigger:"blur"},{type:"number",min:0,message:"退款名额不能小于0",trigger:"blur"}]},quotaSaving:!1,batchUpdating:!1,quotaStats:{},eligibleOrders:[],eligibleLoading:!1,recordForm:{orderSn:"",actionType:""},recordList:[],recordPageIndex:1,recordPageSize:10,recordTotalPage:0,recordLoading:!1}},methods:{init:function(t,e){var a=this;this.channelId=t,this.channelName=e,this.visible=!0,this.activeTab="eligible",this.$nextTick((function(){a.refreshQuotaSettings(),a.getEligibleOrders()}))},refreshQuotaSettings:function(){var t=this;this.$http({url:this.$http.adornUrl("/channel/refund-permission/quota-usage/".concat(this.channelId)),method:"get"}).then((function(e){var a=e.data;a&&200===a.code&&(t.quotaStats=a.data||{},t.$http({url:t.$http.adornUrl("/channel/channel/info/".concat(t.channelId)),method:"get"}).then((function(e){var a=e.data;if(a&&200===a.code){var n=a.channel;t.quotaForm.enabled=1===n.refundQuotaEnabled,t.quotaForm.refundQuota=n.refundQuota||0}})))}))},saveQuotaSettings:function(){var t=this;this.$refs.quotaForm.validate((function(e){e&&(t.quotaSaving=!0,t.$http({url:t.$http.adornUrl("/channel/refund-permission/update-quota"),method:"post",data:t.$http.adornData({channelId:t.channelId,refundQuota:t.quotaForm.refundQuota,enabled:t.quotaForm.enabled})}).then((function(e){var a=e.data;t.quotaSaving=!1,a&&200===a.code?(t.$message.success("设置保存成功"),t.refreshQuotaSettings(),t.getEligibleOrders(),t.$emit("refreshDataList")):t.$message.error(a.msg||"保存失败")})).catch((function(){t.quotaSaving=!1})))}))},batchUpdatePermissions:function(){var t=this;this.$confirm("确定要重新分配该渠道的退款权限吗？这将清除现有权限分配并重新计算。","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.batchUpdating=!0,t.$http({url:t.$http.adornUrl("/channel/refund-permission/batch-update/".concat(t.channelId)),method:"post"}).then((function(e){var a=e.data;t.batchUpdating=!1,a&&200===a.code?(t.$message.success("权限重新分配成功"),t.refreshQuotaSettings(),t.getEligibleOrders(),t.$emit("refreshDataList")):t.$message.error(a.msg||"重新分配失败")})).catch((function(){t.batchUpdating=!1}))}))},getEligibleOrders:function(){var t=this;this.eligibleLoading=!0,this.$http({url:this.$http.adornUrl("/channel/refund-permission/eligible-orders/".concat(this.channelId)),method:"get"}).then((function(e){var a=e.data;t.eligibleLoading=!1,a&&200===a.code&&(t.eligibleOrders=a.data||[])})).catch((function(){t.eligibleLoading=!1}))},releasePermission:function(t){var e=this;this.$confirm("确定要释放订单 ".concat(t.order_sn," 的退款权限吗？"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/channel/refund-permission/release-permission/".concat(t.order_id)),method:"post"}).then((function(t){var a=t.data;a&&200===a.code?(e.$message.success("权限释放成功"),e.refreshQuotaSettings(),e.getEligibleOrders(),e.$emit("refreshDataList")):e.$message.error(a.msg||"释放失败")}))}))},handleTabClick:function(t){"records"===t.name&&this.getRecordList()},getRecordList:function(){var t=this;this.recordLoading=!0,this.$http({url:this.$http.adornUrl("/channel/refund-permission/quota-records"),method:"get",params:this.$http.adornParams({page:this.recordPageIndex,limit:this.recordPageSize,channelId:this.channelId,orderSn:this.recordForm.orderSn,actionType:this.recordForm.actionType})}).then((function(e){var a=e.data;t.recordLoading=!1,a&&200===a.code&&(t.recordList=a.page.list,t.recordTotalPage=a.page.totalCount)})).catch((function(){t.recordLoading=!1}))},resetRecordForm:function(){this.recordForm.orderSn="",this.recordForm.actionType="",this.recordPageIndex=1,this.getRecordList()},recordSizeChangeHandle:function(t){this.recordPageSize=t,this.recordPageIndex=1,this.getRecordList()},recordCurrentChangeHandle:function(t){this.recordPageIndex=t,this.getRecordList()}}}),l=o,s=(a("8ae0"),a("2877")),i=Object(s["a"])(l,n,r,!1,null,"bda983d0",null);e["default"]=i.exports},7672:function(t,e,a){"use strict";a.r(e);a("a4d3"),a("e01a"),a("b0c0");var n=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.dataFormSubmit()}}},[e("el-form-item",{attrs:{label:"渠道名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"渠道名称"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",{attrs:{label:"渠道编号",prop:"code"}},[e("el-input",{attrs:{placeholder:"渠道编号"},model:{value:t.dataForm.code,callback:function(e){t.$set(t.dataForm,"code",e)},expression:"dataForm.code"}})],1),e("el-form-item",{attrs:{label:"上级渠道",prop:"parentId"}},[e("el-select",{attrs:{placeholder:"请选择上级渠道",clearable:""},model:{value:t.dataForm.parentId,callback:function(e){t.$set(t.dataForm,"parentId",e)},expression:"dataForm.parentId"}},t._l(t.parentChannelList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"联系人",prop:"contactName"}},[e("el-input",{attrs:{placeholder:"联系人姓名"},model:{value:t.dataForm.contactName,callback:function(e){t.$set(t.dataForm,"contactName",e)},expression:"dataForm.contactName"}})],1),e("el-form-item",{attrs:{label:"联系电话",prop:"contactMobile"}},[e("el-input",{attrs:{placeholder:"联系电话"},model:{value:t.dataForm.contactMobile,callback:function(e){t.$set(t.dataForm,"contactMobile",e)},expression:"dataForm.contactMobile"}})],1),e("el-form-item",{attrs:{label:"联系邮箱",prop:"contactEmail"}},[e("el-input",{attrs:{placeholder:"联系邮箱"},model:{value:t.dataForm.contactEmail,callback:function(e){t.$set(t.dataForm,"contactEmail",e)},expression:"dataForm.contactEmail"}})],1),e("el-form-item",{attrs:{label:"渠道地址",prop:"address"}},[e("el-input",{attrs:{placeholder:"渠道地址"},model:{value:t.dataForm.address,callback:function(e){t.$set(t.dataForm,"address",e)},expression:"dataForm.address"}})],1),e("el-form-item",{attrs:{label:"佣金比例",prop:"commissionRate"}},[e("el-input-number",{attrs:{precision:4,step:1e-4,max:1,min:0,placeholder:"佣金比例"},model:{value:t.dataForm.commissionRate,callback:function(e){t.$set(t.dataForm,"commissionRate",e)},expression:"dataForm.commissionRate"}}),e("span",{staticStyle:{"margin-left":"10px",color:"#999"}},[t._v("范围：0-1，如0.05表示5%")])],1),e("el-form-item",{attrs:{label:"退款名额控制",prop:"refundQuotaEnabled"}},[e("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},model:{value:t.dataForm.refundQuotaEnabled,callback:function(e){t.$set(t.dataForm,"refundQuotaEnabled",e)},expression:"dataForm.refundQuotaEnabled"}})],1),1===t.dataForm.refundQuotaEnabled?e("el-form-item",{attrs:{label:"退款名额",prop:"refundQuota"}},[e("el-input-number",{attrs:{min:0,max:9999,placeholder:"退款名额"},model:{value:t.dataForm.refundQuota,callback:function(e){t.$set(t.dataForm,"refundQuota",e)},expression:"dataForm.refundQuota"}}),e("span",{staticStyle:{"margin-left":"10px",color:"#999"}},[t._v("该渠道可退款的订单数量上限")])],1):t._e(),e("el-form-item",{attrs:{label:"状态",prop:"status"}},[e("el-radio-group",{model:{value:t.dataForm.status,callback:function(e){t.$set(t.dataForm,"status",e)},expression:"dataForm.status"}},[e("el-radio",{attrs:{label:0}},[t._v("禁用")]),e("el-radio",{attrs:{label:1}},[t._v("启用")])],1)],1),e("el-form-item",{attrs:{label:"渠道描述",prop:"description"}},[e("el-input",{attrs:{type:"textarea",placeholder:"渠道描述"},model:{value:t.dataForm.description,callback:function(e){t.$set(t.dataForm,"description",e)},expression:"dataForm.description"}})],1),e("el-form-item",{attrs:{label:"备注",prop:"remarks"}},[e("el-input",{attrs:{type:"textarea",placeholder:"备注信息"},model:{value:t.dataForm.remarks,callback:function(e){t.$set(t.dataForm,"remarks",e)},expression:"dataForm.remarks"}})],1),e("el-divider",[t._v("管理员账号信息")]),t.dataForm.id?t._e():e("el-alert",{staticStyle:{"margin-bottom":"20px"},attrs:{title:"系统将自动创建渠道管理员账号",type:"info",closable:!1}},[e("template",{slot:"default"},[e("p",[e("strong",[t._v("用户名：")]),t._v("channel_"+t._s(t.dataForm.code||"[渠道编号]"))]),e("p",[e("strong",[t._v("默认密码：")]),t._v("123456")]),e("p",[e("strong",[t._v("说明：")]),t._v("渠道创建成功后，管理员可使用此账号登录系统管理渠道业务员")])])],2),t.dataForm.id?e("el-alert",{staticStyle:{"margin-bottom":"20px"},attrs:{title:"管理员账号管理",type:"warning",closable:!1}},[e("template",{slot:"default"},[e("p",[e("strong",[t._v("当前用户名：")]),t._v("channel_"+t._s(t.originalCode||t.dataForm.code))]),t.dataForm.code!==t.originalCode?e("p",[e("strong",[t._v("更新后用户名：")]),t._v("channel_"+t._s(t.dataForm.code))]):t._e(),e("p",[e("strong",[t._v("说明：")]),t._v("如果修改了渠道编号，管理员用户名也会相应更新")]),e("p",[e("strong",[t._v("管理：")]),t._v('可在"渠道管理员"页面进行密码重置等操作')])])],2):t._e()],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],o=(a("4de4"),a("d3b7"),a("0643"),a("2382"),{data:function(){return{visible:!1,dataForm:{id:0,name:"",code:"",parentId:"",contactName:"",contactMobile:"",contactEmail:"",address:"",commissionRate:0,refundQuotaEnabled:0,refundQuota:0,status:1,description:"",remarks:""},parentChannelList:[],originalCode:"",dataRule:{name:[{required:!0,message:"渠道名称不能为空",trigger:"blur"}],code:[{required:!0,message:"渠道编号不能为空",trigger:"blur"}],contactName:[{required:!0,message:"联系人不能为空",trigger:"blur"}],contactMobile:[{required:!0,message:"联系电话不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确",trigger:"blur"}],contactEmail:[{type:"email",message:"邮箱格式不正确",trigger:"blur"}],refundQuota:[{type:"number",min:0,message:"退款名额不能小于0",trigger:"blur"}]}}},methods:{init:function(t){var e=this;this.dataForm.id=t||0,this.visible=!0,this.getParentChannelList(),this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.dataForm.id&&e.$http({url:e.$http.adornUrl("/channel/channel/info/".concat(e.dataForm.id)),method:"get",params:e.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm.name=a.channel.name,e.dataForm.code=a.channel.code,e.dataForm.parentId=a.channel.parentId,e.dataForm.contactName=a.channel.contactName,e.dataForm.contactMobile=a.channel.contactMobile,e.dataForm.contactEmail=a.channel.contactEmail,e.dataForm.address=a.channel.address,e.dataForm.commissionRate=a.channel.commissionRate,e.dataForm.status=a.channel.status,e.dataForm.description=a.channel.description,e.dataForm.remarks=a.channel.remarks,e.originalCode=a.channel.code)}))}))},getParentChannelList:function(){var t=this;this.$http({url:this.$http.adornUrl("/channel/channel/select"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.parentChannelList=a.channelList||[],t.dataForm.id&&(t.parentChannelList=t.parentChannelList.filter((function(e){return e.id!==t.dataForm.id}))))}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/channel/channel/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,name:t.dataForm.name,code:t.dataForm.code,parentId:t.dataForm.parentId||null,contactName:t.dataForm.contactName,contactMobile:t.dataForm.contactMobile,contactEmail:t.dataForm.contactEmail,address:t.dataForm.address,commissionRate:t.dataForm.commissionRate,status:t.dataForm.status,description:t.dataForm.description,remarks:t.dataForm.remarks})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))}}}),l=o,s=a("2877"),i=Object(s["a"])(l,n,r,!1,null,null,null);e["default"]=i.exports},"8ae0":function(t,e,a){"use strict";a("c671")},a15b:function(t,e,a){"use strict";var n=a("23e7"),r=a("e330"),o=a("44ad"),l=a("fc6a"),s=a("a640"),i=r([].join),c=o!==Object,d=c||!s("join",",");n({target:"Array",proto:!0,forced:d},{join:function(t){return i(l(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var n=a("23e7"),r=a("d024"),o=a("c430");n({target:"Iterator",proto:!0,real:!0,forced:o},{map:r})},abf6:function(t,e,a){},b680:function(t,e,a){"use strict";var n=a("23e7"),r=a("e330"),o=a("5926"),l=a("408a"),s=a("1148"),i=a("d039"),c=RangeError,d=String,u=Math.floor,m=r(s),p=r("".slice),h=r(1..toFixed),g=function(t,e,a){return 0===e?a:e%2===1?g(t,e-1,a*t):g(t*t,e/2,a)},f=function(t){var e=0,a=t;while(a>=4096)e+=12,a/=4096;while(a>=2)e+=1,a/=2;return e},b=function(t,e,a){var n=-1,r=a;while(++n<6)r+=e*t[n],t[n]=r%1e7,r=u(r/1e7)},v=function(t,e){var a=6,n=0;while(--a>=0)n+=t[a],t[a]=u(n/e),n=n%e*1e7},_=function(t){var e=6,a="";while(--e>=0)if(""!==a||0===e||0!==t[e]){var n=d(t[e]);a=""===a?n:a+m("0",7-n.length)+n}return a},F=i((function(){return"0.000"!==h(8e-5,3)||"1"!==h(.9,0)||"1.25"!==h(1.255,2)||"1000000000000000128"!==h(0xde0b6b3a7640080,0)}))||!i((function(){h({})}));n({target:"Number",proto:!0,forced:F},{toFixed:function(t){var e,a,n,r,s=l(this),i=o(t),u=[0,0,0,0,0,0],h="",F="0";if(i<0||i>20)throw new c("Incorrect fraction digits");if(s!==s)return"NaN";if(s<=-1e21||s>=1e21)return d(s);if(s<0&&(h="-",s=-s),s>1e-21)if(e=f(s*g(2,69,1))-69,a=e<0?s*g(2,-e,1):s/g(2,e,1),a*=4503599627370496,e=52-e,e>0){b(u,0,a),n=i;while(n>=7)b(u,1e7,0),n-=7;b(u,g(10,n,1),0),n=e-1;while(n>=23)v(u,1<<23),n-=23;v(u,1<<n),b(u,1,1),v(u,2),F=_(u)}else b(u,0,a),b(u,1<<-e,0),F=_(u)+m("0",i);return i>0?(r=F.length,F=h+(r<=i?"0."+m("0",i-r)+F:p(F,0,r-i)+"."+p(F,r-i))):F=h+F,F}})},bf51:function(t,e,a){"use strict";a("abf6")},c671:function(t,e,a){},d024:function(t,e,a){"use strict";var n=a("c65b"),r=a("59ed"),o=a("825a"),l=a("46c4"),s=a("c5cc"),i=a("9bdd"),c=s((function(){var t=this.iterator,e=o(n(this.next,t)),a=this.done=!!e.done;if(!a)return i(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return o(this),r(t),new c(l(this),{mapper:t})}},d81d:function(t,e,a){"use strict";var n=a("23e7"),r=a("b727").map,o=a("1dde"),l=o("map");n({target:"Array",proto:!0,forced:!l},{map:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})}}]);
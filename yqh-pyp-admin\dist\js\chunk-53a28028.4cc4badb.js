(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-53a28028","chunk-0506e191","chunk-0506e191"],{1148:function(t,e,s){"use strict";var a=s("5926"),r=s("577e"),n=s("1d80"),i=RangeError;t.exports=function(t){var e=r(n(this)),s="",l=a(t);if(l<0||l===1/0)throw new i("Wrong number of repetitions");for(;l>0;(l>>>=1)&&(e+=e))1&l&&(s+=e);return s}},"284d":function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"".concat(t.salesmanName," - 绑定客户列表"),"close-on-click-modal":!1,visible:t.visible,width:"80%"},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{staticClass:"search-form",attrs:{inline:!0,model:t.searchForm}},[e("el-form-item",{attrs:{label:"客户昵称"}},[e("el-input",{attrs:{placeholder:"请输入客户昵称",clearable:""},model:{value:t.searchForm.wxUserName,callback:function(e){t.$set(t.searchForm,"wxUserName",e)},expression:"searchForm.wxUserName"}})],1),e("el-form-item",{attrs:{label:"手机号"}},[e("el-input",{attrs:{placeholder:"请输入手机号",clearable:""},model:{value:t.searchForm.mobile,callback:function(e){t.$set(t.searchForm,"mobile",e)},expression:"searchForm.mobile"}})],1),e("el-form-item",{attrs:{label:"绑定状态"}},[e("el-select",{attrs:{placeholder:"全部",clearable:""},model:{value:t.searchForm.status,callback:function(e){t.$set(t.searchForm,"status",e)},expression:"searchForm.status"}},[e("el-option",{attrs:{label:"有效",value:1}}),e("el-option",{attrs:{label:"已失效",value:0}}),e("el-option",{attrs:{label:"已解绑",value:2}})],1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.getCustomerList()}}},[t._v("查询")]),e("el-button",{on:{click:function(e){return t.resetSearch()}}},[t._v("重置")])],1)],1),e("div",{staticClass:"stats-overview"},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v(t._s(t.customerStats.totalCustomers||0))]),e("div",{staticClass:"stats-label"},[t._v("总客户数")])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v(t._s(t.customerStats.activeCustomers||0))]),e("div",{staticClass:"stats-label"},[t._v("有效绑定")])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v(t._s(t.customerStats.todayBindings||0))]),e("div",{staticClass:"stats-label"},[t._v("今日新增")])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v("¥"+t._s(t.customerStats.totalOrderAmount||0))]),e("div",{staticClass:"stats-label"},[t._v("订单总额")])])])],1)],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:t.customerList,border:""}},[e("el-table-column",{attrs:{prop:"wxUserName","header-align":"center",align:"center",label:"客户信息"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"customer-info"},[e("div",{staticClass:"customer-name"},[t._v(t._s(s.row.wxUserName))]),e("div",{staticClass:"customer-mobile"},[t._v(t._s(s.row.wxUserMobile||"未绑定手机"))])])]}}])}),e("el-table-column",{attrs:{prop:"bindingType","header-align":"center",align:"center",label:"绑定方式"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-tag",{attrs:{type:t.getBindingTypeTagType(s.row.bindingType)}},[t._v(" "+t._s(t.getBindingTypeText(s.row.bindingType))+" ")])]}}])}),e("el-table-column",{attrs:{prop:"bindingTime","header-align":"center",align:"center",width:"150",label:"绑定时间"}}),e("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",label:"状态"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-tag",{attrs:{type:t.getStatusTagType(s.row.status)}},[t._v(" "+t._s(t.getStatusText(s.row.status))+" ")])]}}])}),e("el-table-column",{attrs:{prop:"expiryTime","header-align":"center",align:"center",width:"150",label:"失效时间"},scopedSlots:t._u([{key:"default",fn:function(s){return[s.row.expiryTime?e("span",[t._v(t._s(s.row.expiryTime))]):e("span",{staticStyle:{color:"#67C23A"}},[t._v("永久有效")])]}}])}),e("el-table-column",{attrs:{"header-align":"center",align:"center",label:"订单数量"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("span",{staticStyle:{color:"#409EFF"}},[t._v(t._s(s.row.orderCount))])]}}])}),e("el-table-column",{attrs:{"header-align":"center",align:"center",label:"订单金额"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("span",{staticStyle:{color:"#E6A23C","font-weight":"bold"}},[t._v("¥"+t._s(s.row.orderAmount))])]}}])}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.viewOrdersHandle(s.row)}}},[t._v("查看订单")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.viewHistoryHandle(s.row)}}},[t._v("绑定历史")])]}}])})],1),e("el-pagination",{staticStyle:{"margin-top":"20px"},attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.exportCustomers()}}},[t._v("导出客户")]),e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("关闭")])],1)],1)},r=[],n=(s("14d9"),s("b680"),{data:function(){return{visible:!1,salesmanId:null,salesmanName:"",searchForm:{wxUserName:"",mobile:"",status:""},customerList:[],customerStats:{},listLoading:!1,pageIndex:1,pageSize:10,totalPage:0}},methods:{init:function(t,e){this.salesmanId=t,this.salesmanName=e,this.visible=!0,this.resetSearch(),this.getCustomerList(),this.getCustomerStats()},getCustomerList:function(){var t=this;this.listLoading=!0,this.$http({url:this.$http.adornUrl("/salesman/wxuserbinding/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,salesmanId:this.salesmanId,wxUserName:this.searchForm.wxUserName,mobile:this.searchForm.mobile,status:this.searchForm.status})}).then((function(e){var s=e.data;s&&200===s.code?(t.customerList=s.page.list||[],t.totalPage=s.page.totalCount||0):(t.customerList=[],t.totalPage=0),t.listLoading=!1})).catch((function(){t.listLoading=!1}))},getCustomerStats:function(){var t=this;this.$http({url:this.$http.adornUrl("/salesman/wxuserbinding/customerStats"),method:"get",params:this.$http.adornParams({salesmanId:this.salesmanId})}).then((function(e){var s=e.data;s&&200===s.code&&(t.customerStats=s.stats||{})}))},resetSearch:function(){this.searchForm={wxUserName:"",mobile:"",status:""},this.pageIndex=1},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getCustomerList()},currentChangeHandle:function(t){this.pageIndex=t,this.getCustomerList()},viewOrdersHandle:function(t){var e=this;this.visible=!1,this.$nextTick((function(){e.$router.push({path:"/salesman-order",query:{userId:t.wxUserId,salesmanId:e.salesmanId}})}))},viewHistoryHandle:function(t){var e=this;this.visible=!1,this.$nextTick((function(){e.$router.push({path:"/salesman-wx-user-binding",query:{wxUserId:t.wxUserId}})}))},exportCustomers:function(){this.$message.info("导出功能开发中...")},getBindingTypeText:function(t){var e={1:"二维码扫描",2:"邀请链接",3:"手动绑定",4:"系统分配"};return e[t]||"未知"},getBindingTypeTagType:function(t){var e={1:"primary",2:"success",3:"warning",4:"info"};return e[t]||""},getStatusText:function(t){var e={0:"已失效",1:"有效",2:"已解绑"};return e[t]||"未知"},getStatusTagType:function(t){var e={0:"danger",1:"success",2:"info"};return e[t]||""},getOrderCount:function(t){return Math.floor(20*Math.random())},getOrderAmount:function(t){return(1e4*Math.random()).toFixed(2)}}}),i=n,l=(s("4538"),s("2877")),o=Object(l["a"])(i,a,r,!1,null,"2ce1a99e",null);e["default"]=o.exports},"33ad":function(t,e,s){},4538:function(t,e,s){"use strict";s("33ad")},b680:function(t,e,s){"use strict";var a=s("23e7"),r=s("e330"),n=s("5926"),i=s("408a"),l=s("1148"),o=s("d039"),c=RangeError,u=String,d=Math.floor,m=r(l),h=r("".slice),p=r(1..toFixed),g=function(t,e,s){return 0===e?s:e%2===1?g(t,e-1,s*t):g(t*t,e/2,s)},f=function(t){var e=0,s=t;while(s>=4096)e+=12,s/=4096;while(s>=2)e+=1,s/=2;return e},v=function(t,e,s){var a=-1,r=s;while(++a<6)r+=e*t[a],t[a]=r%1e7,r=d(r/1e7)},b=function(t,e){var s=6,a=0;while(--s>=0)a+=t[s],t[s]=d(a/e),a=a%e*1e7},w=function(t){var e=6,s="";while(--e>=0)if(""!==s||0===e||0!==t[e]){var a=u(t[e]);s=""===s?a:s+m("0",7-a.length)+a}return s},x=o((function(){return"0.000"!==p(8e-5,3)||"1"!==p(.9,0)||"1.25"!==p(1.255,2)||"1000000000000000128"!==p(0xde0b6b3a7640080,0)}))||!o((function(){p({})}));a({target:"Number",proto:!0,forced:x},{toFixed:function(t){var e,s,a,r,l=i(this),o=n(t),d=[0,0,0,0,0,0],p="",x="0";if(o<0||o>20)throw new c("Incorrect fraction digits");if(l!==l)return"NaN";if(l<=-1e21||l>=1e21)return u(l);if(l<0&&(p="-",l=-l),l>1e-21)if(e=f(l*g(2,69,1))-69,s=e<0?l*g(2,-e,1):l/g(2,e,1),s*=4503599627370496,e=52-e,e>0){v(d,0,s),a=o;while(a>=7)v(d,1e7,0),a-=7;v(d,g(10,a,1),0),a=e-1;while(a>=23)b(d,1<<23),a-=23;b(d,1<<a),v(d,1,1),b(d,2),x=w(d)}else v(d,0,s),v(d,1<<-e,0),x=w(d)+m("0",o);return o>0?(r=x.length,x=p+(r<=o?"0."+m("0",o-r)+x:h(x,0,r-o)+"."+h(x,r-o))):x=p+x,x}})}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5f19a3c2"],{"1ccb":function(t,e,n){"use strict";n.r(e);n("4de4"),n("d81d"),n("b0c0"),n("d3b7"),n("a573");var a=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"menu-input-group",staticStyle:{"border-bottom":"2px #e8e8e8 solid"}},[e("div",{staticClass:"menu-name"},[t._v(t._s(t.button.name))]),e("div",{staticClass:"menu-del",on:{click:function(e){return t.$emit("delMenu")}}},[t._v("删除菜单")])]),e("div",{staticClass:"menu-input-group"},[e("div",{staticClass:"menu-label"},[t._v("菜单名称")]),e("div",{staticClass:"menu-input"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.button.name,expression:"button.name"}],staticClass:"menu-input-text",attrs:{type:"text",name:"name",placeholder:"请输入菜单名称"},domProps:{value:t.button.name},on:{input:[function(e){e.target.composing||t.$set(t.button,"name",e.target.value)},function(e){return t.checkMenuName(t.button.name)}]}}),e("p",{directives:[{name:"show",rawName:"v-show",value:t.menuNameBounds,expression:"menuNameBounds"}],staticClass:"menu-tips",staticStyle:{color:"#e15f63"}},[t._v("字数超过上限")]),e("p",{staticClass:"menu-tips"},[t._v("字数不超过"+t._s(1==t.selectedMenuLevel?"5":"8")+"个汉字")])])]),e("div",{directives:[{name:"show",rawName:"v-show",value:!t.button.subButtons||0==t.button.subButtons.length,expression:"!button.subButtons || button.subButtons.length==0"}]},[e("div",{staticClass:"menu-input-group"},[e("div",{staticClass:"menu-label"},[t._v("菜单内容")]),e("div",{staticClass:"menu-input"},[e("select",{directives:[{name:"model",rawName:"v-model",value:t.button.type,expression:"button.type"}],staticClass:"menu-input-text",attrs:{name:"type"},on:{change:function(e){var n=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.$set(t.button,"type",e.target.multiple?n:n[0])}}},[e("option",{attrs:{value:"view"}},[t._v("跳转网页(view)")]),e("option",{attrs:{value:"media_id"}},[t._v("发送消息(media_id)")]),e("option",{attrs:{value:"miniprogram"}},[t._v("打开指定小程序(miniprogram)")]),e("option",{attrs:{value:"click"}},[t._v("自定义点击事件(click)")]),e("option",{attrs:{value:"scancode_push"}},[t._v("扫码上传消息(scancode_push)")]),e("option",{attrs:{value:"scancode_waitmsg"}},[t._v("扫码提示下发(scancode_waitmsg)")]),e("option",{attrs:{value:"pic_sysphoto"}},[t._v("系统相机拍照(pic_sysphoto)")]),e("option",{attrs:{value:"pic_photo_or_album"}},[t._v("弹出拍照或者相册(pic_photo_or_album)")]),e("option",{attrs:{value:"pic_weixin"}},[t._v("弹出微信相册(pic_weixin)")]),e("option",{attrs:{value:"location_select"}},[t._v("弹出地理位置选择器(location_select)")])])])]),"view"==t.button.type?e("div",{staticClass:"menu-content"},[e("div",{staticClass:"menu-input-group"},[e("p",{staticClass:"menu-tips"},[t._v("订阅者点击该子菜单会跳到以下链接")]),e("div",{staticClass:"menu-label"},[t._v("页面地址")]),e("div",{staticClass:"menu-input"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.button.url,expression:"button.url"}],staticClass:"menu-input-text",attrs:{type:"text",placeholder:""},domProps:{value:t.button.url},on:{input:function(e){e.target.composing||t.$set(t.button,"url",e.target.value)}}})])])]):"media_id"==t.button.type?e("div",{staticClass:"menu-content"},[e("div",{staticClass:"menu-input-group"},[e("p",{staticClass:"menu-tips"},[t._v("订阅者点击该菜单会收到以下图文消息")]),e("div",{staticClass:"menu-label"},[t._v("media_id")]),e("div",{staticClass:"menu-input"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.button.mediaId,expression:"button.mediaId"}],staticClass:"menu-input-text",attrs:{type:"text",placeholder:"图文消息media_id"},domProps:{value:t.button.mediaId},on:{input:function(e){e.target.composing||t.$set(t.button,"mediaId",e.target.value)}}})])])]):"miniprogram"==t.button.type?e("div",{staticClass:"menu-content"},[e("div",{staticClass:"menu-input-group"},[e("p",{staticClass:"menu-tips"},[t._v("订阅者点击该子菜单会跳到以下小程序")]),e("div",{staticClass:"menu-label"},[t._v("小程序appId")]),e("div",{staticClass:"menu-input"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.button.appId,expression:"button.appId"}],staticClass:"menu-input-text",attrs:{type:"text",placeholder:"小程序的appId（仅认证公众号可配置）"},domProps:{value:t.button.appId},on:{input:function(e){e.target.composing||t.$set(t.button,"appId",e.target.value)}}})])]),e("div",{staticClass:"menu-input-group"},[e("div",{staticClass:"menu-label"},[t._v("小程序路径")]),e("div",{staticClass:"menu-input"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.button.pagePath,expression:"button.pagePath"}],staticClass:"menu-input-text",attrs:{type:"text",placeholder:"小程序的页面路径 pages/index/index"},domProps:{value:t.button.pagePath},on:{input:function(e){e.target.composing||t.$set(t.button,"pagePath",e.target.value)}}})])]),e("div",{staticClass:"menu-input-group"},[e("div",{staticClass:"menu-label"},[t._v("备用网页")]),e("div",{staticClass:"menu-input"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.button.url,expression:"button.url"}],staticClass:"menu-input-text",attrs:{type:"text",placeholder:""},domProps:{value:t.button.url},on:{input:function(e){e.target.composing||t.$set(t.button,"url",e.target.value)}}}),e("p",{staticClass:"menu-tips"},[t._v("旧版微信客户端无法支持小程序，用户点击菜单时将会打开备用网页。")])])])]):e("div",{staticClass:"menu-content"},[e("div",{staticClass:"menu-input-group"},[e("p",{staticClass:"menu-tips"},[t._v("用于消息接口推送，不超过128字节")]),e("div",{staticClass:"menu-label"},[t._v("菜单KEY值")]),e("div",{staticClass:"menu-input"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.button.key,expression:"button.key"}],staticClass:"menu-input-text",attrs:{type:"text",placeholder:""},domProps:{value:t.button.key},on:{input:function(e){e.target.composing||t.$set(t.button,"key",e.target.value)}}})])])])])])},s=[],i=(n("a9e3"),n("ac1f"),n("466d"),{props:{selectedMenuLevel:{type:Number,default:1},button:{type:Object,required:!0}},data:function(){return{menuNameBounds:!1}},methods:{checkMenuName:function(t){1==this.selectedMenuLevel&&this.getMenuNameLen(t)<=10||2==this.selectedMenuLevel&&this.getMenuNameLen(t)<=16?this.menuNameBounds=!1:this.menuNameBounds=!0},getMenuNameLen:function(t){for(var e=0,n=0;n<t.length;n++){var a=t.charAt(n);null!=a.match(/[^\x00-\xff]/gi)?e+=2:e+=1}return e}}}),u=i,o=n("2877"),l=Object(o["a"])(u,a,s,!1,null,null,null);e["default"]=l.exports},"466d":function(t,e,n){"use strict";var a=n("c65b"),s=n("d784"),i=n("825a"),u=n("7234"),o=n("50c4"),l=n("577e"),r=n("1d80"),p=n("dc4a"),c=n("8aa5"),m=n("14c3");s("match",(function(t,e,n){return[function(e){var n=r(this),s=u(e)?void 0:p(e,t);return s?a(s,e,n):new RegExp(e)[t](l(n))},function(t){var a=i(this),s=l(t),u=n(e,a,s);if(u.done)return u.value;if(!a.global)return m(a,s);var r=a.unicode;a.lastIndex=0;var p,d=[],v=0;while(null!==(p=m(a,s))){var b=l(p[0]);d[v]=b,""===b&&(a.lastIndex=c(s,o(a.lastIndex),r)),v++}return 0===v?null:d}]}))},a573:function(t,e,n){"use strict";n("ab43")},ab43:function(t,e,n){"use strict";var a=n("23e7"),s=n("d024"),i=n("c430");a({target:"Iterator",proto:!0,real:!0,forced:i},{map:s})},d024:function(t,e,n){"use strict";var a=n("c65b"),s=n("59ed"),i=n("825a"),u=n("46c4"),o=n("c5cc"),l=n("9bdd"),r=o((function(){var t=this.iterator,e=i(a(this.next,t)),n=this.done=!!e.done;if(!n)return l(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return i(this),s(t),new r(u(this),{mapper:t})}},d81d:function(t,e,n){"use strict";var a=n("23e7"),s=n("b727").map,i=n("1dde"),u=i("map");a({target:"Array",proto:!0,forced:!u},{map:function(t){return s(this,t,arguments.length>1?arguments[1]:void 0)}})}}]);
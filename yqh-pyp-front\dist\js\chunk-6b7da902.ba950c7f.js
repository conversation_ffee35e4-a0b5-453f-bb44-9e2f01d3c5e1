(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6b7da902"],{"8c32":function(t,i,n){"use strict";n.r(i);n("7f7f");var s=function(){var t=this,i=t._self._c;return i("div",{staticClass:"page"},[i("img",{staticStyle:{width:"100%"},attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/20230324/9dc5404a275c42ac9a07f42ccd9cc245.png",alt:""}}),i("div",{staticClass:"activity-info"},[i("div",{staticStyle:{"font-size":"18px","font-weight":"bold",padding:"3px 0px"}},[t._v("\n      "+t._s(t.activityInfo.name)+"\n    ")]),i("div",{staticStyle:{"font-size":"16px","font-weight":"bold",padding:"3px 0px"}},[t._v("\n      "+t._s(t.activityInfo.startTime.substring(0,11))+" -\n      "+t._s(t.activityInfo.endTime.substring(5,11))+"\n    ")])]),1==t.userInfo.status&&1==t.userInfo.signType?i("div",{staticClass:"sign-info"},[i("img",{staticStyle:{width:"180px"},attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/20210618/7361a2571f8a41809960a577bb123733.png",alt:""}}),i("van-button",{staticClass:"bottom-button",attrs:{round:"",type:"info",block:""}},[t._v("签到成功")])],1):1==t.userInfo.status&&1!=t.userInfo.signType?i("div",{staticClass:"sign-info"},[i("img",{staticStyle:{width:"180px"},attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/20221020/168906ff7dc74239835061f0a8cfe5fd.png",alt:""}}),i("van-button",{staticClass:"bottom-button",attrs:{round:"",type:"warning",block:""},on:{click:t.sign}},[t._v("点击签到")])],1):i("div",{staticClass:"sign-info"},[i("img",{staticStyle:{width:"180px"},attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/20211228/13da716239e94e0fa7f84712e06ce9d1.png",alt:""}}),i("van-button",{staticClass:"bottom-button",attrs:{round:"",type:"warning",block:""},on:{click:function(i){return t.$router.push({name:"applyIndex",query:{id:t.activityId}})}}},[t._v("您还未报名，点击报名")])],1),1==t.userInfo.status?i("div",{staticClass:"apply-info"},[i("div",{staticStyle:{"padding-left":"30px"}},[t._v("姓名："+t._s(t.userInfo.contact))]),i("div",{staticStyle:{"padding-left":"30px"}},[t._v("联系方式："+t._s(t.userInfo.mobile))]),i("div",{staticStyle:{"padding-left":"30px"}},[t._v("报名通道："+t._s(t.channelInfo.name))])]):t._e()])},c=[],a=n("b2e5"),o=n.n(a),e={components:{VueQrcode:o.a},data:function(){return{activityId:void 0,userInfo:{},activityInfo:{},channelInfo:{}}},mounted:function(){document.title="电子签到",this.activityId=this.$route.query.id,this.getActivityInfo(),this.getUserActivityInfo()},methods:{sign:function(){var t=this;this.$fly.get("/pyp/web/activityusersign/signByQrCode",{activityUserId:this.userInfo.id,type:1}).then((function(i){200==i.code?vant.Dialog.confirm({title:"提示",message:"“"+t.userInfo.contact+"”签到成功",confirmButtonText:"确定",showCancelButton:!1}).then((function(){t.getUserActivityInfo()})).catch((function(){})):vant.Dialog.confirm({title:"失败",message:i.msg,confirmButtonText:"确定",showCancelButton:!1}).then((function(){})).catch((function(){}))}))},getUserActivityInfo:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/applyUserInfo/".concat(this.activityId)).then((function(i){200==i.code?(t.userInfo=i.activityUserEntity,t.userInfo.applyActivityChannelConfigId&&t.getApplyActivityChannelConfig(t.userInfo.applyActivityChannelConfigId)):vant.Toast("您还未报名，点击报名")}))},getApplyActivityChannelConfig:function(t){var i=this;this.$fly.get("/pyp/web/apply/applyactivitychannelconfig/info/".concat(t)).then((function(t){200==t.code&&(i.channelInfo=t.applyActivityChannelConfig)}))},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(i){200==i.code?t.activityInfo=i.activity:t.activityInfo={}}))}}},f=e,y=(n("b749"),n("2877")),l=Object(y["a"])(f,s,c,!1,null,"2c27e067",null);i["default"]=l.exports},"9a3e":function(t,i,n){},b749:function(t,i,n){"use strict";n("9a3e")}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5305445d"],{"6c00":function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e._self._c;return a("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(a){e.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"订单状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"订单状态",filterable:""},model:{value:e.dataForm.status,callback:function(a){e.$set(e.dataForm,"status",a)},expression:"dataForm.status"}},e._l(e.orderStatus,(function(e){return a("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1),a("el-form-item",{attrs:{label:"支付来源",prop:"source"}},[a("el-select",{attrs:{placeholder:"支付来源",filterable:""},model:{value:e.dataForm.source,callback:function(a){e.$set(e.dataForm,"source",a)},expression:"dataForm.source"}},e._l(e.sources,(function(e){return a("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1),a("el-form-item",{attrs:{label:"备注",prop:"remarks"}},[a("el-input",{attrs:{placeholder:"备注",clearable:""},model:{value:e.dataForm.remarks,callback:function(a){e.$set(e.dataForm,"remarks",a)},expression:"dataForm.remarks"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){e.visible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},i=[],n=(t("b0c0"),t("7de9")),o={data:function(){return{visible:!1,dataForm:{id:0,activityId:"",activityUserId:"",userId:"",status:"",name:"",orderSn:"",source:"",remarks:""},sources:n["f"],orderStatus:n["e"],dataRule:{status:[{required:!0,message:"订单状态不能为空",trigger:"blur"}],source:[{required:!0,message:"支付来源不能为空",trigger:"blur"}]}}},methods:{init:function(e){var a=this;this.dataForm.id=e,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.$http({url:a.$http.adornUrl("/activity/activityuserapplyorder/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(e){var t=e.data;t&&200===t.code&&(a.dataForm.activityId=t.activityUserApplyOrder.activityId,a.dataForm.userId=t.activityUserApplyOrder.userId,a.dataForm.activityUserId=t.activityUserApplyOrder.activityUserId,a.dataForm.status=t.activityUserApplyOrder.status,a.dataForm.name=t.activityUserApplyOrder.name,a.dataForm.orderSn=t.activityUserApplyOrder.orderSn,a.dataForm.source=t.activityUserApplyOrder.source,a.dataForm.remarks=t.activityUserApplyOrder.remarks)}))}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(a){a&&e.$http({url:e.$http.adornUrl("/activity/activityuserapplyorder/update"),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,activityId:e.dataForm.activityId,userId:e.dataForm.userId,status:e.dataForm.status,name:e.dataForm.name,activityUserId:e.dataForm.activityUserId,orderSn:e.dataForm.orderSn,source:e.dataForm.source,remarks:e.dataForm.remarks})}).then((function(a){var t=a.data;t&&200===t.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(t.msg)}))}))}}},l=o,d=t("2877"),s=Object(d["a"])(l,r,i,!1,null,null,null);a["default"]=s.exports},"7de9":function(e,a,t){"use strict";t.d(a,"g",(function(){return r})),t.d(a,"f",(function(){return i})),t.d(a,"e",(function(){return n})),t.d(a,"a",(function(){return o})),t.d(a,"b",(function(){return l})),t.d(a,"c",(function(){return d})),t.d(a,"d",(function(){return s}));var r=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],i=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],n=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],o=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],l=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],d=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],s=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f1ed47b8"],{"06d9":function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"mod-config"},[t("el-tabs",{attrs:{type:"card"},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[t("el-tab-pane",{attrs:{label:"防重复检查",name:"duplicate"}},[t("div",{staticClass:"tab-content"},[t("el-form",{attrs:{inline:!0,model:e.duplicateForm,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"业务类型"}},[t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.duplicateForm.businessType,callback:function(t){e.$set(e.duplicateForm,"businessType",t)},expression:"duplicateForm.businessType"}},[t("el-option",{attrs:{label:"创建活动",value:"CREATE_ACTIVITY"}}),t("el-option",{attrs:{label:"充值次数",value:"RECHARGE_COUNT"}}),t("el-option",{attrs:{label:"用户转发",value:"USER_FORWARD"}})],1)],1),t("el-form-item",{attrs:{label:"业务ID"}},[t("el-input",{attrs:{placeholder:"请输入业务ID"},model:{value:e.duplicateForm.businessId,callback:function(t){e.$set(e.duplicateForm,"businessId",t)},expression:"duplicateForm.businessId"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.checkDuplicate()}}},[e._v("检查重复")]),t("el-button",{on:{click:function(t){return e.generateUniqueKey()}}},[e._v("生成唯一标识")])],1)],1),e.duplicateResult?t("div",{staticClass:"result-panel"},[t("el-alert",{attrs:{title:e.duplicateResult.exists?"佣金记录已存在":"佣金记录不存在",type:e.duplicateResult.exists?"warning":"success",closable:!1}}),e.duplicateResult.uniqueKey?t("div",{staticClass:"unique-key"},[t("p",[t("strong",[e._v("唯一标识：")]),e._v(e._s(e.duplicateResult.uniqueKey))])]):e._e()],1):e._e()],1)]),t("el-tab-pane",{attrs:{label:"失败重试",name:"retry"}},[t("div",{staticClass:"tab-content"},[t("div",{staticClass:"operation-buttons"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.retryFailedCommissions()}}},[e._v("重试失败佣金")]),t("el-button",{attrs:{type:"success"},on:{click:function(t){return e.getFailureList()}}},[e._v("查看失败记录")]),t("el-button",{attrs:{type:"warning"},on:{click:function(t){return e.clearFailureRecords()}}},[e._v("清理失败记录")])],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.failureLoading,expression:"failureLoading"}],staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:e.failureList,border:""}},[t("el-table-column",{attrs:{prop:"businessType","header-align":"center",align:"center",label:"业务类型"}}),t("el-table-column",{attrs:{prop:"businessId","header-align":"center",align:"center",label:"业务ID"}}),t("el-table-column",{attrs:{prop:"salesmanName","header-align":"center",align:"center",label:"业务员"}}),t("el-table-column",{attrs:{prop:"failureReason","header-align":"center",align:"center",label:"失败原因"}}),t("el-table-column",{attrs:{prop:"retryCount","header-align":"center",align:"center",label:"重试次数"}}),t("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",width:"150",label:"失败时间"}}),t("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",label:"状态"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:e.getFailureStatusTagType(s.row.status)}},[e._v(" "+e._s(e.getFailureStatusText(s.row.status))+" ")])]}}])}),t("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"120",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(s){return[0===s.row.status?t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.retryFailure(s.row)}}},[e._v("重试")]):e._e(),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.ignoreFailure(s.row)}}},[e._v("忽略")])]}}])})],1)],1)]),t("el-tab-pane",{attrs:{label:"数据一致性",name:"consistency"}},[t("div",{staticClass:"tab-content"},[t("div",{staticClass:"operation-buttons"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.batchValidateConsistency()}}},[e._v("批量验证一致性")]),t("el-button",{attrs:{type:"success"},on:{click:function(t){return e.getInconsistentList()}}},[e._v("查看不一致记录")]),t("el-button",{attrs:{type:"warning"},on:{click:function(t){return e.batchRepairInconsistent()}}},[e._v("批量修复")])],1),t("el-form",{staticStyle:{"margin-top":"20px"},attrs:{inline:!0,model:e.consistencyForm}},[t("el-form-item",{attrs:{label:"佣金记录ID"}},[t("el-input",{attrs:{placeholder:"请输入佣金记录ID"},model:{value:e.consistencyForm.commissionRecordId,callback:function(t){e.$set(e.consistencyForm,"commissionRecordId",t)},expression:"consistencyForm.commissionRecordId"}})],1),t("el-form-item",[t("el-button",{on:{click:function(t){return e.validateSingleConsistency()}}},[e._v("验证单个记录")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.consistencyLoading,expression:"consistencyLoading"}],staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:e.inconsistentList,border:""}},[t("el-table-column",{attrs:{prop:"commissionRecordId","header-align":"center",align:"center",label:"佣金记录ID"}}),t("el-table-column",{attrs:{prop:"businessType","header-align":"center",align:"center",label:"业务类型"}}),t("el-table-column",{attrs:{prop:"businessId","header-align":"center",align:"center",label:"业务ID"}}),t("el-table-column",{attrs:{prop:"salesmanName","header-align":"center",align:"center",label:"业务员"}}),t("el-table-column",{attrs:{prop:"inconsistencyReason","header-align":"center",align:"center",label:"不一致原因"}}),t("el-table-column",{attrs:{prop:"detectTime","header-align":"center",align:"center",width:"150",label:"检测时间"}}),t("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"120",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.repairInconsistent(s.row)}}},[e._v("修复")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.viewDetails(s.row)}}},[e._v("详情")])]}}])})],1)],1)]),t("el-tab-pane",{attrs:{label:"锁管理",name:"lock"}},[t("div",{staticClass:"tab-content"},[t("el-form",{attrs:{inline:!0,model:e.lockForm,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"业务类型"}},[t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.lockForm.businessType,callback:function(t){e.$set(e.lockForm,"businessType",t)},expression:"lockForm.businessType"}},[t("el-option",{attrs:{label:"创建活动",value:"CREATE_ACTIVITY"}}),t("el-option",{attrs:{label:"充值次数",value:"RECHARGE_COUNT"}}),t("el-option",{attrs:{label:"用户转发",value:"USER_FORWARD"}})],1)],1),t("el-form-item",{attrs:{label:"业务ID"}},[t("el-input",{attrs:{placeholder:"请输入业务ID"},model:{value:e.lockForm.businessId,callback:function(t){e.$set(e.lockForm,"businessId",t)},expression:"lockForm.businessId"}})],1),t("el-form-item",{attrs:{label:"业务员ID"}},[t("el-input",{attrs:{placeholder:"请输入业务员ID"},model:{value:e.lockForm.salesmanId,callback:function(t){e.$set(e.lockForm,"salesmanId",t)},expression:"lockForm.salesmanId"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.checkLockStatus()}}},[e._v("查看锁状态")]),t("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.releaseLock()}}},[e._v("释放锁")]),t("el-button",{attrs:{type:"warning"},on:{click:function(t){return e.cleanupExpiredData()}}},[e._v("清理过期数据")])],1)],1),e.lockResult?t("div",{staticClass:"result-panel"},[t("el-alert",{attrs:{title:e.lockResult.message,type:e.lockResult.type,closable:!1}},[e.lockResult.lockKey?t("template",{slot:"description"},[t("p",[t("strong",[e._v("锁键：")]),e._v(e._s(e.lockResult.lockKey))])]):e._e()],2)],1):e._e()],1)])],1)],1)},n=[],i=s("5530"),l={data:function(){return{activeTab:"duplicate",duplicateForm:{businessType:"",businessId:""},duplicateResult:null,failureList:[],failureLoading:!1,consistencyForm:{commissionRecordId:""},inconsistentList:[],consistencyLoading:!1,lockForm:{businessType:"",businessId:"",salesmanId:""},lockResult:null}},methods:{checkDuplicate:function(){var e=this;this.duplicateForm.businessType&&this.duplicateForm.businessId?this.$http({url:this.$http.adornUrl("/salesman/commissionsafety/checkExists"),method:"get",params:this.$http.adornParams({businessType:this.duplicateForm.businessType,businessId:this.duplicateForm.businessId})}).then((function(t){var s=t.data;s&&200===s.code?e.duplicateResult={exists:s.exists}:e.$message.error(s.msg)})):this.$message.warning("请填写完整信息")},generateUniqueKey:function(){var e=this;this.duplicateForm.businessType&&this.duplicateForm.businessId?this.$http({url:this.$http.adornUrl("/salesman/commissionsafety/generateUniqueKey"),method:"get",params:this.$http.adornParams({businessType:this.duplicateForm.businessType,businessId:this.duplicateForm.businessId,salesmanId:1})}).then((function(t){var s=t.data;s&&200===s.code?e.duplicateResult=Object(i["a"])(Object(i["a"])({},e.duplicateResult),{},{uniqueKey:s.uniqueKey}):e.$message.error(s.msg)})):this.$message.warning("请填写完整信息")},retryFailedCommissions:function(){var e=this;this.$http({url:this.$http.adornUrl("/salesman/commissionsafety/retryFailed"),method:"post"}).then((function(t){var s=t.data;s&&200===s.code?(e.$message.success(s.message),e.getFailureList()):e.$message.error(s.msg)}))},getFailureList:function(){var e=this;this.failureLoading=!0,setTimeout((function(){e.failureList=[],e.failureLoading=!1}),1e3)},clearFailureRecords:function(){var e=this;this.$confirm("确定要清理所有失败记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$message.success("清理完成"),e.getFailureList()}))},batchValidateConsistency:function(){var e=this;this.$http({url:this.$http.adornUrl("/salesman/commissionsafety/batchValidate"),method:"post"}).then((function(t){var s=t.data;s&&200===s.code?(e.$message.success(s.message),e.getInconsistentList()):e.$message.error(s.msg)}))},getInconsistentList:function(){var e=this;this.consistencyLoading=!0,setTimeout((function(){e.inconsistentList=[],e.consistencyLoading=!1}),1e3)},validateSingleConsistency:function(){var e=this;this.consistencyForm.commissionRecordId?this.$http({url:this.$http.adornUrl("/salesman/commissionsafety/validateConsistency"),method:"get",params:this.$http.adornParams({commissionRecordId:this.consistencyForm.commissionRecordId})}).then((function(t){var s=t.data;s&&200===s.code?s.consistent?e.$message.success(s.message):e.$message.warning(s.message):e.$message.error(s.msg)})):this.$message.warning("请输入佣金记录ID")},checkLockStatus:function(){var e=this;this.lockForm.businessType&&this.lockForm.businessId&&this.lockForm.salesmanId?this.$http({url:this.$http.adornUrl("/salesman/commissionsafety/lockStatus"),method:"get",params:this.$http.adornParams({businessType:this.lockForm.businessType,businessId:this.lockForm.businessId,salesmanId:this.lockForm.salesmanId})}).then((function(t){var s=t.data;s&&200===s.code?e.lockResult={message:s.message,type:"info",lockKey:s.lockKey}:e.$message.error(s.msg)})):this.$message.warning("请填写完整信息")},releaseLock:function(){var e=this;this.lockForm.businessType&&this.lockForm.businessId&&this.lockForm.salesmanId?this.$http({url:this.$http.adornUrl("/salesman/commissionsafety/releaseLock"),method:"post",params:this.$http.adornParams({businessType:this.lockForm.businessType,businessId:this.lockForm.businessId,salesmanId:this.lockForm.salesmanId})}).then((function(t){var s=t.data;s&&200===s.code?(e.$message.success(s.message),e.lockResult={message:"锁已释放",type:"success"}):e.$message.error(s.msg)})):this.$message.warning("请填写完整信息")},cleanupExpiredData:function(){var e=this;this.$http({url:this.$http.adornUrl("/salesman/commissionsafety/cleanup"),method:"post"}).then((function(t){var s=t.data;s&&200===s.code?e.$message.success(s.message):e.$message.error(s.msg)}))},getFailureStatusText:function(e){var t={0:"待重试",1:"重试成功",2:"重试失败",3:"已忽略"};return t[e]||"未知"},getFailureStatusTagType:function(e){var t={0:"warning",1:"success",2:"danger",3:"info"};return t[e]||""}}},r=l,o=(s("ff16"),s("2877")),c=Object(o["a"])(r,a,n,!1,null,"25e36143",null);t["default"]=c.exports},fa11:function(e,t,s){},ff16:function(e,t,s){"use strict";s("fa11")}}]);
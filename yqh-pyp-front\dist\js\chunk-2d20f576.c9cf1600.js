(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d20f576"],{b2e5:function(t,r,e){
/*!
 * vue-qrcode v1.0.2
 * https://fengyuanchen.github.io/vue-qrcode
 *
 * Copyright 2018-present <PERSON>
 * Released under the MIT license
 *
 * Date: 2020-01-18T06:04:33.222Z
 */
(function(r,e){t.exports=e()})(0,(function(){"use strict";function t(){throw new Error("Dynamic requires are not currently supported by rollup-plugin-commonjs")}function r(t,r){return r={exports:{}},t(r,r.exports),r.exports}var e=r((function(r,e){(function(t){r.exports=t()})((function(){return function(){function r(e,n,o){function i(a,f){if(!n[a]){if(!e[a]){var s="function"==typeof t&&t;if(!f&&s)return s(a,!0);if(u)return u(a,!0);var h=new Error("Cannot find module '"+a+"'");throw h.code="MODULE_NOT_FOUND",h}var c=n[a]={exports:{}};e[a][0].call(c.exports,(function(t){var r=e[a][1][t];return i(r||t)}),c,c.exports,r,e,n,o)}return n[a].exports}for(var u="function"==typeof t&&t,a=0;a<o.length;a++)i(o[a]);return i}return r}()({1:[function(t,r,e){r.exports=function(){return"function"===typeof Promise&&Promise.prototype&&Promise.prototype.then}},{}],2:[function(t,r,e){var n=t("./utils").getSymbolSize;e.getRowColCoords=function(t){if(1===t)return[];for(var r=Math.floor(t/7)+2,e=n(t),o=145===e?26:2*Math.ceil((e-13)/(2*r-2)),i=[e-7],u=1;u<r-1;u++)i[u]=i[u-1]-o;return i.push(6),i.reverse()},e.getPositions=function(t){for(var r=[],n=e.getRowColCoords(t),o=n.length,i=0;i<o;i++)for(var u=0;u<o;u++)0===i&&0===u||0===i&&u===o-1||i===o-1&&0===u||r.push([n[i],n[u]]);return r}},{"./utils":21}],3:[function(t,r,e){var n=t("./mode"),o=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function i(t){this.mode=n.ALPHANUMERIC,this.data=t}i.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){var r;for(r=0;r+2<=this.data.length;r+=2){var e=45*o.indexOf(this.data[r]);e+=o.indexOf(this.data[r+1]),t.put(e,11)}this.data.length%2&&t.put(o.indexOf(this.data[r]),6)},r.exports=i},{"./mode":14}],4:[function(t,r,e){function n(){this.buffer=[],this.length=0}n.prototype={get:function(t){var r=Math.floor(t/8);return 1===(this.buffer[r]>>>7-t%8&1)},put:function(t,r){for(var e=0;e<r;e++)this.putBit(1===(t>>>r-e-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){var r=Math.floor(this.length/8);this.buffer.length<=r&&this.buffer.push(0),t&&(this.buffer[r]|=128>>>this.length%8),this.length++}},r.exports=n},{}],5:[function(t,r,e){var n=t("../utils/buffer");function o(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=n.alloc(t*t),this.reservedBit=n.alloc(t*t)}o.prototype.set=function(t,r,e,n){var o=t*this.size+r;this.data[o]=e,n&&(this.reservedBit[o]=!0)},o.prototype.get=function(t,r){return this.data[t*this.size+r]},o.prototype.xor=function(t,r,e){this.data[t*this.size+r]^=e},o.prototype.isReserved=function(t,r){return this.reservedBit[t*this.size+r]},r.exports=o},{"../utils/buffer":28}],6:[function(t,r,e){var n=t("../utils/buffer"),o=t("./mode");function i(t){this.mode=o.BYTE,this.data=n.from(t)}i.getBitsLength=function(t){return 8*t},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){for(var r=0,e=this.data.length;r<e;r++)t.put(this.data[r],8)},r.exports=i},{"../utils/buffer":28,"./mode":14}],7:[function(t,r,e){var n=t("./error-correction-level"),o=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],i=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];e.getBlocksCount=function(t,r){switch(r){case n.L:return o[4*(t-1)+0];case n.M:return o[4*(t-1)+1];case n.Q:return o[4*(t-1)+2];case n.H:return o[4*(t-1)+3];default:return}},e.getTotalCodewordsCount=function(t,r){switch(r){case n.L:return i[4*(t-1)+0];case n.M:return i[4*(t-1)+1];case n.Q:return i[4*(t-1)+2];case n.H:return i[4*(t-1)+3];default:return}}},{"./error-correction-level":8}],8:[function(t,r,e){function n(t){if("string"!==typeof t)throw new Error("Param is not a string");var r=t.toLowerCase();switch(r){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw new Error("Unknown EC Level: "+t)}}e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2},e.isValid=function(t){return t&&"undefined"!==typeof t.bit&&t.bit>=0&&t.bit<4},e.from=function(t,r){if(e.isValid(t))return t;try{return n(t)}catch(o){return r}}},{}],9:[function(t,r,e){var n=t("./utils").getSymbolSize,o=7;e.getPositions=function(t){var r=n(t);return[[0,0],[r-o,0],[0,r-o]]}},{"./utils":21}],10:[function(t,r,e){var n=t("./utils"),o=1335,i=21522,u=n.getBCHDigit(o);e.getEncodedBits=function(t,r){var e=t.bit<<3|r,a=e<<10;while(n.getBCHDigit(a)-u>=0)a^=o<<n.getBCHDigit(a)-u;return(e<<10|a)^i}},{"./utils":21}],11:[function(t,r,e){var n=t("../utils/buffer"),o=n.alloc(512),i=n.alloc(256);(function(){for(var t=1,r=0;r<255;r++)o[r]=t,i[t]=r,t<<=1,256&t&&(t^=285);for(r=255;r<512;r++)o[r]=o[r-255]})(),e.log=function(t){if(t<1)throw new Error("log("+t+")");return i[t]},e.exp=function(t){return o[t]},e.mul=function(t,r){return 0===t||0===r?0:o[i[t]+i[r]]}},{"../utils/buffer":28}],12:[function(t,r,e){var n=t("./mode"),o=t("./utils");function i(t){this.mode=n.KANJI,this.data=t}i.getBitsLength=function(t){return 13*t},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){var r;for(r=0;r<this.data.length;r++){var e=o.toSJIS(this.data[r]);if(e>=33088&&e<=40956)e-=33088;else{if(!(e>=57408&&e<=60351))throw new Error("Invalid SJIS character: "+this.data[r]+"\nMake sure your charset is UTF-8");e-=49472}e=192*(e>>>8&255)+(255&e),t.put(e,13)}},r.exports=i},{"./mode":14,"./utils":21}],13:[function(t,r,e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};var n={N1:3,N2:3,N3:40,N4:10};function o(t,r,n){switch(t){case e.Patterns.PATTERN000:return(r+n)%2===0;case e.Patterns.PATTERN001:return r%2===0;case e.Patterns.PATTERN010:return n%3===0;case e.Patterns.PATTERN011:return(r+n)%3===0;case e.Patterns.PATTERN100:return(Math.floor(r/2)+Math.floor(n/3))%2===0;case e.Patterns.PATTERN101:return r*n%2+r*n%3===0;case e.Patterns.PATTERN110:return(r*n%2+r*n%3)%2===0;case e.Patterns.PATTERN111:return(r*n%3+(r+n)%2)%2===0;default:throw new Error("bad maskPattern:"+t)}}e.isValid=function(t){return null!=t&&""!==t&&!isNaN(t)&&t>=0&&t<=7},e.from=function(t){return e.isValid(t)?parseInt(t,10):void 0},e.getPenaltyN1=function(t){for(var r=t.size,e=0,o=0,i=0,u=null,a=null,f=0;f<r;f++){o=i=0,u=a=null;for(var s=0;s<r;s++){var h=t.get(f,s);h===u?o++:(o>=5&&(e+=n.N1+(o-5)),u=h,o=1),h=t.get(s,f),h===a?i++:(i>=5&&(e+=n.N1+(i-5)),a=h,i=1)}o>=5&&(e+=n.N1+(o-5)),i>=5&&(e+=n.N1+(i-5))}return e},e.getPenaltyN2=function(t){for(var r=t.size,e=0,o=0;o<r-1;o++)for(var i=0;i<r-1;i++){var u=t.get(o,i)+t.get(o,i+1)+t.get(o+1,i)+t.get(o+1,i+1);4!==u&&0!==u||e++}return e*n.N2},e.getPenaltyN3=function(t){for(var r=t.size,e=0,o=0,i=0,u=0;u<r;u++){o=i=0;for(var a=0;a<r;a++)o=o<<1&2047|t.get(u,a),a>=10&&(1488===o||93===o)&&e++,i=i<<1&2047|t.get(a,u),a>=10&&(1488===i||93===i)&&e++}return e*n.N3},e.getPenaltyN4=function(t){for(var r=0,e=t.data.length,o=0;o<e;o++)r+=t.data[o];var i=Math.abs(Math.ceil(100*r/e/5)-10);return i*n.N4},e.applyMask=function(t,r){for(var e=r.size,n=0;n<e;n++)for(var i=0;i<e;i++)r.isReserved(i,n)||r.xor(i,n,o(t,i,n))},e.getBestMask=function(t,r){for(var n=Object.keys(e.Patterns).length,o=0,i=1/0,u=0;u<n;u++){r(u),e.applyMask(u,t);var a=e.getPenaltyN1(t)+e.getPenaltyN2(t)+e.getPenaltyN3(t)+e.getPenaltyN4(t);e.applyMask(u,t),a<i&&(i=a,o=u)}return o}},{}],14:[function(t,r,e){var n=t("./version-check"),o=t("./regex");function i(t){if("string"!==typeof t)throw new Error("Param is not a string");var r=t.toLowerCase();switch(r){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw new Error("Unknown mode: "+t)}}e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(t,r){if(!t.ccBits)throw new Error("Invalid mode: "+t);if(!n.isValid(r))throw new Error("Invalid version: "+r);return r>=1&&r<10?t.ccBits[0]:r<27?t.ccBits[1]:t.ccBits[2]},e.getBestModeForData=function(t){return o.testNumeric(t)?e.NUMERIC:o.testAlphanumeric(t)?e.ALPHANUMERIC:o.testKanji(t)?e.KANJI:e.BYTE},e.toString=function(t){if(t&&t.id)return t.id;throw new Error("Invalid mode")},e.isValid=function(t){return t&&t.bit&&t.ccBits},e.from=function(t,r){if(e.isValid(t))return t;try{return i(t)}catch(n){return r}}},{"./regex":19,"./version-check":22}],15:[function(t,r,e){var n=t("./mode");function o(t){this.mode=n.NUMERIC,this.data=t.toString()}o.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){var r,e,n;for(r=0;r+3<=this.data.length;r+=3)e=this.data.substr(r,3),n=parseInt(e,10),t.put(n,10);var o=this.data.length-r;o>0&&(e=this.data.substr(r),n=parseInt(e,10),t.put(n,3*o+1))},r.exports=o},{"./mode":14}],16:[function(t,r,e){var n=t("../utils/buffer"),o=t("./galois-field");e.mul=function(t,r){for(var e=n.alloc(t.length+r.length-1),i=0;i<t.length;i++)for(var u=0;u<r.length;u++)e[i+u]^=o.mul(t[i],r[u]);return e},e.mod=function(t,r){var e=n.from(t);while(e.length-r.length>=0){for(var i=e[0],u=0;u<r.length;u++)e[u]^=o.mul(r[u],i);var a=0;while(a<e.length&&0===e[a])a++;e=e.slice(a)}return e},e.generateECPolynomial=function(t){for(var r=n.from([1]),i=0;i<t;i++)r=e.mul(r,[1,o.exp(i)]);return r}},{"../utils/buffer":28,"./galois-field":11}],17:[function(t,r,e){var n=t("../utils/buffer"),o=t("./utils"),i=t("./error-correction-level"),u=t("./bit-buffer"),a=t("./bit-matrix"),f=t("./alignment-pattern"),s=t("./finder-pattern"),h=t("./mask-pattern"),c=t("./error-correction-code"),l=t("./reed-solomon-encoder"),g=t("./version"),p=t("./format-info"),d=t("./mode"),y=t("./segments"),v=t("isarray");function w(t,r){for(var e=t.size,n=s.getPositions(r),o=0;o<n.length;o++)for(var i=n[o][0],u=n[o][1],a=-1;a<=7;a++)if(!(i+a<=-1||e<=i+a))for(var f=-1;f<=7;f++)u+f<=-1||e<=u+f||(a>=0&&a<=6&&(0===f||6===f)||f>=0&&f<=6&&(0===a||6===a)||a>=2&&a<=4&&f>=2&&f<=4?t.set(i+a,u+f,!0,!0):t.set(i+a,u+f,!1,!0))}function m(t){for(var r=t.size,e=8;e<r-8;e++){var n=e%2===0;t.set(e,6,n,!0),t.set(6,e,n,!0)}}function b(t,r){for(var e=f.getPositions(r),n=0;n<e.length;n++)for(var o=e[n][0],i=e[n][1],u=-2;u<=2;u++)for(var a=-2;a<=2;a++)-2===u||2===u||-2===a||2===a||0===u&&0===a?t.set(o+u,i+a,!0,!0):t.set(o+u,i+a,!1,!0)}function E(t,r){for(var e,n,o,i=t.size,u=g.getEncodedBits(r),a=0;a<18;a++)e=Math.floor(a/3),n=a%3+i-8-3,o=1===(u>>a&1),t.set(e,n,o,!0),t.set(n,e,o,!0)}function A(t,r,e){var n,o,i=t.size,u=p.getEncodedBits(r,e);for(n=0;n<15;n++)o=1===(u>>n&1),n<6?t.set(n,8,o,!0):n<8?t.set(n+1,8,o,!0):t.set(i-15+n,8,o,!0),n<8?t.set(8,i-n-1,o,!0):n<9?t.set(8,15-n-1+1,o,!0):t.set(8,15-n-1,o,!0);t.set(i-8,8,1,!0)}function B(t,r){for(var e=t.size,n=-1,o=e-1,i=7,u=0,a=e-1;a>0;a-=2){6===a&&a--;while(1){for(var f=0;f<2;f++)if(!t.isReserved(o,a-f)){var s=!1;u<r.length&&(s=1===(r[u]>>>i&1)),t.set(o,a-f,s),i--,-1===i&&(u++,i=7)}if(o+=n,o<0||e<=o){o-=n,n=-n;break}}}}function T(t,r,e){var n=new u;e.forEach((function(r){n.put(r.mode.bit,4),n.put(r.getLength(),d.getCharCountIndicator(r.mode,t)),r.write(n)}));var i=o.getSymbolTotalCodewords(t),a=c.getTotalCodewordsCount(t,r),f=8*(i-a);n.getLengthInBits()+4<=f&&n.put(0,4);while(n.getLengthInBits()%8!==0)n.putBit(0);for(var s=(f-n.getLengthInBits())/8,h=0;h<s;h++)n.put(h%2?17:236,8);return R(n,t,r)}function R(t,r,e){for(var i=o.getSymbolTotalCodewords(r),u=c.getTotalCodewordsCount(r,e),a=i-u,f=c.getBlocksCount(r,e),s=i%f,h=f-s,g=Math.floor(i/f),p=Math.floor(a/f),d=p+1,y=g-p,v=new l(y),w=0,m=new Array(f),b=new Array(f),E=0,A=n.from(t.buffer),B=0;B<f;B++){var T=B<h?p:d;m[B]=A.slice(w,w+T),b[B]=v.encode(m[B]),w+=T,E=Math.max(E,T)}var R,C,P=n.alloc(i),I=0;for(R=0;R<E;R++)for(C=0;C<f;C++)R<m[C].length&&(P[I++]=m[C][R]);for(R=0;R<y;R++)for(C=0;C<f;C++)P[I++]=b[C][R];return P}function C(t,r,e,n){var i;if(v(t))i=y.fromArray(t);else{if("string"!==typeof t)throw new Error("Invalid data");var u=r;if(!u){var f=y.rawSplit(t);u=g.getBestVersionForData(f,e)}i=y.fromString(t,u||40)}var s=g.getBestVersionForData(i,e);if(!s)throw new Error("The amount of data is too big to be stored in a QR Code");if(r){if(r<s)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+s+".\n")}else r=s;var c=T(r,e,i),l=o.getSymbolSize(r),p=new a(l);return w(p,r),m(p),b(p,r),A(p,e,0),r>=7&&E(p,r),B(p,c),isNaN(n)&&(n=h.getBestMask(p,A.bind(null,p,e))),h.applyMask(n,p),A(p,e,n),{modules:p,version:r,errorCorrectionLevel:e,maskPattern:n,segments:i}}e.create=function(t,r){if("undefined"===typeof t||""===t)throw new Error("No input text");var e,n,u=i.M;return"undefined"!==typeof r&&(u=i.from(r.errorCorrectionLevel,i.M),e=g.from(r.version),n=h.from(r.maskPattern),r.toSJISFunc&&o.setToSJISFunction(r.toSJISFunc)),C(t,e,u,n)}},{"../utils/buffer":28,"./alignment-pattern":2,"./bit-buffer":4,"./bit-matrix":5,"./error-correction-code":7,"./error-correction-level":8,"./finder-pattern":9,"./format-info":10,"./mask-pattern":13,"./mode":14,"./reed-solomon-encoder":18,"./segments":20,"./utils":21,"./version":23,isarray:33}],18:[function(t,r,e){var n=t("../utils/buffer"),o=t("./polynomial"),i=t("buffer").Buffer;function u(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}u.prototype.initialize=function(t){this.degree=t,this.genPoly=o.generateECPolynomial(this.degree)},u.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");var r=n.alloc(this.degree),e=i.concat([t,r],t.length+this.degree),u=o.mod(e,this.genPoly),a=this.degree-u.length;if(a>0){var f=n.alloc(this.degree);return u.copy(f,a),f}return u},r.exports=u},{"../utils/buffer":28,"./polynomial":16,buffer:30}],19:[function(t,r,e){var n="[0-9]+",o="[A-Z $%*+\\-./:]+",i="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";i=i.replace(/u/g,"\\u");var u="(?:(?![A-Z0-9 $%*+\\-./:]|"+i+")(?:.|[\r\n]))+";e.KANJI=new RegExp(i,"g"),e.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),e.BYTE=new RegExp(u,"g"),e.NUMERIC=new RegExp(n,"g"),e.ALPHANUMERIC=new RegExp(o,"g");var a=new RegExp("^"+i+"$"),f=new RegExp("^"+n+"$"),s=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");e.testKanji=function(t){return a.test(t)},e.testNumeric=function(t){return f.test(t)},e.testAlphanumeric=function(t){return s.test(t)}},{}],20:[function(t,r,e){var n=t("./mode"),o=t("./numeric-data"),i=t("./alphanumeric-data"),u=t("./byte-data"),a=t("./kanji-data"),f=t("./regex"),s=t("./utils"),h=t("dijkstrajs");function c(t){return unescape(encodeURIComponent(t)).length}function l(t,r,e){var n,o=[];while(null!==(n=t.exec(e)))o.push({data:n[0],index:n.index,mode:r,length:n[0].length});return o}function g(t){var r,e,o=l(f.NUMERIC,n.NUMERIC,t),i=l(f.ALPHANUMERIC,n.ALPHANUMERIC,t);s.isKanjiModeEnabled()?(r=l(f.BYTE,n.BYTE,t),e=l(f.KANJI,n.KANJI,t)):(r=l(f.BYTE_KANJI,n.BYTE,t),e=[]);var u=o.concat(i,r,e);return u.sort((function(t,r){return t.index-r.index})).map((function(t){return{data:t.data,mode:t.mode,length:t.length}}))}function p(t,r){switch(r){case n.NUMERIC:return o.getBitsLength(t);case n.ALPHANUMERIC:return i.getBitsLength(t);case n.KANJI:return a.getBitsLength(t);case n.BYTE:return u.getBitsLength(t)}}function d(t){return t.reduce((function(t,r){var e=t.length-1>=0?t[t.length-1]:null;return e&&e.mode===r.mode?(t[t.length-1].data+=r.data,t):(t.push(r),t)}),[])}function y(t){for(var r=[],e=0;e<t.length;e++){var o=t[e];switch(o.mode){case n.NUMERIC:r.push([o,{data:o.data,mode:n.ALPHANUMERIC,length:o.length},{data:o.data,mode:n.BYTE,length:o.length}]);break;case n.ALPHANUMERIC:r.push([o,{data:o.data,mode:n.BYTE,length:o.length}]);break;case n.KANJI:r.push([o,{data:o.data,mode:n.BYTE,length:c(o.data)}]);break;case n.BYTE:r.push([{data:o.data,mode:n.BYTE,length:c(o.data)}])}}return r}function v(t,r){for(var e={},o={start:{}},i=["start"],u=0;u<t.length;u++){for(var a=t[u],f=[],s=0;s<a.length;s++){var h=a[s],c=""+u+s;f.push(c),e[c]={node:h,lastCount:0},o[c]={};for(var l=0;l<i.length;l++){var g=i[l];e[g]&&e[g].node.mode===h.mode?(o[g][c]=p(e[g].lastCount+h.length,h.mode)-p(e[g].lastCount,h.mode),e[g].lastCount+=h.length):(e[g]&&(e[g].lastCount=h.length),o[g][c]=p(h.length,h.mode)+4+n.getCharCountIndicator(h.mode,r))}}i=f}for(l=0;l<i.length;l++)o[i[l]]["end"]=0;return{map:o,table:e}}function w(t,r){var e,f=n.getBestModeForData(t);if(e=n.from(r,f),e!==n.BYTE&&e.bit<f.bit)throw new Error('"'+t+'" cannot be encoded with mode '+n.toString(e)+".\n Suggested mode is: "+n.toString(f));switch(e!==n.KANJI||s.isKanjiModeEnabled()||(e=n.BYTE),e){case n.NUMERIC:return new o(t);case n.ALPHANUMERIC:return new i(t);case n.KANJI:return new a(t);case n.BYTE:return new u(t)}}e.fromArray=function(t){return t.reduce((function(t,r){return"string"===typeof r?t.push(w(r,null)):r.data&&t.push(w(r.data,r.mode)),t}),[])},e.fromString=function(t,r){for(var n=g(t,s.isKanjiModeEnabled()),o=y(n),i=v(o,r),u=h.find_path(i.map,"start","end"),a=[],f=1;f<u.length-1;f++)a.push(i.table[u[f]].node);return e.fromArray(d(a))},e.rawSplit=function(t){return e.fromArray(g(t,s.isKanjiModeEnabled()))}},{"./alphanumeric-data":3,"./byte-data":6,"./kanji-data":12,"./mode":14,"./numeric-data":15,"./regex":19,"./utils":21,dijkstrajs:31}],21:[function(t,r,e){var n,o=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];e.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return 4*t+17},e.getSymbolTotalCodewords=function(t){return o[t]},e.getBCHDigit=function(t){var r=0;while(0!==t)r++,t>>>=1;return r},e.setToSJISFunction=function(t){if("function"!==typeof t)throw new Error('"toSJISFunc" is not a valid function.');n=t},e.isKanjiModeEnabled=function(){return"undefined"!==typeof n},e.toSJIS=function(t){return n(t)}},{}],22:[function(t,r,e){e.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40}},{}],23:[function(t,r,e){var n=t("./utils"),o=t("./error-correction-code"),i=t("./error-correction-level"),u=t("./mode"),a=t("./version-check"),f=t("isarray"),s=7973,h=n.getBCHDigit(s);function c(t,r,n){for(var o=1;o<=40;o++)if(r<=e.getCapacity(o,n,t))return o}function l(t,r){return u.getCharCountIndicator(t,r)+4}function g(t,r){var e=0;return t.forEach((function(t){var n=l(t.mode,r);e+=n+t.getBitsLength()})),e}function p(t,r){for(var n=1;n<=40;n++){var o=g(t,n);if(o<=e.getCapacity(n,r,u.MIXED))return n}}e.from=function(t,r){return a.isValid(t)?parseInt(t,10):r},e.getCapacity=function(t,r,e){if(!a.isValid(t))throw new Error("Invalid QR Code version");"undefined"===typeof e&&(e=u.BYTE);var i=n.getSymbolTotalCodewords(t),f=o.getTotalCodewordsCount(t,r),s=8*(i-f);if(e===u.MIXED)return s;var h=s-l(e,t);switch(e){case u.NUMERIC:return Math.floor(h/10*3);case u.ALPHANUMERIC:return Math.floor(h/11*2);case u.KANJI:return Math.floor(h/13);case u.BYTE:default:return Math.floor(h/8)}},e.getBestVersionForData=function(t,r){var e,n=i.from(r,i.M);if(f(t)){if(t.length>1)return p(t,n);if(0===t.length)return 1;e=t[0]}else e=t;return c(e.mode,e.getLength(),n)},e.getEncodedBits=function(t){if(!a.isValid(t)||t<7)throw new Error("Invalid QR Code version");var r=t<<12;while(n.getBCHDigit(r)-h>=0)r^=s<<n.getBCHDigit(r)-h;return t<<12|r}},{"./error-correction-code":7,"./error-correction-level":8,"./mode":14,"./utils":21,"./version-check":22,isarray:33}],24:[function(t,r,e){var n=t("./can-promise"),o=t("./core/qrcode"),i=t("./renderer/canvas"),u=t("./renderer/svg-tag.js");function a(t,r,e,i,u){var a=[].slice.call(arguments,1),f=a.length,s="function"===typeof a[f-1];if(!s&&!n())throw new Error("Callback required as last argument");if(!s){if(f<1)throw new Error("Too few arguments provided");return 1===f?(e=r,r=i=void 0):2!==f||r.getContext||(i=e,e=r,r=void 0),new Promise((function(n,u){try{var a=o.create(e,i);n(t(a,r,i))}catch(f){u(f)}}))}if(f<2)throw new Error("Too few arguments provided");2===f?(u=e,e=r,r=i=void 0):3===f&&(r.getContext&&"undefined"===typeof u?(u=i,i=void 0):(u=i,i=e,e=r,r=void 0));try{var h=o.create(e,i);u(null,t(h,r,i))}catch(c){u(c)}}e.create=o.create,e.toCanvas=a.bind(null,i.render),e.toDataURL=a.bind(null,i.renderToDataURL),e.toString=a.bind(null,(function(t,r,e){return u.render(t,e)}))},{"./can-promise":1,"./core/qrcode":17,"./renderer/canvas":25,"./renderer/svg-tag.js":26}],25:[function(t,r,e){var n=t("./utils");function o(t,r,e){t.clearRect(0,0,r.width,r.height),r.style||(r.style={}),r.height=e,r.width=e,r.style.height=e+"px",r.style.width=e+"px"}function i(){try{return document.createElement("canvas")}catch(t){throw new Error("You need to specify a canvas element")}}e.render=function(t,r,e){var u=e,a=r;"undefined"!==typeof u||r&&r.getContext||(u=r,r=void 0),r||(a=i()),u=n.getOptions(u);var f=n.getImageWidth(t.modules.size,u),s=a.getContext("2d"),h=s.createImageData(f,f);return n.qrToImageData(h.data,t,u),o(s,a,f),s.putImageData(h,0,0),a},e.renderToDataURL=function(t,r,n){var o=n;"undefined"!==typeof o||r&&r.getContext||(o=r,r=void 0),o||(o={});var i=e.render(t,r,o),u=o.type||"image/png",a=o.rendererOpts||{};return i.toDataURL(u,a.quality)}},{"./utils":27}],26:[function(t,r,e){var n=t("./utils");function o(t,r){var e=t.a/255,n=r+'="'+t.hex+'"';return e<1?n+" "+r+'-opacity="'+e.toFixed(2).slice(1)+'"':n}function i(t,r,e){var n=t+r;return"undefined"!==typeof e&&(n+=" "+e),n}function u(t,r,e){for(var n="",o=0,u=!1,a=0,f=0;f<t.length;f++){var s=Math.floor(f%r),h=Math.floor(f/r);s||u||(u=!0),t[f]?(a++,f>0&&s>0&&t[f-1]||(n+=u?i("M",s+e,.5+h+e):i("m",o,0),o=0,u=!1),s+1<r&&t[f+1]||(n+=i("h",a),a=0)):o++}return n}e.render=function(t,r,e){var i=n.getOptions(r),a=t.modules.size,f=t.modules.data,s=a+2*i.margin,h=i.color.light.a?"<path "+o(i.color.light,"fill")+' d="M0 0h'+s+"v"+s+'H0z"/>':"",c="<path "+o(i.color.dark,"stroke")+' d="'+u(f,a,i.margin)+'"/>',l='viewBox="0 0 '+s+" "+s+'"',g=i.width?'width="'+i.width+'" height="'+i.width+'" ':"",p='<svg xmlns="http://www.w3.org/2000/svg" '+g+l+' shape-rendering="crispEdges">'+h+c+"</svg>\n";return"function"===typeof e&&e(null,p),p}},{"./utils":27}],27:[function(t,r,e){function n(t){if("number"===typeof t&&(t=t.toString()),"string"!==typeof t)throw new Error("Color should be defined as hex string");var r=t.slice().replace("#","").split("");if(r.length<3||5===r.length||r.length>8)throw new Error("Invalid hex color: "+t);3!==r.length&&4!==r.length||(r=Array.prototype.concat.apply([],r.map((function(t){return[t,t]})))),6===r.length&&r.push("F","F");var e=parseInt(r.join(""),16);return{r:e>>24&255,g:e>>16&255,b:e>>8&255,a:255&e,hex:"#"+r.slice(0,6).join("")}}e.getOptions=function(t){t||(t={}),t.color||(t.color={});var r="undefined"===typeof t.margin||null===t.margin||t.margin<0?4:t.margin,e=t.width&&t.width>=21?t.width:void 0,o=t.scale||4;return{width:e,scale:e?4:o,margin:r,color:{dark:n(t.color.dark||"#000000ff"),light:n(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},e.getScale=function(t,r){return r.width&&r.width>=t+2*r.margin?r.width/(t+2*r.margin):r.scale},e.getImageWidth=function(t,r){var n=e.getScale(t,r);return Math.floor((t+2*r.margin)*n)},e.qrToImageData=function(t,r,n){for(var o=r.modules.size,i=r.modules.data,u=e.getScale(o,n),a=Math.floor((o+2*n.margin)*u),f=n.margin*u,s=[n.color.light,n.color.dark],h=0;h<a;h++)for(var c=0;c<a;c++){var l=4*(h*a+c),g=n.color.light;if(h>=f&&c>=f&&h<a-f&&c<a-f){var p=Math.floor((h-f)/u),d=Math.floor((c-f)/u);g=s[i[p*o+d]?1:0]}t[l++]=g.r,t[l++]=g.g,t[l++]=g.b,t[l]=g.a}}},{}],28:[function(t,r,e){var n=t("isarray");function o(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()}catch(r){return!1}}u.TYPED_ARRAY_SUPPORT=o();var i=u.TYPED_ARRAY_SUPPORT?2147483647:1073741823;function u(t,r,e){return u.TYPED_ARRAY_SUPPORT||this instanceof u?"number"===typeof t?h(this,t):m(this,t,r,e):new u(t,r,e)}function a(t){if(t>=i)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+i.toString(16)+" bytes");return 0|t}function f(t){return t!==t}function s(t,r){var e;return u.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(r),e.__proto__=u.prototype):(e=t,null===e&&(e=new u(r)),e.length=r),e}function h(t,r){var e=s(t,r<0?0:0|a(r));if(!u.TYPED_ARRAY_SUPPORT)for(var n=0;n<r;++n)e[n]=0;return e}function c(t,r){var e=0|y(r),n=s(t,e),o=n.write(r);return o!==e&&(n=n.slice(0,o)),n}function l(t,r){for(var e=r.length<0?0:0|a(r.length),n=s(t,e),o=0;o<e;o+=1)n[o]=255&r[o];return n}function g(t,r,e,n){if(e<0||r.byteLength<e)throw new RangeError("'offset' is out of bounds");if(r.byteLength<e+(n||0))throw new RangeError("'length' is out of bounds");var o;return o=void 0===e&&void 0===n?new Uint8Array(r):void 0===n?new Uint8Array(r,e):new Uint8Array(r,e,n),u.TYPED_ARRAY_SUPPORT?o.__proto__=u.prototype:o=l(t,o),o}function p(t,r){if(u.isBuffer(r)){var e=0|a(r.length),n=s(t,e);return 0===n.length||r.copy(n,0,0,e),n}if(r){if("undefined"!==typeof ArrayBuffer&&r.buffer instanceof ArrayBuffer||"length"in r)return"number"!==typeof r.length||f(r.length)?s(t,0):l(t,r);if("Buffer"===r.type&&Array.isArray(r.data))return l(t,r.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function d(t,r){var e;r=r||1/0;for(var n=t.length,o=null,i=[],u=0;u<n;++u){if(e=t.charCodeAt(u),e>55295&&e<57344){if(!o){if(e>56319){(r-=3)>-1&&i.push(239,191,189);continue}if(u+1===n){(r-=3)>-1&&i.push(239,191,189);continue}o=e;continue}if(e<56320){(r-=3)>-1&&i.push(239,191,189),o=e;continue}e=65536+(o-55296<<10|e-56320)}else o&&(r-=3)>-1&&i.push(239,191,189);if(o=null,e<128){if((r-=1)<0)break;i.push(e)}else if(e<2048){if((r-=2)<0)break;i.push(e>>6|192,63&e|128)}else if(e<65536){if((r-=3)<0)break;i.push(e>>12|224,e>>6&63|128,63&e|128)}else{if(!(e<1114112))throw new Error("Invalid code point");if((r-=4)<0)break;i.push(e>>18|240,e>>12&63|128,e>>6&63|128,63&e|128)}}return i}function y(t){if(u.isBuffer(t))return t.length;if("undefined"!==typeof ArrayBuffer&&"function"===typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!==typeof t&&(t=""+t);var r=t.length;return 0===r?0:d(t).length}function v(t,r,e,n){for(var o=0;o<n;++o){if(o+e>=r.length||o>=t.length)break;r[o+e]=t[o]}return o}function w(t,r,e,n){return v(d(r,t.length-e),t,e,n)}function m(t,r,e,n){if("number"===typeof r)throw new TypeError('"value" argument must not be a number');return"undefined"!==typeof ArrayBuffer&&r instanceof ArrayBuffer?g(t,r,e,n):"string"===typeof r?c(t,r):p(t,r)}u.TYPED_ARRAY_SUPPORT&&(u.prototype.__proto__=Uint8Array.prototype,u.__proto__=Uint8Array,"undefined"!==typeof Symbol&&Symbol.species&&u[Symbol.species]===u&&Object.defineProperty(u,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1})),u.prototype.write=function(t,r,e){void 0===r||void 0===e&&"string"===typeof r?(e=this.length,r=0):isFinite(r)&&(r|=0,isFinite(e)?e|=0:e=void 0);var n=this.length-r;if((void 0===e||e>n)&&(e=n),t.length>0&&(e<0||r<0)||r>this.length)throw new RangeError("Attempt to write outside buffer bounds");return w(this,t,r,e)},u.prototype.slice=function(t,r){var e,n=this.length;if(t=~~t,r=void 0===r?n:~~r,t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),r<0?(r+=n,r<0&&(r=0)):r>n&&(r=n),r<t&&(r=t),u.TYPED_ARRAY_SUPPORT)e=this.subarray(t,r),e.__proto__=u.prototype;else{var o=r-t;e=new u(o,void 0);for(var i=0;i<o;++i)e[i]=this[i+t]}return e},u.prototype.copy=function(t,r,e,n){if(e||(e=0),n||0===n||(n=this.length),r>=t.length&&(r=t.length),r||(r=0),n>0&&n<e&&(n=e),n===e)return 0;if(0===t.length||0===this.length)return 0;if(r<0)throw new RangeError("targetStart out of bounds");if(e<0||e>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-r<n-e&&(n=t.length-r+e);var o,i=n-e;if(this===t&&e<r&&r<n)for(o=i-1;o>=0;--o)t[o+r]=this[o+e];else if(i<1e3||!u.TYPED_ARRAY_SUPPORT)for(o=0;o<i;++o)t[o+r]=this[o+e];else Uint8Array.prototype.set.call(t,this.subarray(e,e+i),r);return i},u.prototype.fill=function(t,r,e){if("string"===typeof t){if("string"===typeof r?(r=0,e=this.length):"string"===typeof e&&(e=this.length),1===t.length){var n=t.charCodeAt(0);n<256&&(t=n)}}else"number"===typeof t&&(t&=255);if(r<0||this.length<r||this.length<e)throw new RangeError("Out of range index");if(e<=r)return this;var o;if(r>>>=0,e=void 0===e?this.length:e>>>0,t||(t=0),"number"===typeof t)for(o=r;o<e;++o)this[o]=t;else{var i=u.isBuffer(t)?t:new u(t),a=i.length;for(o=0;o<e-r;++o)this[o+r]=i[o%a]}return this},u.concat=function(t,r){if(!n(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return s(null,0);var e;if(void 0===r)for(r=0,e=0;e<t.length;++e)r+=t[e].length;var o=h(null,r),i=0;for(e=0;e<t.length;++e){var a=t[e];if(!u.isBuffer(a))throw new TypeError('"list" argument must be an Array of Buffers');a.copy(o,i),i+=a.length}return o},u.byteLength=y,u.prototype._isBuffer=!0,u.isBuffer=function(t){return!(null==t||!t._isBuffer)},r.exports.alloc=function(t){var r=new u(t);return r.fill(0),r},r.exports.from=function(t){return new u(t)}},{isarray:33}],29:[function(t,r,e){e.byteLength=h,e.toByteArray=l,e.fromByteArray=d;for(var n=[],o=[],i="undefined"!==typeof Uint8Array?Uint8Array:Array,u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,f=u.length;a<f;++a)n[a]=u[a],o[u.charCodeAt(a)]=a;function s(t){var r=t.length;if(r%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var e=t.indexOf("=");-1===e&&(e=r);var n=e===r?0:4-e%4;return[e,n]}function h(t){var r=s(t),e=r[0],n=r[1];return 3*(e+n)/4-n}function c(t,r,e){return 3*(r+e)/4-e}function l(t){var r,e,n=s(t),u=n[0],a=n[1],f=new i(c(t,u,a)),h=0,l=a>0?u-4:u;for(e=0;e<l;e+=4)r=o[t.charCodeAt(e)]<<18|o[t.charCodeAt(e+1)]<<12|o[t.charCodeAt(e+2)]<<6|o[t.charCodeAt(e+3)],f[h++]=r>>16&255,f[h++]=r>>8&255,f[h++]=255&r;return 2===a&&(r=o[t.charCodeAt(e)]<<2|o[t.charCodeAt(e+1)]>>4,f[h++]=255&r),1===a&&(r=o[t.charCodeAt(e)]<<10|o[t.charCodeAt(e+1)]<<4|o[t.charCodeAt(e+2)]>>2,f[h++]=r>>8&255,f[h++]=255&r),f}function g(t){return n[t>>18&63]+n[t>>12&63]+n[t>>6&63]+n[63&t]}function p(t,r,e){for(var n,o=[],i=r;i<e;i+=3)n=(t[i]<<16&16711680)+(t[i+1]<<8&65280)+(255&t[i+2]),o.push(g(n));return o.join("")}function d(t){for(var r,e=t.length,o=e%3,i=[],u=16383,a=0,f=e-o;a<f;a+=u)i.push(p(t,a,a+u>f?f:a+u));return 1===o?(r=t[e-1],i.push(n[r>>2]+n[r<<4&63]+"==")):2===o&&(r=(t[e-2]<<8)+t[e-1],i.push(n[r>>10]+n[r>>4&63]+n[r<<2&63]+"=")),i.join("")}o["-".charCodeAt(0)]=62,o["_".charCodeAt(0)]=63},{}],30:[function(t,r,e){var n=t("base64-js"),o=t("ieee754"),i="function"===typeof Symbol&&"function"===typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;e.Buffer=s,e.SlowBuffer=m,e.INSPECT_MAX_BYTES=50;var u=2147483647;function a(){try{var t=new Uint8Array(1),r={foo:function(){return 42}};return Object.setPrototypeOf(r,Uint8Array.prototype),Object.setPrototypeOf(t,r),42===t.foo()}catch(e){return!1}}function f(t){if(t>u)throw new RangeError('The value "'+t+'" is invalid for option "size"');var r=new Uint8Array(t);return Object.setPrototypeOf(r,s.prototype),r}function s(t,r,e){if("number"===typeof t){if("string"===typeof r)throw new TypeError('The "string" argument must be of type string. Received type number');return g(t)}return h(t,r,e)}function h(t,r,e){if("string"===typeof t)return p(t,r);if(ArrayBuffer.isView(t))return d(t);if(null==t)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(Z(t,ArrayBuffer)||t&&Z(t.buffer,ArrayBuffer))return y(t,r,e);if("number"===typeof t)throw new TypeError('The "value" argument must not be of type number. Received type number');var n=t.valueOf&&t.valueOf();if(null!=n&&n!==t)return s.from(n,r,e);var o=v(t);if(o)return o;if("undefined"!==typeof Symbol&&null!=Symbol.toPrimitive&&"function"===typeof t[Symbol.toPrimitive])return s.from(t[Symbol.toPrimitive]("string"),r,e);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function c(t){if("number"!==typeof t)throw new TypeError('"size" argument must be of type number');if(t<0)throw new RangeError('The value "'+t+'" is invalid for option "size"')}function l(t,r,e){return c(t),t<=0?f(t):void 0!==r?"string"===typeof e?f(t).fill(r,e):f(t).fill(r):f(t)}function g(t){return c(t),f(t<0?0:0|w(t))}function p(t,r){if("string"===typeof r&&""!==r||(r="utf8"),!s.isEncoding(r))throw new TypeError("Unknown encoding: "+r);var e=0|b(t,r),n=f(e),o=n.write(t,r);return o!==e&&(n=n.slice(0,o)),n}function d(t){for(var r=t.length<0?0:0|w(t.length),e=f(r),n=0;n<r;n+=1)e[n]=255&t[n];return e}function y(t,r,e){if(r<0||t.byteLength<r)throw new RangeError('"offset" is outside of buffer bounds');if(t.byteLength<r+(e||0))throw new RangeError('"length" is outside of buffer bounds');var n;return n=void 0===r&&void 0===e?new Uint8Array(t):void 0===e?new Uint8Array(t,r):new Uint8Array(t,r,e),Object.setPrototypeOf(n,s.prototype),n}function v(t){if(s.isBuffer(t)){var r=0|w(t.length),e=f(r);return 0===e.length||t.copy(e,0,0,r),e}return void 0!==t.length?"number"!==typeof t.length||W(t.length)?f(0):d(t):"Buffer"===t.type&&Array.isArray(t.data)?d(t.data):void 0}function w(t){if(t>=u)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+u.toString(16)+" bytes");return 0|t}function m(t){return+t!=t&&(t=0),s.alloc(+t)}function b(t,r){if(s.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||Z(t,ArrayBuffer))return t.byteLength;if("string"!==typeof t)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var e=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===e)return 0;for(var o=!1;;)switch(r){case"ascii":case"latin1":case"binary":return e;case"utf8":case"utf-8":return q(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*e;case"hex":return e>>>1;case"base64":return Q(t).length;default:if(o)return n?-1:q(t).length;r=(""+r).toLowerCase(),o=!0}}function E(t,r,e){var n=!1;if((void 0===r||r<0)&&(r=0),r>this.length)return"";if((void 0===e||e>this.length)&&(e=this.length),e<=0)return"";if(e>>>=0,r>>>=0,e<=r)return"";t||(t="utf8");while(1)switch(t){case"hex":return O(this,r,e);case"utf8":case"utf-8":return N(this,r,e);case"ascii":return _(this,r,e);case"latin1":case"binary":return k(this,r,e);case"base64":return S(this,r,e);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return Y(this,r,e);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}function A(t,r,e){var n=t[r];t[r]=t[e],t[e]=n}function B(t,r,e,n,o){if(0===t.length)return-1;if("string"===typeof e?(n=e,e=0):e>2147483647?e=2147483647:e<-2147483648&&(e=-2147483648),e=+e,W(e)&&(e=o?0:t.length-1),e<0&&(e=t.length+e),e>=t.length){if(o)return-1;e=t.length-1}else if(e<0){if(!o)return-1;e=0}if("string"===typeof r&&(r=s.from(r,n)),s.isBuffer(r))return 0===r.length?-1:T(t,r,e,n,o);if("number"===typeof r)return r&=255,"function"===typeof Uint8Array.prototype.indexOf?o?Uint8Array.prototype.indexOf.call(t,r,e):Uint8Array.prototype.lastIndexOf.call(t,r,e):T(t,[r],e,n,o);throw new TypeError("val must be string, number or Buffer")}function T(t,r,e,n,o){var i,u=1,a=t.length,f=r.length;if(void 0!==n&&(n=String(n).toLowerCase(),"ucs2"===n||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||r.length<2)return-1;u=2,a/=2,f/=2,e/=2}function s(t,r){return 1===u?t[r]:t.readUInt16BE(r*u)}if(o){var h=-1;for(i=e;i<a;i++)if(s(t,i)===s(r,-1===h?0:i-h)){if(-1===h&&(h=i),i-h+1===f)return h*u}else-1!==h&&(i-=i-h),h=-1}else for(e+f>a&&(e=a-f),i=e;i>=0;i--){for(var c=!0,l=0;l<f;l++)if(s(t,i+l)!==s(r,l)){c=!1;break}if(c)return i}return-1}function R(t,r,e,n){e=Number(e)||0;var o=t.length-e;n?(n=Number(n),n>o&&(n=o)):n=o;var i=r.length;n>i/2&&(n=i/2);for(var u=0;u<n;++u){var a=parseInt(r.substr(2*u,2),16);if(W(a))return u;t[e+u]=a}return u}function C(t,r,e,n){return X(q(r,t.length-e),t,e,n)}function P(t,r,e,n){return X(V(r),t,e,n)}function I(t,r,e,n){return P(t,r,e,n)}function M(t,r,e,n){return X(Q(r),t,e,n)}function U(t,r,e,n){return X($(r,t.length-e),t,e,n)}function S(t,r,e){return 0===r&&e===t.length?n.fromByteArray(t):n.fromByteArray(t.slice(r,e))}function N(t,r,e){e=Math.min(t.length,e);var n=[],o=r;while(o<e){var i,u,a,f,s=t[o],h=null,c=s>239?4:s>223?3:s>191?2:1;if(o+c<=e)switch(c){case 1:s<128&&(h=s);break;case 2:i=t[o+1],128===(192&i)&&(f=(31&s)<<6|63&i,f>127&&(h=f));break;case 3:i=t[o+1],u=t[o+2],128===(192&i)&&128===(192&u)&&(f=(15&s)<<12|(63&i)<<6|63&u,f>2047&&(f<55296||f>57343)&&(h=f));break;case 4:i=t[o+1],u=t[o+2],a=t[o+3],128===(192&i)&&128===(192&u)&&128===(192&a)&&(f=(15&s)<<18|(63&i)<<12|(63&u)<<6|63&a,f>65535&&f<1114112&&(h=f))}null===h?(h=65533,c=1):h>65535&&(h-=65536,n.push(h>>>10&1023|55296),h=56320|1023&h),n.push(h),o+=c}return x(n)}e.kMaxLength=u,s.TYPED_ARRAY_SUPPORT=a(),s.TYPED_ARRAY_SUPPORT||"undefined"===typeof console||"function"!==typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(s.prototype,"parent",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.buffer}}),Object.defineProperty(s.prototype,"offset",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.byteOffset}}),"undefined"!==typeof Symbol&&null!=Symbol.species&&s[Symbol.species]===s&&Object.defineProperty(s,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1}),s.poolSize=8192,s.from=function(t,r,e){return h(t,r,e)},Object.setPrototypeOf(s.prototype,Uint8Array.prototype),Object.setPrototypeOf(s,Uint8Array),s.alloc=function(t,r,e){return l(t,r,e)},s.allocUnsafe=function(t){return g(t)},s.allocUnsafeSlow=function(t){return g(t)},s.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==s.prototype},s.compare=function(t,r){if(Z(t,Uint8Array)&&(t=s.from(t,t.offset,t.byteLength)),Z(r,Uint8Array)&&(r=s.from(r,r.offset,r.byteLength)),!s.isBuffer(t)||!s.isBuffer(r))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===r)return 0;for(var e=t.length,n=r.length,o=0,i=Math.min(e,n);o<i;++o)if(t[o]!==r[o]){e=t[o],n=r[o];break}return e<n?-1:n<e?1:0},s.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(t,r){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return s.alloc(0);var e;if(void 0===r)for(r=0,e=0;e<t.length;++e)r+=t[e].length;var n=s.allocUnsafe(r),o=0;for(e=0;e<t.length;++e){var i=t[e];if(Z(i,Uint8Array)&&(i=s.from(i)),!s.isBuffer(i))throw new TypeError('"list" argument must be an Array of Buffers');i.copy(n,o),o+=i.length}return n},s.byteLength=b,s.prototype._isBuffer=!0,s.prototype.swap16=function(){var t=this.length;if(t%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var r=0;r<t;r+=2)A(this,r,r+1);return this},s.prototype.swap32=function(){var t=this.length;if(t%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var r=0;r<t;r+=4)A(this,r,r+3),A(this,r+1,r+2);return this},s.prototype.swap64=function(){var t=this.length;if(t%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var r=0;r<t;r+=8)A(this,r,r+7),A(this,r+1,r+6),A(this,r+2,r+5),A(this,r+3,r+4);return this},s.prototype.toString=function(){var t=this.length;return 0===t?"":0===arguments.length?N(this,0,t):E.apply(this,arguments)},s.prototype.toLocaleString=s.prototype.toString,s.prototype.equals=function(t){if(!s.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===s.compare(this,t)},s.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},i&&(s.prototype[i]=s.prototype.inspect),s.prototype.compare=function(t,r,e,n,o){if(Z(t,Uint8Array)&&(t=s.from(t,t.offset,t.byteLength)),!s.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===r&&(r=0),void 0===e&&(e=t?t.length:0),void 0===n&&(n=0),void 0===o&&(o=this.length),r<0||e>t.length||n<0||o>this.length)throw new RangeError("out of range index");if(n>=o&&r>=e)return 0;if(n>=o)return-1;if(r>=e)return 1;if(r>>>=0,e>>>=0,n>>>=0,o>>>=0,this===t)return 0;for(var i=o-n,u=e-r,a=Math.min(i,u),f=this.slice(n,o),h=t.slice(r,e),c=0;c<a;++c)if(f[c]!==h[c]){i=f[c],u=h[c];break}return i<u?-1:u<i?1:0},s.prototype.includes=function(t,r,e){return-1!==this.indexOf(t,r,e)},s.prototype.indexOf=function(t,r,e){return B(this,t,r,e,!0)},s.prototype.lastIndexOf=function(t,r,e){return B(this,t,r,e,!1)},s.prototype.write=function(t,r,e,n){if(void 0===r)n="utf8",e=this.length,r=0;else if(void 0===e&&"string"===typeof r)n=r,e=this.length,r=0;else{if(!isFinite(r))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");r>>>=0,isFinite(e)?(e>>>=0,void 0===n&&(n="utf8")):(n=e,e=void 0)}var o=this.length-r;if((void 0===e||e>o)&&(e=o),t.length>0&&(e<0||r<0)||r>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var i=!1;;)switch(n){case"hex":return R(this,t,r,e);case"utf8":case"utf-8":return C(this,t,r,e);case"ascii":return P(this,t,r,e);case"latin1":case"binary":return I(this,t,r,e);case"base64":return M(this,t,r,e);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return U(this,t,r,e);default:if(i)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),i=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var L=4096;function x(t){var r=t.length;if(r<=L)return String.fromCharCode.apply(String,t);var e="",n=0;while(n<r)e+=String.fromCharCode.apply(String,t.slice(n,n+=L));return e}function _(t,r,e){var n="";e=Math.min(t.length,e);for(var o=r;o<e;++o)n+=String.fromCharCode(127&t[o]);return n}function k(t,r,e){var n="";e=Math.min(t.length,e);for(var o=r;o<e;++o)n+=String.fromCharCode(t[o]);return n}function O(t,r,e){var n=t.length;(!r||r<0)&&(r=0),(!e||e<0||e>n)&&(e=n);for(var o="",i=r;i<e;++i)o+=G[t[i]];return o}function Y(t,r,e){for(var n=t.slice(r,e),o="",i=0;i<n.length;i+=2)o+=String.fromCharCode(n[i]+256*n[i+1]);return o}function D(t,r,e){if(t%1!==0||t<0)throw new RangeError("offset is not uint");if(t+r>e)throw new RangeError("Trying to access beyond buffer length")}function j(t,r,e,n,o,i){if(!s.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(r>o||r<i)throw new RangeError('"value" argument is out of bounds');if(e+n>t.length)throw new RangeError("Index out of range")}function F(t,r,e,n,o,i){if(e+n>t.length)throw new RangeError("Index out of range");if(e<0)throw new RangeError("Index out of range")}function z(t,r,e,n,i){return r=+r,e>>>=0,i||F(t,r,e,4),o.write(t,r,e,n,23,4),e+4}function H(t,r,e,n,i){return r=+r,e>>>=0,i||F(t,r,e,8),o.write(t,r,e,n,52,8),e+8}s.prototype.slice=function(t,r){var e=this.length;t=~~t,r=void 0===r?e:~~r,t<0?(t+=e,t<0&&(t=0)):t>e&&(t=e),r<0?(r+=e,r<0&&(r=0)):r>e&&(r=e),r<t&&(r=t);var n=this.subarray(t,r);return Object.setPrototypeOf(n,s.prototype),n},s.prototype.readUIntLE=function(t,r,e){t>>>=0,r>>>=0,e||D(t,r,this.length);var n=this[t],o=1,i=0;while(++i<r&&(o*=256))n+=this[t+i]*o;return n},s.prototype.readUIntBE=function(t,r,e){t>>>=0,r>>>=0,e||D(t,r,this.length);var n=this[t+--r],o=1;while(r>0&&(o*=256))n+=this[t+--r]*o;return n},s.prototype.readUInt8=function(t,r){return t>>>=0,r||D(t,1,this.length),this[t]},s.prototype.readUInt16LE=function(t,r){return t>>>=0,r||D(t,2,this.length),this[t]|this[t+1]<<8},s.prototype.readUInt16BE=function(t,r){return t>>>=0,r||D(t,2,this.length),this[t]<<8|this[t+1]},s.prototype.readUInt32LE=function(t,r){return t>>>=0,r||D(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},s.prototype.readUInt32BE=function(t,r){return t>>>=0,r||D(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},s.prototype.readIntLE=function(t,r,e){t>>>=0,r>>>=0,e||D(t,r,this.length);var n=this[t],o=1,i=0;while(++i<r&&(o*=256))n+=this[t+i]*o;return o*=128,n>=o&&(n-=Math.pow(2,8*r)),n},s.prototype.readIntBE=function(t,r,e){t>>>=0,r>>>=0,e||D(t,r,this.length);var n=r,o=1,i=this[t+--n];while(n>0&&(o*=256))i+=this[t+--n]*o;return o*=128,i>=o&&(i-=Math.pow(2,8*r)),i},s.prototype.readInt8=function(t,r){return t>>>=0,r||D(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},s.prototype.readInt16LE=function(t,r){t>>>=0,r||D(t,2,this.length);var e=this[t]|this[t+1]<<8;return 32768&e?4294901760|e:e},s.prototype.readInt16BE=function(t,r){t>>>=0,r||D(t,2,this.length);var e=this[t+1]|this[t]<<8;return 32768&e?4294901760|e:e},s.prototype.readInt32LE=function(t,r){return t>>>=0,r||D(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},s.prototype.readInt32BE=function(t,r){return t>>>=0,r||D(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},s.prototype.readFloatLE=function(t,r){return t>>>=0,r||D(t,4,this.length),o.read(this,t,!0,23,4)},s.prototype.readFloatBE=function(t,r){return t>>>=0,r||D(t,4,this.length),o.read(this,t,!1,23,4)},s.prototype.readDoubleLE=function(t,r){return t>>>=0,r||D(t,8,this.length),o.read(this,t,!0,52,8)},s.prototype.readDoubleBE=function(t,r){return t>>>=0,r||D(t,8,this.length),o.read(this,t,!1,52,8)},s.prototype.writeUIntLE=function(t,r,e,n){if(t=+t,r>>>=0,e>>>=0,!n){var o=Math.pow(2,8*e)-1;j(this,t,r,e,o,0)}var i=1,u=0;this[r]=255&t;while(++u<e&&(i*=256))this[r+u]=t/i&255;return r+e},s.prototype.writeUIntBE=function(t,r,e,n){if(t=+t,r>>>=0,e>>>=0,!n){var o=Math.pow(2,8*e)-1;j(this,t,r,e,o,0)}var i=e-1,u=1;this[r+i]=255&t;while(--i>=0&&(u*=256))this[r+i]=t/u&255;return r+e},s.prototype.writeUInt8=function(t,r,e){return t=+t,r>>>=0,e||j(this,t,r,1,255,0),this[r]=255&t,r+1},s.prototype.writeUInt16LE=function(t,r,e){return t=+t,r>>>=0,e||j(this,t,r,2,65535,0),this[r]=255&t,this[r+1]=t>>>8,r+2},s.prototype.writeUInt16BE=function(t,r,e){return t=+t,r>>>=0,e||j(this,t,r,2,65535,0),this[r]=t>>>8,this[r+1]=255&t,r+2},s.prototype.writeUInt32LE=function(t,r,e){return t=+t,r>>>=0,e||j(this,t,r,4,4294967295,0),this[r+3]=t>>>24,this[r+2]=t>>>16,this[r+1]=t>>>8,this[r]=255&t,r+4},s.prototype.writeUInt32BE=function(t,r,e){return t=+t,r>>>=0,e||j(this,t,r,4,4294967295,0),this[r]=t>>>24,this[r+1]=t>>>16,this[r+2]=t>>>8,this[r+3]=255&t,r+4},s.prototype.writeIntLE=function(t,r,e,n){if(t=+t,r>>>=0,!n){var o=Math.pow(2,8*e-1);j(this,t,r,e,o-1,-o)}var i=0,u=1,a=0;this[r]=255&t;while(++i<e&&(u*=256))t<0&&0===a&&0!==this[r+i-1]&&(a=1),this[r+i]=(t/u>>0)-a&255;return r+e},s.prototype.writeIntBE=function(t,r,e,n){if(t=+t,r>>>=0,!n){var o=Math.pow(2,8*e-1);j(this,t,r,e,o-1,-o)}var i=e-1,u=1,a=0;this[r+i]=255&t;while(--i>=0&&(u*=256))t<0&&0===a&&0!==this[r+i+1]&&(a=1),this[r+i]=(t/u>>0)-a&255;return r+e},s.prototype.writeInt8=function(t,r,e){return t=+t,r>>>=0,e||j(this,t,r,1,127,-128),t<0&&(t=255+t+1),this[r]=255&t,r+1},s.prototype.writeInt16LE=function(t,r,e){return t=+t,r>>>=0,e||j(this,t,r,2,32767,-32768),this[r]=255&t,this[r+1]=t>>>8,r+2},s.prototype.writeInt16BE=function(t,r,e){return t=+t,r>>>=0,e||j(this,t,r,2,32767,-32768),this[r]=t>>>8,this[r+1]=255&t,r+2},s.prototype.writeInt32LE=function(t,r,e){return t=+t,r>>>=0,e||j(this,t,r,4,2147483647,-2147483648),this[r]=255&t,this[r+1]=t>>>8,this[r+2]=t>>>16,this[r+3]=t>>>24,r+4},s.prototype.writeInt32BE=function(t,r,e){return t=+t,r>>>=0,e||j(this,t,r,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[r]=t>>>24,this[r+1]=t>>>16,this[r+2]=t>>>8,this[r+3]=255&t,r+4},s.prototype.writeFloatLE=function(t,r,e){return z(this,t,r,!0,e)},s.prototype.writeFloatBE=function(t,r,e){return z(this,t,r,!1,e)},s.prototype.writeDoubleLE=function(t,r,e){return H(this,t,r,!0,e)},s.prototype.writeDoubleBE=function(t,r,e){return H(this,t,r,!1,e)},s.prototype.copy=function(t,r,e,n){if(!s.isBuffer(t))throw new TypeError("argument should be a Buffer");if(e||(e=0),n||0===n||(n=this.length),r>=t.length&&(r=t.length),r||(r=0),n>0&&n<e&&(n=e),n===e)return 0;if(0===t.length||0===this.length)return 0;if(r<0)throw new RangeError("targetStart out of bounds");if(e<0||e>=this.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-r<n-e&&(n=t.length-r+e);var o=n-e;if(this===t&&"function"===typeof Uint8Array.prototype.copyWithin)this.copyWithin(r,e,n);else if(this===t&&e<r&&r<n)for(var i=o-1;i>=0;--i)t[i+r]=this[i+e];else Uint8Array.prototype.set.call(t,this.subarray(e,n),r);return o},s.prototype.fill=function(t,r,e,n){if("string"===typeof t){if("string"===typeof r?(n=r,r=0,e=this.length):"string"===typeof e&&(n=e,e=this.length),void 0!==n&&"string"!==typeof n)throw new TypeError("encoding must be a string");if("string"===typeof n&&!s.isEncoding(n))throw new TypeError("Unknown encoding: "+n);if(1===t.length){var o=t.charCodeAt(0);("utf8"===n&&o<128||"latin1"===n)&&(t=o)}}else"number"===typeof t?t&=255:"boolean"===typeof t&&(t=Number(t));if(r<0||this.length<r||this.length<e)throw new RangeError("Out of range index");if(e<=r)return this;var i;if(r>>>=0,e=void 0===e?this.length:e>>>0,t||(t=0),"number"===typeof t)for(i=r;i<e;++i)this[i]=t;else{var u=s.isBuffer(t)?t:s.from(t,n),a=u.length;if(0===a)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<e-r;++i)this[i+r]=u[i%a]}return this};var J=/[^+/0-9A-Za-z-_]/g;function K(t){if(t=t.split("=")[0],t=t.trim().replace(J,""),t.length<2)return"";while(t.length%4!==0)t+="=";return t}function q(t,r){var e;r=r||1/0;for(var n=t.length,o=null,i=[],u=0;u<n;++u){if(e=t.charCodeAt(u),e>55295&&e<57344){if(!o){if(e>56319){(r-=3)>-1&&i.push(239,191,189);continue}if(u+1===n){(r-=3)>-1&&i.push(239,191,189);continue}o=e;continue}if(e<56320){(r-=3)>-1&&i.push(239,191,189),o=e;continue}e=65536+(o-55296<<10|e-56320)}else o&&(r-=3)>-1&&i.push(239,191,189);if(o=null,e<128){if((r-=1)<0)break;i.push(e)}else if(e<2048){if((r-=2)<0)break;i.push(e>>6|192,63&e|128)}else if(e<65536){if((r-=3)<0)break;i.push(e>>12|224,e>>6&63|128,63&e|128)}else{if(!(e<1114112))throw new Error("Invalid code point");if((r-=4)<0)break;i.push(e>>18|240,e>>12&63|128,e>>6&63|128,63&e|128)}}return i}function V(t){for(var r=[],e=0;e<t.length;++e)r.push(255&t.charCodeAt(e));return r}function $(t,r){for(var e,n,o,i=[],u=0;u<t.length;++u){if((r-=2)<0)break;e=t.charCodeAt(u),n=e>>8,o=e%256,i.push(o),i.push(n)}return i}function Q(t){return n.toByteArray(K(t))}function X(t,r,e,n){for(var o=0;o<n;++o){if(o+e>=r.length||o>=t.length)break;r[o+e]=t[o]}return o}function Z(t,r){return t instanceof r||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===r.name}function W(t){return t!==t}var G=function(){for(var t="0123456789abcdef",r=new Array(256),e=0;e<16;++e)for(var n=16*e,o=0;o<16;++o)r[n+o]=t[e]+t[o];return r}()},{"base64-js":29,ieee754:32}],31:[function(t,r,e){var n={single_source_shortest_paths:function(t,r,e){var o={},i={};i[r]=0;var u,a,f,s,h,c,l,g,p,d=n.PriorityQueue.make();d.push(r,0);while(!d.empty())for(f in u=d.pop(),a=u.value,s=u.cost,h=t[a]||{},h)h.hasOwnProperty(f)&&(c=h[f],l=s+c,g=i[f],p="undefined"===typeof i[f],(p||g>l)&&(i[f]=l,d.push(f,l),o[f]=a));if("undefined"!==typeof e&&"undefined"===typeof i[e]){var y=["Could not find a path from ",r," to ",e,"."].join("");throw new Error(y)}return o},extract_shortest_path_from_predecessor_list:function(t,r){var e=[],n=r;while(n)e.push(n),t[n],n=t[n];return e.reverse(),e},find_path:function(t,r,e){var o=n.single_source_shortest_paths(t,r,e);return n.extract_shortest_path_from_predecessor_list(o,e)},PriorityQueue:{make:function(t){var r,e=n.PriorityQueue,o={};for(r in t=t||{},e)e.hasOwnProperty(r)&&(o[r]=e[r]);return o.queue=[],o.sorter=t.sorter||e.default_sorter,o},default_sorter:function(t,r){return t.cost-r.cost},push:function(t,r){var e={value:t,cost:r};this.queue.push(e),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};"undefined"!==typeof r&&(r.exports=n)},{}],32:[function(t,r,e){e.read=function(t,r,e,n,o){var i,u,a=8*o-n-1,f=(1<<a)-1,s=f>>1,h=-7,c=e?o-1:0,l=e?-1:1,g=t[r+c];for(c+=l,i=g&(1<<-h)-1,g>>=-h,h+=a;h>0;i=256*i+t[r+c],c+=l,h-=8);for(u=i&(1<<-h)-1,i>>=-h,h+=n;h>0;u=256*u+t[r+c],c+=l,h-=8);if(0===i)i=1-s;else{if(i===f)return u?NaN:1/0*(g?-1:1);u+=Math.pow(2,n),i-=s}return(g?-1:1)*u*Math.pow(2,i-n)},e.write=function(t,r,e,n,o,i){var u,a,f,s=8*i-o-1,h=(1<<s)-1,c=h>>1,l=23===o?Math.pow(2,-24)-Math.pow(2,-77):0,g=n?0:i-1,p=n?1:-1,d=r<0||0===r&&1/r<0?1:0;for(r=Math.abs(r),isNaN(r)||r===1/0?(a=isNaN(r)?1:0,u=h):(u=Math.floor(Math.log(r)/Math.LN2),r*(f=Math.pow(2,-u))<1&&(u--,f*=2),r+=u+c>=1?l/f:l*Math.pow(2,1-c),r*f>=2&&(u++,f/=2),u+c>=h?(a=0,u=h):u+c>=1?(a=(r*f-1)*Math.pow(2,o),u+=c):(a=r*Math.pow(2,c-1)*Math.pow(2,o),u=0));o>=8;t[e+g]=255&a,g+=p,a/=256,o-=8);for(u=u<<o|a,s+=o;s>0;t[e+g]=255&u,g+=p,u/=256,s-=8);t[e+g-p]|=128*d}},{}],33:[function(t,r,e){var n={}.toString;r.exports=Array.isArray||function(t){return"[object Array]"==n.call(t)}},{}]},{},[24])(24)}))})),n={name:"qrcode",props:{value:null,options:Object,tag:{type:String,default:"canvas"}},render:function(t){return t(this.tag,this.$slots.default)},watch:{$props:{deep:!0,immediate:!0,handler:function(){this.$el&&this.generate()}}},methods:{generate:function(){var t=this,r=this.options,n=this.tag,o=String(this.value);"canvas"===n?e.toCanvas(this.$el,o,r,(function(t){if(t)throw t})):"img"===n?e.toDataURL(o,r,(function(r,e){if(r)throw r;t.$el.src=e})):e.toString(o,r,(function(r,e){if(r)throw r;t.$el.innerHTML=e}))}},mounted:function(){this.generate()}};return n}))}}]);
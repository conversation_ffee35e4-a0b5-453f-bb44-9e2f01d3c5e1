(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-551f4c89","chunk-51e248ae"],{"2dad":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-row",{attrs:{gutter:24}},[e("el-col",{attrs:{span:17}},[e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"参数名",clearable:""},model:{value:t.dataForm.key,callback:function(e){t.$set(t.dataForm,"key",e)},expression:"dataForm.key"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),t.isAuth("cms:cms:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("cms:cms:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"activityName","header-align":"center",align:"center",label:"会议名称"}}),e("el-table-column",{attrs:{prop:"title","header-align":"center",align:"center",label:"标题"}}),e("el-table-column",{attrs:{prop:"pname","header-align":"center",align:"center",label:"父节点"}}),e("el-table-column",{attrs:{prop:"mobileIcon","header-align":"center",align:"center",label:"移动端图标"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[t.isImageUrl(a.row.mobileIcon)?e("img",{staticClass:"image-sm",attrs:{src:a.row.mobileIcon}}):e("a",{attrs:{href:a.row.mobileIcon,target:"_blank"}},[t._v(t._s(a.row.mobileIcon))])])}}])}),e("el-table-column",{attrs:{"show-overflow-tooltip":"",prop:"url","header-align":"center",align:"center",label:"外部链接"}}),e("el-table-column",{attrs:{prop:"model","header-align":"center",align:"center",label:"模块跳转"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("span",[t._v(t._s(t._f("modelFilter")(a.row.model)))])])}}])}),e("el-table-column",{attrs:{prop:"paixu","header-align":"center",align:"center",label:"排序"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}})],1),e("el-col",{attrs:{span:7}},[e("div",{staticClass:"mod-user"},[e("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"center"}},[e("vue-qrcode",{attrs:{options:{width:120},value:t.iframeSrc}}),e("div",{staticStyle:{"margin-left":"20px","font-size":"24px","font-weight":"600"}},[t._v("扫码通过手机预览")])],1),e("iframe",{ref:"iframeaaa",staticStyle:{width:"90%",height:"800px"},attrs:{src:t.iframeSrc}})])])],1),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e()],1)},n=[],r=(a("99af"),a("4de4"),a("a15b"),a("d81d"),a("b0c0"),a("d3b7"),a("ac1f"),a("00b4"),a("a573"),a("b2e5")),o=a.n(r),l=a("c69b"),d={data:function(){return{appid:"",iframeSrc:"",dataForm:{key:"",activityId:void 0},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,activityInfo:{}}},components:{AddOrUpdate:l["default"],VueQrcode:o.a},filters:{modelFilter:function(t){var e=[{name:"个人中心",value:'{"name": "meMine","query": {"id": "${activityId}"}}'},{name:"参会报名",value:'{"name": "applyIndex","query": {"id": "${activityId}"}}'},{name:"会议议程",value:'{"name": "schedulesIndex","query": {"id": "${activityId}"}}'},{name:"会议议程(新)",value:'{"name": "schedulesIndexNew","query": {"id": "${activityId}"}}'},{name:"嘉宾列表",value:'{"name": "schedulesExperts","query": {"id": "${activityId}"}}'},{name:"酒店预订",value:'{"name": "hotelIndex","query": {"id": "${activityId}"}}'},{name:"会议直播",value:'{"name": "livesIndex","query": {"id": "${activityId}"}}'},{name:"考试&问卷",value:'{"name": "examIndex","query": {"id": "${activityId}"}}'},{name:"展商列表",value:'{"name": "merchantIndex","query": {"id": "${activityId}"}}'},{name:"导航列表",value:'{"name": "divNav","query": {"id": "${activityId}"}}'},{name:"座位查询",value:'{"name": "divZuowei","query": {"id": "${activityId}"}}'},{name:"专家信息确认",value:'{"name": "expertIndexCheck","query": {"id": "${activityId}"}}'}],a=e.filter((function(e){return e.value===t}));if(a.length>=1)return a[0].name}},activated:function(){this.appid=this.$cookie.get("appid"),this.dataForm.activityId=this.$route.query.activityId,this.getDataList()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/cms/cms/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,key:this.dataForm.key,activityId:this.dataForm.activityId})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1})),this.getAccountInfo()},getActivity:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activity/info/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.activityInfo=a.activity,t.iframeSrc=t.wxAccount.baseUrl+"cms/index?pc=false&id="+t.dataForm.activityId)}))},getAccountInfo:function(){var t=this;this.$http({url:this.$http.adornUrl("/manage/wxAccount/info/".concat(this.appid)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.wxAccount=a.wxAccount,t.getActivity())}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(e.dataForm.activityId,t)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/cms/cms/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},isImageUrl:function(t){return t&&/.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(t)}}},c=d,s=a("2877"),u=Object(s["a"])(c,i,n,!1,null,null,null);e["default"]=u.exports},"7de9":function(t,e,a){"use strict";a.d(e,"g",(function(){return i})),a.d(e,"f",(function(){return n})),a.d(e,"e",(function(){return r})),a.d(e,"a",(function(){return o})),a.d(e,"b",(function(){return l})),a.d(e,"c",(function(){return d})),a.d(e,"d",(function(){return c}));var i=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],n=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],r=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],o=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],l=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],d=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],c=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]},a15b:function(t,e,a){"use strict";var i=a("23e7"),n=a("e330"),r=a("44ad"),o=a("fc6a"),l=a("a640"),d=n([].join),c=r!==Object,s=c||!l("join",",");i({target:"Array",proto:!0,forced:s},{join:function(t){return d(o(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var i=a("23e7"),n=a("d024"),r=a("c430");i({target:"Iterator",proto:!0,real:!0,forced:r},{map:n})},c69b:function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"标题",prop:"title"}},[e("el-input",{attrs:{placeholder:"标题"},model:{value:t.dataForm.title,callback:function(e){t.$set(t.dataForm,"title",e)},expression:"dataForm.title"}})],1),e("el-form-item",{attrs:{label:"父id",prop:"pid"}},[e("el-select",{attrs:{placeholder:"父id",filterable:""},model:{value:t.dataForm.pid,callback:function(e){t.$set(t.dataForm,"pid",e)},expression:"dataForm.pid"}},[e("el-option",{attrs:{label:"无",value:"0"}}),t._l(t.cmsList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.title,value:t.id}})}))],2)],1),e("el-form-item",{attrs:{label:"移动端图标",prop:"mobileIcon"}},[e("el-upload",{staticClass:"avatar-uploader",attrs:{"before-upload":t.checkFileSize,"list-type":"picture-card","show-file-list":!1,accept:".jpg, .jpeg, .png, .gif","on-success":t.appSuccessHandle,action:t.url}},[t.dataForm.mobileIcon?e("img",{staticClass:"avatar",attrs:{width:"100px",src:t.dataForm.mobileIcon}}):e("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),e("el-form-item",{attrs:{label:"排序",prop:"paixu"}},[e("el-input-number",{attrs:{min:0,max:100,label:"排序"},model:{value:t.dataForm.paixu,callback:function(e){t.$set(t.dataForm,"paixu",e)},expression:"dataForm.paixu"}})],1),e("div",{staticStyle:{color:"red"}},[t._v("配置外部链接后，“模块跳转”和“内容”不生效")]),e("el-form-item",{attrs:{label:"外部链接",prop:"url"}},[e("el-input",{attrs:{placeholder:"外部链接(如果配置内容失效)"},model:{value:t.dataForm.url,callback:function(e){t.$set(t.dataForm,"url",e)},expression:"dataForm.url"}})],1),e("el-form-item",{attrs:{label:"背景颜色",prop:"color"}},[e("el-color-picker",{model:{value:t.dataForm.color,callback:function(e){t.$set(t.dataForm,"color",e)},expression:"dataForm.color"}})],1),e("el-form-item",{attrs:{label:"模块跳转",prop:"model"}},[e("el-select",{attrs:{placeholder:"模块跳转",filterable:""},model:{value:t.dataForm.model,callback:function(e){t.$set(t.dataForm,"model",e)},expression:"dataForm.model"}},[e("el-option",{attrs:{label:"无",value:""}}),t._l(t.modelList,(function(t){return e("el-option",{key:t.name,attrs:{label:t.name,value:t.value}})}))],2)],1),e("el-form-item",{attrs:{label:"内容",prop:"content"}},[e("tinymce-editor",{ref:"editor",model:{value:t.dataForm.content,callback:function(e){t.$set(t.dataForm,"content",e)},expression:"dataForm.content"}})],1),e("el-form-item",{attrs:{label:"动画效果",prop:"animate"}},[e("el-select",{attrs:{placeholder:"动画效果",filterable:""},model:{value:t.dataForm.animate,callback:function(e){t.$set(t.dataForm,"animate",e)},expression:"dataForm.animate"}},[e("el-option",{attrs:{label:"无",value:""}}),t._l(t.animate,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})}))],2)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"经度",prop:"longitude"}},[e("el-input",{attrs:{placeholder:"经度"},model:{value:t.dataForm.longitude,callback:function(e){t.$set(t.dataForm,"longitude",e)},expression:"dataForm.longitude"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"纬度",prop:"latitude"}},[e("el-input",{attrs:{placeholder:"纬度"},model:{value:t.dataForm.latitude,callback:function(e){t.$set(t.dataForm,"latitude",e)},expression:"dataForm.latitude"}})],1)],1)],1),e("a",{staticStyle:{color:"red","margin-left":"50px"},attrs:{target:"_blank",href:"https://lbs.qq.com/tool/getpoint/index.html"}},[t._v("腾讯地图坐标拾取工具")])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},n=[],r=(a("d3b7"),a("25f0"),a("3ca3"),a("ddb0"),a("7de9")),o=a("7c8d"),l=a.n(o),d={components:{TinymceEditor:function(){return Promise.all([a.e("chunk-03be236c"),a.e("chunk-2d0a4b8c")]).then(a.bind(null,"26dc"))},OssUploader:function(){return a.e("chunk-2d0e97b1").then(a.bind(null,"8e5c"))}},data:function(){return{animate:r["a"],gradient:r["b"],visible:!1,imgAppDialogVisible:!1,dialogAppImageUrl:"",url:"",cmsList:[],modelList:[{name:"个人中心",value:'{"name": "meMine","query": {"id": "${activityId}"}}'},{name:"会议议程",value:'{"name": "schedulesIndex","query": {"id": "${activityId}"}}'},{name:"会议议程(新)",value:'{"name": "schedulesIndexNew","query": {"id": "${activityId}"}}'},{name:"嘉宾列表",value:'{"name": "schedulesExperts","query": {"id": "${activityId}"}}'},{name:"参会报名",value:'{"name": "applyIndex","query": {"id": "${activityId}"}}'},{name:"酒店预订",value:'{"name": "hotelIndex","query": {"id": "${activityId}"}}'},{name:"会议直播",value:'{"name": "livesIndex","query": {"id": "${activityId}"}}'},{name:"考试&问卷",value:'{"name": "examIndex","query": {"id": "${activityId}"}}'},{name:"展商列表",value:'{"name": "merchantIndex","query": {"id": "${activityId}"}}'},{name:"导航列表",value:'{"name": "divNav","query": {"id": "${activityId}"}}'},{name:"座位查询",value:'{"name": "divZuowei","query": {"id": "${activityId}"}}'},{name:"专家信息确认",value:'{"name": "expertIndexCheck","query": {"id": "${activityId}"}}'}],dataForm:{id:0,title:"",pid:"0",activityId:"",mobileIcon:"",content:"",mobileContent:"",url:"",model:"",color:"",longitude:"",latitude:"",animate:"",paixu:0},dataRule:{title:[{required:!0,message:"标题不能为空",trigger:"blur"}],pid:[{required:!0,message:"父id不能为空",trigger:"blur"}],activityId:[{required:!0,message:"活动表id不能为空",trigger:"blur"}],mobileIcon:[{required:!0,message:"移动端图标不能为空",trigger:"blur"}],paixu:[{required:!0,message:"排序不能为空",trigger:"blur"}]}}},methods:{init:function(t,e){var a=this;this.dataForm.activityId=t,this.dataForm.id=e||0,this.visible=!0,this.url=this.$http.adornUrl("/sys/oss/upload?token=".concat(this.$cookie.get("token"))),this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/cms/cms/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.dataForm.title=e.cms.title,a.dataForm.pid=e.cms.pid.toString(),a.dataForm.activityId=e.cms.activityId,a.dataForm.mobileIcon=e.cms.mobileIcon,a.dataForm.content=e.cms.content,a.dataForm.mobileContent=e.cms.mobileContent,a.dataForm.url=e.cms.url,a.dataForm.paixu=e.cms.paixu,a.dataForm.model=e.cms.model,a.dataForm.color=e.cms.color,a.dataForm.longitude=e.cms.longitude,a.dataForm.latitude=e.cms.latitude,a.dataForm.animate=e.cms.animate)}))})),this.getCmsByActivityId()},getCmsByActivityId:function(){var t=this;this.$http({url:this.$http.adornUrl("/cms/cms/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.cmsList=a.result)}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/cms/cms/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,title:t.dataForm.title,pid:t.dataForm.pid,activityId:t.dataForm.activityId,mobileIcon:t.dataForm.mobileIcon,content:t.dataForm.content,mobileContent:t.dataForm.mobileContent,url:t.dataForm.url,model:t.dataForm.model,color:t.dataForm.color,longitude:t.dataForm.longitude,latitude:t.dataForm.latitude,animate:t.dataForm.animate,paixu:t.dataForm.paixu})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))},checkFileSize:function(t){return t.size/1024>110?(this.$message.error("".concat(t.name,"文件大于100KB，请选择小于100KB大小的icon")),!1):!(t.size/1024>100)||new Promise((function(e,a){new l.a(t,{quality:.8,success:function(t){e(t)}})}))},beforeUploadHandle:function(t){if("image/jpg"!==t.type&&"image/jpeg"!==t.type&&"image/png"!==t.type&&"image/gif"!==t.type)return this.$message.error("只支持jpg、png、gif格式的图片！"),!1},appSuccessHandle:function(t,e,a){t&&200===t.code?this.dataForm.mobileIcon=t.url:this.$message.error(t.msg)}}},c=d,s=a("2877"),u=Object(s["a"])(c,i,n,!1,null,null,null);e["default"]=u.exports},d024:function(t,e,a){"use strict";var i=a("c65b"),n=a("59ed"),r=a("825a"),o=a("46c4"),l=a("c5cc"),d=a("9bdd"),c=l((function(){var t=this.iterator,e=r(i(this.next,t)),a=this.done=!!e.done;if(!a)return d(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return r(this),n(t),new c(o(this),{mapper:t})}},d81d:function(t,e,a){"use strict";var i=a("23e7"),n=a("b727").map,r=a("1dde"),o=r("map");i({target:"Array",proto:!0,forced:!o},{map:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}})}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d21ef54"],{d87a:function(e,a,t){"use strict";t.r(a);var i=function(){var e=this,a=e._self._c;return a("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(a){e.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"收费类型id",prop:"channelId"}},[a("el-input",{attrs:{placeholder:"收费类型id"},model:{value:e.dataForm.channelId,callback:function(a){e.$set(e.dataForm,"channelId",a)},expression:"dataForm.channelId"}})],1),a("el-form-item",{attrs:{label:"会议id",prop:"activityId"}},[a("el-input",{attrs:{placeholder:"会议id"},model:{value:e.dataForm.activityId,callback:function(a){e.$set(e.dataForm,"activityId",a)},expression:"dataForm.activityId"}})],1),a("el-form-item",{attrs:{label:"邀请码",prop:"inviteCode"}},[a("el-input",{attrs:{placeholder:"邀请码"},model:{value:e.dataForm.inviteCode,callback:function(a){e.$set(e.dataForm,"inviteCode",a)},expression:"dataForm.inviteCode"}})],1),a("el-form-item",{attrs:{label:"是否使用 0-未使用 1-已使用",prop:"isUse"}},[a("el-input",{attrs:{placeholder:"是否使用 0-未使用 1-已使用"},model:{value:e.dataForm.isUse,callback:function(a){e.$set(e.dataForm,"isUse",a)},expression:"dataForm.isUse"}})],1),a("el-form-item",{attrs:{label:"使用时间",prop:"useTime"}},[a("el-input",{attrs:{placeholder:"使用时间"},model:{value:e.dataForm.useTime,callback:function(a){e.$set(e.dataForm,"useTime",a)},expression:"dataForm.useTime"}})],1),a("el-form-item",{attrs:{label:"使用人",prop:"useBy"}},[a("el-input",{attrs:{placeholder:"使用人"},model:{value:e.dataForm.useBy,callback:function(a){e.$set(e.dataForm,"useBy",a)},expression:"dataForm.useBy"}})],1),a("el-form-item",{attrs:{label:"使用时间",prop:"useByName"}},[a("el-input",{attrs:{placeholder:"使用时间"},model:{value:e.dataForm.useByName,callback:function(a){e.$set(e.dataForm,"useByName",a)},expression:"dataForm.useByName"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){e.visible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},r=[],o={data:function(){return{visible:!1,dataForm:{id:0,channelId:"",activityId:"",inviteCode:"",isUse:"",useTime:"",useBy:"",useByName:""},dataRule:{channelId:[{required:!0,message:"收费类型id不能为空",trigger:"blur"}],activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],inviteCode:[{required:!0,message:"邀请码不能为空",trigger:"blur"}],isUse:[{required:!0,message:"是否使用不能为空",trigger:"blur"}],useTime:[{required:!0,message:"使用时间不能为空",trigger:"blur"}],useBy:[{required:!0,message:"使用人不能为空",trigger:"blur"}],useByName:[{required:!0,message:"使用时间不能为空",trigger:"blur"}]}}},methods:{init:function(e){var a=this;this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/apply/applyinvitecode/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(e){var t=e.data;t&&200===t.code&&(a.dataForm.channelId=t.applyInviteCode.channelId,a.dataForm.activityId=t.applyInviteCode.activityId,a.dataForm.inviteCode=t.applyInviteCode.inviteCode,a.dataForm.isUse=t.applyInviteCode.isUse,a.dataForm.useTime=t.applyInviteCode.useTime,a.dataForm.useBy=t.applyInviteCode.useBy,a.dataForm.useByName=t.applyInviteCode.useByName)}))}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(a){a&&e.$http({url:e.$http.adornUrl("/apply/applyinvitecode/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,channelId:e.dataForm.channelId,activityId:e.dataForm.activityId,inviteCode:e.dataForm.inviteCode,isUse:e.dataForm.isUse,useTime:e.dataForm.useTime,useBy:e.dataForm.useBy,useByName:e.dataForm.useByName})}).then((function(a){var t=a.data;t&&200===t.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(t.msg)}))}))}}},s=o,d=t("2877"),l=Object(d["a"])(s,i,r,!1,null,null,null);a["default"]=l.exports}}]);
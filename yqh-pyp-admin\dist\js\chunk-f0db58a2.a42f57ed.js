(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f0db58a2"],{"149e":function(t,a,o){"use strict";o.r(a);var e=function(){var t=this,a=t._self._c;return a("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible,width:"800px"},on:{"update:visible":function(a){t.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&t._k(a.keyCode,"enter",13,a.key,"Enter")?null:t.dataFormSubmit()}}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"平台类型",prop:"platformType"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择平台类型"},model:{value:t.dataForm.platformType,callback:function(a){t.$set(t.dataForm,"platformType",a)},expression:"dataForm.platformType"}},[a("el-option",{attrs:{label:"抖音团购",value:"douyin"}}),a("el-option",{attrs:{label:"美团团购",value:"meituan"}}),a("el-option",{attrs:{label:"大众点评团购",value:"dianping"}})],1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"团购券名称",prop:"couponName"}},[a("el-input",{attrs:{placeholder:"团购券名称"},model:{value:t.dataForm.couponName,callback:function(a){t.$set(t.dataForm,"couponName",a)},expression:"dataForm.couponName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"团购券描述",prop:"couponDescription"}},[a("el-input",{attrs:{type:"textarea",placeholder:"团购券描述"},model:{value:t.dataForm.couponDescription,callback:function(a){t.$set(t.dataForm,"couponDescription",a)},expression:"dataForm.couponDescription"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"原价",prop:"originalPrice"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{precision:2,min:0,placeholder:"原价"},model:{value:t.dataForm.originalPrice,callback:function(a){t.$set(t.dataForm,"originalPrice",a)},expression:"dataForm.originalPrice"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"团购价",prop:"groupPrice"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{precision:2,min:0,placeholder:"团购价"},model:{value:t.dataForm.groupPrice,callback:function(a){t.$set(t.dataForm,"groupPrice",a)},expression:"dataForm.groupPrice"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"团购券ID",prop:"couponId"}},[a("el-input",{attrs:{placeholder:"平台提供的团购券ID"},model:{value:t.dataForm.couponId,callback:function(a){t.$set(t.dataForm,"couponId",a)},expression:"dataForm.couponId"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"封面图片",prop:"coverImage"}},[a("div",{staticClass:"image-upload-container"},[t.coverImages.length>0?a("div",{staticClass:"image-preview"},[a("img",{staticClass:"preview-image",attrs:{src:t.coverImages[0].url,alt:"封面图片"},on:{click:function(a){return t.previewImage(t.coverImages[0].url)}}}),a("div",{staticClass:"image-actions"},[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.openImageModal("coverImage")}}},[t._v("更换")]),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.removeCoverImage()}}},[t._v("删除")])],1)]):a("div",{staticClass:"upload-placeholder",on:{click:function(a){return t.openImageModal("coverImage")}}},[a("i",{staticClass:"el-icon-plus"}),a("div",[t._v("点击上传封面图片")])])]),a("div",{staticStyle:{color:"#909399","font-size":"12px","margin-top":"5px"}},[t._v("建议尺寸：750*400，大小2MB以下")])])],1)],1),a("el-row"),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"总数量",prop:"totalCount"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,placeholder:"总数量（空表示不限量）"},model:{value:t.dataForm.totalCount,callback:function(a){t.$set(t.dataForm,"totalCount",a)},expression:"dataForm.totalCount"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"已售数量",prop:"soldCount"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,placeholder:"已售数量"},model:{value:t.dataForm.soldCount,callback:function(a){t.$set(t.dataForm,"soldCount",a)},expression:"dataForm.soldCount"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"排序（数字越大越靠前）"},model:{value:t.dataForm.sortOrder,callback:function(a){t.$set(t.dataForm,"sortOrder",a)},expression:"dataForm.sortOrder"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:t.dataForm.status,callback:function(a){t.$set(t.dataForm,"status",a)},expression:"dataForm.status"}},[a("el-radio",{attrs:{label:1}},[t._v("上架")]),a("el-radio",{attrs:{label:0}},[t._v("下架")])],1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注信息",prop:"remarks"}},[a("el-input",{attrs:{type:"textarea",placeholder:"备注信息"},model:{value:t.dataForm.remarks,callback:function(a){t.$set(t.dataForm,"remarks",a)},expression:"dataForm.remarks"}})],1)],1)],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){t.visible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.dataFormSubmit()}}},[t._v("确定")])],1),a("ImageUploadModal",{attrs:{visible:t.imageModalVisible,multiple:!1,"max-count":1,"default-images":t.getCurrentImages()},on:{"update:visible":function(a){t.imageModalVisible=a},confirm:t.handleImageConfirm}}),a("el-dialog",{attrs:{visible:t.imgDialogVisible,width:"60%"},on:{"update:visible":function(a){t.imgDialogVisible=a}}},[a("img",{attrs:{width:"100%",src:t.dialogImageUrl,alt:""}})])],1)},r=[],i=(o("d3b7"),o("3ca3"),o("ddb0"),{components:{ImageUploadModal:function(){return Promise.all([o.e("chunk-2d0e1c2e"),o.e("chunk-e9144aa4"),o.e("chunk-297aad0f")]).then(o.bind(null,"4185"))}},data:function(){return{visible:!1,activityList:[],imageModalVisible:!1,currentImageField:"",coverImages:[],imgDialogVisible:!1,dialogImageUrl:"",dataForm:{id:0,activityId:"",platformType:"",couponName:"",couponDescription:"",originalPrice:null,groupPrice:null,discountInfo:"",couponUrl:"",couponId:"",qrCodeUrl:"",coverImage:"",startTime:null,endTime:null,totalCount:null,soldCount:0,status:1,sortOrder:0,remarks:""},dataRule:{activityId:[{required:!0,message:"活动不能为空",trigger:"change"}],platformType:[{required:!0,message:"平台类型不能为空",trigger:"change"}],couponName:[{required:!0,message:"团购券名称不能为空",trigger:"blur"}],groupPrice:[{required:!0,message:"团购价不能为空",trigger:"blur"}]}}},methods:{init:function(t,a){var o=this;this.dataForm.id=t||0,this.dataForm.activityId=a,this.visible=!0,this.getActivityList(),this.initImageArrays(),this.$nextTick((function(){o.$refs["dataForm"].resetFields(),o.dataForm.id&&o.$http({url:o.$http.adornUrl("/groupbuying/coupon/info/".concat(o.dataForm.id)),method:"get",params:o.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(o.dataForm.activityId=a.coupon.activityId,o.dataForm.platformType=a.coupon.platformType,o.dataForm.couponName=a.coupon.couponName,o.dataForm.couponDescription=a.coupon.couponDescription,o.dataForm.originalPrice=a.coupon.originalPrice,o.dataForm.groupPrice=a.coupon.groupPrice,o.dataForm.discountInfo=a.coupon.discountInfo,o.dataForm.couponUrl=a.coupon.couponUrl,o.dataForm.couponId=a.coupon.couponId,o.dataForm.qrCodeUrl=a.coupon.qrCodeUrl,o.dataForm.coverImage=a.coupon.coverImage,o.dataForm.startTime=a.coupon.startTime,o.dataForm.endTime=a.coupon.endTime,o.dataForm.totalCount=a.coupon.totalCount,o.dataForm.soldCount=a.coupon.soldCount,o.dataForm.status=a.coupon.status,o.dataForm.sortOrder=a.coupon.sortOrder,o.dataForm.remarks=a.coupon.remarks,o.initImageArrays())}))}))},getActivityList:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activity/list"),method:"get",params:this.$http.adornParams({page:1,limit:1e3})}).then((function(a){var o=a.data;o&&200===o.code&&(t.activityList=o.page.list)}))},initImageArrays:function(){this.dataForm.coverImage?this.coverImages=[{url:this.dataForm.coverImage,name:"cover.jpg"}]:this.coverImages=[]},openImageModal:function(t){this.currentImageField=t,this.imageModalVisible=!0},getCurrentImages:function(){switch(this.currentImageField){case"coverImage":return this.coverImages;default:return[]}},handleImageConfirm:function(t){switch(this.currentImageField){case"coverImage":this.coverImages=t,this.dataForm.coverImage=t.length>0?t[0].url:"";break}this.imageModalVisible=!1},previewImage:function(t){this.dialogImageUrl=t,this.imgDialogVisible=!0},removeCoverImage:function(){this.coverImages=[],this.dataForm.coverImage=""},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(a){a&&t.$http({url:t.$http.adornUrl("/groupbuying/coupon/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,activityId:t.dataForm.activityId,platformType:t.dataForm.platformType,couponName:t.dataForm.couponName,couponDescription:t.dataForm.couponDescription,originalPrice:t.dataForm.originalPrice,groupPrice:t.dataForm.groupPrice,discountInfo:t.dataForm.discountInfo,couponUrl:t.dataForm.couponUrl,couponId:t.dataForm.couponId,qrCodeUrl:t.dataForm.qrCodeUrl,coverImage:t.dataForm.coverImage,startTime:t.dataForm.startTime,endTime:t.dataForm.endTime,totalCount:t.dataForm.totalCount,soldCount:t.dataForm.soldCount,status:t.dataForm.status,sortOrder:t.dataForm.sortOrder,remarks:t.dataForm.remarks})}).then((function(a){var o=a.data;o&&200===o.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(o.msg)}))}))}}}),l=i,n=(o("7f3c"),o("2877")),s=Object(n["a"])(l,e,r,!1,null,"3a4f6b44",null);a["default"]=s.exports},"7f3c":function(t,a,o){"use strict";o("e988")},e988:function(t,a,o){}}]);
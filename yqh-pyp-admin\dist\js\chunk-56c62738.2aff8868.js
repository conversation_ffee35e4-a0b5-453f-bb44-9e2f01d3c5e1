(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-56c62738","chunk-2d0bdd69"],{"2e66":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"库存时间",prop:"stockDate"}},[e("el-date-picker",{staticStyle:{windth:"100%"},attrs:{disabled:"",type:"date","value-format":"yyyy/MM/dd",placeholder:"库存时间"},model:{value:t.dataForm.stockDate,callback:function(e){t.$set(t.dataForm,"stockDate",e)},expression:"dataForm.stockDate"}})],1),e("el-form-item",{attrs:{label:"价格",prop:"price"}},[e("el-input",{attrs:{placeholder:"库存时间对应价格"},model:{value:t.dataForm.price,callback:function(e){t.$set(t.dataForm,"price",e)},expression:"dataForm.price"}},[e("template",{slot:"append"},[t._v(" RMB/间 ")])],2)],1),e("el-form-item",{attrs:{label:"总库存",prop:"number"}},[e("el-input",{attrs:{placeholder:"总库存"},model:{value:t.dataForm.number,callback:function(e){t.$set(t.dataForm,"number",e)},expression:"dataForm.number"}},[e("template",{slot:"append"},[t._v(" 间 ")])],2)],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},i=[],r=(a("d9e2"),a("61f7")),n={data:function(){var t=function(t,e,a){Object(r["d"])(e)?a():a(new Error("必须为数字"))},e=function(t,e,a){Object(r["b"])(e)?a():a(new Error("必须为数字"))};return{visible:!1,hotelActivityRoom:{},dataForm:{id:0,hotelActivityRoomId:"",activityId:"",hotelId:"",hotelActivityId:"",stockDate:"",price:"",bedPrice:"",number:0,alreadyNumber:0,spareNumber:0},dataRule:{stockDate:[{required:!0,message:"库存时间不能为空",trigger:"blur"}],price:[{required:!0,message:"库存时间对应价格不能为空",trigger:"blur"},{validator:e,trigger:"blur"}],number:[{required:!0,message:"总库存不能为空",trigger:"blur"},{validator:t,trigger:"blur"}]}}},methods:{init:function(t,e,a){var o=this;this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){o.$refs["dataForm"].resetFields(),o.dataForm.id?o.$http({url:o.$http.adornUrl("/hotel/hotelactivityroomstock/info/".concat(o.dataForm.id)),method:"get",params:o.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(o.dataForm.hotelActivityRoomId=e.hotelActivityRoomStock.hotelActivityRoomId,o.dataForm.activityId=e.hotelActivityRoomStock.activityId,o.dataForm.hotelId=e.hotelActivityRoomStock.hotelId,o.dataForm.hotelActivityId=e.hotelActivityRoomStock.hotelActivityId,o.dataForm.stockDate=e.hotelActivityRoomStock.stockDate,o.dataForm.price=e.hotelActivityRoomStock.price,o.dataForm.bedPrice=e.hotelActivityRoomStock.bedPrice,o.dataForm.number=e.hotelActivityRoomStock.number,o.dataForm.alreadyNumber=e.hotelActivityRoomStock.alreadyNumber,o.dataForm.spareNumber=e.hotelActivityRoomStock.spareNumber,o.dataForm.createOn=e.hotelActivityRoomStock.createOn,o.dataForm.createBy=e.hotelActivityRoomStock.createBy,o.dataForm.updateOn=e.hotelActivityRoomStock.updateOn,o.dataForm.updateBy=e.hotelActivityRoomStock.updateBy)})):(o.dataForm.hotelActivityRoomId=e,o.dataForm.stockDate=a,o.getRoomInfo())}))},getRoomInfo:function(){var t=this;this.$http({url:this.$http.adornUrl("/hotel/hotelactivityroom/info/".concat(this.dataForm.hotelActivityRoomId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.hotelActivityRoom=a.hotelActivityRoom,t.dataForm.activityId=a.hotelActivityRoom.activityId,t.dataForm.hotelId=a.hotelActivityRoom.hotelId,t.dataForm.hotelActivityId=a.hotelActivityRoom.hotelActivityId,t.dataForm.price=a.hotelActivityRoom.price,t.dataForm.bedPrice=a.hotelActivityRoom.bedPrice)}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/hotel/hotelactivityroomstock/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,hotelActivityRoomId:t.dataForm.hotelActivityRoomId,activityId:t.dataForm.activityId,hotelId:t.dataForm.hotelId,hotelActivityId:t.dataForm.hotelActivityId,stockDate:t.dataForm.stockDate,price:t.dataForm.price,bedPrice:t.dataForm.bedPrice,number:t.dataForm.number,alreadyNumber:t.dataForm.alreadyNumber,spareNumber:t.dataForm.spareNumber})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))}}},l=n,d=a("2877"),c=Object(d["a"])(l,o,i,!1,null,null,null);e["default"]=c.exports},"82fe":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"参数名",clearable:""},model:{value:t.dataForm.key,callback:function(e){t.$set(t.dataForm,"key",e)},expression:"dataForm.key"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),t.isAuth("hotel:hotelactivityroomstock:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("hotel:hotelactivityroomstock:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"hotelActivityRoomId","header-align":"center",align:"center",label:"会议酒店房型id"}}),e("el-table-column",{attrs:{prop:"activityId","header-align":"center",align:"center",label:"会议id"}}),e("el-table-column",{attrs:{prop:"hotelId","header-align":"center",align:"center",label:"酒店id"}}),e("el-table-column",{attrs:{prop:"hotelActivityId","header-align":"center",align:"center",label:"会议酒店id"}}),e("el-table-column",{attrs:{prop:"stockDate","header-align":"center",align:"center",label:"库存时间"}}),e("el-table-column",{attrs:{prop:"price","header-align":"center",align:"center",label:"库存时间对应价格"}}),e("el-table-column",{attrs:{prop:"bedPrice","header-align":"center",align:"center",label:"库存时间对应床位价格"}}),e("el-table-column",{attrs:{prop:"number","header-align":"center",align:"center",label:"总库存"}}),e("el-table-column",{attrs:{prop:"alreadyNumber","header-align":"center",align:"center",label:"已占用库存"}}),e("el-table-column",{attrs:{prop:"spareNumber","header-align":"center",align:"center",label:"剩余库存"}}),e("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",label:"创建时间"}}),e("el-table-column",{attrs:{prop:"updateOn","header-align":"center",align:"center",label:"更新时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e()],1)},i=[],r=(a("99af"),a("a15b"),a("d81d"),a("a573"),a("2e66")),n={data:function(){return{dataForm:{key:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1}},components:{AddOrUpdate:r["default"]},activated:function(){this.getDataList()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/hotel/hotelactivityroomstock/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,key:this.dataForm.key})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(t)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/hotel/hotelactivityroomstock/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))}}},l=n,d=a("2877"),c=Object(d["a"])(l,o,i,!1,null,null,null);e["default"]=c.exports},a15b:function(t,e,a){"use strict";var o=a("23e7"),i=a("e330"),r=a("44ad"),n=a("fc6a"),l=a("a640"),d=i([].join),c=r!==Object,s=c||!l("join",",");o({target:"Array",proto:!0,forced:s},{join:function(t){return d(n(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var o=a("23e7"),i=a("d024"),r=a("c430");o({target:"Iterator",proto:!0,real:!0,forced:r},{map:i})},d024:function(t,e,a){"use strict";var o=a("c65b"),i=a("59ed"),r=a("825a"),n=a("46c4"),l=a("c5cc"),d=a("9bdd"),c=l((function(){var t=this.iterator,e=r(o(this.next,t)),a=this.done=!!e.done;if(!a)return d(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return r(this),i(t),new c(n(this),{mapper:t})}},d81d:function(t,e,a){"use strict";var o=a("23e7"),i=a("b727").map,r=a("1dde"),n=r("map");o({target:"Array",proto:!0,forced:!n},{map:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-564666f0","chunk-43478e7c"],{"0903":function(t,e,a){"use strict";a("7eb5")},"0ccb":function(t,e,a){"use strict";var i=a("e330"),r=a("50c4"),n=a("577e"),o=a("1148"),s=a("1d80"),l=i(o),d=i("".slice),c=Math.ceil,u=function(t){return function(e,a,i){var o,u,p=n(s(e)),m=r(a),h=p.length,f=void 0===i?" ":n(i);return m<=h||""===f?p:(o=m-h,u=l(f,c(o/f.length)),u.length>o&&(u=d(u,0,o)),t?p+u:u+p)}};t.exports={start:u(!1),end:u(!0)}},1148:function(t,e,a){"use strict";var i=a("5926"),r=a("577e"),n=a("1d80"),o=RangeError;t.exports=function(t){var e=r(n(this)),a="",s=i(t);if(s<0||s===1/0)throw new o("Wrong number of repetitions");for(;s>0;(s>>>=1)&&(e+=e))1&s&&(a+=e);return a}},"117f":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"学术任务","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[t.topic.length>0?e("div",[e("h2",{staticStyle:{"text-align":"center"}},[t._v("主题主持任务")]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.topic,border:"","row-class-name":t.tableRowClassName}},[e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"主题名称"}}),e("el-table-column",{attrs:{prop:"placeName","header-align":"center",align:"center",label:"主题所属场地"}}),e("el-table-column",{attrs:{prop:"startTime","header-align":"center",align:"center",label:"开始时间"}}),e("el-table-column",{attrs:{prop:"endTime","header-align":"center",align:"center",label:"结束时间"}})],1)],1):t._e(),t.topicSpeaker.length>0?e("div",[e("h2",{staticStyle:{"text-align":"center"}},[t._v("主题主席任务")]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.topicSpeaker,border:"","row-class-name":t.tableRowClassName}},[e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"主题名称"}}),e("el-table-column",{attrs:{prop:"placeName","header-align":"center",align:"center",label:"主题所属场地"}}),e("el-table-column",{attrs:{prop:"startTime","header-align":"center",align:"center",label:"开始时间"}}),e("el-table-column",{attrs:{prop:"endTime","header-align":"center",align:"center",label:"结束时间"}})],1)],1):t._e(),t.scheduleSpeaker.length>0?e("div",[e("h2",{staticStyle:{"text-align":"center"}},[t._v("日程主持任务")]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.scheduleSpeaker,border:"","row-class-name":t.tableRowClassName}},[e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"日程名称"}}),e("el-table-column",{attrs:{prop:"placeTopicName","header-align":"center",align:"center",label:"日程所属主题"}}),e("el-table-column",{attrs:{prop:"placeName","header-align":"center",align:"center",label:"日程所属场地"}}),e("el-table-column",{attrs:{prop:"startTime","header-align":"center",align:"center",label:"开始时间"}}),e("el-table-column",{attrs:{prop:"endTime","header-align":"center",align:"center",label:"结束时间"}})],1)],1):t._e(),t.schedule.length>0?e("div",[e("h2",{staticStyle:{"text-align":"center"}},[t._v("日程讲课任务")]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.schedule,border:"","row-class-name":t.tableRowClassName}},[e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"日程名称"}}),e("el-table-column",{attrs:{prop:"placeTopicName","header-align":"center",align:"center",label:"日程所属主题"}}),e("el-table-column",{attrs:{prop:"placeName","header-align":"center",align:"center",label:"日程所属场地"}}),e("el-table-column",{attrs:{prop:"startTime","header-align":"center",align:"center",label:"开始时间"}}),e("el-table-column",{attrs:{prop:"endTime","header-align":"center",align:"center",label:"结束时间"}})],1)],1):t._e(),t.scheduleDiscuss.length>0?e("div",[e("h2",{staticStyle:{"text-align":"center"}},[t._v("日程讨论任务")]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.scheduleDiscuss,border:"","row-class-name":t.tableRowClassName}},[e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"日程名称"}}),e("el-table-column",{attrs:{prop:"placeTopicName","header-align":"center",align:"center",label:"日程所属主题"}}),e("el-table-column",{attrs:{prop:"placeName","header-align":"center",align:"center",label:"日程所属场地"}}),e("el-table-column",{attrs:{prop:"startTime","header-align":"center",align:"center",label:"开始时间"}}),e("el-table-column",{attrs:{prop:"endTime","header-align":"center",align:"center",label:"结束时间"}})],1)],1):t._e(),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("确定")])],1)])},r=[],n={data:function(){return{visible:!1,id:"",topic:[],topicSpeaker:[],schedule:[],scheduleSpeaker:[],scheduleDiscuss:[]}},methods:{init:function(t){var e=this;this.id=t||0,this.visible=!0,this.$http({url:this.$http.adornUrl("/activity/activityguest/getTopicAndSchedule/".concat(this.id)),method:"get"}).then((function(t){var a=t.data;a&&200===a.code&&(e.topic=a.result.topic,e.topicSpeaker=a.result.topicSpeaker,e.schedule=a.result.schedule,e.scheduleSpeaker=a.result.scheduleSpeaker,e.scheduleDiscuss=a.result.scheduleDiscuss)}))},tableRowClassName:function(t){var e=t.row;t.rowIndex;return e.isRepeat?"row-row":""}}},o=n,s=(a("3446"),a("2877")),l=Object(s["a"])(o,i,r,!1,null,null,null);e["default"]=l.exports},3446:function(t,e,a){"use strict";a("de9c")},"3a01":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.dataFormSubmit()}}},[e("el-form-item",{attrs:{label:"类型",prop:"type"}},[e("el-select",{attrs:{placeholder:"类型",filterable:""},model:{value:t.dataForm.type,callback:function(e){t.$set(t.dataForm,"type",e)},expression:"dataForm.type"}},t._l(t.tripType,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],n=a("593c"),o={data:function(){return{times:[],tripType:n["m"],visible:!1,dataForm:{repeatToken:"",id:0,type:0},dataRule:{type:[{required:!0,message:"类型不能为空",trigger:"blur"}]}}},methods:{dateChange:function(t){this.dataForm.inStartDate=t[0],this.dataForm.inEndDate=t[1]},init:function(t,e,a){var i=this;this.getToken(),this.dataForm.id=t||0,this.dataForm.activityGuestId=a||0,this.dataForm.activityId=e||0,this.visible=!0,this.$nextTick((function(){i.$refs["dataForm"].resetFields(),i.dataForm.id&&i.$http({url:i.$http.adornUrl("/activity/activityguesttrip/info/".concat(i.dataForm.id)),method:"get",params:i.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(i.dataForm.type=e.activityGuestTrip.type)}))}))},getToken:function(){var t=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.repeatToken=a.result)}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/activity/activityguesttrip/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({repeatToken:t.dataForm.repeatToken,id:t.dataForm.id||void 0,type:t.dataForm.type})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):(t.$message.error(a.msg),"不能重复提交"!=a.msg&&t.getToken())}))}))}}},s=o,l=a("2877"),d=Object(l["a"])(s,i,r,!1,null,null,null);e["default"]=d.exports},"4d90":function(t,e,a){"use strict";var i=a("23e7"),r=a("0ccb").start,n=a("9a0c");i({target:"String",proto:!0,forced:n},{padStart:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})},"7eb5":function(t,e,a){},"985b":function(t,e,a){"use strict";a.r(e);a("b0c0"),a("ac1f"),a("841c");var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"联系人姓名",prop:"name"}},[e("el-autocomplete",{staticStyle:{width:"100%"},attrs:{"fetch-suggestions":t.search,label:"name",placeholder:"请输入内容"},on:{select:t.selectRsult},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("span",{staticStyle:{float:"left"}},[t._v(t._s(a.item.name))]),e("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v(t._s(a.item.unit))])])}}]),model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",{attrs:{label:"联系人电话",prop:"mobile"}},[e("el-input",{attrs:{placeholder:"联系人电话"},model:{value:t.dataForm.mobile,callback:function(e){t.$set(t.dataForm,"mobile",e)},expression:"dataForm.mobile"}})],1),e("el-form-item",{attrs:{label:"工作单位",prop:"unit"}},[e("el-input",{attrs:{placeholder:"工作单位"},model:{value:t.dataForm.unit,callback:function(e){t.$set(t.dataForm,"unit",e)},expression:"dataForm.unit"}})],1),e("el-form-item",{attrs:{label:"来程类型",prop:"inType"}},[e("el-select",{attrs:{placeholder:"来程类型",filterable:""},model:{value:t.dataForm.inType,callback:function(e){t.$set(t.dataForm,"inType",e)},expression:"dataForm.inType"}},t._l(t.guestGoType,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1),e("el-form-item",{attrs:{label:"来程日期",prop:"inDate"}},[e("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy/MM/dd",placeholder:"开始日期"},model:{value:t.dataForm.inDate,callback:function(e){t.$set(t.dataForm,"inDate",e)},expression:"dataForm.inDate"}})],1),e("el-form-item",{attrs:{label:"来程航班/火车号",prop:"inNumber"}},[e("el-input",{attrs:{placeholder:"来程航班/火车号"},model:{value:t.dataForm.inNumber,callback:function(e){t.$set(t.dataForm,"inNumber",e)},expression:"dataForm.inNumber"}})],1),e("el-form-item",{attrs:{label:"返程类型",prop:"outType"}},[e("el-select",{attrs:{placeholder:"返程类型",filterable:""},model:{value:t.dataForm.outType,callback:function(e){t.$set(t.dataForm,"outType",e)},expression:"dataForm.outType"}},t._l(t.guestGoType,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1),e("el-form-item",{attrs:{label:"返程日期",prop:"outDate"}},[e("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy/MM/dd",placeholder:"开始日期"},model:{value:t.dataForm.outDate,callback:function(e){t.$set(t.dataForm,"outDate",e)},expression:"dataForm.outDate"}})],1),e("el-form-item",{attrs:{label:"返程航班/火车号",prop:"outNumber"}},[e("el-input",{attrs:{placeholder:"返程航班/火车号"},model:{value:t.dataForm.outNumber,callback:function(e){t.$set(t.dataForm,"outNumber",e)},expression:"dataForm.outNumber"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],n=(a("d3b7"),a("3ca3"),a("ddb0"),a("7c8d")),o=a.n(n),s=a("593c"),l={components:{TinymceEditor:function(){return Promise.all([a.e("chunk-03be236c"),a.e("chunk-2d0a4b8c")]).then(a.bind(null,"26dc"))}},data:function(){return{guestGoType:s["c"],visible:!1,url:"",loading:!1,searchResult:[],dataForm:{id:0,activityId:"",name:"",mobile:"",unit:"",wxUserId:"",inType:"",inDate:"",inNumber:"",outType:"",outDate:"",outNumber:""},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],name:[{required:!0,message:"联系人姓名不能为空",trigger:"blur"}]},timeout:null}},methods:{init:function(t,e){var a=this;this.dataForm.activityId=t,this.dataForm.content="",this.dataForm.id=e||0,this.visible=!0,this.url=this.$http.adornUrl("/sys/oss/upload?token=".concat(this.$cookie.get("token"))),this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/activity/activityguest/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.dataForm.activityId=e.activityGuest.activityId,a.dataForm.name=e.activityGuest.name,a.dataForm.mobile=e.activityGuest.mobile,a.dataForm.unit=e.activityGuest.unit,a.dataForm.wxUserId=e.activityGuest.wxUserId,a.dataForm.inType=e.activityGuest.inType,a.dataForm.inDate=e.activityGuest.inDate,a.dataForm.inNumber=e.activityGuest.inNumber,a.dataForm.outType=e.activityGuest.outType,a.dataForm.outDate=e.activityGuest.outDate,a.dataForm.outNumber=e.activityGuest.outNumber,a.dataForm.isSave=!1)}))}))},search:function(t,e){var a=this;""!==t&&(this.loading=!0,this.$http({url:this.$http.adornUrl("/activity/guest/findByName"),method:"get",params:this.$http.adornParams({name:t})}).then((function(t){var i=t.data;a.loading=!1,i&&200===i.code?(a.searchResult=i.result,clearTimeout(a.timeout),a.timeout=setTimeout((function(){e(a.searchResult)}),100*Math.random())):a.$message.error(i.msg)})))},createStateFilter:function(t){return function(e){return 0===e.value.toLowerCase().indexOf(t.toLowerCase())}},selectRsult:function(t){this.dataForm.name=t.name,this.dataForm.mobile=t.mobile,this.dataForm.unit=t.unit,this.dataForm.duties=t.duties,this.dataForm.wxUserId=t.wxUserId,this.dataForm.avatar=t.avatar,this.dataForm.content=t.content,this.dataForm.orderBy=t.orderBy,this.dataForm.idCardZheng=t.idCardZheng,this.dataForm.idCardFan=t.idCardFan,this.dataForm.bank=t.bank,this.dataForm.kaihuhang=t.kaihuhang},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/activity/activityguest/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,activityId:t.dataForm.activityId,name:t.dataForm.name,mobile:t.dataForm.mobile,unit:t.dataForm.unit,inType:t.dataForm.inType,inDate:t.dataForm.inDate,inNumber:t.dataForm.inNumber,outType:t.dataForm.outType,outDate:t.dataForm.outDate,outNumber:t.dataForm.outNumber,wxUserId:t.dataForm.wxUserId})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):405==a.code?t.$confirm(a.msg,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.dataForm.isSave=!0,t.dataFormSubmit()})):t.$message.error(a.msg)}))}))},checkFileSize:function(t){return t.size/1024/1024>6?(this.$message.error("".concat(t.name,"文件大于6MB，请选择小于6MB大小的图片")),!1):!(t.size/1024>100)||new Promise((function(e,a){new o.a(t,{quality:.8,success:function(t){e(t)}})}))},beforeUploadHandle:function(t){if("image/jpg"!==t.type&&"image/jpeg"!==t.type&&"image/png"!==t.type&&"image/gif"!==t.type)return this.$message.error("只支持jpg、png、gif格式的图片！"),!1},appSuccessHandle:function(t,e,a){t&&200===t.code?this.dataForm.avatar=t.url:this.$message.error(t.msg)},idCardZhengSuccessHandle:function(t,e,a){t&&200===t.code?this.dataForm.idCardZheng=t.url:this.$message.error(t.msg)},idCardFanSuccessHandle:function(t,e,a){t&&200===t.code?this.dataForm.idCardFan=t.url:this.$message.error(t.msg)}}},d=l,c=a("2877"),u=Object(c["a"])(d,i,r,!1,null,null,null);e["default"]=u.exports},"9a0c":function(t,e,a){"use strict";var i=a("b5db");t.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(i)},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var i=a("23e7"),r=a("d024"),n=a("c430");i({target:"Iterator",proto:!0,real:!0,forced:n},{map:r})},d024:function(t,e,a){"use strict";var i=a("c65b"),r=a("59ed"),n=a("825a"),o=a("46c4"),s=a("c5cc"),l=a("9bdd"),d=s((function(){var t=this.iterator,e=n(i(this.next,t)),a=this.done=!!e.done;if(!a)return l(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return n(this),r(t),new d(o(this),{mapper:t})}},d4b5:function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"联系人",clearable:""},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",[e("el-input",{attrs:{placeholder:"联系方式",clearable:""},model:{value:t.dataForm.mobile,callback:function(e){t.$set(t.dataForm,"mobile",e)},expression:"dataForm.mobile"}})],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"发送行程填写",filterable:""},model:{value:t.dataForm.sendTrip,callback:function(e){t.$set(t.dataForm,"sendTrip",e)},expression:"dataForm.sendTrip"}},[e("el-option",{attrs:{label:"全部(发送行程填写)",value:""}}),t._l(t.yesOrNo,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})}))],2)],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"是否出票",filterable:""},model:{value:t.dataForm.isBuy,callback:function(e){t.$set(t.dataForm,"isBuy",e)},expression:"dataForm.isBuy"}},[e("el-option",{attrs:{label:"全部(是否出票)",value:""}}),t._l(t.yesOrNo,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})}))],2)],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"飞机订单状态",filterable:""},on:{change:t.changePlaneOrderStatus},model:{value:t.dataForm.planeOrderStatus,callback:function(e){t.$set(t.dataForm,"planeOrderStatus",e)},expression:"dataForm.planeOrderStatus"}},[e("el-option",{attrs:{label:"全部(飞机订单状态)",value:""}}),t._l(t.tripPlaneStatus,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})}))],2)],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"火车订单状态",filterable:""},on:{change:t.changeTrainOrderStatus},model:{value:t.dataForm.trainOrderStatus,callback:function(e){t.$set(t.dataForm,"trainOrderStatus",e)},expression:"dataForm.trainOrderStatus"}},[e("el-option",{attrs:{label:"全部(火车订单状态)",value:""}}),t._l(t.tripTrainStatus,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})}))],2)],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")])],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:"","span-method":t.arraySpanMethod},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"专家信息"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{on:{click:function(e){return t.updateUser(a.row)}}},[e("div",[t._v(t._s(a.row.activityGuestName))]),e("div",[t._v(t._s(a.row.activityGuestMobile))])])}}])}),e("el-table-column",{attrs:{prop:"sendTrip","header-align":"center",align:"center",label:"行程短信发送"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-tag",{attrs:{type:1==a.row.sendTrip?"success":"danger"}},[t._v(t._s(1==a.row.sendTrip?"已发送":"未发送"))]),a.row.sendTrip?t._e():e("el-button",{staticStyle:{"margin-left":"5px"},attrs:{type:"text",size:"mini"},on:{click:function(e){return t.sendTaskHandle(a.row.activityGuestId)}}},[t._v("发送短信")])],1)}}])}),e("el-table-column",{attrs:{prop:"sendTripTime","header-align":"center",align:"center",label:"发送时间"}}),e("el-table-column",{attrs:{prop:"isLink","header-align":"center",align:"center",label:"接送确认",width:"200px"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",[1===a.row.isLink?e("div",[1==a.row.isLinkStart?e("div",[t._v("接站点: "+t._s(a.row.linkStart||"暂无")+" ")]):t._e(),1===a.row.isLinkEnd?e("div",[t._v("送站点: "+t._s(a.row.linkEnd||"暂无")+" ")]):t._e(),e("div",[t._v("确认时间:"+t._s(t._f("formatDate")(a.row.isLinkTime)))])]):t._e()])]}}])}),e("el-table-column",{attrs:{prop:"mobile","header-align":"center",align:"center",label:"交通工具(班次)"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"center","flex-direction":"column"}},[null!==a.row.inType?e("el-tag",{class:"tag-color tag-color-"+a.row.inType,attrs:{type:"primary"}},[t._v(t._s(t.guestGoType[a.row.inType].value))]):t._e(),a.row.inNumber?e("div",[t._v("("+t._s(a.row.inNumber)+")")]):t._e()],1)}}])}),e("el-table-column",{attrs:{prop:"inDate","header-align":"center",align:"center",label:"日期"}}),e("el-table-column",{attrs:{prop:"unit","header-align":"center",align:"center",label:"出发地点-到达地点"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("span",[t._v(t._s(a.row.inStartPlace)+"-"+t._s(a.row.inStartTerminal))]),t._v("-"),e("span",[t._v(t._s(a.row.inEndPlace)+"-"+t._s(a.row.inEndTerminal))])])}}])}),e("el-table-column",{attrs:{prop:"unit",width:"300px","header-align":"center",align:"center",label:"出发时间-到达时间"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[a.row.inStartDate&&a.row.inEndDate?e("span",[t._v(t._s(a.row.inStartDate)+"-"+t._s(t._f("dateFilter")(a.row.inEndDate)))]):e("span",[t._v("-")])])}}])}),e("el-table-column",{attrs:{prop:"type","header-align":"center",align:"center",label:"类型"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-tag",{class:"tag-color tag-color-"+a.row.type,attrs:{type:"primary"},on:{click:function(e){return t.activityguesttripupdatetype(a.row.id)}}},[t._v(t._s(null==a.row.type?"空":t.tripType[a.row.type].value))])],1)}}])}),e("el-table-column",{attrs:{prop:"price","header-align":"center",align:"center",label:"价格"}}),e("el-table-column",{attrs:{prop:"unit","header-align":"center",align:"center",label:"是否出票"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[a.row.id?e("div",[e("el-tag",{class:"tag-color tag-color-"+a.row.isBuy,attrs:{type:"primary"},on:{click:function(e){return t.activityguesttripupdatestatusHandle(a.row.id)}}},[t._v(t._s(null==a.row.isBuy?"空":t.isBuy[a.row.isBuy].value))])],1):e("div",[t._v("-")])])}}])}),e("el-table-column",{attrs:{prop:"unit","header-align":"center",align:"center",label:"订单状态"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[a.row.id?e("div",[0==a.row.inType?e("el-tag",{class:"tag-color tag-color-1",attrs:{type:"primary"},on:{click:function(e){return t.activityguesttripupdatestatusHandle(a.row.id)}}},[t._v(t._s(t._f("tripPlaneStatusFilter")(a.row.orderStatus)))]):1==a.row.inType?e("el-tag",{class:"tag-color tag-color-2",attrs:{type:"primary"},on:{click:function(e){return t.activityguesttripupdatestatusHandle(a.row.id)}}},[t._v(t._s(t._f("tripTrainStatusFilter")(a.row.orderStatus)))]):t._e()],1):e("div",[t._v("-")])])}}])}),e("el-table-column",{attrs:{prop:"remarks","header-align":"center",align:"center",label:"备注"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"300",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.id&&!a.row.isCha?e("el-button",{staticStyle:{color:"red"},attrs:{type:"text",size:"small"},on:{click:function(e){return t.activityguesttripaddorupdateHandle(a.row.id,a.row.activityId,a.row.activityGuestId,1)}}},[t._v("改签")]):t._e(),!a.row.id||2!=a.row.orderStatus&&4!=a.row.orderStatus?t._e():e("el-button",{staticStyle:{color:"red"},attrs:{type:"text",size:"small"},on:{click:function(e){return t.planeTuiPiao(a.row.id)}}},[t._v("退票")]),a.row.id&&0==a.row.orderStatus?e("el-button",{staticStyle:{color:"red"},attrs:{type:"text",size:"small"},on:{click:function(e){return t.planeChuPiao(a.row.id)}}},[t._v("出票&扣款")]):t._e(),a.row.id?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.activityguesttripaddorupdateHandle(a.row.id,a.row.activityId,a.row.activityGuestId,0)}}},[t._v("修改")]):t._e(),a.row.id?t._e():e("el-button",{staticStyle:{color:"red"},attrs:{type:"text",size:"small"},on:{click:function(e){return t.activityguesttripaddorupdateHandle("",a.row.activityId,a.row.activityGuestId,0)}}},[t._v("新增专家行程")]),a.row.id?e("el-button",{staticStyle:{color:"red"},attrs:{type:"text",size:"small"},on:{click:function(e){return t.activityguesttripaddorupdateHandle("",a.row.activityId,a.row.activityGuestId,0)}}},[t._v("继续新增专家行程")]):t._e(),a.row.id?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")]):t._e()]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e(),t.showtaskVisible?e("showtask",{ref:"showtask",on:{refreshDataList:t.getDataList}}):t._e(),t.activityguesttripupdatestatusVisible?e("activityguesttripupdatestatus",{ref:"activityguesttripupdatestatus",on:{refreshDataList:t.getDataList}}):t._e(),t.activityguesttripaddorupdateVisible?e("activityguesttripaddorupdate",{ref:"activityguesttripaddorupdate",on:{refreshDataList:t.getDataList}}):t._e(),t.activityguesttripupdatetypeVisible?e("activityguesttripupdatetype",{ref:"activityguesttripupdatetype",on:{refreshDataList:t.getDataList}}):t._e(),t.activityguestaddorupdateVisible?e("activityguestaddorupdate",{ref:"activityguestaddorupdate",on:{refreshDataList:t.getDataList}}):t._e()],1)},r=[],n=(a("99af"),a("4de4"),a("a15b"),a("d81d"),a("d3b7"),a("ac1f"),a("00b4"),a("25f0"),a("4d90"),a("0643"),a("2382"),a("a573"),a("985b")),o=a("117f"),s=a("f6f9"),l=a("f232"),d=a("3a01"),c=a("1c99"),u=a("593c"),p=a("7de9"),m={data:function(){return{isBuy:u["d"],tripPlaneStatus:u["k"],tripTrainStatus:u["l"],guestGoType:u["c"],tripType:u["m"],yesOrNo:p["g"],dataForm:{inType:"",isBuy:"",planeOrderStatus:"",trainOrderStatus:"",name:"",mobile:"",sendTrip:"",isFirstChar:0,activityId:void 0},activityInfo:{},dataList:[],pageIndex:1,pageSize:200,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,dataMerge:[],showtaskVisible:!1,activityguesttripupdatestatusVisible:!1,activityguesttripaddorupdateVisible:!1,activityguesttripupdatetypeVisible:!1,activityguestaddorupdateVisible:!1}},components:{AddOrUpdate:n["default"],showtask:o["default"],activityguesttripupdatestatus:s["default"],activityguesttripaddorupdate:l["default"],activityguesttripupdatetype:d["default"],activityguestaddorupdate:c["default"]},filters:{dateFilter:function(t){return t.substring(11)},tripPlaneStatusFilter:function(t){var e=u["k"].filter((function(e){return e.key===t}));if(e.length>=1)return e[0].value},tripTrainStatusFilter:function(t){var e=u["l"].filter((function(e){return e.key===t}));if(e.length>=1)return e[0].value},formatDate:function(t){if(null==t||""===t)return"暂无";var e=new Date(t);return e.getFullYear()+"-"+(e.getMonth()+1).toString().padStart(2,"0")+"-"+e.getDate().toString().padStart(2,"0")+" "+e.getHours().toString().padStart(2,"0")+":"+e.getMinutes().toString().padStart(2,"0")}},activated:function(){this.dataForm.activityId=this.$route.query.activityId,this.getActivity()},methods:{changePlaneOrderStatus:function(t){this.dataForm.orderStatus=t,this.dataForm.inType=0,this.dataForm.trainOrderStatus="",this.getDataList()},changeTrainOrderStatus:function(t){this.dataForm.orderStatus=t,this.dataForm.inType=1,this.dataForm.planeOrderStatus="",this.getDataList()},updateUser:function(t){var e=this;this.activityguestaddorupdateVisible=!0,this.$nextTick((function(){e.$refs.activityguestaddorupdate.init(t.activityId,t.activityGuestId)}))},getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/activity/activityguest/tripList"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,name:this.dataForm.name,mobile:this.dataForm.mobile,sendTrip:this.dataForm.sendTrip,activityId:this.dataForm.activityId,orderStatus:this.dataForm.orderStatus,isBuy:this.dataForm.isBuy,isFirstChar:this.activityInfo.isFirstChar,inType:this.dataForm.inType})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount,t.dataMerge=a.page.extra):(t.dataList=[],t.dataMerge=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(e.dataForm.activityId,t)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/activity/activityguesttrip/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},planeChuPiao:function(t){var e=this;this.$confirm("确定出票?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/panhe/plane/createOrder"),method:"get",params:e.$http.adornParams({tripId:t})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},planeTuiPiao:function(t){var e=this;this.$confirm("确定退票?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/panhe/plane/applyRefundTicket"),method:"get",params:e.$http.adornParams({tripId:t})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},planePay:function(t){var e=this;this.$confirm("确定支付?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/panhe/plane/pay"),method:"get",params:e.$http.adornParams({tripId:t})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},sendTaskHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定[".concat(t?"发送行程填写短信":"批量发送行程填写短信","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/activity/activityguest/sendTrip"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"发送操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},getActivity:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activity/info/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.activityInfo=a.activity,t.getDataList())}))},updateIsFirstChar:function(t){var e=this;this.$http({url:this.$http.adornUrl("/activity/activity/updateIsFirstChar"),method:"post",data:this.$http.adornData({id:this.dataForm.activityId,isFirstChar:t})}).then((function(t){var a=t.data;a&&200===a.code?(e.$message.success("操作成功"),e.getActivity(),e.pageIndex=1,e.getDataList()):e.$message.error(a.msg)}))},showTaskHandle:function(t){var e=this;this.showtaskVisible=!0,this.$nextTick((function(){e.$refs.showtask.init(t)}))},activityguesttripupdatestatusHandle:function(t){var e=this;this.activityguesttripupdatestatusVisible=!0,this.$nextTick((function(){e.$refs.activityguesttripupdatestatus.init(t)}))},activityguesttripaddorupdateHandle:function(t,e,a,i){var r=this;this.activityguesttripaddorupdateVisible=!0,this.$nextTick((function(){r.$refs.activityguesttripaddorupdate.init(t,e,a,i)}))},activityguesttripupdatetype:function(t){var e=this;this.activityguesttripupdatetypeVisible=!0,this.$nextTick((function(){e.$refs.activityguesttripupdatetype.init(t)}))},isImageUrl:function(t){return t&&/.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(t)},exportHandle:function(){var t=this.$http.adornUrl("/activity/activityguest/exportplane?"+["token="+this.$cookie.get("token"),"page=1","limit=65535","name="+this.dataForm.name,"mobile="+this.dataForm.mobile,"activityId="+this.dataForm.activityId,"isFirstChar="+this.activityInfo.isFirstChar].join("&"));window.open(t)},arraySpanMethod:function(t){t.row,t.column;var e=t.rowIndex,a=t.columnIndex;if(0===a||1===a||2===a){for(var i=0,r=0,n=0,o=0;o<this.dataMerge.length;o++){if(e==i){r=o,n=1,i+=this.dataMerge[o];break}if(e<i){r=o,n=0;break}i+=this.dataMerge[o]}return console.log(e),1==n?[this.dataMerge[r],1]:[0,0]}}}},h=m,f=(a("0903"),a("2877")),g=Object(f["a"])(h,i,r,!1,null,"dd96611e",null);e["default"]=g.exports},d81d:function(t,e,a){"use strict";var i=a("23e7"),r=a("b727").map,n=a("1dde"),o=n("map");i({target:"Array",proto:!0,forced:!o},{map:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})},de9c:function(t,e,a){},f6f9:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.dataFormSubmit()}}},[e("el-form-item",{attrs:{label:"是否出票",prop:"isBuy"}},[e("el-select",{attrs:{placeholder:"是否出票",filterable:""},model:{value:t.dataForm.isBuy,callback:function(e){t.$set(t.dataForm,"isBuy",e)},expression:"dataForm.isBuy"}},t._l(t.isBuy,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1),e("el-form-item",{attrs:{label:"订单状态",prop:"orderStatus"}},[0==t.dataForm.inType?e("el-select",{attrs:{placeholder:"订单状态",filterable:""},model:{value:t.dataForm.orderStatus,callback:function(e){t.$set(t.dataForm,"orderStatus",e)},expression:"dataForm.orderStatus"}},t._l(t.tripPlaneStatus,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1):1==t.dataForm.inType?e("el-select",{attrs:{placeholder:"订单状态",filterable:""},model:{value:t.dataForm.orderStatus,callback:function(e){t.$set(t.dataForm,"orderStatus",e)},expression:"dataForm.orderStatus"}},t._l(t.tripTrainStatus,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1):t._e()],1),e("el-form-item",{attrs:{label:"价格",prop:"price"}},[e("el-input",{attrs:{placeholder:"价格"},model:{value:t.dataForm.price,callback:function(e){t.$set(t.dataForm,"price",e)},expression:"dataForm.price"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],n=a("7de9"),o=a("593c"),s={data:function(){return{isBuy:o["d"],tripPlaneStatus:o["k"],tripTrainStatus:o["l"],times:[],yesOrNo:n["g"],guestGoType:o["c"],visible:!1,dataForm:{repeatToken:"",id:0,isBuy:0,inType:0,orderStatus:0,price:0},dataRule:{isBuy:[{required:!0,message:"是否购买不能为空",trigger:"blur"}],price:[{required:!0,message:"价格不能为空",trigger:"blur"}]}}},methods:{dateChange:function(t){this.dataForm.inStartDate=t[0],this.dataForm.inEndDate=t[1]},init:function(t,e,a){var i=this;this.getToken(),this.dataForm.id=t||0,this.dataForm.activityGuestId=a||0,this.dataForm.activityId=e||0,this.visible=!0,this.$nextTick((function(){i.$refs["dataForm"].resetFields(),i.dataForm.id&&i.$http({url:i.$http.adornUrl("/activity/activityguesttrip/info/".concat(i.dataForm.id)),method:"get",params:i.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(i.dataForm.isBuy=e.activityGuestTrip.isBuy,i.dataForm.price=e.activityGuestTrip.price,i.dataForm.orderStatus=e.activityGuestTrip.orderStatus,i.dataForm.inType=e.activityGuestTrip.inType)}))}))},getToken:function(){var t=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.repeatToken=a.result)}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/activity/activityguesttrip/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({repeatToken:t.dataForm.repeatToken,id:t.dataForm.id||void 0,isBuy:t.dataForm.isBuy,orderStatus:t.dataForm.orderStatus,price:t.dataForm.price})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):(t.$message.error(a.msg),"不能重复提交"!=a.msg&&t.getToken())}))}))}}},l=s,d=a("2877"),c=Object(d["a"])(l,i,r,!1,null,null,null);e["default"]=c.exports}}]);
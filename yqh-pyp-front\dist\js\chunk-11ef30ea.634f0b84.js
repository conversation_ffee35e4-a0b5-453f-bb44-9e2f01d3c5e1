(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-11ef30ea"],{"02d7":function(t,a,e){"use strict";e.d(a,"a",(function(){return i}));var i=[{key:0,value:"未提交"},{key:1,value:"已提交"},{key:2,value:"已通过"},{key:3,value:"未通过"},{key:4,value:"已超时"},{key:5,value:"作废"}]},"0509":function(t,a,e){"use strict";e.r(a);e("7f7f");var i=function(){var t=this,a=t._self._c;return a("div",[a("van-search",{attrs:{placeholder:"请输入您要搜索的考试&问卷关键词","show-action":"",shape:"round"},model:{value:t.dataForm.name,callback:function(a){t.$set(t.dataForm,"name",a)},expression:"dataForm.name"}},[a("div",{staticClass:"search-text",attrs:{slot:"action"},on:{click:t.onSearch},slot:"action"},[t._v("搜索")])]),a("van-list",{attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:t.onLoad},model:{value:t.loading,callback:function(a){t.loading=a},expression:"loading"}},[a("div",{staticClass:"nav-title",staticStyle:{"margin-top":"8px"}},[a("div",{staticClass:"color"}),a("div",{staticClass:"text"},[t._v("考试&问卷列表")])]),t._l(t.dataList,(function(e){return a("van-card",{key:e.id,staticStyle:{background:"white"},scopedSlots:t._u([{key:"num",fn:function(){return[e.examStatus||0==e.examStatus?0==e.examStatus?a("van-button",{attrs:{size:"small",round:"",type:"info"},on:{click:function(a){return t.startExam(e.id)}}},[t._v("继续答题")]):a("van-button",{attrs:{size:"small",round:"",type:"info"}},[t._v(t._s(t.examActivityUserStatus[e.examStatus].value))]):a("van-button",{attrs:{size:"small",round:"",type:"primary"},on:{click:function(a){return t.startExam(e.id)}}},[t._v("开始答题")])]},proxy:!0}],null,!0)},[a("div",{staticStyle:{"font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(e.name))]),a("div",{staticStyle:{"padding-top":"5px","font-size":"14px"},attrs:{slot:"price"},slot:"price"},[0==e.type?a("van-tag",{staticStyle:{"margin-right":"10px"},attrs:{size:"medium",plain:"",type:"danger"}},[t._v("考试")]):a("van-tag",{staticStyle:{"margin-right":"10px"},attrs:{size:"medium",plain:"",type:"primary"}},[t._v("问卷")]),0==e.examType?a("van-tag",{attrs:{size:"medium",plain:"",type:"danger"}},[t._v("统一时间答题")]):a("van-tag",{attrs:{size:"medium",plain:"",type:"primary"}},[t._v("随时答题")])],1),a("div",{staticStyle:{"padding-top":"5px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[0==e.examType?a("div",[t._v("\n          "+t._s(e.examStartTime)+" ~ "+t._s(e.examEndTime)+"\n        ")]):a("div",[t._v("答题时长："+t._s(e.examTime)+"分钟")])])])}))],2)],1)},n=[],s=(e("ac6a"),e("02d7")),o={data:function(){return{examActivityUserStatus:s["a"],openid:void 0,activityId:void 0,dataForm:{name:""},loading:!1,finished:!1,dataList:[],pageIndex:1,pageSize:10,totalPage:0}},props:["activityId"],mounted:function(){this.openid=this.$cookie.get("openid"),this.$wxShare()},methods:{onSearch:function(){this.pageIndex=1,this.dataList=[],this.getActivityList()},onLoad:function(){this.getActivityList()},getActivityList:function(){var t=this;this.$fly.get("/pyp/web/exam/list",{page:this.pageIndex,limit:this.pageSize,activityId:this.activityId,name:this.dataForm.name}).then((function(a){200==a.code?a.page.list&&a.page.list.length>0?(a.page.list.forEach((function(a){t.dataList.push(a)})),t.totalPage=a.page.totalPage,t.pageIndex++,t.totalPage<t.pageIndex?t.finished=!0:t.finished=!1):t.finished=!0:(vant.Toast(a.msg),t.dataList=[],t.totalPage=0,t.finished=!0)}))},startExam:function(t){var a=this;this.$fly.get("/pyp/web/exam/startExam",{examId:t}).then((function(e){200==e.code?a.$router.push({name:"examDetail",query:{examActivityUserId:e.examActivityUserId,id:a.activityId,examId:t,token:e.token}}):vant.Toast(e.msg)}))}}},r=o,l=e("2877"),d=Object(l["a"])(r,i,n,!1,null,null,null);a["default"]=d.exports},ac6a:function(t,a,e){for(var i=e("cadf"),n=e("0d58"),s=e("2aba"),o=e("7726"),r=e("32e9"),l=e("84f2"),d=e("2b4c"),c=d("iterator"),u=d("toStringTag"),m=l.Array,p={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},v=n(p),g=0;g<v.length;g++){var y,f=v[g],h=p[f],x=o[f],S=x&&x.prototype;if(S&&(S[c]||r(S,c,m),S[u]||r(S,u,f),l[f]=m,h))for(y in i)S[y]||s(S,y,i[y],!0)}}}]);
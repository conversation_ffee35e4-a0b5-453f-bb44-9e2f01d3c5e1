(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-bcf2ee1e","chunk-2d0e447e"],{"900a":function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"类型",prop:"type"}},[e("el-select",{attrs:{placeholder:"类型",filterable:""},model:{value:t.dataForm.type,callback:function(e){t.$set(t.dataForm,"type",e)},expression:"dataForm.type"}},t._l(t.typeList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),1==t.dataForm.type?e("el-form-item",{attrs:{label:"banner广告",prop:"name"}},[e("el-upload",{staticClass:"avatar-uploader",attrs:{"before-upload":t.checkFileSize,"list-type":"picture-card","show-file-list":!1,accept:".jpg, .jpeg, .png, .gif","on-success":t.backgroundSuccessHandle,action:t.url}},[t.dataForm.name?e("img",{staticClass:"avatar",attrs:{width:"100px",src:t.dataForm.name}}):e("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1):e("el-form-item",{attrs:{label:"名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"名称"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",{attrs:{label:"链接",prop:"url"}},[e("el-input",{attrs:{placeholder:"链接"},model:{value:t.dataForm.url,callback:function(e){t.$set(t.dataForm,"url",e)},expression:"dataForm.url"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},n=[],r=(a("d3b7"),a("7c8d")),o=a.n(r),s={data:function(){return{visible:!1,url:"",dataForm:{id:0,activityId:"",name:"",type:"",url:""},typeList:[{id:0,name:"自定义"},{id:1,name:"banner广告"},{id:2,name:"文件下载"}],dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],name:[{required:!0,message:"名称不能为空",trigger:"blur"}],type:[{required:!0,message:"0-自定义，1-banner广告，2-文件下载不能为空",trigger:"blur"}],url:[{required:!0,message:"链接不能为空",trigger:"blur"}]}}},methods:{init:function(t,e){var a=this;this.url=this.$http.adornUrl("/sys/oss/upload?token=".concat(this.$cookie.get("token"))),this.dataForm.id=e||0,this.dataForm.activityId=t,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/activity/activitybottom/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.dataForm.activityId=e.activityBottom.activityId,a.dataForm.name=e.activityBottom.name,a.dataForm.type=e.activityBottom.type,a.dataForm.url=e.activityBottom.url)}))}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/activity/activitybottom/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,activityId:t.dataForm.activityId,name:t.dataForm.name,type:t.dataForm.type,url:t.dataForm.url})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))},checkFileSize:function(t){return t.size/1024/1024>6?(this.$message.error("".concat(t.name,"文件大于6MB，请选择小于6MB大小的图片")),!1):!(t.size/1024>100)||new Promise((function(e,a){new o.a(t,{quality:.8,success:function(t){e(t)}})}))},backgroundSuccessHandle:function(t,e,a){t&&200===t.code?(this.dataForm.name=t.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(t.msg)}}},l=s,d=a("2877"),c=Object(d["a"])(l,i,n,!1,null,null,null);e["default"]=c.exports},"98a7":function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"名称",clearable:""},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),t.isAuth("activity:activitybottom:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("activity:activitybottom:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"type","header-align":"center",align:"center",label:"类型"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-tag",{attrs:{type:1==a.row.type?"primary":2==a.row.type?"success":"danger"}},[t._v(t._s(1==a.row.type?"banner广告":2==a.row.type?"文件下载":"自定义"))])],1)}}])}),e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"名称"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[1==a.row.type?e("img",{staticClass:"image-sm",attrs:{src:a.row.name}}):e("div",[t._v(t._s(a.row.name))])])}}])}),e("el-table-column",{attrs:{prop:"url","header-align":"center",align:"center",label:"链接"}}),e("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",label:"创建时间"}}),e("el-table-column",{attrs:{prop:"updateOn","header-align":"center",align:"center",label:"更新时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e()],1)},n=[],r=(a("99af"),a("a15b"),a("d81d"),a("a573"),a("900a")),o={data:function(){return{dataForm:{name:"",type:"",activityId:void 0},typeList:[{id:0,name:"自定义"},{id:1,name:"banner广告"},{id:2,name:"文件下载"}],dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1}},components:{AddOrUpdate:r["default"]},activated:function(){this.dataForm.activityId=this.$route.query.activityId,this.getDataList()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/activity/activitybottom/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,name:this.dataForm.name,activityId:this.dataForm.activityId})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(e.dataForm.activityId,t)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/activity/activitybottom/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))}}},s=o,l=a("2877"),d=Object(l["a"])(s,i,n,!1,null,null,null);e["default"]=d.exports},a15b:function(t,e,a){"use strict";var i=a("23e7"),n=a("e330"),r=a("44ad"),o=a("fc6a"),s=a("a640"),l=n([].join),d=r!==Object,c=d||!s("join",",");i({target:"Array",proto:!0,forced:c},{join:function(t){return l(o(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var i=a("23e7"),n=a("d024"),r=a("c430");i({target:"Iterator",proto:!0,real:!0,forced:r},{map:n})},d024:function(t,e,a){"use strict";var i=a("c65b"),n=a("59ed"),r=a("825a"),o=a("46c4"),s=a("c5cc"),l=a("9bdd"),d=s((function(){var t=this.iterator,e=r(i(this.next,t)),a=this.done=!!e.done;if(!a)return l(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return r(this),n(t),new d(o(this),{mapper:t})}},d81d:function(t,e,a){"use strict";var i=a("23e7"),n=a("b727").map,r=a("1dde"),o=r("map");i({target:"Array",proto:!0,forced:!o},{map:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}})}}]);
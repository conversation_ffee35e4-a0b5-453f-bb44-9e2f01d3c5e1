(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c7f79834","chunk-2d0a4b8c","chunk-2d0a4b8c","chunk-58faa667"],{"083a":function(t,e,a){"use strict";var i=a("0d51"),r=TypeError;t.exports=function(t,e){if(!delete t[e])throw new r("Cannot delete property "+i(e)+" of "+i(t))}},"7db0":function(t,e,a){"use strict";var i=a("23e7"),r=a("b727").find,o=a("44d2"),n="find",s=!0;n in[]&&Array(1)[n]((function(){s=!1})),i({target:"Array",proto:!0,forced:s},{find:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}}),o(n)},"7de9":function(t,e,a){"use strict";a.d(e,"g",(function(){return i})),a.d(e,"f",(function(){return r})),a.d(e,"e",(function(){return o})),a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return s})),a.d(e,"c",(function(){return l})),a.d(e,"d",(function(){return d}));var i=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],r=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],o=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],n=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],s=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],l=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],d=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]},a434:function(t,e,a){"use strict";var i=a("23e7"),r=a("7b0b"),o=a("23cb"),n=a("5926"),s=a("07fa"),l=a("3a34"),d=a("3511"),c=a("65f0"),m=a("8418"),u=a("083a"),p=a("1dde"),y=p("splice"),h=Math.max,g=Math.min;i({target:"Array",proto:!0,forced:!y},{splice:function(t,e){var a,i,p,y,F,v,f=r(this),b=s(f),k=o(t,b),I=arguments.length;for(0===I?a=i=0:1===I?(a=0,i=b-k):(a=I-2,i=g(h(n(e),0),b-k)),d(b+a-i),p=c(f,i),y=0;y<i;y++)F=k+y,F in f&&m(p,y,f[F]);if(p.length=i,a<i){for(y=k;y<b-i;y++)F=y+i,v=y+a,F in f?f[v]=f[F]:u(f,v);for(y=b;y>b-i+a;y--)u(f,y-1)}else if(a>i)for(y=b-i;y>k;y--)F=y+i-1,v=y+a-1,F in f?f[v]=f[F]:u(f,v);for(y=0;y<a;y++)f[y+k]=arguments[y+2];return l(f,b-i+a),p}})},f665:function(t,e,a){"use strict";var i=a("23e7"),r=a("2266"),o=a("59ed"),n=a("825a"),s=a("46c4");i({target:"Iterator",proto:!0,real:!0},{find:function(t){n(this),o(t);var e=s(this),a=0;return r(e,(function(e,i){if(t(e,a++))return i(e)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},fea6:function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("div",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"}},[e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"会议名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"会议名称"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"会议编号",prop:"code"}},[e("el-input",{attrs:{placeholder:"会议编号"},model:{value:t.dataForm.code,callback:function(e){t.$set(t.dataForm,"code",e)},expression:"dataForm.code"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"会议日期",prop:"times"}},[e("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetimerange","value-format":"yyyy/MM/dd HH:mm:ss","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.dateChange},model:{value:t.dataForm.times,callback:function(e){t.$set(t.dataForm,"times",e)},expression:"dataForm.times"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"会议类型",prop:"activityType"}},[e("el-select",{attrs:{filterable:""},model:{value:t.dataForm.activityType,callback:function(e){t.$set(t.dataForm,"activityType",e)},expression:"dataForm.activityType"}},t._l(t.activityType,(function(t){return e("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"客户",prop:"clientId"}},[e("el-select",{attrs:{filterable:""},model:{value:t.dataForm.clientId,callback:function(e){t.$set(t.dataForm,"clientId",e)},expression:"dataForm.clientId"}},t._l(t.client,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"科室",prop:"duties"}},[e("el-input",{attrs:{placeholder:"会议名称"},model:{value:t.dataForm.duties,callback:function(e){t.$set(t.dataForm,"duties",e)},expression:"dataForm.duties"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"客户类型",prop:"clientType"}},[e("el-select",{attrs:{filterable:""},model:{value:t.dataForm.clientType,callback:function(e){t.$set(t.dataForm,"clientType",e)},expression:"dataForm.clientType"}},t._l(t.clientType,(function(t){return e("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"新/老客户",prop:"oldOrNew"}},[e("el-select",{attrs:{filterable:""},model:{value:t.dataForm.oldOrNew,callback:function(e){t.$set(t.dataForm,"oldOrNew",e)},expression:"dataForm.oldOrNew"}},t._l(t.oldOrNew,(function(t){return e("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1)],1),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"主题主持劳务费",prop:"topicGuest"}},[e("el-input",{attrs:{placeholder:"主题主持劳务费"},model:{value:t.dataForm.topicGuest,callback:function(e){t.$set(t.dataForm,"topicGuest",e)},expression:"dataForm.topicGuest"}})],1)],1),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"主题讨论劳务费",prop:"topicDiscuss"}},[e("el-input",{attrs:{placeholder:"主题主席劳务费"},model:{value:t.dataForm.topicDiscuss,callback:function(e){t.$set(t.dataForm,"topicDiscuss",e)},expression:"dataForm.topicDiscuss"}})],1)],1),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"主题主席劳务费",prop:"topicSpeaker"}},[e("el-input",{attrs:{placeholder:"主题主席劳务费"},model:{value:t.dataForm.topicSpeaker,callback:function(e){t.$set(t.dataForm,"topicSpeaker",e)},expression:"dataForm.topicSpeaker"}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"日程讲者劳务费",prop:"scheduleGuest"}},[e("el-input",{attrs:{placeholder:"日程讲者劳务费"},model:{value:t.dataForm.scheduleGuest,callback:function(e){t.$set(t.dataForm,"scheduleGuest",e)},expression:"dataForm.scheduleGuest"}})],1)],1),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"日程主持劳务费",prop:"scheduleSpeaker"}},[e("el-input",{attrs:{placeholder:"日程主持劳务费"},model:{value:t.dataForm.scheduleSpeaker,callback:function(e){t.$set(t.dataForm,"scheduleSpeaker",e)},expression:"dataForm.scheduleSpeaker"}})],1)],1),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"日程讨论劳务费",prop:"scheduleDiscuss"}},[e("el-input",{attrs:{placeholder:"日程讨论劳务费"},model:{value:t.dataForm.scheduleDiscuss,callback:function(e){t.$set(t.dataForm,"scheduleDiscuss",e)},expression:"dataForm.scheduleDiscuss"}})],1)],1)],1),e("el-form-item",{attrs:{label:"区域地址",prop:"cityId"}},[e("el-select",{attrs:{placeholder:"省",filterable:""},on:{change:t.provinceChange},model:{value:t.dataForm.provinceId,callback:function(e){t.$set(t.dataForm,"provinceId",e)},expression:"dataForm.provinceId"}},t._l(t.provinces,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1),e("el-select",{staticStyle:{"margin-left":"10px"},attrs:{placeholder:"市",filterable:""},on:{change:t.getCityName},model:{value:t.dataForm.cityId,callback:function(e){t.$set(t.dataForm,"cityId",e)},expression:"dataForm.cityId"}},t._l(t.cities,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"详细地址",prop:"address"}},[e("el-input",{attrs:{placeholder:"详细地址"},model:{value:t.dataForm.address,callback:function(e){t.$set(t.dataForm,"address",e)},expression:"dataForm.address"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){return t.turnBack()}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],o=a("ade3"),n=(a("d9e2"),a("7db0"),a("a434"),a("d3b7"),a("ac1f"),a("00b4"),a("25f0"),a("3ca3"),a("5319"),a("0643"),a("fffc"),a("ddb0"),a("7c8d")),s=a.n(n),l=a("7de9"),d={components:{TinymceEditor:function(){return a.e("chunk-03be236c").then(a.bind(null,"26dc"))},OssUploader:function(){return a.e("chunk-2d0e97b1").then(a.bind(null,"8e5c"))}},data:function(){var t=function(t,e,a){var i=/^[^\u4e00-\u9fa5]{3,10}$/;i.test(e)?a():a(new Error("会议编号3到10位，不能包含中文"))};return{activityType:[],client:[],clientType:[],oldOrNew:[],yesOrNo:l["g"],btnLoading:!1,configActivityType:[],appFileList:[],con:[],fileList:[],cityName:"",imgDialogVisible:!1,imgAppDialogVisible:!1,dialogImageUrl:"",dialogAppImageUrl:"",music:[],provinces:[],cities:[],url:"",dataForm:{id:0,times:[],code:"",name:"",startTime:"",endTime:"",provinceId:"",cityId:"",pcBanner:"",mobileBanner:"",background:"",type:["0"],subscribeImg:"",applyBackground:"",backImg:"",pvCount:"",uvCount:"",longitude:"",latitude:"",address:"",bottomColor:"",fontColor:"",templateId:"1",applyNotify:"",hotelNotify:"",turnurl:"",isCountdown:1,isShow:1,showSub:0,isHot:0,isIndex:0,appid:"",topicGuest:0,topicSpeaker:0,topicDiscuss:0,scheduleGuest:0,scheduleSpeaker:0,scheduleDiscuss:0,introduction:"",showApplyNumber:0,hotelNeedApply:0,liveNeedApply:1,applySuccessLive:1,applySuccessHotel:1,onlyOneHotel:0,cancelApplyHotel:0,backUrl:"",musicUrl:"",ad:"",adColor:"",adTime:"",shareUrl:"",configActivityTypeId:"",clientId:"",clientType:"",duties:"",activityType:"",oldOrNew:""},typeList:[{id:"0",name:"自定义"},{id:"1",name:"banner广告"},{id:"2",name:"文件下载"}],templateList:[{id:"1",name:"模板1-传统九宫格"},{id:"2",name:"模板2-含背景九宫格"},{id:"31",name:"模板3-4114布局"},{id:"4",name:"模板4-圆形九宫格"},{id:"5",name:"模板5-纯图标九宫格"},{id:"61",name:"模板61-3-3(1-2)-3-1布局"},{id:"62",name:"模板62-3-3(1-2)-2布局"},{id:"63",name:"模板63-3-3(1-2)-2-2-2布局"}],sysTemplate:[],dataRule:{code:[{required:!0,message:"会议编号不能为空",trigger:"blur"},{validator:t,trigger:"blur"}],name:[{required:!0,message:"会议名称不能为空",trigger:"blur"}],times:[{required:!0,message:"会议时间不能为空",trigger:"blur"}],provinceId:[{required:!0,message:"省份不能为空",trigger:"blur"}],cityId:[{required:!0,message:"城市不能为空",trigger:"blur"}],pcBanner:[{required:!0,message:"会议图片不能为空",trigger:"blur"}],mobileBanner:[{required:!0,message:"手机端图片不能为空",trigger:"blur"}],isShow:[{required:!0,message:"公众号显示不能为空",trigger:"blur"}]}}},activated:function(){this.init()},methods:{init:function(){var t=this;this.dataForm.id=this.$route.query.id||0,this.url=this.$http.adornUrl("/sys/oss/upload?token=".concat(this.$cookie.get("token"))),this.appFileList=[],this.fileList=[],this.$nextTick((function(){t.$refs["dataForm"].resetFields(),t.dataForm.appid=t.$cookie.get("appid"),t.dataForm.id&&t.$http({url:t.$http.adornUrl("/activity/activity/info/".concat(t.dataForm.id)),method:"get",params:t.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.$set(t.dataForm,"times",[a.activity.startTime,a.activity.endTime]),t.appFileList=a.activity.appFileList,t.fileList=a.activity.fileList,t.dataForm.code=a.activity.code,t.dataForm.name=a.activity.name,t.dataForm.startTime=a.activity.startTime,t.dataForm.endTime=a.activity.endTime,t.dataForm.provinceId=a.activity.provinceId,t.dataForm.cityId=a.activity.cityId,t.dataForm.pcBanner=a.activity.pcBanner,t.dataForm.mobileBanner=a.activity.mobileBanner,t.dataForm.subscribeImg=a.activity.subscribeImg,t.dataForm.applyBackground=a.activity.applyBackground,t.dataForm.backImg=a.activity.backImg,t.dataForm.background=a.activity.background,t.dataForm.type=a.activity.type?a.activity.type.split(","):[],t.dataForm.pvCount=a.activity.pvCount,t.dataForm.uvCount=a.activity.uvCount,t.dataForm.longitude=a.activity.longitude,t.dataForm.latitude=a.activity.latitude,t.dataForm.address=a.activity.address,t.dataForm.introduction=a.activity.introduction,t.dataForm.bottomColor=a.activity.bottomColor,t.dataForm.fontColor=a.activity.fontColor,t.dataForm.templateId=a.activity.templateId,t.dataForm.applyNotify=a.activity.applyNotify,t.dataForm.hotelNotify=a.activity.hotelNotify,t.dataForm.isCountdown=a.activity.isCountdown,t.dataForm.isShow=a.activity.isShow,t.dataForm.showSub=a.activity.showSub,t.dataForm.isHot=a.activity.isHot,t.dataForm.isIndex=a.activity.isIndex,t.dataForm.appid=a.activity.appid,t.dataForm.topicGuest=a.activity.topicGuest,t.dataForm.topicSpeaker=a.activity.topicSpeaker,t.dataForm.topicDiscuss=a.activity.topicDiscuss,t.dataForm.scheduleGuest=a.activity.scheduleGuest,t.dataForm.scheduleSpeaker=a.activity.scheduleSpeaker,t.dataForm.scheduleDiscuss=a.activity.scheduleDiscuss,t.dataForm.showApplyNumber=a.activity.showApplyNumber,t.dataForm.hotelNeedApply=a.activity.hotelNeedApply,t.dataForm.liveNeedApply=a.activity.liveNeedApply,t.dataForm.applySuccessLive=a.activity.applySuccessLive,t.dataForm.applySuccessHotel=a.activity.applySuccessHotel,t.dataForm.onlyOneHotel=a.activity.onlyOneHotel,t.dataForm.cancelApplyHotel=a.activity.cancelApplyHotel,t.dataForm.turnurl=a.activity.turnurl,t.dataForm.backUrl=a.activity.backUrl,t.dataForm.musicUrl=a.activity.musicUrl,t.dataForm.ad=a.activity.ad,t.dataForm.adColor=a.activity.adColor,t.dataForm.adTime=a.activity.adTime,t.dataForm.shareUrl=a.activity.shareUrl,t.dataForm.configActivityTypeId=a.activity.configActivityTypeId,t.dataForm.clientId=a.activity.clientId,t.dataForm.clientType=a.activity.clientType,t.dataForm.duties=a.activity.duties,t.dataForm.activityType=a.activity.activityType,t.dataForm.oldOrNew=a.activity.oldOrNew,t.$http({url:t.$http.adornUrl("/sys/region/pid/".concat(t.dataForm.provinceId)),method:"get",params:t.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code?t.cities=a.list:t.cities=[]})))}))})),this.getProvinces(),this.getResult(),this.getTempalte(),this.getConfigActivityType(),this.findClient()},findClient:function(){var t=this;this.$http({url:this.$http.adornUrl("/client/client/findAll"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.client=a.result)}))},getResult:function(){var t=this;this.$http({url:this.$http.adornUrl("/sys/config/findOnlyParamKey"),method:"get",params:this.$http.adornParams({paramKey:"clientType"})}).then((function(e){var a=e.data;a&&200===a.code&&(t.clientType=a.result.paramValue?a.result.paramValue.split(","):[])})),this.$http({url:this.$http.adornUrl("/sys/config/findOnlyParamKey"),method:"get",params:this.$http.adornParams({paramKey:"oldOrNew"})}).then((function(e){var a=e.data;a&&200===a.code&&(t.oldOrNew=a.result.paramValue?a.result.paramValue.split(","):[])})),this.$http({url:this.$http.adornUrl("/sys/config/findOnlyParamKey"),method:"get",params:this.$http.adornParams({paramKey:"activityType"})}).then((function(e){var a=e.data;a&&200===a.code&&(t.activityType=a.result.paramValue?a.result.paramValue.split(","):[])}))},turnBack:function(){this.$router.go(-1)},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/activity/activity/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])({id:t.dataForm.id||void 0,code:t.dataForm.code,name:t.dataForm.name,startTime:t.dataForm.startTime,endTime:t.dataForm.endTime,provinceId:t.dataForm.provinceId,cityId:t.dataForm.cityId,pcBanner:t.dataForm.pcBanner,mobileBanner:t.dataForm.mobileBanner,subscribeImg:t.dataForm.subscribeImg,applyBackground:t.dataForm.applyBackground,backImg:t.dataForm.backImg,background:t.dataForm.background,type:t.dataForm.type.toString(),pvCount:t.dataForm.pvCount,uvCount:t.dataForm.uvCount,longitude:t.dataForm.longitude,latitude:t.dataForm.latitude,address:t.dataForm.address,introduction:t.dataForm.introduction,bottomColor:t.dataForm.bottomColor,fontColor:t.dataForm.fontColor,templateId:t.dataForm.templateId,applyNotify:t.dataForm.applyNotify,hotelNotify:t.dataForm.hotelNotify,isCountdown:t.dataForm.isCountdown,isShow:t.dataForm.isShow,showSub:t.dataForm.showSub,isIndex:t.dataForm.isIndex,isHot:t.dataForm.isHot,appid:t.dataForm.appid,topicGuest:t.dataForm.topicGuest,topicSpeaker:t.dataForm.topicSpeaker,topicDiscuss:t.dataForm.topicDiscuss,scheduleGuest:t.dataForm.scheduleGuest,scheduleSpeaker:t.dataForm.scheduleSpeaker,scheduleDiscuss:t.dataForm.scheduleDiscuss,showApplyNumber:t.dataForm.showApplyNumber,hotelNeedApply:t.dataForm.hotelNeedApply,liveNeedApply:t.dataForm.liveNeedApply,applySuccessLive:t.dataForm.applySuccessLive,applySuccessHotel:t.dataForm.applySuccessHotel,onlyOneHotel:t.dataForm.onlyOneHotel,cancelApplyHotel:t.dataForm.cancelApplyHotel,turnurl:t.dataForm.turnurl,backUrl:t.dataForm.backUrl,musicUrl:t.dataForm.musicUrl,ad:t.dataForm.ad},"ad",t.dataForm.ad),"adColor",t.dataForm.adColor),"adTime",t.dataForm.adTime),"shareUrl",t.dataForm.shareUrl),"configActivityTypeId",t.dataForm.configActivityTypeId),"clientId",t.dataForm.clientId),"clientType",t.dataForm.clientType),"duties",t.dataForm.duties),"activityType",t.dataForm.activityType),"oldOrNew",t.dataForm.oldOrNew))}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.turnBack()}}):t.$message.error(a.msg)}))}))},getProvinces:function(){var t=this;this.$http({url:this.$http.adornUrl("/sys/region/pid/100000"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code?t.provinces=a.list:t.provinces=[]}))},getMusic:function(){var t=this;this.$http({url:this.$http.adornUrl("/sys/sysmusic/findAll"),method:"get"}).then((function(e){var a=e.data;a&&200===a.code?t.music=a.result:t.music=[]}))},getTempalte:function(){var t=this;this.$http({url:this.$http.adornUrl("/sys/systemplate/findAll"),method:"get"}).then((function(e){var a=e.data;a&&200===a.code?t.sysTemplate=a.result:t.sysTemplate=[]}))},getConfigActivityType:function(){var t=this;this.$http({url:this.$http.adornUrl("/config/configactivitytype/findByAppid"),method:"get"}).then((function(e){var a=e.data;a&&200===a.code?t.configActivityType=a.result:t.configActivityType=[]}))},provinceChange:function(t){var e=this;void 0!==t&&(this.cities={},this.dataForm.cityId=void 0,this.dataForm.jieSongCityName=[],this.$http({url:this.$http.adornUrl("/sys/region/pid/".concat(t)),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code?e.cities=a.list:e.cities=[]})))},getCityName:function(t){var e=this.cities.find((function(e){return e.id===t}));this.cityName=e.name.replace("市","")},checkFileSize:function(t){return t.size/1024/1024>6?(this.$message.error("".concat(t.name,"文件大于6MB，请选择小于6MB大小的图片")),!1):!(t.size/1024>100)||new Promise((function(e,a){new s.a(t,{quality:.8,success:function(t){e(t)}})}))},beforeUploadHandle:function(t){if("image/jpg"!==t.type&&"image/jpeg"!==t.type&&"image/png"!==t.type&&"image/gif"!==t.type)return this.$message.error("只支持jpg、png、gif格式的图片！"),!1},successHandle:function(t,e,a){this.fileList=a,this.successNum1++,t&&200===t.code?this.dataForm.pcBanner&&0!=this.dataForm.pcBanner.length?this.dataForm.pcBanner+=","+t.url:this.dataForm.pcBanner=t.url:this.$message.error(t.msg)},appSuccessHandle:function(t,e,a){this.appFileList=a,this.successNum++,t&&200===t.code?this.dataForm.mobileBanner&&0!=this.dataForm.mobileBanner.length?this.dataForm.mobileBanner+=","+t.url:this.dataForm.mobileBanner=t.url:this.$message.error(t.msg)},backgroundSuccessHandle:function(t,e,a){t&&200===t.code?(this.dataForm.background=t.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(t.msg)},subscribeImgSuccessHandle:function(t,e,a){t&&200===t.code?(this.dataForm.subscribeImg=t.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(t.msg)},applyBackgroundSuccessHandle:function(t,e,a){t&&200===t.code?(this.dataForm.applyBackground=t.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(t.msg)},backImgSuccessHandle:function(t,e,a){t&&200===t.code?(this.dataForm.backImg=t.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(t.msg)},adSuccessHandle:function(t,e,a){t&&200===t.code?(this.dataForm.ad=t.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(t.msg)},shareUrlSuccessHandle:function(t,e,a){t&&200===t.code?(this.dataForm.shareUrl=t.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(t.msg)},handleRemove:function(t){this.dataForm.pcBanner=(","+this.dataForm.pcBanner+",").replace(","+t.url+",",",").substr(1).replace(/,$/,""),this.fileList.splice(this.fileList.indexOf(t),1)},handleAppRemove:function(t){this.dataForm.mobileBanner=(","+this.dataForm.mobileBanner+",").replace(","+t.url+",",",").substr(1).replace(/,$/,""),this.appFileList.splice(this.appFileList.indexOf(t),1)},handlePictureCardPreview:function(t){this.dialogImageUrl=t.url,this.imgDialogVisible=!0},handleAppPictureCardPreview:function(t){this.dialogAppImageUrl=t.url,this.imgAppDialogVisible=!0},dateChange:function(t){this.dataForm.startTime=t[0],this.dataForm.endTime=t[1],console.log(t)}}},c=d,m=a("2877"),u=Object(m["a"])(c,i,r,!1,null,null,null);e["default"]=u.exports},fffc:function(t,e,a){"use strict";a("f665")}}]);
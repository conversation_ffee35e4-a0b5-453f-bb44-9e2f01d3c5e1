(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0e9264ee"],{"24ce":function(t,e,o){},"386d":function(t,e,o){"use strict";var i=o("cb7c"),a=o("83a1"),n=o("5f1b");o("214f")("search",1,(function(t,e,o,s){return[function(o){var i=t(this),a=void 0==o?void 0:o[e];return void 0!==a?a.call(o,i):new RegExp(o)[e](String(i))},function(t){var e=s(o,t,this);if(e.done)return e.value;var c=i(t),r=String(this),l=c.lastIndex;a(l,0)||(c.lastIndex=0);var d=n(c,r);return a(c.lastIndex,l)||(c.lastIndex=l),null===d?-1:d.index}]}))},"7a64":function(t,e,o){"use strict";o("24ce")},"83a1":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},cd1d:function(t,e,o){"use strict";o.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"page"},[e("van-empty",{attrs:{description:0==t.hotelOrder.totalAmount&&"已付款"==t.statusText?"已预订":t.statusText,image:0==t.hotelOrder.status?"http://mpjoy.oss-cn-beijing.aliyuncs.com/20211228/2eaf8b0e481b47eab31cece39a2bfec0.png":1==t.hotelOrder.status?"http://mpjoy.oss-cn-beijing.aliyuncs.com/20210618/7361a2571f8a41809960a577bb123733.png":"http://mpjoy.oss-cn-beijing.aliyuncs.com/20211228/13da716239e94e0fa7f84712e06ce9d1.png"}},[0x196ae6ecb4990000!=t.activityId?e("div",{staticClass:"orderInfo"},[e("div",{staticStyle:{color:"#969799","font-size":"16px","padding-bottom":"20px","text-align":"center"}},[t._v("\n        订单费用：￥"+t._s(t.hotelOrder.totalAmount)+"元")])]):t._e(),0x173d6b4cea590000!=t.activityId?e("div",{staticClass:"botton"},[e("div",{staticClass:"button-item"},[e("img",{staticClass:"button-image",attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/191a0fb893ae4ab8af27f20d0f395b7d.png",alt:""},on:{click:t.back}})]),0==t.hotelOrder.status||1==t.hotelOrder.status?e("div",{staticClass:"button-item"},[e("img",{staticClass:"button-image",attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/7b6f394d16be435b963ae7b2101207a5.png",alt:""},on:{click:t.calcelOrder}})]):t._e(),t.hotelRooms&&1==t.hotelRooms.isBankTransfer&&0==t.hotelOrder.status?e("div",{staticClass:"button-item"},[e("img",{staticClass:"button-image",attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/75b6dacaf5714fa28408a26203d52e92.png",alt:""},on:{click:t.bankTransfer}})]):t._e(),t.hotelRooms&&1==t.hotelRooms.isWechatPay&&0==t.hotelOrder.status?e("div",{staticClass:"button-item"},[e("img",{staticClass:"button-image",attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/1d27eefbace94b3c9b993312f66b5501.png",alt:""},on:{click:t.weixin}})]):t._e(),t.hotelRooms&&1==t.hotelRooms.isAliPay&&0==t.hotelOrder.status?e("div",{staticClass:"button-item"},[e("img",{staticClass:"button-image",attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/c70a37e66e8d494bad00a643ad642d07.png",alt:""},on:{click:t.ali}})]):t._e(),0x16eba6c96f403000!=t.activityId?e("div",{staticClass:"button-item"},[e("img",{staticClass:"button-image",attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/c1d6aa26252a40e294ae986c180abc8b.png",alt:""},on:{click:function(e){return t.$router.push({name:"cmsIndex",query:{id:t.hotelOrder.activityId}})}}})]):t._e(),e("div",{staticClass:"button-item"},[e("img",{staticClass:"button-image",attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/b8711e1f644f4c919ad39a3186a77c83.png",alt:""},on:{click:function(e){return t.$router.push({name:"meMine",query:{id:t.hotelOrder.activityId}})}}})])]):t._e()])],1)},a=[],n=(o("a481"),o("386d"),{data:function(){return{activityId:void 0,orderId:void 0,statusText:"",hotelOrder:{},hotelRooms:{},orderDetail:[]}},mounted:function(){document.title="预订提交成功",this.activityId=this.$route.query.id,this.orderId=this.$route.query.orderId,this.rebuildUrl(),this.getActivityInfo()},methods:{back:function(){this.$router.push({name:"cmsIndex",query:{id:this.hotelOrder.activityId}})},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/web/hotel/hotelorder/info/".concat(this.orderId)).then((function(e){t.loading=!1,200==e.code?(t.hotelOrder=e.result,t.orderDetail=e.orderDetail,t.statusText=e.statusText,t.orderDetail&&t.orderDetail.length>0&&t.getRooms(t.orderDetail[0].hotelActivityRoomId)):(vant.Toast(e.msg),t.hotelOrder={},t.statusText="")}))},getRooms:function(t){var e=this;this.$fly.get("/pyp/web/hotel/hotelactivityroom/info/".concat(t)).then((function(t){200==t.code&&(e.hotelRooms=t.result,console.log(e.hotelRooms))}))},bankTransfer:function(){vant.Dialog.confirm({title:"付款须知",message:this.hotelRooms.bankTransferNotify,confirmButtonText:"取消",cancelButtonText:"已缴费(忽略)"}).then((function(){})).catch((function(){}))},calcelOrder:function(){var t=this;vant.Dialog.confirm({title:"提示",message:"确认取消订单?"}).then((function(){t.$fly.get("/pyp/web/hotel/hotelorder/cancel",{orderId:t.orderId}).then((function(e){e&&200===e.code?(vant.Toast("取消成功"),t.getActivityInfo()):vant.Toast(e.msg)}))})).catch((function(){}))},ali:function(){var t=this;this.$fly.get("/pyp/web/hotel/hotelactivity/payAli",{orderId:this.orderId}).then((function(e){e&&200===e.code?t.$router.push({name:"commonAlipay",query:{form:encodeURIComponent(e.result)}}):vant.Toast(e.msg)}))},weixin:function(){var t=this;this.$fly.get("/pyp/web/hotel/hotelactivity/pay",{orderId:t.orderId}).then((function(t){t&&200===t.code?WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:t.result.appId,timeStamp:t.result.timeStamp,nonceStr:t.result.nonceStr,package:t.result.packageValue,signType:t.result.signType,paySign:t.result.paySign},(function(t){console.log("开始支付"),location.reload()})):vant.Toast(t.msg)}))},rebuildUrl:function(){var t=window.location,e=t.href,o=t.protocol,i=t.host,a=t.pathname,n=t.search,s=t.hash;console.log(window.location),n=n||"?";var c="".concat(o,"//").concat(i).concat(a).concat(n).concat(s);console.log(c),c!==e&&window.location.replace(c)}}}),s=n,c=(o("7a64"),o("2877")),r=Object(c["a"])(s,i,a,!1,null,"10bfad98",null);e["default"]=r.exports}}]);
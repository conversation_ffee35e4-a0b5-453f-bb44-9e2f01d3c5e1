(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d22ba00"],{f088:function(e,n,t){"use strict";t.r(n);t("b0c0");var u=function(){var e=this,n=e._self._c;return e.menu.list&&e.menu.list.length>=1?n("el-submenu",{attrs:{index:e.menu.menuId+"","popper-class":"site-sidebar--"+e.sidebarLayout<PERSON>kin+"-popper"}},[n("template",{slot:"title"},[n("i",{staticClass:"site-sidebar__menu-icon",class:e.menu.icon}),n("span",[e._v(e._s(e.menu.name))])]),e._l(e.menu.list,(function(t){return n("sub-menu",{key:t.menuId,attrs:{menu:t,dynamicMenuRoutes:e.dynamicMenuRoutes}})}))],2):n("el-menu-item",{attrs:{index:e.menu.menuId+""},on:{click:function(n){return e.gotoRouteHandle(e.menu)}}},[n("i",{staticClass:"site-sidebar__menu-icon fa",class:e.menu.icon}),n("span",[e._v(e._s(e.menu.name))])])},s=[],i=(t("4de4"),t("14d9"),t("d3b7"),t("0643"),t("2382"),{name:"sub-menu",props:{menu:{type:Object,required:!0},dynamicMenuRoutes:{type:Array,required:!0}},components:{SubMenu:r},computed:{sidebarLayoutSkin:{get:function(){return this.$store.state.common.sidebarLayoutSkin}}},methods:{gotoRouteHandle:function(e){var n=this.dynamicMenuRoutes.filter((function(n){return n.meta.menuId===e.menuId}));n.length>=1&&this.$router.push({name:n[0].name})}}}),a=i,o=t("2877"),m=Object(o["a"])(a,u,s,!1,null,null,null),r=n["default"]=m.exports}}]);
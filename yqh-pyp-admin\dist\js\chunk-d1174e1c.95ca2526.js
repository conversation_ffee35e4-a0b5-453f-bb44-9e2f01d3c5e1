(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d1174e1c"],{ed56:function(t,e,a){"use strict";a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return l}));var r=[{key:0,value:"预告"},{key:1,value:"直播"},{key:2,value:"录播"}],i=[{key:0,value:"未确认"},{key:1,value:"确认通过"},{key:2,value:"确认不通过"}],l=[{key:0,value:"自定义内容"},{key:1,value:"自定义链接"},{key:2,value:"会议日程"},{key:3,value:"会议嘉宾"},{key:4,value:"聊天室"},{key:5,value:"考试&问卷"},{key:6,value:"展商列表"},{key:7,value:"录播视频列表"}]},faea:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"主题嘉宾修改","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"}},[t.placeActivityTopicGuest.length>0?e("el-table",{staticStyle:{margin:"20px 0"},attrs:{data:t.placeActivityTopicGuest,border:""}},[e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"嘉宾名称"}}),e("el-table-column",{attrs:{prop:"orderBy","show-overflow-tooltip":"true","header-align":"center",align:"center",label:"排序"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-input",{attrs:{placeholder:"排序"},model:{value:a.row.orderBy,callback:function(e){t.$set(a.row,"orderBy",e)},expression:"scope.row.orderBy"}})],1)}}],null,!1,525224533)}),e("el-table-column",{attrs:{prop:"confirmStatus","show-overflow-tooltip":"true","header-align":"center",align:"center",label:"确认状态"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-select",{attrs:{placeholder:"确认状态",filterable:""},model:{value:a.row.confirmStatus,callback:function(e){t.$set(a.row,"confirmStatus",e)},expression:"scope.row.confirmStatus"}},t._l(t.confirmStatus,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1)}}],null,!1,1300827402)}),e("el-table-column",{attrs:{prop:"confirmTime","header-align":"center",align:"center",label:"确认时间"}}),e("el-table-column",{attrs:{prop:"confirmReason","header-align":"center",align:"center",label:"拒绝理由"}})],1):t._e()],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{disabled:0==t.placeActivityTopicGuest.length,type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},i=[],l=a("ed56"),o={data:function(){return{confirmStatus:l["a"],visible:!1,placeActivityTopicGuest:[],guestList:[],dataForm:{id:0,activityId:"",name:"",placeId:"",topicGuestIds:[],orderBy:0},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],name:[{required:!0,message:"会场名称不能为空",trigger:"blur"}],placeId:[{required:!0,message:"场地不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序，数值越小越靠前不能为空",trigger:"blur"}]}}},methods:{init:function(t,e){var a=this;this.dataForm.activityId=t,this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/place/placeactivitytopicguest/findByTopicId/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.placeActivityTopicGuest=e.result)}))}))},getGuest:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activityguest/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.guestList=a.result)}))},dataFormSubmit:function(){var t=this;this.$http({url:this.$http.adornUrl("/place/placeactivitytopicguest/updateBatch"),method:"post",data:this.placeActivityTopicGuest}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}}},n=o,s=a("2877"),c=Object(s["a"])(n,r,i,!1,null,null,null);e["default"]=c.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-70b580ed","chunk-2d0a4b8c","chunk-2d0a4b8c","chunk-58faa667"],{"02d7":function(t,e,a){"use strict";a.d(e,"d",(function(){return o})),a.d(e,"c",(function(){return i})),a.d(e,"b",(function(){return r})),a.d(e,"a",(function(){return n}));var o=[{key:0,value:"考卷"},{key:1,value:"问卷"}],i=[{key:0,value:"统一时间考试"},{key:1,value:"随时考试"}],r=[{key:0,value:"单选"},{key:1,value:"多选"},{key:2,value:"填空"}],n=[{key:0,value:"未提交"},{key:1,value:"已提交"},{key:2,value:"已通过"},{key:3,value:"未通过"},{key:4,value:"已超时"},{key:5,value:"作废"}]},"083a":function(t,e,a){"use strict";var o=a("0d51"),i=TypeError;t.exports=function(t,e){if(!delete t[e])throw new i("Cannot delete property "+o(e)+" of "+o(t))}},"7f00":function(t,e,a){"use strict";a.r(e);a("b0c0");var o=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"题目类型",prop:"type"}},[e("el-select",{attrs:{placeholder:"题目类型",filterable:""},on:{change:t.changeTypeHandle},model:{value:t.dataForm.type,callback:function(e){t.$set(t.dataForm,"type",e)},expression:"dataForm.type"}},t._l(t.examQuestionType,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1),e("el-form-item",{attrs:{label:"题目名称",prop:"name"}},[e("tinymce-editor",{ref:"editor",model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",{attrs:{label:"自定义分值",prop:"points"}},[e("el-input",{attrs:{placeholder:"自定义分值"},model:{value:t.dataForm.points,callback:function(e){t.$set(t.dataForm,"points",e)},expression:"dataForm.points"}})],1),2!=t.dataForm.type?e("div",[e("el-form-item",{attrs:{label:"选项"}},[e("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.addOptionHandle(t.options.length)}}},[t._v("添加选项")]),e("el-table",{attrs:{data:t.options}},[e("el-table-column",{attrs:{prop:"optionId","header-align":"center",align:"center",label:"选项"}}),e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"内容"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-input",{attrs:{placeholder:"内容"},model:{value:a.row.name,callback:function(e){t.$set(a.row,"name",e)},expression:"scope.row.name"}})],1)}}],null,!1,2191848166)}),e("el-table-column",{attrs:{"header-align":"center",align:"center",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.deletePaymentHandle(a.row)}}},[t._v("删除")])],1)}}],null,!1,3870104636)})],1)],1),0==t.dataForm.type?e("el-form-item",{attrs:{label:"答案",prop:"charOptionId"}},[e("el-select",{attrs:{placeholder:"请选择类型"},model:{value:t.dataForm.charOptionId,callback:function(e){t.$set(t.dataForm,"charOptionId",e)},expression:"dataForm.charOptionId"}},t._l(t.options,(function(t){return e("el-option",{key:t.optionId,attrs:{label:t.optionId,value:t.optionId}})})),1)],1):e("el-form-item",{attrs:{label:"答案",prop:"charOptionId"}},[e("el-select",{attrs:{multiple:"",placeholder:"请选择类型"},model:{value:t.dataForm.charOptionId,callback:function(e){t.$set(t.dataForm,"charOptionId",e)},expression:"dataForm.charOptionId"}},t._l(t.options,(function(t){return e("el-option",{key:t.optionId,attrs:{label:t.optionId,value:t.optionId}})})),1)],1)],1):e("div",[e("el-form-item",{attrs:{label:"答案",prop:"charOptionId"}},[e("el-input",{attrs:{placeholder:"答案"},model:{value:t.dataForm.charOptionId,callback:function(e){t.$set(t.dataForm,"charOptionId",e)},expression:"dataForm.charOptionId"}})],1)],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},i=[],r=(a("14d9"),a("a434"),a("d3b7"),a("25f0"),a("3ca3"),a("ddb0"),a("02d7")),n={data:function(){return{examQuestionType:r["b"],visible:!1,dataForm:{id:0,activityId:"",examId:"",name:"",type:"",optionId:"",charOptionId:"",points:0},options:[{optionId:"A",name:""}],character:["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],examId:[{required:!0,message:"考卷id不能为空",trigger:"blur"}],name:[{required:!0,message:"题目名称不能为空",trigger:"blur"}],type:[{required:!0,message:"题目类型不能为空",trigger:"blur"}],optionId:[{required:!0,message:"正确答案不能为空",trigger:"blur"}],points:[{required:!0,message:"自定义分值不能为空",trigger:"blur"}]}}},components:{TinymceEditor:function(){return Promise.all([a.e("chunk-2d0e1c2e"),a.e("chunk-03be236c")]).then(a.bind(null,"26dc"))},OssUploader:function(){return a.e("chunk-2d0e97b1").then(a.bind(null,"8e5c"))}},methods:{init:function(t,e,a){var o=this;this.dataForm.activityId=t,this.dataForm.examId=e,this.dataForm.id=a||0,this.visible=!0,this.$nextTick((function(){o.$refs["dataForm"].resetFields(),o.options=[],o.dataForm.id&&(o.$http({url:o.$http.adornUrl("/exam/examquestion/info/".concat(o.dataForm.id)),method:"get",params:o.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(o.dataForm.activityId=e.examQuestion.activityId,o.dataForm.examId=e.examQuestion.examId,o.dataForm.name=e.examQuestion.name,o.dataForm.type=e.examQuestion.type,1==o.dataForm.type?o.dataForm.charOptionId=e.examQuestion.charOptionId.split(","):o.dataForm.charOptionId=e.examQuestion.charOptionId,o.dataForm.points=e.examQuestion.points)})),o.$http({url:o.$http.adornUrl("/exam/examquestionoption/findByQuestionId/".concat(o.dataForm.id)),method:"get",params:o.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(o.options=e.result)})))}))},changeTypeHandle:function(){1==this.dataForm.type?this.dataForm.charOptionId=[]:this.dataForm.charOptionId=""},addOptionHandle:function(t){if(t>=26)return this.$message.error("选项数量已上线"),!1;this.options.push({optionId:this.character[t],name:""}),this.$forceUpdate()},deletePaymentHandle:function(t){var e=this.options.indexOf(t);if(this.options.length<=1)this.$message.error("至少保留一个选项");else if(-1!==e){if(e==this.options.length-1)this.options.splice(e,1);else{if(0==this.dataForm.type)(this.dataForm.charOptionId=this.options[e].id)&&(this.dataForm.charOptionId="");else if(1==this.dataForm.type&&-1!=this.dataForm.charOptionId.indexOf(this.options[e].id))for(var a in this.dataForm.charOptionId.splice(this.dataForm.charOptionId.indexOf(this.options[e].id),1),this.dataForm.charOptionId)this.character.indexOf(this.dataForm.charOptionId[a])>this.character.indexOf(this.options[e].id)&&(this.dataForm.charOptionId[a]=this.character[this.character.indexOf(this.dataForm.charOptionId[a])-1]);for(var o in this.options)o>e&&(this.options[o].optionId=this.character[o-1]);this.options.splice(e,1)}this.$forceUpdate()}},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){if(e){if(2!=t.dataForm.type)for(var a=0;a<t.options.length;a++)if(""==t.options[a].optionId||""==t.options[a].name)return t.$message.error("选项不为空，请填写完整"),!1;1==t.dataForm.type&&(t.dataForm.charOptionId=t.dataForm.charOptionId.toString()),t.$http({url:t.$http.adornUrl("/exam/examquestion/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,activityId:t.dataForm.activityId,examId:t.dataForm.examId,name:t.dataForm.name,type:t.dataForm.type,optionId:t.dataForm.optionId,charOptionId:t.dataForm.charOptionId,points:t.dataForm.points,examQuestionOptionEntities:t.options})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}}))}}},d=n,s=a("2877"),l=Object(s["a"])(d,o,i,!1,null,null,null);e["default"]=l.exports},a434:function(t,e,a){"use strict";var o=a("23e7"),i=a("7b0b"),r=a("23cb"),n=a("5926"),d=a("07fa"),s=a("3a34"),l=a("3511"),c=a("65f0"),p=a("8418"),m=a("083a"),u=a("1dde"),h=u("splice"),f=Math.max,F=Math.min;o({target:"Array",proto:!0,forced:!h},{splice:function(t,e){var a,o,u,h,v,b,y=i(this),I=d(y),g=r(t,I),O=arguments.length;for(0===O?a=o=0:1===O?(a=0,o=I-g):(a=O-2,o=F(f(n(e),0),I-g)),l(I+a-o),u=c(y,o),h=0;h<o;h++)v=g+h,v in y&&p(u,h,y[v]);if(u.length=o,a<o){for(h=g;h<I-o;h++)v=h+o,b=h+a,v in y?y[b]=y[v]:m(y,b);for(h=I;h>I-o+a;h--)m(y,h-1)}else if(a>o)for(h=I-o;h>g;h--)v=h+o-1,b=h+a-1,v in y?y[b]=y[v]:m(y,b);for(h=0;h<a;h++)y[h+g]=arguments[h+2];return s(y,I-o+a),u}})}}]);
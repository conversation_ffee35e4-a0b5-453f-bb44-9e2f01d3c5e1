(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-30a4a34b"],{"28a5":function(t,e,i){"use strict";var a=i("aae3"),n=i("cb7c"),s=i("ebd6"),o=i("0390"),r=i("9def"),c=i("5f1b"),l=i("520a"),u=i("79e5"),d=Math.min,h=[].push,f="split",y="length",v="lastIndex",g=4294967295,p=!u((function(){RegExp(g,"y")}));i("214f")("split",2,(function(t,e,i,u){var m;return m="c"=="abbc"[f](/(b)*/)[1]||4!="test"[f](/(?:)/,-1)[y]||2!="ab"[f](/(?:ab)*/)[y]||4!="."[f](/(.?)(.?)/)[y]||"."[f](/()()/)[y]>1||""[f](/.?/)[y]?function(t,e){var n=String(this);if(void 0===t&&0===e)return[];if(!a(t))return i.call(n,t,e);var s,o,r,c=[],u=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),d=0,f=void 0===e?g:e>>>0,p=new RegExp(t.source,u+"g");while(s=l.call(p,n)){if(o=p[v],o>d&&(c.push(n.slice(d,s.index)),s[y]>1&&s.index<n[y]&&h.apply(c,s.slice(1)),r=s[0][y],d=o,c[y]>=f))break;p[v]===s.index&&p[v]++}return d===n[y]?!r&&p.test("")||c.push(""):c.push(n.slice(d)),c[y]>f?c.slice(0,f):c}:"0"[f](void 0,0)[y]?function(t,e){return void 0===t&&0===e?[]:i.call(this,t,e)}:i,[function(i,a){var n=t(this),s=void 0==i?void 0:i[e];return void 0!==s?s.call(i,n,a):m.call(String(n),i,a)},function(t,e){var a=u(m,t,this,e,m!==i);if(a.done)return a.value;var l=n(t),h=String(this),f=s(l,RegExp),y=l.unicode,v=(l.ignoreCase?"i":"")+(l.multiline?"m":"")+(l.unicode?"u":"")+(p?"y":"g"),I=new f(p?l:"^(?:"+l.source+")",v),S=void 0===e?g:e>>>0;if(0===S)return[];if(0===h.length)return null===c(I,h)?[h]:[];var b=0,k=0,x=[];while(k<h.length){I.lastIndex=p?k:0;var L,w=c(I,p?h:h.slice(k));if(null===w||(L=d(r(I.lastIndex+(p?0:k)),h.length))===b)k=o(h,k,y);else{if(x.push(h.slice(b,k)),x.length===S)return x;for(var M=1;M<=w.length-1;M++)if(x.push(w[M]),x.length===S)return x;k=b=L}}return x.push(h.slice(b)),x}]}))},"66c7":function(t,e,i){"use strict";i("4917"),i("a481");var a=/([yMdhsm])(\1*)/g,n="yyyy-MM-dd";function s(t,e){e-=(t+"").length;for(var i=0;i<e;i++)t="0"+t;return t}e["a"]={formatDate:{format:function(t,e){return e=e||n,e.replace(a,(function(e){switch(e.charAt(0)){case"y":return s(t.getFullYear(),e.length);case"M":return s(t.getMonth()+1,e.length);case"d":return s(t.getDate(),e.length);case"w":return t.getDay()+1;case"h":return s(t.getHours(),e.length);case"m":return s(t.getMinutes(),e.length);case"s":return s(t.getSeconds(),e.length)}}))},parse:function(t,e){var i=e.match(a),n=t.match(/(\d)+/g);if(i.length==n.length){for(var s=new Date(1970,0,1),o=0;o<i.length;o++){var r=parseInt(n[o]),c=i[o];switch(c.charAt(0)){case"y":s.setFullYear(r);break;case"M":s.setMonth(r-1);break;case"d":s.setDate(r);break;case"h":s.setHours(r);break;case"m":s.setMinutes(r);break;case"s":s.setSeconds(r);break}}return s}return null},toWeek:function(t){var e=new Date(t).getDay(),i="";switch(e){case 0:i="s";break;case 1:i="m";break;case 2:i="t";break;case 3:i="w";break;case 4:i="t";break;case 5:i="f";break;case 6:i="s";break}return i}},toUserLook:function(t){var e=Math.floor(t/3600%24),i=Math.floor(t/60%60);return e<1?i+"分":e+"时"+i+"分"}}},ac6a:function(t,e,i){for(var a=i("cadf"),n=i("0d58"),s=i("2aba"),o=i("7726"),r=i("32e9"),c=i("84f2"),l=i("2b4c"),u=l("iterator"),d=l("toStringTag"),h=c.Array,f={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},y=n(f),v=0;v<y.length;v++){var g,p=y[v],m=f[p],I=o[p],S=I&&I.prototype;if(S&&(S[u]||r(S,u,h),S[d]||r(S,d,p),c[p]=h,m))for(g in a)S[g]||s(S,g,a[g],!0)}},e9e6:function(t,e,i){"use strict";i.r(e);i("7f7f");var a=function(){var t=this,e=t._self._c;return e("div",[e("van-search",{attrs:{placeholder:"请输入您要搜索的酒店名称","show-action":"",shape:"round"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}},[e("div",{staticClass:"search-text",attrs:{slot:"action"},on:{click:t.onSearch},slot:"action"},[t._v("搜索")])]),e("van-list",{attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:t.onLoad},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},[e("div",{staticClass:"nav-title",staticStyle:{"margin-top":"8px"}},[e("div",{staticClass:"color"}),e("div",{staticClass:"text"},[t._v("酒店列表")])]),t._l(t.dataList,(function(i){return e("van-card",{key:i.id,staticStyle:{background:"white"},attrs:{thumb:i.hotelImg?i.hotelImg:"van-icon"},scopedSlots:t._u([{key:"tags",fn:function(){return[t._l(i.roomTagName,(function(i){return e("van-tag",{key:i,staticStyle:{margin:"5px 10px 5px 0px"},attrs:{size:"medium",plain:"",type:"danger"}},[t._v(t._s(i))])})),i.brief?e("van-tag",{staticStyle:{margin:"5px 10px 5px 0px"},attrs:{size:"medium",plain:"",type:"danger"}},[t._v(t._s(i.brief))]):t._e()]},proxy:!0},{key:"num",fn:function(){return[i.roomTagName&&i.roomTagName.length>0?e("van-button",{attrs:{size:"small",round:"",type:"primary",plain:""},on:{click:function(e){return t.$router.push({name:"hotelRoom",query:{id:i.id,activityId:i.activityId}})}}},[t._v("选择房型")]):t._e(),i.isDetail?e("van-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"small",round:"",type:"info",plain:""},on:{click:function(e){return t.$router.push({name:"hotelDetail",query:{id:i.id,activityId:i.activityId}})}}},[t._v("详细介绍")]):t._e(),i.isMobile?e("van-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"small",round:"",type:"primary",plain:""},on:{click:function(e){return t.callPhone(i.mobile)}}},[t._v("联系酒店")]):t._e()]},proxy:!0}],null,!0)},[e("div",{staticStyle:{"font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(i.hotelName))]),i.hotelStar?e("div",{staticStyle:{"padding-top":"10px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[e("van-rate",{attrs:{readonly:"",size:10,color:"#ffd21e","void-icon":"star","void-color":"#eee"},model:{value:i.hotelStar,callback:function(e){t.$set(i,"hotelStar",e)},expression:"item.hotelStar"}})],1):t._e(),e("div",{staticStyle:{"font-size":"14px"},attrs:{slot:"price"},slot:"price"},[t._v(t._s(i.hotelAddress))])])}))],2),e("img",{staticClass:"back",attrs:{src:t.activityInfo.backImg,alt:""},on:{click:t.cmsTurnBack}})],1)},n=[],s=(i("ac6a"),i("28a5"),i("6762"),i("2fdb"),i("66c7")),o={data:function(){return{openid:void 0,activityId:void 0,dataForm:{name:"",status:1},activityInfo:{},loading:!1,finished:!1,dataList:[],pageIndex:1,pageSize:10,totalPage:0}},mounted:function(){document.title="预定酒店",this.activityId=this.$route.query.id,this.openid=this.$cookie.get("openid"),this.$wxShare(),this.getActivityInfo()},methods:{onSearch:function(){this.pageIndex=1,this.dataList=[],this.getActivityList()},onLoad:function(){this.getActivityList()},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(e){200==e.code?(t.activityInfo=e.activity,t.activityInfo.backImg=t.activityInfo.backImg||"http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png",e.activity.hotelNeedApply?t.checkApply():t.loadActivityDo()):t.activityInfo={}}))},checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(e){200==e.code?(t.isPay=e.isPay,t.$store.commit("apply/update",t.isPay),1==e.isPay&&1==e.verifyStatus?t.loadActivityDo():(sessionStorage.setItem("returnUrl",encodeURIComponent(window.location.href)),t.$router.push({name:"applyIndex",query:{id:t.activityId}}))):vant.Toast(e.msg)}))},loadActivityDo:function(){var t=this;this.activityInfo.onlyOneHotel?this.$fly.get("/pyp/web/hotel/hotelorder/findMyActivityOrder",{activityId:this.activityId}).then((function(e){200==e.code?e.result&&e.result.length>0?(vant.Toast("已提交酒店信息"),t.$router.push({name:"hotelSuccess",query:{orderId:e.result[0].id,id:t.activityId}})):t.getShareAndNotify():vant.Toast(e.msg)})):this.getShareAndNotify()},getShareAndNotify:function(){this.activityInfo.hotelNotify&&vant.Dialog.alert({title:"预订须知",message:this.activityInfo.hotelNotify}).then((function(){}));var t=s["a"].formatDate.format(new Date(this.activityInfo.startTime),"yyyy年MM月dd日"),e=s["a"].formatDate.format(new Date(this.activityInfo.endTime),"MM月dd日");if(t.includes(e)){var i="时间:"+t+"\n地址:"+this.activityInfo.address;this.$wxShare("预定酒店-"+this.activityInfo.name,this.activityInfo.shareUrl?this.activityInfo.shareUrl:this.activityInfo.mobileBanner.split(",")[0],i)}else{var a="时间:"+t+"-"+e+"\n地址:"+this.activityInfo.address;this.$wxShare("预定酒店-"+this.activityInfo.name,this.activityInfo.shareUrl?this.activityInfo.shareUrl:this.activityInfo.mobileBanner.split(",")[0],a)}},getActivityList:function(){var t=this;this.$fly.get("/pyp/web/hotel/hotelactivity/list",{page:this.pageIndex,limit:this.pageSize,activityId:this.activityId,status:this.dataForm.status,name:this.dataForm.name}).then((function(e){t.loading=!1,200==e.code?e.page.list&&e.page.list.length>0?(e.page.list.forEach((function(e){e.roomTagName=e.roomTagName?e.roomTagName.split(","):[],t.dataList.push(e)})),t.totalPage=e.page.totalPage,t.pageIndex++,t.loading=!1,t.totalPage<t.pageIndex?t.finished=!0:t.finished=!1):t.finished=!0:(vant.Toast(e.msg),t.dataList=[],t.totalPage=0,t.finished=!0)}))},callPhone:function(t){window.location.href="tel:"+t},cmsTurnBack:function(){this.$router.go(-1)}}},r=o,c=i("2877"),l=Object(c["a"])(r,a,n,!1,null,null,null);e["default"]=l.exports}}]);
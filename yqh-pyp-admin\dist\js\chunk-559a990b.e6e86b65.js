(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-559a990b"],{"7b68":function(e,t,a){"use strict";a("ce4e")},"91f2":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:"绑定变更历史","close-on-click-modal":!1,visible:e.visible,width:"80%"},on:{"update:visible":function(t){e.visible=t}}},[t("div",{staticClass:"history-header"},[t("el-form",{attrs:{inline:!0,model:e.searchForm}},[t("el-form-item",{attrs:{label:"操作类型"}},[t("el-select",{attrs:{placeholder:"全部",clearable:""},model:{value:e.searchForm.operationType,callback:function(t){e.$set(e.searchForm,"operationType",t)},expression:"searchForm.operationType"}},[t("el-option",{attrs:{label:"新增绑定",value:1}}),t("el-option",{attrs:{label:"更换业务员",value:2}}),t("el-option",{attrs:{label:"解除绑定",value:3}}),t("el-option",{attrs:{label:"系统自动解绑",value:4}})],1)],1),t("el-form-item",[t("el-button",{on:{click:function(t){return e.getHistoryList()}}},[e._v("查询")])],1)],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.historyLoading,expression:"historyLoading"}],staticStyle:{width:"100%"},attrs:{data:e.historyList,border:""}},[t("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",width:"150",label:"操作时间"}}),t("el-table-column",{attrs:{prop:"operationType","header-align":"center",align:"center",label:"操作类型"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{attrs:{type:e.getOperationTypeTagType(a.row.operationType)}},[e._v(" "+e._s(e.getOperationTypeText(a.row.operationType))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"oldSalesmanName","header-align":"center",align:"center",label:"原业务员"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.oldSalesmanName?t("span",[e._v(e._s(a.row.oldSalesmanName))]):t("span",{staticStyle:{color:"#999"}},[e._v("-")])]}}])}),t("el-table-column",{attrs:{prop:"newSalesmanName","header-align":"center",align:"center",label:"新业务员"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.newSalesmanName?t("span",[e._v(e._s(a.row.newSalesmanName))]):t("span",{staticStyle:{color:"#999"}},[e._v("-")])]}}])}),t("el-table-column",{attrs:{prop:"operationReason","header-align":"center",align:"center",label:"操作原因"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.operationReason?t("span",[e._v(e._s(a.row.operationReason))]):t("span",{staticStyle:{color:"#999"}},[e._v("-")])]}}])}),t("el-table-column",{attrs:{prop:"bindingSource","header-align":"center",align:"center",label:"绑定来源"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.bindingSource?t("span",[e._v(e._s(a.row.bindingSource))]):t("span",{staticStyle:{color:"#999"}},[e._v("-")])]}}])}),t("el-table-column",{attrs:{prop:"operatorType","header-align":"center",align:"center",label:"操作人类型"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getOperatorTypeText(t.row.operatorType))+" ")]}}])})],1),t("el-pagination",{staticStyle:{"margin-top":"20px"},attrs:{"current-page":e.pageIndex,"page-sizes":[10,20,50],"page-size":e.pageSize,total:e.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle}}),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("关闭")])],1)],1)},o=[],r={data:function(){return{visible:!1,wxUserId:null,historyList:[],historyLoading:!1,pageIndex:1,pageSize:10,totalPage:0,searchForm:{operationType:""}}},methods:{init:function(e){this.wxUserId=e,this.visible=!0,this.pageIndex=1,this.getHistoryList()},getHistoryList:function(){var e=this;this.historyLoading=!0,this.$http({url:this.$http.adornUrl("/salesman/wxuserbinding/history"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,wxUserId:this.wxUserId,operationType:this.searchForm.operationType})}).then((function(t){var a=t.data;a&&200===a.code?(e.historyList=a.page.list,e.totalPage=a.page.totalCount):(e.historyList=[],e.totalPage=0),e.historyLoading=!1})).catch((function(){e.historyLoading=!1}))},sizeChangeHandle:function(e){this.pageSize=e,this.pageIndex=1,this.getHistoryList()},currentChangeHandle:function(e){this.pageIndex=e,this.getHistoryList()},getOperationTypeText:function(e){var t={1:"新增绑定",2:"更换业务员",3:"解除绑定",4:"系统自动解绑"};return t[e]||"未知"},getOperationTypeTagType:function(e){var t={1:"success",2:"warning",3:"danger",4:"info"};return t[e]||""},getOperatorTypeText:function(e){var t={1:"客户自己",2:"业务员",3:"管理员",4:"系统"};return t[e]||"未知"}}},i=r,l=(a("7b68"),a("2877")),s=Object(l["a"])(i,n,o,!1,null,"299edab2",null);t["default"]=s.exports},ce4e:function(e,t,a){}}]);
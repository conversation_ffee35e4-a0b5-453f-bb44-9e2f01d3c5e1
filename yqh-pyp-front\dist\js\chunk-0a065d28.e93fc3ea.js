(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0a065d28","chunk-37a545c8"],{"062e":function(t,i,s){"use strict";s("c827")},"1b69":function(t,i,s){"use strict";s.r(i);s("7f7f");var e,a=function(){var t=this,i=t._self._c;return i("div",{staticStyle:{background:"white","margin-bottom":"20px"}},[i("van-row",{attrs:{gutter:"20"}},[i("van-col",{attrs:{span:"16"}},[i("van-swipe",{staticStyle:{width:"100%"},attrs:{autoplay:3e3}},t._l(t.activityInfo.appFileList,(function(t,s){return i("van-swipe-item",{key:s},[i("van-image",{attrs:{width:"100%",src:t.url}})],1)})),1)],1),i("van-col",{attrs:{span:"8"}},[i("div",{staticStyle:{"margin-top":"20px"}},[i("van-cell",{attrs:{title:"会议名称",value:t.activityInfo.name}}),t.activityInfo.address?i("van-cell",{attrs:{title:"会议地点",value:t.activityInfo.address}}):t._e(),i("van-cell",{attrs:{title:"会议开始时间",value:t.activityInfo.startTime}}),i("van-cell",{attrs:{title:"会议结束时间",value:t.activityInfo.endTime}}),i("van-cell",{attrs:{title:"登录信息"},scopedSlots:t._u([{key:"default",fn:function(){return[t.userInfo?i("van-button",{attrs:{type:"primary",size:"small",round:""}},[t._v(t._s(t.userInfo.username)+"("+t._s(t.userInfo.mobile)+")")]):i("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.showLogin}},[t._v("未登录(点击登录)")])]},proxy:!0}])}),"1736999159118508033"!=t.activityId?i("van-cell",{attrs:{title:"报名状态"},scopedSlots:t._u([{key:"default",fn:function(){return[1==t.isPay?i("van-button",{attrs:{type:"primary",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名")]):2==t.isPay?i("van-button",{attrs:{type:"warning",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名未交费")]):i("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.turnApply}},[t._v("未报名")])]},proxy:!0}],null,!1,567203517)}):t._e()],1)])],1),i("van-tabs",{staticClass:"nav",on:{click:t.onClick},model:{value:t.cmsId,callback:function(i){t.cmsId=i},expression:"cmsId"}},t._l(t.cmsList,(function(t){return i("van-tab",{key:t.id,attrs:{title:t.title,name:t.id}})})),1),i("pclogin")],1)},n=[],c=s("ade3"),o=(s("a481"),s("6762"),s("2fdb"),s("cacf")),l=s("7dcb"),r=function(){var t=this,i=t._self._c;return i("van-dialog",{attrs:{title:"登录操作",width:"400px"},on:{confirm:t.login},model:{value:t.showPcLogin,callback:function(i){t.showPcLogin=i},expression:"showPcLogin"}},[i("div",{staticClass:"text-center padding"},[i("van-cell-group",{attrs:{inset:""}},[i("van-field",{attrs:{name:"手机号",label:"手机号",required:"",placeholder:"手机号",rules:[{required:!0,message:"请填写手机号"}]},model:{value:t.mobile,callback:function(i){t.mobile=i},expression:"mobile"}}),"1736999159118508033"!=t.activityId?i("van-field",{attrs:{center:"",clearable:"",maxlength:"6",label:"短信验证码",required:"",placeholder:"请输入短信验证码"},scopedSlots:t._u([{key:"button",fn:function(){return[i("van-button",{attrs:{size:"small",type:"primary",disabled:t.waiting},on:{click:function(i){return t.doSendSmsCode()}}},[t.waiting?i("span",[t._v(t._s(t.waitingTime)+"秒后重发")]):i("span",{staticStyle:{"font-size":"13px"}},[t._v("获取验证码")])])]},proxy:!0}],null,!1,850170227),model:{value:t.code,callback:function(i){t.code=i},expression:"code"}}):t._e()],1)],1)])},d=[],v={name:"pclogin",computed:{showPcLogin:{get:function(){return this.$store.state.user.showPcLogin},set:function(t){this.$store.commit("user/changePcLogin",t)}}},data:function(){return{activityId:void 0,waiting:!1,waitingTime:60,mobile:"",code:""}},mounted:function(){this.activityId=this.$route.query.id},methods:{login:function(){var t=this;return this.mobile?Object(o["b"])(this.mobile)?this.code||0x181b0f883e693000==this.activityId?/^\d{6}$/.test(this.code)||0x181b0f883e693000==this.activityId?void this.$fly.post("/pyp/web/user/pcLogin",{mobile:this.mobile,code:0x181b0f883e693000==this.activityId?"121212":this.code}).then((function(i){i&&200===i.code?(vant.Toast("登录成功"),t.$store.commit("user/update",i.userInfo),t.showPcLogin=!1,"applyIndex"!=t.$route.name&&"livesDetail"!=t.$route.name||location.reload()):vant.Toast(i.msg)})):(vant.Toast("验证码错误"),!1):(vant.Toast("请输入验证码"),!1):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},doSendSmsCode:function(){var t=this;return this.mobile?Object(o["b"])(this.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.mobile,activityId:this.activityId}).then((function(i){i&&200===i.code?(t.countdown(),vant.Toast("发送验证码成功")):vant.Toast(i.msg)})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var t=this;this.waiting=!0;var i=window.setInterval((function(){t.waitingTime--,t.waitingTime<0&&(window.clearInterval(i),t.waitingTime=60,t.waiting=!1)}),1e3)}}},u=v,h=s("2877"),m=Object(h["a"])(u,r,d,!1,null,null,null),f=m.exports,p={components:{pclogin:f},data:function(){return{activityId:void 0,bannerIndex:0,cmsId:void 0,cmsList:[],cmsInfo:{},activityInfo:{}}},computed:{userInfo:{get:function(){return this.$store.state.user.userInfo},set:function(t){this.$store.commit("user/update",t)}},isPay:{get:function(){return this.$store.state.apply.isPay},set:function(t){this.$store.commit("apply/update",t)}}},mounted:function(){var t=(new Date).getTime();localStorage.setItem("logAddTime",t+36e5),this.openid=this.$cookie.get("openid"),this.activityId=this.$route.query.id,this.cmsId=sessionStorage.getItem("cmsId")||this.$route.query.cmsId,this.checkLogin(),this.getActivityInfo(),this.activityLogCount()},methods:(e={showLogin:function(){this.$store.commit("user/changePcLogin",!0)},checkLogin:function(){var t=this;this.$fly.get("/pyp/web/user/checkLogin").then((function(i){t.userInfo=i.result,t.userInfo?t.checkApply():t.isPay=0}))},bannerIndexChange:function(t){this.bannerIndex=t},checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(i){200==i.code?(t.isPay=i.isPay,1==t.isPay||2==t.isPay&&vant.Dialog.alert({title:"提示",message:"您有一笔注册费待支付，点击跳转"}).then((function(){t.$router.push({name:"applySuccess",query:{orderId:i.result,id:t.activityId}})}))):vant.Toast(i.msg)}))},changeShowType:function(t){this.$emit("changeShowType",t)},turnApply:function(){var t=this.cmsList.filter((function(t){return t.model&&t.model.includes("applyIndex")}));if(t){var i=t[0];sessionStorage.setItem("cmsId",i.id);var s=i.model.replace("${activityId}",i.activityId);this.$router.push(JSON.parse(s))}else vant.Toast("暂未开启报名")},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(i){if(t.loading=!1,200==i.code){t.activityInfo=i.activity,document.title=t.activityInfo.name;var s=t.activityInfo.startTime,e=new Date(s.replace(/-/g,"/")),a=new Date,n=e.getTime()-a.getTime();t.dateCompare=n>0?n:0,t.getCmsList()}}))},activityLogCount:function(){var t=localStorage.getItem("logAddTime"),i=(new Date).getTime();i>t&&this.$fly.post("/pyp/activity/activityviewlog/count",{activityId:this.activityId,device:l["a"].getVersion()}).then((function(t){}))},getCmsList:function(){var t=this;this.$fly.get("/pyp/cms/cms/findByActivityId/".concat(this.activityId)).then((function(i){t.loading=!1,200==i.code?(t.cmsList=i.result,t.cmsId?t.changeShowType(t.cmsId):t.changeShowType(t.cmsList[0].id)):(vant.Toast(i.msg),t.cmsList=[])}))},onClick:function(t){if(console.log(t),sessionStorage.setItem("cmsId",t),this.cmsInfo=this.cmsList.filter((function(i){return i.id==t}))[0],this.cmsInfo.url&&Object(o["d"])(this.cmsInfo.url))location.href=this.cmsInfo.url;else if(this.cmsInfo.model&&this.isJSON(this.cmsInfo.model)){var i=this.cmsInfo.model.replace("${activityId}",this.cmsInfo.activityId);this.$router.push(JSON.parse(i))}else"cmsIndex"==this.$route.name?this.changeShowType(t):this.$router.push({name:"cmsIndex",query:{id:this.activityId,cmsId:t}})}},Object(c["a"])(e,"bannerIndexChange",(function(t){this.bannerIndex=t})),Object(c["a"])(e,"isJSON",(function(t){try{return JSON.parse(t),!0}catch(i){return!1}})),e)},C=p,g=(s("dd7a"),Object(h["a"])(C,a,n,!1,null,"7bd3d808",null));i["default"]=g.exports},"7dcb":function(t,i,s){"use strict";s("a481"),s("4917");i["a"]={getVersion:function(){var t=navigator.userAgent||window.navigator.userAgent,i=/HUAWEI|HONOR/gi,s=/[^;]+(?= Build)/gi,e=/CPU iPhone OS \d[_\d]*/gi,a=/CPU OS \d[_\d]*/gi,n=/Windows NT \d[\.\d]*/gi,c=/Linux x\d[_\d]*/gi,o=/Mac OS X \d[_|\.\d]*/gi;return/Android/gi.test(t)?i.test(t)?t.match(i)[0]+t.match(s)[0]:t.match(s)[0]:/iPhone/gi.test(t)?t.match(e)[0].replace(/CPU iPhone OS/gi,"iPhone").replace(/_/g,"."):/iPad/gi.test(t)?t.match(a)[0].replace(/CPU OS/gi,"iPad").replace(/_/g,"."):/Windows/gi.test(t)?"Windows "+(Math.min(parseInt(t.match(n)[0].replace(/Windows NT /,""))+1,10)<7?"XP":Math.min(parseInt(t.match(n)[0].replace(/Windows NT /,""))+1,10)):/Linux/gi.test(t)?t.match(c)[0]:/Macintosh/gi.test(t)?t.match(o)[0].replace(/_/g,"."):"unknown"}}},aa07:function(t,i,s){"use strict";s.r(i);s("7f7f");var e=function(){var t=this,i=t._self._c;return i("div",{staticClass:"children-page"},[t.isMobilePhone?t._e():i("pcheader"),i("div",{staticClass:"custom-navbar"},[i("van-icon",{staticClass:"nav-back",attrs:{name:"arrow-left"},on:{click:function(i){return t.$router.go(-1)}}}),i("span",{staticClass:"nav-title"},[t._v("子业务员管理")]),i("div",{staticClass:"nav-actions"},[i("van-icon",{staticClass:"nav-action",attrs:{name:"orders-o"},on:{click:t.goToOrderList}})],1)],1),t.loading?i("van-loading",{staticClass:"loading-center",attrs:{type:"spinner",color:"#ffffff",size:"24px"}},[t._v("\n    正在加载...\n  ")]):i("div",{staticClass:"content-section"},[i("div",{staticClass:"stats-overview"},[i("div",{staticClass:"stats-card"},[i("div",{staticClass:"card-header"},[i("div",{staticClass:"header-icon"},[i("van-icon",{attrs:{name:"chart-trending-o",size:"20",color:"#ffffff"}})],1),i("div",{staticClass:"header-text"},[i("h3",[t._v("团队概况")]),i("p",[t._v("下级业务员统计")])])]),i("div",{staticClass:"stats-grid"},[i("div",{staticClass:"stat-item"},[i("div",{staticClass:"stat-number"},[t._v(t._s(t.children.length))]),i("div",{staticClass:"stat-label"},[t._v("直属下级")])]),i("div",{staticClass:"stat-item"},[i("div",{staticClass:"stat-number"},[t._v(t._s(t.totalGrandChildren))]),i("div",{staticClass:"stat-label"},[t._v("间接下级")])]),i("div",{staticClass:"stat-item"},[i("div",{staticClass:"stat-number"},[t._v(t._s(t.totalTeamOrders))]),i("div",{staticClass:"stat-label"},[t._v("团队订单")])]),i("div",{staticClass:"stat-item"},[i("div",{staticClass:"stat-number"},[t._v("¥"+t._s(t.totalTeamAmount.toFixed(0)))]),i("div",{staticClass:"stat-label"},[t._v("团队业绩")])])])])]),t.children.length>0?i("div",{staticClass:"children-list"},[i("div",{staticClass:"section-header"},[i("h3",[t._v("下级业务员列表")]),i("span",{staticClass:"count-badge"},[t._v(t._s(t.children.length)+"人")])]),t._l(t.children,(function(s){return i("div",{key:s.id,staticClass:"child-card",on:{click:function(i){return t.viewChildDetails(s)}}},[i("div",{staticClass:"child-header"},[i("div",{staticClass:"child-avatar"},[i("van-icon",{attrs:{name:"manager-o",size:"24"}})],1),i("div",{staticClass:"child-info"},[i("div",{staticClass:"child-name"},[t._v("\n              "+t._s(s.name)+"\n              "),s.level?i("van-tag",{staticClass:"level-tag",attrs:{type:t.getLevelType(s.level),size:"mini"}},[t._v("\n                L"+t._s(s.level)+"\n              ")]):t._e()],1),i("div",{staticClass:"child-details"},[i("span",{staticClass:"detail-item"},[t._v(t._s(s.code))]),i("span",{staticClass:"detail-item"},[t._v(t._s(s.mobile))])])]),i("div",{staticClass:"child-actions"},[i("van-icon",{attrs:{name:"arrow",size:"16"}})],1)]),i("div",{staticClass:"performance-grid"},[i("div",{staticClass:"perf-item"},[i("div",{staticClass:"perf-number"},[t._v(t._s(s.totalOrders||0))]),i("div",{staticClass:"perf-label"},[t._v("订单")])]),i("div",{staticClass:"perf-item"},[i("div",{staticClass:"perf-number"},[t._v("¥"+t._s((s.totalAmount||0).toFixed(0)))]),i("div",{staticClass:"perf-label"},[t._v("销售额")])]),i("div",{staticClass:"perf-item"},[i("div",{staticClass:"perf-number"},[t._v(t._s(s.childrenCount||0))]),i("div",{staticClass:"perf-label"},[t._v("下级")])])]),i("div",{staticClass:"child-footer"},[i("span",{staticClass:"join-time"},[t._v("加入时间："+t._s(t.formatDate(s.createOn)))])])])}))],2):i("div",{staticClass:"empty-state"},[i("van-empty",{attrs:{description:"暂无下级业务员"}},[i("van-button",{attrs:{type:"primary",size:"small",round:""},on:{click:t.goToInvite}},[t._v("\n          邀请业务员\n        ")])],1)],1)]),i("van-popup",{attrs:{position:"bottom",round:""},model:{value:t.showChildDetails,callback:function(i){t.showChildDetails=i},expression:"showChildDetails"}},[t.selectedChild?i("div",{staticClass:"details-popup"},[i("div",{staticClass:"details-header"},[i("div",{staticClass:"header-left"},[i("div",{staticClass:"avatar-container"},[i("div",{staticClass:"avatar"},[i("van-icon",{attrs:{name:"manager-o",size:"24"}})],1),i("div",{staticClass:"level-badge",class:"level-"+(t.selectedChild.level||1)},[t._v("\n              L"+t._s(t.selectedChild.level||1)+"\n            ")])]),i("div",{staticClass:"header-info"},[i("h3",[t._v(t._s(t.selectedChild.name))]),i("p",[t._v(t._s(t.selectedChild.code))])])]),i("div",{staticClass:"header-right"},[i("van-icon",{staticClass:"close-btn",attrs:{name:"cross"},on:{click:function(i){t.showChildDetails=!1}}})],1)]),i("div",{staticClass:"details-content"},[i("div",{staticClass:"info-card"},[i("div",{staticClass:"card-header"},[i("van-icon",{attrs:{name:"contact",size:"18"}}),i("h4",[t._v("基本信息")])],1),i("div",{staticClass:"card-content"},[i("div",{staticClass:"info-row"},[i("div",{staticClass:"info-item"},[i("van-icon",{attrs:{name:"user-o",size:"16"}}),i("span",{staticClass:"info-label"},[t._v("姓名")]),i("span",{staticClass:"info-value"},[t._v(t._s(t.selectedChild.name))])],1)]),i("div",{staticClass:"info-row"},[i("div",{staticClass:"info-item"},[i("van-icon",{attrs:{name:"idcard",size:"16"}}),i("span",{staticClass:"info-label"},[t._v("编号")]),i("span",{staticClass:"info-value"},[t._v(t._s(t.selectedChild.code))])],1)]),i("div",{staticClass:"info-row"},[i("div",{staticClass:"info-item"},[i("van-icon",{attrs:{name:"phone-o",size:"16"}}),i("span",{staticClass:"info-label"},[t._v("手机")]),i("span",{staticClass:"info-value"},[t._v(t._s(t.selectedChild.mobile))])],1)]),t.selectedChild.email?i("div",{staticClass:"info-row"},[i("div",{staticClass:"info-item"},[i("van-icon",{attrs:{name:"envelop-o",size:"16"}}),i("span",{staticClass:"info-label"},[t._v("邮箱")]),i("span",{staticClass:"info-value"},[t._v(t._s(t.selectedChild.email))])],1)]):t._e()])]),t.selectedChildStats?i("div",{staticClass:"info-card"},[i("div",{staticClass:"card-header"},[i("van-icon",{attrs:{name:"chart-trending-o",size:"18"}}),i("h4",[t._v("业绩统计")])],1),i("div",{staticClass:"card-content"},[i("div",{staticClass:"stats-grid"},[i("div",{staticClass:"stat-item"},[i("div",{staticClass:"stat-icon orders"},[i("van-icon",{attrs:{name:"orders-o",size:"20"}})],1),i("div",{staticClass:"stat-info"},[i("span",{staticClass:"stat-value"},[t._v(t._s(t.selectedChildStats.totalOrders||0))]),i("span",{staticClass:"stat-label"},[t._v("总订单")])])]),i("div",{staticClass:"stat-item"},[i("div",{staticClass:"stat-icon amount"},[i("van-icon",{attrs:{name:"gold-coin-o",size:"20"}})],1),i("div",{staticClass:"stat-info"},[i("span",{staticClass:"stat-value"},[t._v("¥"+t._s((t.selectedChildStats.totalAmount||0).toFixed(2)))]),i("span",{staticClass:"stat-label"},[t._v("销售总额")])])]),i("div",{staticClass:"stat-item"},[i("div",{staticClass:"stat-icon commission"},[i("van-icon",{attrs:{name:"cash-back-record",size:"20"}})],1),i("div",{staticClass:"stat-info"},[i("span",{staticClass:"stat-value"},[t._v("¥"+t._s((t.selectedChildStats.totalCommission||0).toFixed(2)))]),i("span",{staticClass:"stat-label"},[t._v("佣金收入")])])]),i("div",{staticClass:"stat-item"},[i("div",{staticClass:"stat-icon team"},[i("van-icon",{attrs:{name:"friends-o",size:"20"}})],1),i("div",{staticClass:"stat-info"},[i("span",{staticClass:"stat-value"},[t._v(t._s(t.selectedChildStats.grandChildrenCount||0))]),i("span",{staticClass:"stat-label"},[t._v("下级业务员")])])])])])]):t._e()]),i("div",{staticClass:"details-footer"},[i("van-button",{attrs:{size:"large",type:"primary",round:"",block:""},on:{click:function(i){t.showChildDetails=!1}}},[i("van-icon",{attrs:{name:"success",size:"16"}}),t._v("\n          确定\n        ")],1)],1)]):t._e()])],1)},a=[],n=(s("96cf"),s("1da1")),c=s("cacf"),o=s("1b69"),l={components:{pcheader:o["default"]},data:function(){return{isMobilePhone:Object(c["c"])(),loading:!1,children:[],totalGrandChildren:0,showChildDetails:!1,selectedChild:null,selectedChildStats:null}},computed:{totalTeamOrders:function(){return this.children.reduce((function(t,i){return t+(i.totalOrders||0)}),0)},totalTeamAmount:function(){return this.children.reduce((function(t,i){return t+(i.totalAmount||0)}),0)}},mounted:function(){document.title="子业务员管理",this.loadChildren()},methods:{loadChildren:function(){var t=Object(n["a"])(regeneratorRuntime.mark((function t(){var i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.loading=!0,t.next=4,this.$fly.get("/pyp/web/salesman/getChildren");case 4:i=t.sent,200===i.code?(this.children=i.children||[],this.totalGrandChildren=this.children.reduce((function(t,i){return t+(i.childrenCount||0)}),0)):vant.Toast(i.msg||"加载失败"),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](0),console.error("加载子业务员列表失败:",t.t0),vant.Toast("加载失败");case 12:return t.prev=12,this.loading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,this,[[0,8,12,15]])})));function i(){return t.apply(this,arguments)}return i}(),viewChildDetails:function(){var t=Object(n["a"])(regeneratorRuntime.mark((function t(i){var s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.selectedChild=i,t.next=4,this.$fly.get("/pyp/web/salesman/getChildStats",{childId:i.id});case 4:s=t.sent,200===s.code?(this.selectedChildStats=s.stats,this.showChildDetails=!0):vant.Toast(s.msg||"获取详情失败"),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](0),console.error("获取子业务员详情失败:",t.t0),vant.Toast("获取详情失败");case 12:case"end":return t.stop()}}),t,this,[[0,8]])})));function i(i){return t.apply(this,arguments)}return i}(),getLevelType:function(t){switch(t){case 1:return"success";case 2:return"primary";case 3:return"warning";default:return"default"}},formatDate:function(t){if(!t)return"";var i=new Date(t);return i.toLocaleDateString("zh-CN")},goToInvite:function(){this.$router.push({name:"salesmanInvite",query:this.$route.query})},goToOrderList:function(){this.$router.push({name:"salesmanOrders"})}}},r=l,d=(s("062e"),s("2877")),v=Object(d["a"])(r,e,a,!1,null,"1b5bb008",null);i["default"]=v.exports},ade3:function(t,i,s){"use strict";s.d(i,"a",(function(){return c}));var e=s("53ca");function a(t,i){if("object"!==Object(e["a"])(t)||null===t)return t;var s=t[Symbol.toPrimitive];if(void 0!==s){var a=s.call(t,i||"default");if("object"!==Object(e["a"])(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===i?String:Number)(t)}function n(t){var i=a(t,"string");return"symbol"===Object(e["a"])(i)?i:String(i)}function c(t,i,s){return i=n(i),i in t?Object.defineProperty(t,i,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[i]=s,t}},c827:function(t,i,s){},cad8:function(t,i,s){},dd7a:function(t,i,s){"use strict";s("cad8")}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a889b03c","chunk-bc6d6208"],{"34ae":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return o})),a.d(t,"d",(function(){return r}));var i=[{key:0,value:"整间"},{key:1,value:"男床位"},{key:2,value:"女床位"}],n=[{key:0,value:"整间"},{key:1,value:"拼住"},{key:2,value:"拼住"}],o=[{key:0,value:"已取消"},{key:1,value:"已入住"}],r=[{key:0,value:"未开启"},{key:1,value:"已开启"}]},"7de9":function(e,t,a){"use strict";a.d(t,"g",(function(){return i})),a.d(t,"f",(function(){return n})),a.d(t,"e",(function(){return o})),a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return l})),a.d(t,"c",(function(){return s})),a.d(t,"d",(function(){return d}));var i=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],n=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],o=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],r=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],l=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],s=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],d=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]},"95d5":function(e,t,a){"use strict";a.r(t);a("b0c0");var i=function(){var e=this,t=e._self._c;return t("div",[t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{closed:e.closeDialog,"update:visible":function(t){e.visible=t}}},[t("el-form",{attrs:{inline:!0,model:e.dataForm}},[t("el-form-item",[t("el-select",{attrs:{size:"mini"},on:{change:e.hotelChange},model:{value:e.dataForm.hotelActivityId,callback:function(t){e.$set(e.dataForm,"hotelActivityId",t)},expression:"dataForm.hotelActivityId"}},[t("el-option",{attrs:{label:"全部(酒店)",value:""}}),e._l(e.hotels,(function(e){return t("el-option",{key:e.id,attrs:{label:e.hotelName,value:e.id}})}))],2)],1),t("el-form-item",[t("el-select",{attrs:{size:"mini"},model:{value:e.dataForm.hotelActivityRoomId,callback:function(t){e.$set(e.dataForm,"hotelActivityRoomId",t)},expression:"dataForm.hotelActivityRoomId"}},[t("el-option",{attrs:{label:"全部(房型)",value:""}}),e._l(e.rooms,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1),t("el-form-item",[t("el-input",{attrs:{size:"mini",placeholder:"订单编号",clearable:""},model:{value:e.dataForm.orderSn,callback:function(t){e.$set(e.dataForm,"orderSn",t)},expression:"dataForm.orderSn"}})],1),t("el-form-item",[t("el-input",{attrs:{size:"mini",placeholder:"联系人",clearable:""},model:{value:e.dataForm.contact,callback:function(t){e.$set(e.dataForm,"contact",t)},expression:"dataForm.contact"}})],1),t("el-form-item",[t("el-input",{attrs:{size:"mini",placeholder:"联系方式",clearable:""},model:{value:e.dataForm.mobile,callback:function(t){e.$set(e.dataForm,"mobile",t)},expression:"dataForm.mobile"}})],1),t("el-form-item",[t("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.onSearch()}}},[e._v("查询")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{height:"400",size:"mini",data:e.dataList,border:""}},[t("el-table-column",{attrs:{"show-overflow-tooltip":"",prop:"orderSn","header-align":"center",align:"center",label:"订单号"}}),t("el-table-column",{attrs:{prop:"orderStatus","header-align":"center",align:"center",width:"75",label:"订单状态"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[t("el-tag",{class:"tag-color-mini tag-color-"+a.row.orderStatus,attrs:{type:"primary"}},[e._v(e._s(e.orderStatus[a.row.orderStatus].value))])],1)}}])}),t("el-table-column",{attrs:{prop:"roomType","header-align":"center",align:"center",width:"75",label:"房间类型"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[t("el-tag",{class:"tag-color-mini tag-color-"+a.row.roomType,attrs:{type:"primary"}},[e._v(e._s(e.roomType[a.row.roomType].value))])],1)}}])}),t("el-table-column",{attrs:{prop:"address","header-align":"center",align:"center",width:"60",label:"已分/总"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[e._v(" "+e._s(a.row.assignNumber)+"/"+e._s(a.row.number)+" ")])}}])}),t("el-table-column",{attrs:{prop:"contact","header-align":"center",align:"center",width:"130",label:"联系人"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[t("el-input",{attrs:{size:"mini",width:"100%",disabled:a.row.assignNumber==a.row.number,placeholder:"输入联系人"},model:{value:a.row.contact,callback:function(t){e.$set(a.row,"contact",t)},expression:"scope.row.contact"}})],1)}}])}),t("el-table-column",{attrs:{prop:"mobile","header-align":"center",align:"center",width:"130",label:"联系方式"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[t("el-input",{attrs:{size:"mini",width:"100%",disabled:a.row.assignNumber==a.row.number,placeholder:"输入联系方式"},model:{value:a.row.mobile,callback:function(t){e.$set(a.row,"mobile",t)},expression:"scope.row.mobile"}})],1)}}])}),t("el-table-column",{attrs:{prop:"checkSex","header-align":"center",align:"center",width:"70",label:"校验男女"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[0!=a.row.roomType?t("el-switch",{attrs:{disabled:a.row.assignNumber==a.row.number},model:{value:a.row.checkSex,callback:function(t){e.$set(a.row,"checkSex",t)},expression:"scope.row.checkSex"}}):e._e()],1)}}])}),t("el-table-column",{attrs:{prop:"tag","header-align":"center",align:"center",width:"130",label:"备注(选填)"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[t("el-select",{attrs:{filterable:"",size:"mini"},model:{value:a.row.tag,callback:function(t){e.$set(a.row,"tag",t)},expression:"scope.row.tag"}},e._l(e.assignTag,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.name}})})),1)],1)}}])}),t("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"80",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.assignNumber!=a.row.number?t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.selectNumber(a.row)}}},[e._v("选择房号")]):e._e()]}}])})],1),t("el-pagination",{attrs:{"current-page":e.pageIndex,"page-sizes":[10,20,50,100],"page-size":e.pageSize,total:e.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle}}),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("关闭页面")])],1)],1),t("el-dialog",{attrs:{title:"选择房间号","close-on-click-modal":!1,visible:e.numberVisible},on:{"update:visible":function(t){e.numberVisible=t}}},[t("el-form",{attrs:{inline:!0,model:e.indexOrder,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"房间号",prop:"number"}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("tags-editor",{attrs:{limit:e.indexOrder.number-e.indexOrder.assignNumber,name:"直接新增房间号"},model:{value:e.indexOrder.roomNumber,callback:function(t){e.$set(e.indexOrder,"roomNumber",t)},expression:"indexOrder.roomNumber"}}),t("el-button",{directives:[{name:"show",rawName:"v-show",value:e.indexOrder.number-e.indexOrder.assignNumber>e.indexOrder.roomNumber.length,expression:"(indexOrder.number - indexOrder.assignNumber) > indexOrder.roomNumber.length"}],staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"small"},on:{click:e.addRoomNumber}},[e._v("选择已有房间号")])],1)])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.numberVisible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.select}},[e._v("确认")]),t("el-button",{attrs:{type:"success"},on:{click:e.selectAndPrint}},[e._v("确认并打印")])],1)],1),e.hotelactivityroomassignselectrommnumberVisible?t("hotelactivityroomassignselectrommnumber",{ref:"hotelactivityroomassignselectrommnumber",on:{select:e.selectRoomNumber}}):e._e()],1)},n=[],o=(a("14d9"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("00b4"),a("3ca3"),a("ddb0"),a("b42b")),r=a("7de9"),l=a("34ae"),s={data:function(){return{hotelactivityroomassignselectrommnumberVisible:!1,appid:"",hotels:[],rooms:[],roomType:l["b"],roomTypeFjsd:l["c"],roomAssignStatus:l["a"],orderStatus:r["e"],visible:!1,numberVisible:!1,dataForm:{contact:"",mobile:"",hotelActivityId:"",hotelActivityRoomId:"",numberId:"",activityId:""},assignTag:[],dataList:[],dataListLoading:!1,indexOrder:{orderDetailId:"",contact:"",mobile:"",roomNumber:[]},pageIndex:1,pageSize:10,totalPage:0}},components:{tagsEditor:function(){return a.e("chunk-12954b2e").then(a.bind(null,"9bdf"))},hotelactivityroomassignselectrommnumber:o["default"]},methods:{init:function(e,t,a){this.dataForm.activityId=e,this.dataForm.hotelActivityId=t,this.dataForm.hotelActivityRoomId=a,this.dataForm.hotelActivityId&&this.findRoom(this.dataForm.hotelActivityId),this.visible=!0,this.getDataList(),this.findHotel(),this.getTag(),this.appid=this.$cookie.get("appid")},getTag:function(){var e=this;this.$http({url:this.$http.adornUrl("/hotel/hotelassigntag/findByActivityId/".concat(this.dataForm.activityId)),method:"get"}).then((function(t){var a=t.data;a&&200===a.code&&(e.assignTag=a.result)}))},findHotel:function(){var e=this;this.$http({url:this.$http.adornUrl("/hotel/hotelactivity/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.hotels=a.result)}))},hotelChange:function(e){this.dataForm.hotelActivityRoomId="",this.findRoom(e)},findRoom:function(e){var t=this;this.$http({url:this.$http.adornUrl("/hotel/hotelactivityroom/findByHotelActivityId/".concat(e)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.rooms=a.result)}))},onSearch:function(){this.pageIndex=1,this.getDataList()},getDataList:function(){var e=this;this.$http({url:this.$http.adornUrl("/hotel/hotelorderdetail/findByParams"),method:"get",params:this.$http.adornParams({activityId:this.dataForm.activityId,roomId:this.dataForm.hotelActivityRoomId,hotelActivityId:this.dataForm.hotelActivityId,orderSn:this.dataForm.orderSn,contact:this.dataForm.contact,mobile:this.dataForm.mobile,page:this.pageIndex,limit:this.pageSize})}).then((function(t){var a=t.data;a&&200===a.code?(e.dataList=a.page.list,e.totalPage=a.page.totalCount):(e.dataList=[],e.totalPage=0)}))},addRoomNumber:function(){var e=this;this.hotelactivityroomassignselectrommnumberVisible=!0,this.$nextTick((function(){e.$refs.hotelactivityroomassignselectrommnumber.init(e.dataForm.activityId,e.indexOrder.hotelActivityId,e.indexOrder.hotelActivityRoomId)}))},selectRoomNumber:function(e){this.indexOrder.roomNumber.push(e)},selectNumber:function(e){this.indexOrder=JSON.parse(JSON.stringify(e)),this.$set(this.indexOrder,"roomNumber",[]),this.numberVisible=!0},select:function(){var e=this;if(!this.indexOrder.roomNumber)return this.$message.error("请选择房间号"),!1;this.$confirm("确认分房?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/hotel/hotelactivityroomnumber/assignBatch"),method:"post",data:e.$http.adornData({roomNumber:e.indexOrder.roomNumber,orderDetailId:e.indexOrder.id,mobile:e.indexOrder.mobile,contact:e.indexOrder.contact,hotelActivityRoomId:e.indexOrder.hotelActivityRoomId,checkSex:e.indexOrder.checkSex,tag:e.indexOrder.tag})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.numberVisible=!1,e.$emit("refreshDataList")}}):e.$message.error(a.msg)}))}))},selectAndPrint:function(){var e=this;if(!this.indexOrder.roomNumber)return this.$message.error("请选择房间号"),!1;this.$confirm("确认分房?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/hotel/hotelactivityroomnumber/assignBatch"),method:"post",data:e.$http.adornData({roomNumber:e.indexOrder.roomNumber,orderDetailId:e.indexOrder.id,mobile:e.indexOrder.mobile,contact:e.indexOrder.contact,hotelActivityRoomId:e.indexOrder.hotelActivityRoomId,checkSex:e.indexOrder.checkSex,tag:e.indexOrder.tag})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.numberVisible=!1,e.$emit("print",a.result)}}):e.$message.error(a.msg)}))}))},sizeChangeHandle:function(e){this.pageSize=e,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(e){this.pageIndex=e,this.getDataList()},closeDialog:function(){this.$emit("refreshDataList")},isImageUrl:function(e){return e&&/.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(e)}}},d=s,c=a("2877"),u=Object(c["a"])(d,i,n,!1,null,null,null);t["default"]=u.exports},b42b:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e._self._c;return t("div",[t("el-dialog",{attrs:{title:"选择房间号","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{attrs:{inline:!0,model:e.dataForm}},[t("el-form-item",[t("el-input",{attrs:{placeholder:"房间号",clearable:""},model:{value:e.dataForm.number,callback:function(t){e.$set(e.dataForm,"number",t)},expression:"dataForm.number"}})],1),t("el-form-item",[t("el-button",{on:{click:function(t){return e.getDataList()}}},[e._v("查询")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{size:"mini",data:e.dataList,border:""}},[t("el-table-column",{attrs:{prop:"roomName","header-align":"center",align:"center",label:"房型名称"}}),t("el-table-column",{attrs:{prop:"number","header-align":"center",align:"center",label:"房号"}}),t("el-table-column",{attrs:{prop:"isAssign","header-align":"center",align:"center",label:"是否分配"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[t("el-tag",{class:"tag-color tag-color-"+a.row.isAssign,attrs:{type:"primary"}},[e._v(e._s(e.yesOrNo[a.row.isAssign].value))])],1)}}])}),e.dataList.length>0?t("div",e._l(e.dataList[0].assignVos,(function(a,i){return t("el-table-column",{key:i,attrs:{"header-align":"center",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v(e._s(a.row.assignVos[i].number)+" "),a.row.assignVos[i].number>0?t("span",[e._v("("+e._s(a.row.assignVos[i].number<1?e.roomType[a.row.assignVos[i].roomType].value:"满")+")")]):e._e()]}}],null,!0)},[t("template",{slot:"header"},[e._v(" "+e._s(a.date)+" ")])],2)})),1):e._e(),t("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"180",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{staticStyle:{color:"red"},attrs:{type:"text",size:"small"},on:{click:function(t){return e.assignHandle(a.row)}}},[e._v("选择")])]}}])})],1),t("el-pagination",{attrs:{"current-page":e.pageIndex,"page-sizes":[10,20,50,100],"page-size":e.pageSize,total:e.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle}})],1)],1)},n=[],o=(a("d3b7"),a("3ca3"),a("ddb0"),a("34ae")),r=a("7de9"),l={data:function(){return{visible:!1,appid:"",roomType:o["b"],hotels:[],rooms:[],yesOrNo:r["g"],dataForm:{activityId:"",hotelActivityId:"",hotelActivityRoomId:"",number:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,addnumberVisible:!1,assignVisible:!1,assignpeopleVisible:!1}},components:{tagsEditor:function(){return a.e("chunk-4dba3ada").then(a.bind(null,"a55c"))}},methods:{init:function(e,t,a){this.dataForm.activityId=e,this.dataForm.hotelActivityId=t,this.dataForm.hotelActivityRoomId=a,this.visible=!0,this.getDataList()},sizeChangeHandle:function(e){this.pageSize=e,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(e){this.pageIndex=e,this.getDataList()},getDataList:function(){var e=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/hotel/hotelactivityroomnumber/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,number:this.dataForm.number,activityId:this.dataForm.activityId,hotelActivityId:this.dataForm.hotelActivityId,hotelActivityRoomId:this.dataForm.hotelActivityRoomId})}).then((function(t){var a=t.data;a&&200===a.code?(e.dataList=a.page.list,e.totalPage=a.page.totalCount):(e.dataList=[],e.totalPage=0),e.dataListLoading=!1}))},assignHandle:function(e){this.$emit("select",e.number),this.visible=!1}}},s=l,d=a("2877"),c=Object(d["a"])(s,i,n,!1,null,null,null);t["default"]=c.exports}}]);
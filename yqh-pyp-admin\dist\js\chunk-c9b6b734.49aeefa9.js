(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c9b6b734","chunk-a8c83fd8","chunk-eb19e230"],{"55e3":function(t,e,a){},"702d":function(t,e,a){"use strict";a.r(e);a("b0c0");var n=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"开发接入信息","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("div",[e("div",{staticClass:"list-item"},[e("span",{staticClass:"label"},[t._v("公众号:")]),t._v(t._s(t.account.name))]),e("div",{staticClass:"list-item"},[e("span",{staticClass:"label"},[t._v("token:")]),t._v(t._s(t.account.token))]),e("div",{staticClass:"list-item"},[e("span",{staticClass:"label"},[t._v("aesKey:")]),t._v(t._s(t.account.aesKey))]),e("div",{staticClass:"list-item"},[e("span",{staticClass:"label"},[t._v("接入链接:")]),e("span",{domProps:{innerHTML:t._s(t.accessUrl)}})])])])},i=[],r=(a("ac1f"),a("00b4"),{data:function(){return{visible:!1,account:{}}},computed:{accessUrl:function(){var t=location.host;return/^(\d(.\d){3})|localhost/.test(t)&&(t='<span class="text-red">正式域名</span>'),location.protocol+"//"+t+"/wx/wx/msg/"+this.account.appid}},methods:{init:function(t){this.visible=!0,t&&t.appid&&(this.account=t)}}}),o=r,s=(a("d606"),a("2877")),l=Object(s["a"])(o,n,i,!1,null,"da2595c2",null);e["default"]=l.exports},"72a7":function(t,e,a){"use strict";a.d(e,"a",(function(){return n}));var n=[{key:0,value:"公众号",disabled:!1},{key:1,value:"小程序",disabled:!1},{key:2,value:"通用",disabled:!0}]},"8e16":function(t,e,a){"use strict";a.r(e);a("b0c0");var n=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"新增/修改","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"公众号名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"公众号名称"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("div",{staticClass:"padding text-gray"},[t._v("测试号可选择服务号，不同类型账号、是否认证可使用功能权限不同，"),e("a",{attrs:{href:"https://developers.weixin.qq.com/doc/offiaccount/Getting_Started/Explanation_of_interface_privileges.html"}},[t._v("参考文档")])]),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"公众号类型",prop:"type"}},[e("el-select",{attrs:{placeholder:"公众号类型"},model:{value:t.dataForm.type,callback:function(e){t.$set(t.dataForm,"type",e)},expression:"dataForm.type"}},t._l(t.ACCOUNT_TYPES,(function(t,a){return e("el-option",{key:t,attrs:{label:t,value:a}})})),1)],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"是否认证",prop:"verified"}},[e("el-switch",{attrs:{placeholder:"是否认证"},model:{value:t.dataForm.verified,callback:function(e){t.$set(t.dataForm,"verified",e)},expression:"dataForm.verified"}})],1)],1)],1),e("el-form-item",{attrs:{label:"appid",prop:"appid"}},[e("el-input",{attrs:{placeholder:"appid"},model:{value:t.dataForm.appid,callback:function(e){t.$set(t.dataForm,"appid",e)},expression:"dataForm.appid"}})],1),e("el-form-item",{attrs:{label:"appsecret",prop:"secret"}},[e("el-input",{attrs:{placeholder:"appsecret"},model:{value:t.dataForm.secret,callback:function(e){t.$set(t.dataForm,"secret",e)},expression:"dataForm.secret"}})],1),e("el-form-item",{attrs:{label:"token",prop:"token"}},[e("el-input",{attrs:{placeholder:"token"},model:{value:t.dataForm.token,callback:function(e){t.$set(t.dataForm,"token",e)},expression:"dataForm.token"}})],1),e("el-form-item",{attrs:{label:"aesKey",prop:"aesKey"}},[e("el-input",{attrs:{placeholder:"aesKey，可为空"},model:{value:t.dataForm.aesKey,callback:function(e){t.$set(t.dataForm,"aesKey",e)},expression:"dataForm.aesKey"}})],1),e("el-form-item",{attrs:{label:"小程序appid",prop:"miniAppid"}},[e("el-input",{attrs:{placeholder:"miniAppid"},model:{value:t.dataForm.miniAppid,callback:function(e){t.$set(t.dataForm,"miniAppid",e)},expression:"dataForm.miniAppid"}})],1),e("el-form-item",{attrs:{label:"小程序appsecret",prop:"miniSecret"}},[e("el-input",{attrs:{placeholder:"miniSecret"},model:{value:t.dataForm.miniSecret,callback:function(e){t.$set(t.dataForm,"miniSecret",e)},expression:"dataForm.miniSecret"}})],1),e("el-form-item",{attrs:{label:"账号类型",prop:"accountType"}},[e("el-select",{attrs:{placeholder:"账号类型",filterable:""},model:{value:t.dataForm.accountType,callback:function(e){t.$set(t.dataForm,"accountType",e)},expression:"dataForm.accountType"}},t._l(t.accountType,(function(t){return e("el-option",{key:t.key,attrs:{disabled:t.disabled,label:t.value,value:t.key}})})),1)],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},i=[],r=a("2f62"),o=a("72a7"),s={data:function(){return{accountType:o["a"],visible:!1,dataForm:{appid:"",name:"",type:"2",verified:!0,secret:"",token:"my_weixin_token_",aesKey:"",miniAppid:"",miniSecret:"",accountType:0},dataRule:{name:[{required:!0,message:"公众号名称不能为空",trigger:"blur"}],appid:[{required:!0,message:"appid不能为空",trigger:"blur"}],secret:[{required:!0,message:"appsecret不能为空",trigger:"blur"}]}}},computed:Object(r["b"])({ACCOUNT_TYPES:function(t){return t.wxAccount.ACCOUNT_TYPES}}),methods:{init:function(t){var e=this;this.visible=!0,t&&t.appid?(this.dataForm=t,this.dataForm.type=t.type+""):this.$nextTick((function(){e.$refs["dataForm"].resetFields()}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/manage/wxAccount/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData(t.dataForm)}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))}}},l=s,c=a("2877"),d=Object(c["a"])(l,n,i,!1,null,null,null);e["default"]=d.exports},a15b:function(t,e,a){"use strict";var n=a("23e7"),i=a("e330"),r=a("44ad"),o=a("fc6a"),s=a("a640"),l=i([].join),c=r!==Object,d=c||!s("join",",");n({target:"Array",proto:!0,forced:d},{join:function(t){return l(o(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var n=a("23e7"),i=a("d024"),r=a("c430");n({target:"Iterator",proto:!0,real:!0,forced:r},{map:i})},ce07:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"参数名",clearable:""},model:{value:t.dataForm.key,callback:function(e){t.$set(t.dataForm,"key",e)},expression:"dataForm.key"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),t.isAuth("wx:wxaccount:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("wx:wxaccount:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"appid","header-align":"center",align:"center",label:"appid"}}),e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"公众号名称"}}),e("el-table-column",{attrs:{prop:"type","header-align":"center",align:"center",label:"类型",formatter:t.accountTypeFormat}}),e("el-table-column",{attrs:{prop:"verified","header-align":"center",align:"center",label:"是否认证"},scopedSlots:t._u([{key:"default",fn:function(a){return e("span",{},[t._v(t._s(a.row.verified?"是":"否"))])}}])}),e("el-table-column",{attrs:{prop:"accountType","header-align":"center",align:"center",label:"账号类型"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-tag",{class:"tag-color tag-color-"+a.row.accountType,attrs:{type:"primary"}},[t._v(t._s(t.accountType[a.row.accountType].value))])],1)}}])}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.accessInfo(a.row)}}},[t._v("接入")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.appid)}}},[t._v("删除")])]}}])})],1),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e(),t.accountAccessVisible?e("account-access",{ref:"accountAccessDialog"}):t._e()],1)},i=[],r=(a("99af"),a("a15b"),a("d81d"),a("a573"),a("8e16")),o=a("72a7"),s=a("702d"),l=a("2f62"),c={data:function(){return{accountType:o["a"],dataForm:{key:""},dataList:[],dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,accountAccessVisible:!1}},components:{AddOrUpdate:r["default"],AccountAccess:s["default"]},computed:Object(l["b"])({ACCOUNT_TYPES:function(t){return t.wxAccount.ACCOUNT_TYPES}}),activated:function(){this.getDataList()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/manage/wxAccount/list"),method:"get",params:this.$http.adornParams({key:this.dataForm.key})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.list,t.$store.commit("wxAccount/updateAccountList",a.list)):t.dataList=[],t.dataListLoading=!1}))},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(t)}))},accessInfo:function(t){var e=this;this.accountAccessVisible=!0,this.$nextTick((function(){e.$refs.accountAccessDialog.init(t)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.appid}));this.$confirm("确定对[appid=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/manage/wxAccount/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},accountTypeFormat:function(t,e,a){return this.ACCOUNT_TYPES[a]}}},d=c,u=a("2877"),p=Object(u["a"])(d,n,i,!1,null,null,null);e["default"]=p.exports},d024:function(t,e,a){"use strict";var n=a("c65b"),i=a("59ed"),r=a("825a"),o=a("46c4"),s=a("c5cc"),l=a("9bdd"),c=s((function(){var t=this.iterator,e=r(n(this.next,t)),a=this.done=!!e.done;if(!a)return l(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return r(this),i(t),new c(o(this),{mapper:t})}},d606:function(t,e,a){"use strict";a("55e3")},d81d:function(t,e,a){"use strict";var n=a("23e7"),i=a("b727").map,r=a("1dde"),o=r("map");n({target:"Array",proto:!0,forced:!o},{map:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})}}]);
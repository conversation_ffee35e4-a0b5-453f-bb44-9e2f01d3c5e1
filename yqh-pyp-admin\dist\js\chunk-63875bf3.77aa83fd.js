(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-63875bf3","chunk-e8fc7c92"],{"129f":function(a,t,e){"use strict";a.exports=Object.is||function(a,t){return a===t?0!==a||1/a===1/t:a!==a&&t!==t}},"1c99":function(a,t,e){"use strict";e.r(t);e("b0c0"),e("ac1f"),e("841c");var i=function(){var a=this,t=a._self._c;return t("el-dialog",{attrs:{title:a.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:a.visible},on:{"update:visible":function(t){a.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:a.dataForm,rules:a.dataRule,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"联系人姓名",prop:"name"}},[t("el-autocomplete",{staticStyle:{width:"100%"},attrs:{"fetch-suggestions":a.search,label:"name",placeholder:"请输入内容"},on:{select:a.selectRsult},scopedSlots:a._u([{key:"default",fn:function(e){return t("div",{},[t("span",{staticStyle:{float:"left"}},[a._v(a._s(e.item.name))]),t("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[a._v(a._s(e.item.unit))])])}}]),model:{value:a.dataForm.name,callback:function(t){a.$set(a.dataForm,"name",t)},expression:"dataForm.name"}})],1),t("el-form-item",{attrs:{label:"联系人电话",prop:"mobile"}},[t("el-input",{attrs:{placeholder:"联系人电话"},model:{value:a.dataForm.mobile,callback:function(t){a.$set(a.dataForm,"mobile",t)},expression:"dataForm.mobile"}})],1),t("el-form-item",{attrs:{label:"劳务费",prop:"serviceFee"}},[t("el-input",{attrs:{placeholder:"劳务费"},model:{value:a.dataForm.serviceFee,callback:function(t){a.$set(a.dataForm,"serviceFee",t)},expression:"dataForm.serviceFee"}})],1),t("el-form-item",{attrs:{label:"头像",prop:"avatar"}},[t("el-upload",{staticClass:"avatar-uploader",attrs:{"before-upload":a.checkFileSize,"list-type":"picture-card","show-file-list":!1,accept:".jpg, .jpeg, .png, .gif","on-success":a.appSuccessHandle,action:a.url}},[a.dataForm.avatar?t("img",{staticClass:"avatar",attrs:{width:"100px",src:a.dataForm.avatar}}):t("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),t("el-form-item",{attrs:{label:"工作单位",prop:"unit"}},[t("el-input",{attrs:{placeholder:"工作单位"},model:{value:a.dataForm.unit,callback:function(t){a.$set(a.dataForm,"unit",t)},expression:"dataForm.unit"}})],1),t("el-form-item",{attrs:{label:"职称",prop:"duties"}},[t("el-input",{attrs:{placeholder:"职称"},model:{value:a.dataForm.duties,callback:function(t){a.$set(a.dataForm,"duties",t)},expression:"dataForm.duties"}})],1),t("el-form-item",{attrs:{label:"排序，数值越小越靠前",prop:"orderBy"}},[t("el-input",{attrs:{placeholder:"排序，数值越小越靠前"},model:{value:a.dataForm.orderBy,callback:function(t){a.$set(a.dataForm,"orderBy",t)},expression:"dataForm.orderBy"}})],1),t("el-form-item",{attrs:{label:"身份证正面",prop:"idCardZheng"}},[t("el-upload",{staticClass:"avatar-uploader",attrs:{"before-upload":a.checkFileSize,"list-type":"picture-card","show-file-list":!1,accept:".jpg, .jpeg, .png, .gif","on-success":a.idCardZhengSuccessHandle,action:a.url}},[a.dataForm.idCardZheng?t("img",{staticClass:"avatar",attrs:{width:"100px",src:a.dataForm.idCardZheng}}):t("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),t("el-form-item",{attrs:{label:"身份证反面",prop:"idCardFan"}},[t("el-upload",{staticClass:"avatar-uploader",attrs:{"before-upload":a.checkFileSize,"list-type":"picture-card","show-file-list":!1,accept:".jpg, .jpeg, .png, .gif","on-success":a.idCardFanSuccessHandle,action:a.url}},[a.dataForm.idCardFan?t("img",{staticClass:"avatar",attrs:{width:"100px",src:a.dataForm.idCardFan}}):t("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),t("el-form-item",{attrs:{label:"是否参会",prop:"isAttend"}},[t("el-select",{attrs:{placeholder:"是否参会",filterable:""},model:{value:a.dataForm.isAttend,callback:function(t){a.$set(a.dataForm,"isAttend",t)},expression:"dataForm.isAttend"}},[t("el-option",{attrs:{label:"参会",value:1}}),t("el-option",{attrs:{label:"不参会",value:0}})],1)],1),t("el-form-item",{attrs:{label:"身份证类型",prop:"idCardType"}},[t("el-select",{attrs:{placeholder:"身份证类型",filterable:""},model:{value:a.dataForm.idCardType,callback:function(t){a.$set(a.dataForm,"idCardType",t)},expression:"dataForm.idCardType"}},a._l(a.idCardType,(function(a){return t("el-option",{key:a.name,attrs:{label:a.name,value:a.name}})})),1)],1),t("el-form-item",{attrs:{label:"身份证",prop:"idCard"}},[t("el-input",{attrs:{placeholder:"身份证"},model:{value:a.dataForm.idCard,callback:function(t){a.$set(a.dataForm,"idCard",t)},expression:"dataForm.idCard"}})],1),t("el-form-item",{attrs:{label:"银行卡号",prop:"bank"}},[t("el-input",{attrs:{placeholder:"银行卡号"},model:{value:a.dataForm.bank,callback:function(t){a.$set(a.dataForm,"bank",t)},expression:"dataForm.bank"}})],1),t("el-form-item",{attrs:{label:"开户行",prop:"kaihuhang"}},[t("el-input",{attrs:{placeholder:"开户行"},model:{value:a.dataForm.kaihuhang,callback:function(t){a.$set(a.dataForm,"kaihuhang",t)},expression:"dataForm.kaihuhang"}})],1),t("el-form-item",{attrs:{label:"省市区",prop:"area"}},[t("el-cascader",{staticStyle:{width:"100%"},attrs:{size:"large",options:a.options},on:{change:a.handleChange},model:{value:a.dataForm.area,callback:function(t){a.$set(a.dataForm,"area",t)},expression:"dataForm.area"}})],1),t("el-form-item",{attrs:{label:"详细介绍",prop:"content"}},[t("tinymce-editor",{ref:"editor",model:{value:a.dataForm.content,callback:function(t){a.$set(a.dataForm,"content",t)},expression:"dataForm.content"}})],1),t("el-form-item",{attrs:{label:"详细介绍(文件)",prop:"contentFile"}},[t("el-upload",{staticClass:"avatar-uploader",attrs:{"list-type":"picture-card","show-file-list":!1,"before-upload":a.checkFileSize,"on-success":a.subscribeImgSuccessHandle,action:a.url}},[a.dataForm.contentFile?t("div",{staticClass:"file-display"},[t("i",{staticClass:"el-icon-document"}),t("span",{staticClass:"file-name"},[a._v(a._s(a.dataForm.contentFile))]),t("el-button",{attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return t.stopPropagation(),a.copyToClipboard(a.dataForm.contentFile)}}},[a._v("复制")])],1):t("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){a.visible=!1}}},[a._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return a.dataFormSubmit()}}},[a._v("确定")])],1)],1)},r=[],n=(e("a15b"),e("d3b7"),e("3ca3"),e("ddb0"),e("7de9")),o=(e("7c8d"),e("ef6c")),d={components:{TinymceEditor:function(){return Promise.all([e.e("chunk-03be236c"),e.e("chunk-2d0a4b8c")]).then(e.bind(null,"26dc"))}},data:function(){return{options:o["regionData"],idCardType:n["c"],visible:!1,url:"",loading:!1,searchResult:[],dataForm:{id:0,activityId:"",name:"",mobile:"",unit:"",duties:"",wxUserId:"",avatar:"",content:"",isSave:!1,orderBy:0,serviceFee:0,isAttend:1,idCardZheng:"",idCardFan:"",bank:"",kaihuhang:"",idCardType:"",idCard:"",contentFile:""},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],name:[{required:!0,message:"联系人姓名不能为空",trigger:"blur"}]},timeout:null}},methods:{init:function(a,t){var e=this;this.dataForm.activityId=a,this.dataForm.content="",this.dataForm.id=t||0,this.visible=!0,this.url=this.$http.adornUrl("/sys/oss/upload?token=".concat(this.$cookie.get("token"))),this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.dataForm.id&&e.$http({url:e.$http.adornUrl("/activity/activityguest/info/".concat(e.dataForm.id)),method:"get",params:e.$http.adornParams()}).then((function(a){var t=a.data;t&&200===t.code&&(e.dataForm.activityId=t.activityGuest.activityId,e.dataForm.name=t.activityGuest.name,e.dataForm.mobile=t.activityGuest.mobile,e.dataForm.unit=t.activityGuest.unit,e.dataForm.duties=t.activityGuest.duties,e.dataForm.wxUserId=t.activityGuest.wxUserId,e.dataForm.avatar=t.activityGuest.avatar,e.dataForm.content=t.activityGuest.content,e.dataForm.orderBy=t.activityGuest.orderBy,e.dataForm.idCardZheng=t.activityGuest.idCardZheng,e.dataForm.idCardFan=t.activityGuest.idCardFan,e.dataForm.bank=t.activityGuest.bank,e.dataForm.kaihuhang=t.activityGuest.kaihuhang,e.dataForm.serviceFee=t.activityGuest.serviceFee,e.dataForm.idCardType=t.activityGuest.idCardType,e.dataForm.contentFile=t.activityGuest.contentFile,e.dataForm.idCard=t.activityGuest.idCard,e.dataForm.area=t.activityGuest.area?t.activityGuest.area.split(","):[],e.dataForm.isSave=!1,e.dataForm.isAttend=t.activityGuest.isAttend)}))}))},handleChange:function(a){console.log(a)},search:function(a,t){var e=this;""!==a&&(this.loading=!0,this.$http({url:this.$http.adornUrl("/activity/guest/findByName"),method:"get",params:this.$http.adornParams({name:a})}).then((function(a){var i=a.data;e.loading=!1,i&&200===i.code?(e.searchResult=i.result,clearTimeout(e.timeout),e.timeout=setTimeout((function(){t(e.searchResult)}),100*Math.random())):e.$message.error(i.msg)})))},createStateFilter:function(a){return function(t){return 0===t.value.toLowerCase().indexOf(a.toLowerCase())}},selectRsult:function(a){this.dataForm.name=a.name,this.dataForm.mobile=a.mobile,this.dataForm.unit=a.unit,this.dataForm.duties=a.duties,this.dataForm.wxUserId=a.wxUserId,this.dataForm.avatar=a.avatar,this.dataForm.content=a.content,this.dataForm.orderBy=a.orderBy,this.dataForm.idCardZheng=a.idCardZheng,this.dataForm.idCardFan=a.idCardFan,this.dataForm.bank=a.bank,this.dataForm.kaihuhang=a.kaihuhang,this.dataForm.idCardType=a.idCardType,this.dataForm.contentFile=a.contentFile,this.dataForm.idCard=a.idCard,this.dataForm.area=a.area?a.area.split(","):[]},dataFormSubmit:function(){var a=this;this.$refs["dataForm"].validate((function(t){t&&a.$http({url:a.$http.adornUrl("/activity/activityguest/".concat(a.dataForm.id?"update":"save")),method:"post",data:a.$http.adornData({id:a.dataForm.id||void 0,activityId:a.dataForm.activityId,name:a.dataForm.name,mobile:a.dataForm.mobile,unit:a.dataForm.unit,duties:a.dataForm.duties,wxUserId:a.dataForm.wxUserId,avatar:a.dataForm.avatar,isSave:a.dataForm.isSave,content:a.dataForm.content,orderBy:a.dataForm.orderBy,idCardZheng:a.dataForm.idCardZheng,idCardFan:a.dataForm.idCardFan,bank:a.dataForm.bank,kaihuhang:a.dataForm.kaihuhang,serviceFee:a.dataForm.serviceFee,idCard:a.dataForm.idCard,idCardType:a.dataForm.idCardType,contentFile:a.dataForm.contentFile,isAttend:a.dataForm.isAttend,area:a.dataForm.area?a.dataForm.area.join(","):""})}).then((function(t){var e=t.data;e&&200===e.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.visible=!1,a.$emit("refreshDataList")}}):405==e.code?a.$confirm(e.msg,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.dataForm.isSave=!0,a.dataFormSubmit()})):a.$message.error(e.msg)}))}))},checkFileSize:function(a){return!(a.size/1024/1024>6)||(this.$message.error("".concat(a.name,"文件大于6MB,请压缩后上传")),!1)},subscribeImgSuccessHandle:function(a,t,e){a&&200===a.code?(this.dataForm.contentFile=a.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(a.msg)},appSuccessHandle:function(a,t,e){a&&200===a.code?this.dataForm.avatar=a.url:this.$message.error(a.msg)},idCardZhengSuccessHandle:function(a,t,e){a&&200===a.code?this.dataForm.idCardZheng=a.url:this.$message.error(a.msg)},idCardFanSuccessHandle:function(a,t,e){a&&200===a.code?this.dataForm.idCardFan=a.url:this.$message.error(a.msg)},copyToClipboard:function(a){var t=document.createElement("input");t.setAttribute("value",a),document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),this.$message({message:"复制成功",type:"success"})}}},s=d,l=(e("45ab"),e("2877")),c=Object(l["a"])(s,i,r,!1,null,"b82c65de",null);t["default"]=c.exports},"45ab":function(a,t,e){"use strict";e("6541")},6541:function(a,t,e){},"7de9":function(a,t,e){"use strict";e.d(t,"g",(function(){return i})),e.d(t,"f",(function(){return r})),e.d(t,"e",(function(){return n})),e.d(t,"a",(function(){return o})),e.d(t,"b",(function(){return d})),e.d(t,"c",(function(){return s})),e.d(t,"d",(function(){return l}));var i=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],r=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],n=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],o=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],d=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],s=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],l=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]},"841c":function(a,t,e){"use strict";var i=e("c65b"),r=e("d784"),n=e("825a"),o=e("7234"),d=e("1d80"),s=e("129f"),l=e("577e"),c=e("dc4a"),u=e("14c3");r("search",(function(a,t,e){return[function(t){var e=d(this),r=o(t)?void 0:c(t,a);return r?i(r,t,e):new RegExp(t)[a](l(e))},function(a){var i=n(this),r=l(a),o=e(t,i,r);if(o.done)return o.value;var d=i.lastIndex;s(d,0)||(i.lastIndex=0);var c=u(i,r);return s(i.lastIndex,d)||(i.lastIndex=d),null===c?-1:c.index}]}))},a15b:function(a,t,e){"use strict";var i=e("23e7"),r=e("e330"),n=e("44ad"),o=e("fc6a"),d=e("a640"),s=r([].join),l=n!==Object,c=l||!d("join",",");i({target:"Array",proto:!0,forced:c},{join:function(a){return s(o(this),void 0===a?",":a)}})}}]);
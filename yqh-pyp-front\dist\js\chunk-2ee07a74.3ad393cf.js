(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2ee07a74","chunk-37a545c8"],{"1b69":function(t,e,i){"use strict";i.r(e);i("7f7f");var n,s=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{background:"white","margin-bottom":"20px"}},[e("van-row",{attrs:{gutter:"20"}},[e("van-col",{attrs:{span:"16"}},[e("van-swipe",{staticStyle:{width:"100%"},attrs:{autoplay:3e3}},t._l(t.activityInfo.appFileList,(function(t,i){return e("van-swipe-item",{key:i},[e("van-image",{attrs:{width:"100%",src:t.url}})],1)})),1)],1),e("van-col",{attrs:{span:"8"}},[e("div",{staticStyle:{"margin-top":"20px"}},[e("van-cell",{attrs:{title:"会议名称",value:t.activityInfo.name}}),t.activityInfo.address?e("van-cell",{attrs:{title:"会议地点",value:t.activityInfo.address}}):t._e(),e("van-cell",{attrs:{title:"会议开始时间",value:t.activityInfo.startTime}}),e("van-cell",{attrs:{title:"会议结束时间",value:t.activityInfo.endTime}}),e("van-cell",{attrs:{title:"登录信息"},scopedSlots:t._u([{key:"default",fn:function(){return[t.userInfo?e("van-button",{attrs:{type:"primary",size:"small",round:""}},[t._v(t._s(t.userInfo.username)+"("+t._s(t.userInfo.mobile)+")")]):e("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.showLogin}},[t._v("未登录(点击登录)")])]},proxy:!0}])}),"1736999159118508033"!=t.activityId?e("van-cell",{attrs:{title:"报名状态"},scopedSlots:t._u([{key:"default",fn:function(){return[1==t.isPay?e("van-button",{attrs:{type:"primary",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名")]):2==t.isPay?e("van-button",{attrs:{type:"warning",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名未交费")]):e("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.turnApply}},[t._v("未报名")])]},proxy:!0}],null,!1,567203517)}):t._e()],1)])],1),e("van-tabs",{staticClass:"nav",on:{click:t.onClick},model:{value:t.cmsId,callback:function(e){t.cmsId=e},expression:"cmsId"}},t._l(t.cmsList,(function(t){return e("van-tab",{key:t.id,attrs:{title:t.title,name:t.id}})})),1),e("pclogin")],1)},a=[],o=i("ade3"),c=(i("a481"),i("6762"),i("2fdb"),i("cacf")),r=i("7dcb"),l=function(){var t=this,e=t._self._c;return e("van-dialog",{attrs:{title:"登录操作",width:"400px"},on:{confirm:t.login},model:{value:t.showPcLogin,callback:function(e){t.showPcLogin=e},expression:"showPcLogin"}},[e("div",{staticClass:"text-center padding"},[e("van-cell-group",{attrs:{inset:""}},[e("van-field",{attrs:{name:"手机号",label:"手机号",required:"",placeholder:"手机号",rules:[{required:!0,message:"请填写手机号"}]},model:{value:t.mobile,callback:function(e){t.mobile=e},expression:"mobile"}}),"1736999159118508033"!=t.activityId?e("van-field",{attrs:{center:"",clearable:"",maxlength:"6",label:"短信验证码",required:"",placeholder:"请输入短信验证码"},scopedSlots:t._u([{key:"button",fn:function(){return[e("van-button",{attrs:{size:"small",type:"primary",disabled:t.waiting},on:{click:function(e){return t.doSendSmsCode()}}},[t.waiting?e("span",[t._v(t._s(t.waitingTime)+"秒后重发")]):e("span",{staticStyle:{"font-size":"13px"}},[t._v("获取验证码")])])]},proxy:!0}],null,!1,850170227),model:{value:t.code,callback:function(e){t.code=e},expression:"code"}}):t._e()],1)],1)])},u=[],d={name:"pclogin",computed:{showPcLogin:{get:function(){return this.$store.state.user.showPcLogin},set:function(t){this.$store.commit("user/changePcLogin",t)}}},data:function(){return{activityId:void 0,waiting:!1,waitingTime:60,mobile:"",code:""}},mounted:function(){this.activityId=this.$route.query.id},methods:{login:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?this.code||0x181b0f883e693000==this.activityId?/^\d{6}$/.test(this.code)||0x181b0f883e693000==this.activityId?void this.$fly.post("/pyp/web/user/pcLogin",{mobile:this.mobile,code:0x181b0f883e693000==this.activityId?"121212":this.code}).then((function(e){e&&200===e.code?(vant.Toast("登录成功"),t.$store.commit("user/update",e.userInfo),t.showPcLogin=!1,"applyIndex"!=t.$route.name&&"livesDetail"!=t.$route.name||location.reload()):vant.Toast(e.msg)})):(vant.Toast("验证码错误"),!1):(vant.Toast("请输入验证码"),!1):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},doSendSmsCode:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.mobile,activityId:this.activityId}).then((function(e){e&&200===e.code?(t.countdown(),vant.Toast("发送验证码成功")):vant.Toast(e.msg)})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var t=this;this.waiting=!0;var e=window.setInterval((function(){t.waitingTime--,t.waitingTime<0&&(window.clearInterval(e),t.waitingTime=60,t.waiting=!1)}),1e3)}}},m=d,h=i("2877"),v=Object(h["a"])(m,l,u,!1,null,null,null),f=v.exports,p={components:{pclogin:f},data:function(){return{activityId:void 0,bannerIndex:0,cmsId:void 0,cmsList:[],cmsInfo:{},activityInfo:{}}},computed:{userInfo:{get:function(){return this.$store.state.user.userInfo},set:function(t){this.$store.commit("user/update",t)}},isPay:{get:function(){return this.$store.state.apply.isPay},set:function(t){this.$store.commit("apply/update",t)}}},mounted:function(){var t=(new Date).getTime();localStorage.setItem("logAddTime",t+36e5),this.openid=this.$cookie.get("openid"),this.activityId=this.$route.query.id,this.cmsId=sessionStorage.getItem("cmsId")||this.$route.query.cmsId,this.checkLogin(),this.getActivityInfo(),this.activityLogCount()},methods:(n={showLogin:function(){this.$store.commit("user/changePcLogin",!0)},checkLogin:function(){var t=this;this.$fly.get("/pyp/web/user/checkLogin").then((function(e){t.userInfo=e.result,t.userInfo?t.checkApply():t.isPay=0}))},bannerIndexChange:function(t){this.bannerIndex=t},checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(e){200==e.code?(t.isPay=e.isPay,1==t.isPay||2==t.isPay&&vant.Dialog.alert({title:"提示",message:"您有一笔注册费待支付，点击跳转"}).then((function(){t.$router.push({name:"applySuccess",query:{orderId:e.result,id:t.activityId}})}))):vant.Toast(e.msg)}))},changeShowType:function(t){this.$emit("changeShowType",t)},turnApply:function(){var t=this.cmsList.filter((function(t){return t.model&&t.model.includes("applyIndex")}));if(t){var e=t[0];sessionStorage.setItem("cmsId",e.id);var i=e.model.replace("${activityId}",e.activityId);this.$router.push(JSON.parse(i))}else vant.Toast("暂未开启报名")},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(e){if(t.loading=!1,200==e.code){t.activityInfo=e.activity,document.title=t.activityInfo.name;var i=t.activityInfo.startTime,n=new Date(i.replace(/-/g,"/")),s=new Date,a=n.getTime()-s.getTime();t.dateCompare=a>0?a:0,t.getCmsList()}}))},activityLogCount:function(){var t=localStorage.getItem("logAddTime"),e=(new Date).getTime();e>t&&this.$fly.post("/pyp/activity/activityviewlog/count",{activityId:this.activityId,device:r["a"].getVersion()}).then((function(t){}))},getCmsList:function(){var t=this;this.$fly.get("/pyp/cms/cms/findByActivityId/".concat(this.activityId)).then((function(e){t.loading=!1,200==e.code?(t.cmsList=e.result,t.cmsId?t.changeShowType(t.cmsId):t.changeShowType(t.cmsList[0].id)):(vant.Toast(e.msg),t.cmsList=[])}))},onClick:function(t){if(console.log(t),sessionStorage.setItem("cmsId",t),this.cmsInfo=this.cmsList.filter((function(e){return e.id==t}))[0],this.cmsInfo.url&&Object(c["d"])(this.cmsInfo.url))location.href=this.cmsInfo.url;else if(this.cmsInfo.model&&this.isJSON(this.cmsInfo.model)){var e=this.cmsInfo.model.replace("${activityId}",this.cmsInfo.activityId);this.$router.push(JSON.parse(e))}else"cmsIndex"==this.$route.name?this.changeShowType(t):this.$router.push({name:"cmsIndex",query:{id:this.activityId,cmsId:t}})}},Object(o["a"])(n,"bannerIndexChange",(function(t){this.bannerIndex=t})),Object(o["a"])(n,"isJSON",(function(t){try{return JSON.parse(t),!0}catch(e){return!1}})),n)},y=p,g=(i("dd7a"),Object(h["a"])(y,s,a,!1,null,"7bd3d808",null));e["default"]=g.exports},"28a5":function(t,e,i){"use strict";var n=i("aae3"),s=i("cb7c"),a=i("ebd6"),o=i("0390"),c=i("9def"),r=i("5f1b"),l=i("520a"),u=i("79e5"),d=Math.min,m=[].push,h="split",v="length",f="lastIndex",p=4294967295,y=!u((function(){RegExp(p,"y")}));i("214f")("split",2,(function(t,e,i,u){var g;return g="c"=="abbc"[h](/(b)*/)[1]||4!="test"[h](/(?:)/,-1)[v]||2!="ab"[h](/(?:ab)*/)[v]||4!="."[h](/(.?)(.?)/)[v]||"."[h](/()()/)[v]>1||""[h](/.?/)[v]?function(t,e){var s=String(this);if(void 0===t&&0===e)return[];if(!n(t))return i.call(s,t,e);var a,o,c,r=[],u=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),d=0,h=void 0===e?p:e>>>0,y=new RegExp(t.source,u+"g");while(a=l.call(y,s)){if(o=y[f],o>d&&(r.push(s.slice(d,a.index)),a[v]>1&&a.index<s[v]&&m.apply(r,a.slice(1)),c=a[0][v],d=o,r[v]>=h))break;y[f]===a.index&&y[f]++}return d===s[v]?!c&&y.test("")||r.push(""):r.push(s.slice(d)),r[v]>h?r.slice(0,h):r}:"0"[h](void 0,0)[v]?function(t,e){return void 0===t&&0===e?[]:i.call(this,t,e)}:i,[function(i,n){var s=t(this),a=void 0==i?void 0:i[e];return void 0!==a?a.call(i,s,n):g.call(String(s),i,n)},function(t,e){var n=u(g,t,this,e,g!==i);if(n.done)return n.value;var l=s(t),m=String(this),h=a(l,RegExp),v=l.unicode,f=(l.ignoreCase?"i":"")+(l.multiline?"m":"")+(l.unicode?"u":"")+(y?"y":"g"),x=new h(y?l:"^(?:"+l.source+")",f),I=void 0===e?p:e>>>0;if(0===I)return[];if(0===m.length)return null===r(x,m)?[m]:[];var b=0,w=0,S=[];while(w<m.length){x.lastIndex=y?w:0;var k,L=r(x,y?m:m.slice(w));if(null===L||(k=d(c(x.lastIndex+(y?0:w)),m.length))===b)w=o(m,w,v);else{if(S.push(m.slice(b,w)),S.length===I)return S;for(var T=1;T<=L.length-1;T++)if(S.push(L[T]),S.length===I)return S;w=b=k}}return S.push(m.slice(b)),S}]}))},"4f31":function(t,e,i){"use strict";i("5435")},5435:function(t,e,i){},"7dcb":function(t,e,i){"use strict";i("a481"),i("4917");e["a"]={getVersion:function(){var t=navigator.userAgent||window.navigator.userAgent,e=/HUAWEI|HONOR/gi,i=/[^;]+(?= Build)/gi,n=/CPU iPhone OS \d[_\d]*/gi,s=/CPU OS \d[_\d]*/gi,a=/Windows NT \d[\.\d]*/gi,o=/Linux x\d[_\d]*/gi,c=/Mac OS X \d[_|\.\d]*/gi;return/Android/gi.test(t)?e.test(t)?t.match(e)[0]+t.match(i)[0]:t.match(i)[0]:/iPhone/gi.test(t)?t.match(n)[0].replace(/CPU iPhone OS/gi,"iPhone").replace(/_/g,"."):/iPad/gi.test(t)?t.match(s)[0].replace(/CPU OS/gi,"iPad").replace(/_/g,"."):/Windows/gi.test(t)?"Windows "+(Math.min(parseInt(t.match(a)[0].replace(/Windows NT /,""))+1,10)<7?"XP":Math.min(parseInt(t.match(a)[0].replace(/Windows NT /,""))+1,10)):/Linux/gi.test(t)?t.match(o)[0]:/Macintosh/gi.test(t)?t.match(c)[0].replace(/_/g,"."):"unknown"}}},ac6a:function(t,e,i){for(var n=i("cadf"),s=i("0d58"),a=i("2aba"),o=i("7726"),c=i("32e9"),r=i("84f2"),l=i("2b4c"),u=l("iterator"),d=l("toStringTag"),m=r.Array,h={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},v=s(h),f=0;f<v.length;f++){var p,y=v[f],g=h[y],x=o[y],I=x&&x.prototype;if(I&&(I[u]||c(I,u,m),I[d]||c(I,d,y),r[y]=m,g))for(p in n)I[p]||a(I,p,n[p],!0)}},ade3:function(t,e,i){"use strict";i.d(e,"a",(function(){return o}));var n=i("53ca");function s(t,e){if("object"!==Object(n["a"])(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var s=i.call(t,e||"default");if("object"!==Object(n["a"])(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function a(t){var e=s(t,"string");return"symbol"===Object(n["a"])(e)?e:String(e)}function o(t,e,i){return e=a(e),e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}},ae19:function(t,e,i){"use strict";i.r(e);i("7f7f");var n=function(){var t=this,e=t._self._c;return e("div",{class:t.isMobilePhone?"":"pc-container"},[t.isMobilePhone?t._e():e("pcheader"),t._m(0),e("div",{staticClass:"list"},[t._l(t.exam.examQuestionEntities,(function(i,n){return e("div",{key:i.id,staticClass:"box"},[e("div",{staticStyle:{padding:"5px 10px 0px 10px"},domProps:{innerHTML:t._s(i.name)}}),0==i.type?e("van-radio-group",{model:{value:t.exam.examQuestionEntities[n].selectId,callback:function(e){t.$set(t.exam.examQuestionEntities[n],"selectId",e)},expression:"exam.examQuestionEntities[index].selectId"}},[e("van-cell-group",t._l(i.examQuestionOptionEntities,(function(i){return e("van-cell",{key:i.id,attrs:{title:i.optionId+"、"+i.name,clickable:""},on:{click:function(e){return t.choose(i.id,n)}},scopedSlots:t._u([{key:"right-icon",fn:function(){return[e("van-radio",{attrs:{name:i.id}})]},proxy:!0}],null,!0)})})),1)],1):t._e(),1==i.type?e("van-checkbox-group",{on:{change:t.onChange},model:{value:t.exam.examQuestionEntities[n].selectId,callback:function(e){t.$set(t.exam.examQuestionEntities[n],"selectId",e)},expression:"exam.examQuestionEntities[index].selectId"}},[e("van-cell-group",t._l(i.examQuestionOptionEntities,(function(i){return e("van-cell",{key:i.id,attrs:{clickable:"","data-index":i.id,title:i.optionId+"、"+i.name},on:{click:t.toggle},scopedSlots:t._u([{key:"right-icon",fn:function(){return[e("van-checkbox",{ref:"checkboxes",refInFor:!0,attrs:{name:i.id}})]},proxy:!0}],null,!0)})})),1)],1):2==i.type?e("van-cell-group",[e("van-field",{staticStyle:{"border-radius":"unset"},attrs:{placeholder:"请输入答题内容"},model:{value:t.exam.examQuestionEntities[n].selectId,callback:function(e){t.$set(t.exam.examQuestionEntities[n],"selectId",e)},expression:"exam.examQuestionEntities[index].selectId"}})],1):t._e()],1)})),e("div",{staticClass:"bottom"},[e("van-button",{staticStyle:{width:"45%"},attrs:{type:"primary",round:"",block:""},on:{click:t.saveExam}},[t._v("暂存")]),e("van-button",{staticStyle:{width:"45%"},attrs:{type:"info",round:"",block:""},on:{click:t.submitExam}},[t._v("提交")])],1)],2)],1)},s=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"nav-title",staticStyle:{"margin-top":"8px"}},[e("div",{staticClass:"color"}),e("div",{staticClass:"text"},[t._v("开始您的答题")])])}],a=(i("a481"),i("6b54"),i("28a5"),i("ac6a"),i("cacf")),o=i("1b69"),c={components:{pcheader:o["default"]},data:function(){return{isMobilePhone:Object(a["c"])(),openid:void 0,token:void 0,examActivityUserId:void 0,examId:void 0,activityId:void 0,examActivityUserEntity:{},exam:{}}},mounted:function(){document.title="开始答题",this.activityId=this.$route.query.id,this.examActivityUserId=this.$route.query.examActivityUserId,this.examId=this.$route.query.examId,this.token=this.$route.query.token,this.openid=this.$cookie.get("openid"),this.$wxShare(),this.getActivityInfo()},methods:{getActivityInfo:function(){var t=this;this.$fly.get("/pyp/web/exam/detail",{examId:this.examId,examActivityUserId:this.examActivityUserId}).then((function(e){200==e.code?(t.exam=e.exam,t.exam.examQuestionEntities.forEach((function(t){1==t.type&&(t.selectId=t.selectId?t.selectId.split(","):[])})),t.examActivityUserEntity=e.examActivityUserEntity):(vant.Toast(e.msg),t.activityInfo={})}))},choose:function(t,e){this.exam.examQuestionEntities[e].selectId=t,console.log(this.exam.examQuestionEntities[e].selectId)},toggle:function(t){var e=t.currentTarget.dataset.index;this.$refs.checkboxes.filter((function(t){return t.name==e}))[0].toggle()},submitExam:function(){var t=this;this.exam.token=this.token,this.exam.examActivityUserId=this.examActivityUserId,this.exam.examQuestionEntities.forEach((function(t){1==t.type&&(t.selectId=t.selectId?t.selectId.toString():"")})),this.$fly.post("/pyp/web/exam/submitExam",this.exam).then((function(e){if(200==e.code){var i="";i=0==e.type?"考试提交成功,您的成绩是："+e.result.points+"分":"问卷提交成功",vant.Dialog.alert({title:"提交成功",message:i}).then((function(){t.$router.replace({name:"examIndex",query:{id:t.exam.activityId}})}))}else vant.Toast(e.msg),t.exam.examQuestionEntities.forEach((function(t){1==t.type&&(t.selectId=t.selectId?t.selectId.split(","):[])}))}))},saveExam:function(){var t=this;this.exam.token=this.token,this.exam.examActivityUserId=this.examActivityUserId,this.exam.examQuestionEntities.forEach((function(t){1==t.type&&(t.selectId=t.selectId?t.selectId.toString():"")})),this.$fly.post("/pyp/web/exam/saveExam",this.exam).then((function(e){200==e.code?vant.Dialog.alert({title:"暂存成功",message:"暂存考试成功"}).then((function(){t.$router.replace({name:"examIndex",query:{id:t.exam.activityId}})})):(vant.Toast(e.msg),t.exam.examQuestionEntities.forEach((function(t){1==t.type&&(t.selectId=t.selectId?t.selectId.split(","):[])})))}))}}},r=c,l=(i("4f31"),i("2877")),u=Object(l["a"])(r,n,s,!1,null,"553d9e54",null);e["default"]=u.exports},cad8:function(t,e,i){},dd7a:function(t,e,i){"use strict";i("cad8")}}]);
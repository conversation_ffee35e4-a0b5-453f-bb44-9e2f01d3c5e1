(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7792c2a1","chunk-94a0f518"],{"1edd":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-menu"},[e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[t.isAuth("sys:menu:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e()],1)],1),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.dataList,"row-key":"menuId",border:""}},[e("el-table-column",{attrs:{prop:"name","header-align":"center","min-width":"150",label:"名称"}}),e("el-table-column",{attrs:{prop:"parentName","header-align":"center",align:"center",width:"120",label:"上级菜单"}}),e("el-table-column",{attrs:{"header-align":"center",align:"center",label:"图标"},scopedSlots:t._u([{key:"default",fn:function(t){return[e("i",{class:t.row.icon})]}}])}),e("el-table-column",{attrs:{prop:"type","header-align":"center",align:"center",label:"类型"},scopedSlots:t._u([{key:"default",fn:function(a){return[0===a.row.type?e("el-tag",{attrs:{size:"small"}},[t._v("目录")]):1===a.row.type?e("el-tag",{attrs:{size:"small",type:"success"}},[t._v("菜单")]):2===a.row.type?e("el-tag",{attrs:{size:"small",type:"info"}},[t._v("按钮")]):t._e()]}}])}),e("el-table-column",{attrs:{prop:"isShow","header-align":"center",align:"center",label:"是否显示"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.isShow?e("el-tag",{attrs:{size:"small",type:"success"}},[t._v("显示")]):e("el-tag",{attrs:{size:"small",type:"info"}},[t._v("隐藏")])]}}])}),e("el-table-column",{attrs:{prop:"orderNum","header-align":"center",align:"center",label:"排序号"}}),e("el-table-column",{attrs:{prop:"url","header-align":"center",align:"center",width:"150","show-overflow-tooltip":!0,label:"菜单URL"}}),e("el-table-column",{attrs:{prop:"perms","header-align":"center",align:"center",width:"150","show-overflow-tooltip":!0,label:"授权标识"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[t.isAuth("sys:menu:update")?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.menuId)}}},[t._v("修改")]):t._e(),t.isAuth("sys:menu:delete")?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.menuId)}}},[t._v("删除")]):t._e()]}}])})],1),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e()],1)},n=[],o=a("305b"),s=a("ed08"),l={data:function(){return{dataForm:{},dataList:[],dataListLoading:!1,addOrUpdateVisible:!1}},components:{AddOrUpdate:o["default"]},activated:function(){this.getDataList()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/sys/menu/list"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;t.dataList=Object(s["d"])(a,"menuId"),t.dataListLoading=!1}))},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(t)}))},deleteHandle:function(t){var e=this;this.$confirm("确定对[id=".concat(t,"]进行[删除]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/sys/menu/delete/".concat(t)),method:"post",data:e.$http.adornData()}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){return e.getDataList()}}):e.$message.error(a.msg)}))})).catch((function(){}))}}},i=l,d=a("2877"),m=Object(d["a"])(i,r,n,!1,null,null,null);e["default"]=m.exports},"27e5":function(t,e,a){},"305b":function(t,e,a){"use strict";a.r(e);a("b0c0");var r=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"}},[e("el-form-item",{attrs:{label:"类型",prop:"type"}},[e("el-radio-group",{model:{value:t.dataForm.type,callback:function(e){t.$set(t.dataForm,"type",e)},expression:"dataForm.type"}},t._l(t.dataForm.typeList,(function(a,r){return e("el-radio",{key:r,attrs:{label:r}},[t._v(t._s(a))])})),1)],1),e("el-form-item",{attrs:{label:t.dataForm.typeList[t.dataForm.type]+"名称",prop:"name"}},[e("el-input",{attrs:{placeholder:t.dataForm.typeList[t.dataForm.type]+"名称"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",{attrs:{label:"上级菜单",prop:"parentName"}},[e("el-popover",{ref:"menuListPopover",attrs:{placement:"bottom-start",trigger:"click"}},[e("el-tree",{ref:"menuListTree",staticStyle:{height:"400px","overflow-x":"scroll"},attrs:{data:t.menuList,props:t.menuListTreeProps,"node-key":"menuId","default-expand-all":!0,"highlight-current":!0,"expand-on-click-node":!1},on:{"current-change":t.menuListTreeCurrentChangeHandle}})],1),e("el-input",{directives:[{name:"popover",rawName:"v-popover:menuListPopover",arg:"menuListPopover"}],staticClass:"menu-list__input",attrs:{readonly:!0,placeholder:"点击选择上级菜单"},model:{value:t.dataForm.parentName,callback:function(e){t.$set(t.dataForm,"parentName",e)},expression:"dataForm.parentName"}})],1),1===t.dataForm.type?e("el-form-item",{attrs:{label:"菜单路由",prop:"url"}},[e("el-input",{attrs:{placeholder:"菜单路由"},model:{value:t.dataForm.url,callback:function(e){t.$set(t.dataForm,"url",e)},expression:"dataForm.url"}})],1):t._e(),0!==t.dataForm.type?e("el-form-item",{attrs:{label:"授权标识",prop:"perms"}},[e("el-input",{attrs:{placeholder:"多个用逗号分隔, 如: user:list,user:create"},model:{value:t.dataForm.perms,callback:function(e){t.$set(t.dataForm,"perms",e)},expression:"dataForm.perms"}})],1):t._e(),e("el-form-item",{attrs:{label:"是否显示",prop:"isShow"}},[e("el-radio-group",{model:{value:t.dataForm.isShow,callback:function(e){t.$set(t.dataForm,"isShow",e)},expression:"dataForm.isShow"}},t._l(t.isShowList,(function(a,r){return e("el-radio",{key:r,attrs:{label:r}},[t._v(t._s(a))])})),1)],1),2!==t.dataForm.type?e("el-form-item",{attrs:{label:"菜单图标",prop:"icon"}},[e("el-row",[e("el-col",{attrs:{span:12}},[e("el-input",{staticClass:"icon-list__input",attrs:{placeholder:"菜单图标名称"},model:{value:t.dataForm.icon,callback:function(e){t.$set(t.dataForm,"icon",e)},expression:"dataForm.icon"}})],1),e("el-col",{staticClass:"icon-list__tips",attrs:{span:12}},[2!==t.dataForm.type?e("el-form-item",{attrs:{label:"排序号",prop:"orderNum"}},[e("el-input-number",{attrs:{"controls-position":"right",min:0,label:"排序号"},model:{value:t.dataForm.orderNum,callback:function(e){t.$set(t.dataForm,"orderNum",e)},expression:"dataForm.orderNum"}})],1):t._e()],1)],1)],1):t._e(),e("div",[t._v("参考ElementUI图标库, "),e("a",{attrs:{href:"https://element.eleme.cn/#/zh-CN/component/icon",target:"_blank"}},[t._v("找图标")])])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},n=[],o=(a("d9e2"),a("ac1f"),a("00b4"),a("ed08")),s={data:function(){var t=this,e=function(e,a,r){1!==t.dataForm.type||/\S/.test(a)?r():r(new Error("菜单URL不能为空"))};return{visible:!1,dataForm:{id:0,type:1,typeList:["目录","菜单","按钮"],name:"",parentId:0,parentName:"",url:"",perms:"",orderNum:0,icon:"",isShow:1},isShowList:["否","是"],dataRule:{name:[{required:!0,message:"菜单名称不能为空",trigger:"blur"}],parentName:[{required:!0,message:"上级菜单不能为空",trigger:"change"}],url:[{validator:e,trigger:"blur"}]},menuList:[],menuListTreeProps:{label:"name",children:"children"}}},methods:{init:function(t){var e=this;this.dataForm.id=t||0,this.$http({url:this.$http.adornUrl("/sys/menu/select"),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;e.menuList=Object(o["d"])(a.menuList,"menuId")})).then((function(){e.visible=!0,e.$nextTick((function(){e.$refs["dataForm"].resetFields()}))})).then((function(){e.dataForm.id?e.$http({url:e.$http.adornUrl("/sys/menu/info/".concat(e.dataForm.id)),method:"get",params:e.$http.adornParams()}).then((function(t){var a=t.data;e.dataForm.id=a.menu.menuId,e.dataForm.type=a.menu.type,e.dataForm.name=a.menu.name,e.dataForm.parentId=a.menu.parentId,e.dataForm.url=a.menu.url,e.dataForm.perms=a.menu.perms,e.dataForm.orderNum=a.menu.orderNum,e.dataForm.icon=a.menu.icon,e.dataForm.isShow=a.menu.isShow,e.menuListTreeSetCurrentNode()})):e.menuListTreeSetCurrentNode()}))},menuListTreeCurrentChangeHandle:function(t,e){this.dataForm.parentId=t.menuId,this.dataForm.parentName=t.name},menuListTreeSetCurrentNode:function(){this.$refs.menuListTree.setCurrentKey(this.dataForm.parentId),this.dataForm.parentName=(this.$refs.menuListTree.getCurrentNode()||{})["name"]},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/sys/menu/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({menuId:t.dataForm.id||void 0,type:t.dataForm.type,name:t.dataForm.name,parentId:t.dataForm.parentId,url:t.dataForm.url,perms:t.dataForm.perms,orderNum:t.dataForm.orderNum,icon:t.dataForm.icon,isShow:t.dataForm.isShow})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))}}},l=s,i=(a("b33b"),a("2877")),d=Object(i["a"])(l,r,n,!1,null,null,null);e["default"]=d.exports},b33b:function(t,e,a){"use strict";a("27e5")}}]);
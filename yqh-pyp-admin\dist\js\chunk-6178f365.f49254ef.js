(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6178f365"],{"12ef":function(e,t,a){"use strict";a("fe8a")},f044:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e._self._c;return t("div",[t("el-dialog",{attrs:{title:"使用记录",visible:e.visible,width:"80%","close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[t("div",{staticClass:"summary-info"},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"summary-card"},[t("div",{staticClass:"summary-item"},[t("div",{staticClass:"summary-value"},[e._v(e._s(e.activityInfo.allCount||0))]),t("div",{staticClass:"summary-label"},[e._v("总次数")])])])],1),t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"summary-card"},[t("div",{staticClass:"summary-item"},[t("div",{staticClass:"summary-value"},[e._v(e._s(e.activityInfo.useCount||0))]),t("div",{staticClass:"summary-label"},[e._v("已使用")])])])],1),t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"summary-card"},[t("div",{staticClass:"summary-item"},[t("div",{staticClass:"summary-value"},[e._v(e._s((e.activityInfo.allCount||0)-(e.activityInfo.useCount||0)))]),t("div",{staticClass:"summary-label"},[e._v("剩余次数")])])])],1),t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"summary-card"},[t("div",{staticClass:"summary-item"},[t("div",{staticClass:"summary-value",staticStyle:{color:"#e6a23c"}},[e._v(e._s(e.getOverallUsagePercentage())+"%")]),t("div",{staticClass:"summary-label"},[e._v("总使用率")])])])],1)],1)],1),t("div",{staticClass:"record-tabs"},[t("el-tabs",{on:{"tab-click":e.handleTabClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[t("el-tab-pane",{attrs:{label:"充值记录",name:"recharge"}},[t("div",{staticClass:"filter-section",staticStyle:{"margin-bottom":"15px"}},[t("el-form",{attrs:{inline:!0,size:"small"}},[t("el-form-item",{attrs:{label:"订单状态:"}},[t("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"全部状态",clearable:""},on:{change:e.handleRechargeStatusFilter},model:{value:e.rechargeStatusFilter,callback:function(t){e.rechargeStatusFilter=t},expression:"rechargeStatusFilter"}},[t("el-option",{attrs:{label:"全部状态",value:""}}),t("el-option",{attrs:{label:"待支付",value:"0"}}),t("el-option",{attrs:{label:"已支付",value:"1"}}),t("el-option",{attrs:{label:"已取消",value:"2"}}),t("el-option",{attrs:{label:"已退款",value:"3"}})],1)],1),t("el-form-item",{attrs:{label:"充值类型:"}},[t("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"全部类型",clearable:""},on:{change:e.handleRechargeTypeFilter},model:{value:e.rechargeTypeFilter,callback:function(t){e.rechargeTypeFilter=t},expression:"rechargeTypeFilter"}},[t("el-option",{attrs:{label:"全部类型",value:""}}),t("el-option",{attrs:{label:"套餐充值",value:"1"}}),t("el-option",{attrs:{label:"自定义充值",value:"2"}}),t("el-option",{attrs:{label:"系统赠送",value:"3"}}),t("el-option",{attrs:{label:"创建活动套餐",value:"4"}})],1)],1)],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.rechargeLoading,expression:"rechargeLoading"}],staticStyle:{width:"100%"},attrs:{data:e.rechargeList,border:""}},[t("el-table-column",{attrs:{prop:"orderSn","header-align":"center",align:"center",label:"订单号"}}),t("el-table-column",{attrs:{prop:"rechargeType","header-align":"center",align:"center",label:"充值类型"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{staticClass:"status-tag",attrs:{type:e.getRechargeTypeTag(a.row.rechargeType).type}},[e._v(" "+e._s(e.getRechargeTypeTag(a.row.rechargeType).text)+" ")])]}}])}),t("el-table-column",{attrs:{prop:"countValue","header-align":"center",align:"center",label:"充值次数",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",{staticClass:"count-display",staticStyle:{color:"#409EFF"}},[e._v(e._s(a.row.countValue||0))])]}}])}),t("el-table-column",{attrs:{prop:"usedCount","header-align":"center",align:"center",label:"已使用次数",width:"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",{staticClass:"count-display",staticStyle:{color:"#f56c6c"}},[e._v(e._s(a.row.usedCount||0))])]}}])}),t("el-table-column",{attrs:{"header-align":"center",align:"center",label:"剩余可用次数",width:"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",{staticClass:"count-display",staticStyle:{color:"#67c23a"}},[e._v(" "+e._s((a.row.countValue||0)-(a.row.usedCount||0))+" ")])]}}])}),t("el-table-column",{attrs:{"header-align":"center",align:"center",label:"使用率",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-progress",{attrs:{percentage:e.getUsagePercentage(a.row),color:e.getUsageColor(a.row),"stroke-width":8,"show-text":!1}}),t("div",{staticStyle:{"margin-top":"2px","font-size":"12px",color:"#666"}},[e._v(" "+e._s(e.getUsagePercentage(a.row))+"% ")])]}}])}),t("el-table-column",{attrs:{prop:"payAmount","header-align":"center",align:"center",label:"充值金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ¥"+e._s(t.row.amount)+" ")]}}])}),t("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",label:"状态"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{staticClass:"status-tag",attrs:{type:e.getRechargeStatusTag(a.row.status).type}},[e._v(" "+e._s(e.getRechargeStatusTag(a.row.status).text)+" ")])]}}])}),t("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",width:"180",label:"充值时间"}}),t("el-table-column",{attrs:{"header-align":"center",align:"center",width:"120",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.updateRechargeStatus(a.row)}}},[e._v("修改状态")])]}}])})],1),t("el-pagination",{attrs:{"current-page":e.rechargePageIndex,"page-sizes":[10,20,50,100],"page-size":e.rechargePageSize,total:e.rechargeTotalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.rechargeSizeChangeHandle,"current-change":e.rechargeCurrentChangeHandle}})],1),t("el-tab-pane",{attrs:{label:"使用记录",name:"usage"}},[t("div",{staticClass:"filter-section",staticStyle:{"margin-bottom":"15px"}},[t("el-form",{attrs:{inline:!0,size:"small"}},[t("el-form-item",{attrs:{label:"使用类型:"}},[t("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"全部类型",clearable:""},on:{change:e.handleUsageTypeFilter},model:{value:e.usageTypeFilter,callback:function(t){e.usageTypeFilter=t},expression:"usageTypeFilter"}},[t("el-option",{attrs:{label:"全部类型",value:""}}),t("el-option",{attrs:{label:"生成文案",value:"1"}}),t("el-option",{attrs:{label:"生成视频",value:"2"}}),t("el-option",{attrs:{label:"转发",value:"3"}}),t("el-option",{attrs:{label:"生成图文成品",value:"4"}})],1)],1)],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.usageLoading,expression:"usageLoading"}],staticStyle:{width:"100%"},attrs:{data:e.usageList,border:""}},[t("el-table-column",{attrs:{prop:"usageType","header-align":"center",align:"center",label:"使用类型"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{staticClass:"status-tag",attrs:{type:e.getUsageTypeTag(a.row.usageType).type}},[e._v(" "+e._s(e.getUsageTypeTag(a.row.usageType).text)+" ")])]}}])}),t("el-table-column",{attrs:{prop:"usageCount","header-align":"center",align:"center",label:"使用次数"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",{staticClass:"count-display",staticStyle:{color:"#f56c6c"}},[e._v(e._s(a.row.usageCount||0))])]}}])}),t("el-table-column",{attrs:{prop:"description","header-align":"center",align:"center",label:"使用描述","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",width:"180",label:"使用时间"}}),t("el-table-column",{attrs:{"header-align":"center",align:"center",width:"120",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{staticStyle:{color:"#f56c6c"},attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteUsageRecord(a.row)}}},[e._v("删除")])]}}])})],1),t("el-pagination",{attrs:{"current-page":e.usagePageIndex,"page-sizes":[10,20,50,100],"page-size":e.usagePageSize,total:e.usageTotalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.usageSizeChangeHandle,"current-change":e.usageCurrentChangeHandle}})],1)],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("关闭")])],1)]),t("el-dialog",{attrs:{title:"修改订单状态",visible:e.statusDialogVisible,width:"500px","close-on-click-modal":!1},on:{"update:visible":function(t){e.statusDialogVisible=t}}},[t("el-form",{ref:"statusForm",attrs:{model:e.statusForm,rules:e.statusRules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"订单号:"}},[t("span",[e._v(e._s(e.statusForm.orderSn))])]),t("el-form-item",{attrs:{label:"当前状态:"}},[t("el-tag",{attrs:{type:e.getRechargeStatusTag(e.statusForm.currentStatus).type}},[e._v(" "+e._s(e.getRechargeStatusTag(e.statusForm.currentStatus).text)+" ")])],1),t("el-form-item",{attrs:{label:"新状态:",prop:"newStatus"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择新状态"},model:{value:e.statusForm.newStatus,callback:function(t){e.$set(e.statusForm,"newStatus",t)},expression:"statusForm.newStatus"}},[t("el-option",{attrs:{label:"待支付",value:0}}),t("el-option",{attrs:{label:"已支付",value:1}}),t("el-option",{attrs:{label:"已取消",value:2}}),t("el-option",{attrs:{label:"已退款",value:3}})],1)],1),t("el-form-item",{attrs:{label:"备注:"}},[t("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入修改原因或备注"},model:{value:e.statusForm.remarks,callback:function(t){e.$set(e.statusForm,"remarks",t)},expression:"statusForm.remarks"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.statusDialogVisible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary",loading:e.statusUpdateLoading},on:{click:e.confirmUpdateStatus}},[e._v("确定")])],1)],1)],1)},r=[],l=(a("b0c0"),a("a9e3"),{props:{activityId:{type:[String,Number],required:!0}},data:function(){return{activityInfo:{},visible:!1,activeTab:"recharge",rechargeList:[],rechargePageIndex:1,rechargePageSize:10,rechargeTotalPage:0,rechargeLoading:!1,rechargeStatusFilter:"",rechargeTypeFilter:"",usageList:[],usagePageIndex:1,usagePageSize:10,usageTotalPage:0,usageLoading:!1,usageTypeFilter:"",rechargeTypeMap:{1:{text:"套餐充值",type:"primary"},2:{text:"自定义充值",type:"success"},3:{text:"系统赠送",type:"warning"},4:{text:"创建活动套餐",type:"info"}},rechargeStatusMap:{0:{text:"待支付",type:"warning"},1:{text:"已支付",type:"success"},2:{text:"已取消",type:"danger"},3:{text:"已退款",type:"info"},4:{text:"退款中",type:"info"}},usageTypeMap:{1:{text:"生成文案",type:"primary"},2:{text:"生成视频",type:"success"},4:{text:"生成图文成品",type:"success"},3:{text:"转发",type:"info"}},statusDialogVisible:!1,statusUpdateLoading:!1,statusForm:{id:null,orderSn:"",currentStatus:null,newStatus:null,remarks:""},statusRules:{newStatus:[{required:!0,message:"请选择新状态",trigger:"change"}]}}},methods:{init:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.getActivity(),e.getRechargeRecords()}))},getActivity:function(){var e=this;this.$http({url:this.$http.adornUrl("/activity/activity/info/".concat(this.activityId)),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.activityInfo=a.activity)}))},handleTabClick:function(e){"recharge"===e.name?this.getRechargeRecords():"usage"===e.name&&this.getUsageRecords()},getRechargeRecords:function(){var e=this;this.rechargeLoading=!0;var t={page:this.rechargePageIndex,limit:this.rechargePageSize,activityId:this.activityId};""!==this.rechargeStatusFilter&&(t.status=this.rechargeStatusFilter),""!==this.rechargeTypeFilter&&(t.rechargeType=this.rechargeTypeFilter),this.$http({url:this.$http.adornUrl("/activity/rechargerecord/list"),method:"get",params:this.$http.adornParams(t)}).then((function(t){var a=t.data;a&&200===a.code?(e.rechargeList=a.page.list,e.rechargeTotalPage=a.page.totalCount):(e.rechargeList=[],e.rechargeTotalPage=0),e.rechargeLoading=!1})).catch((function(){e.rechargeLoading=!1}))},getUsageRecords:function(){var e=this;this.usageLoading=!0;var t={page:this.usagePageIndex,limit:this.usagePageSize,activityId:this.activityId};""!==this.usageTypeFilter&&(t.usageType=this.usageTypeFilter),this.$http({url:this.$http.adornUrl("/activity/activityrechargeusage/list"),method:"get",params:this.$http.adornParams(t)}).then((function(t){var a=t.data;a&&200===a.code?(e.usageList=a.page.list,e.usageTotalPage=a.page.totalCount):(e.usageList=[],e.usageTotalPage=0),e.usageLoading=!1})).catch((function(){e.usageLoading=!1}))},rechargeSizeChangeHandle:function(e){this.rechargePageSize=e,this.rechargePageIndex=1,this.getRechargeRecords()},rechargeCurrentChangeHandle:function(e){this.rechargePageIndex=e,this.getRechargeRecords()},usageSizeChangeHandle:function(e){this.usagePageSize=e,this.usagePageIndex=1,this.getUsageRecords()},usageCurrentChangeHandle:function(e){this.usagePageIndex=e,this.getUsageRecords()},getRechargeTypeTag:function(e){return this.rechargeTypeMap[e]||{text:"未知",type:"info"}},getRechargeStatusTag:function(e){return this.rechargeStatusMap[e]||{text:"未知",type:"info"}},getUsageTypeTag:function(e){return this.usageTypeMap[e]||{text:"未知",type:"info"}},handleRechargeStatusFilter:function(){this.rechargePageIndex=1,this.getRechargeRecords()},handleRechargeTypeFilter:function(){this.rechargePageIndex=1,this.getRechargeRecords()},handleUsageTypeFilter:function(){this.usagePageIndex=1,this.getUsageRecords()},getUsagePercentage:function(e){var t=e.countValue||0,a=e.usedCount||0;return 0===t?0:Math.round(a/t*100)},getUsageColor:function(e){var t=this.getUsagePercentage(e);return t>=90?"#f56c6c":t>=70?"#e6a23c":t>=50?"#409eff":"#67c23a"},getOverallUsagePercentage:function(){var e=this.activityInfo.allCount||0,t=this.activityInfo.useCount||0;return 0===e?0:Math.round(t/e*100)},updateRechargeStatus:function(e){var t=this;this.statusForm={id:e.id,orderSn:e.orderSn,currentStatus:e.status,newStatus:null,remarks:""},this.statusDialogVisible=!0,this.$nextTick((function(){t.$refs.statusForm&&t.$refs.statusForm.clearValidate()}))},confirmUpdateStatus:function(){var e=this;this.$refs.statusForm?this.$refs.statusForm.validate((function(t){return!!t&&(e.statusForm.currentStatus===e.statusForm.newStatus?(e.$message.warning("新状态与当前状态相同"),!1):(e.statusUpdateLoading=!0,void e.$http({url:e.$http.adornUrl("/activity/rechargerecord/updateStatus"),method:"post",data:e.$http.adornData({id:e.statusForm.id,status:e.statusForm.newStatus,remarks:e.statusForm.remarks})}).then((function(t){var a=t.data;a&&200===a.code?(e.$message.success(a.msg||"状态修改成功"),e.statusDialogVisible=!1,e.getActivity(),e.getRechargeRecords()):e.$message.error(a.msg||"状态修改失败"),e.statusUpdateLoading=!1})).catch((function(){e.$message.error("状态修改失败"),e.statusUpdateLoading=!1}))))})):this.$message.error("表单未准备就绪，请重试")},deleteUsageRecord:function(e){var t=this;this.$confirm("确定要删除这条使用记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:t.$http.adornUrl("/activity/activityrechargeusage/delete"),method:"post",data:t.$http.adornData([e.id])}).then((function(e){var a=e.data;a&&200===a.code?(t.$message.success("删除成功"),t.getUsageRecords()):t.$message.error(a.msg||"删除失败")})).catch((function(){t.$message.error("删除失败")}))})).catch((function(){}))}}}),i=l,n=(a("12ef"),a("2877")),o=Object(n["a"])(i,s,r,!1,null,"58cf2584",null);t["default"]=o.exports},fe8a:function(e,t,a){}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-eb19e230"],{"72a7":function(e,t,a){"use strict";a.d(t,"a",(function(){return r}));var r=[{key:0,value:"公众号",disabled:!1},{key:1,value:"小程序",disabled:!1},{key:2,value:"通用",disabled:!0}]},"8e16":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:"新增/修改","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"公众号名称",prop:"name"}},[t("el-input",{attrs:{placeholder:"公众号名称"},model:{value:e.dataForm.name,callback:function(t){e.$set(e.dataForm,"name",t)},expression:"dataForm.name"}})],1),t("div",{staticClass:"padding text-gray"},[e._v("测试号可选择服务号，不同类型账号、是否认证可使用功能权限不同，"),t("a",{attrs:{href:"https://developers.weixin.qq.com/doc/offiaccount/Getting_Started/Explanation_of_interface_privileges.html"}},[e._v("参考文档")])]),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"公众号类型",prop:"type"}},[t("el-select",{attrs:{placeholder:"公众号类型"},model:{value:e.dataForm.type,callback:function(t){e.$set(e.dataForm,"type",t)},expression:"dataForm.type"}},e._l(e.ACCOUNT_TYPES,(function(e,a){return t("el-option",{key:e,attrs:{label:e,value:a}})})),1)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"是否认证",prop:"verified"}},[t("el-switch",{attrs:{placeholder:"是否认证"},model:{value:e.dataForm.verified,callback:function(t){e.$set(e.dataForm,"verified",t)},expression:"dataForm.verified"}})],1)],1)],1),t("el-form-item",{attrs:{label:"appid",prop:"appid"}},[t("el-input",{attrs:{placeholder:"appid"},model:{value:e.dataForm.appid,callback:function(t){e.$set(e.dataForm,"appid",t)},expression:"dataForm.appid"}})],1),t("el-form-item",{attrs:{label:"appsecret",prop:"secret"}},[t("el-input",{attrs:{placeholder:"appsecret"},model:{value:e.dataForm.secret,callback:function(t){e.$set(e.dataForm,"secret",t)},expression:"dataForm.secret"}})],1),t("el-form-item",{attrs:{label:"token",prop:"token"}},[t("el-input",{attrs:{placeholder:"token"},model:{value:e.dataForm.token,callback:function(t){e.$set(e.dataForm,"token",t)},expression:"dataForm.token"}})],1),t("el-form-item",{attrs:{label:"aesKey",prop:"aesKey"}},[t("el-input",{attrs:{placeholder:"aesKey，可为空"},model:{value:e.dataForm.aesKey,callback:function(t){e.$set(e.dataForm,"aesKey",t)},expression:"dataForm.aesKey"}})],1),t("el-form-item",{attrs:{label:"小程序appid",prop:"miniAppid"}},[t("el-input",{attrs:{placeholder:"miniAppid"},model:{value:e.dataForm.miniAppid,callback:function(t){e.$set(e.dataForm,"miniAppid",t)},expression:"dataForm.miniAppid"}})],1),t("el-form-item",{attrs:{label:"小程序appsecret",prop:"miniSecret"}},[t("el-input",{attrs:{placeholder:"miniSecret"},model:{value:e.dataForm.miniSecret,callback:function(t){e.$set(e.dataForm,"miniSecret",t)},expression:"dataForm.miniSecret"}})],1),t("el-form-item",{attrs:{label:"账号类型",prop:"accountType"}},[t("el-select",{attrs:{placeholder:"账号类型",filterable:""},model:{value:e.dataForm.accountType,callback:function(t){e.$set(e.dataForm,"accountType",t)},expression:"dataForm.accountType"}},e._l(e.accountType,(function(e){return t("el-option",{key:e.key,attrs:{disabled:e.disabled,label:e.value,value:e.key}})})),1)],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},o=[],i=a("2f62"),l=a("72a7"),s={data:function(){return{accountType:l["a"],visible:!1,dataForm:{appid:"",name:"",type:"2",verified:!0,secret:"",token:"my_weixin_token_",aesKey:"",miniAppid:"",miniSecret:"",accountType:0},dataRule:{name:[{required:!0,message:"公众号名称不能为空",trigger:"blur"}],appid:[{required:!0,message:"appid不能为空",trigger:"blur"}],secret:[{required:!0,message:"appsecret不能为空",trigger:"blur"}]}}},computed:Object(i["b"])({ACCOUNT_TYPES:function(e){return e.wxAccount.ACCOUNT_TYPES}}),methods:{init:function(e){var t=this;this.visible=!0,e&&e.appid?(this.dataForm=e,this.dataForm.type=e.type+""):this.$nextTick((function(){t.$refs["dataForm"].resetFields()}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$http({url:e.$http.adornUrl("/manage/wxAccount/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData(e.dataForm)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(a.msg)}))}))}}},n=s,p=a("2877"),d=Object(p["a"])(n,r,o,!1,null,null,null);t["default"]=d.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9f9da83a"],{"197a":function(e,t,i){"use strict";i.r(t);i("7f7f");var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"page"},[t("van-form",[t("div",{directives:[{name:"show",rawName:"v-show",value:e.channelVisible,expression:"channelVisible"}]},[t("div",{staticClass:"nav-title",staticStyle:{"margin-top":"8px"}},[t("div",{staticClass:"color"}),t("div",{staticClass:"text"},[e._v("报名通道选择")])]),t("van-radio-group",{model:{value:e.channelId,callback:function(t){e.channelId=t},expression:"channelId"}},[t("van-cell-group",{attrs:{inset:""}},e._l(e.channelList,(function(i,a){return t("van-cell",{key:i.id,attrs:{title:i.name,clickable:""},on:{click:function(t){return e.chooseChannel(i.id,a)}},scopedSlots:e._u([i.children?null:{key:"right-icon",fn:function(){return[t("van-radio",{attrs:{name:i.id}})]},proxy:!0}],null,!0)},[t("div",{attrs:{slot:"label"},slot:"label"},[i.children?t("div",[t("van-radio-group",{model:{value:e.channelId,callback:function(t){e.channelId=t},expression:"channelId"}},[t("van-cell-group",e._l(i.children,(function(i,n){return t("van-cell",{key:i.id,attrs:{title:i.name,clickable:""},on:{click:function(t){return t.stopPropagation(),e.chooseChannelChild(i.id,a,n)}},scopedSlots:e._u([{key:"right-icon",fn:function(){return[t("van-radio",{attrs:{name:i.id}})]},proxy:!0}],null,!0)},[t("div",{attrs:{slot:"label"},slot:"label"},[i.description?t("div",[e._v(e._s(i.description))]):e._e(),i.price>0?t("div",[e._v("￥"+e._s(i.price)+"元")]):e._e()])])})),1)],1)],1):t("div",[i.description?t("div",[e._v(e._s(i.description))]):e._e(),i.price>0?t("div",[e._v("￥"+e._s(i.price)+"元")]):e._e()])])])})),1)],1)],1),t("div",{staticClass:"nav-title",staticStyle:{"margin-top":"8px"}},[t("div",{staticClass:"color"}),t("div",{staticClass:"text"},[e._v("报名信息修改")])]),t("div",{staticClass:"dyn-item"},[t("van-field",{attrs:{name:"姓名",label:"姓名",required:!0,placeholder:"姓名",rules:[{required:!0,message:"请填写姓名"}]},model:{value:e.userInfo.contact,callback:function(t){e.$set(e.userInfo,"contact",t)},expression:"userInfo.contact"}})],1),t("div",{staticClass:"dyn-item"},[t("van-field",{attrs:{disabled:!0,name:"手机",label:"手机",required:!0,placeholder:"手机",rules:[{required:!0,message:"请填写手机"}]},model:{value:e.userInfo.mobile,callback:function(t){e.$set(e.userInfo,"mobile",t)},expression:"userInfo.mobile"}})],1),e._l(e.applyActivityConfigList,(function(i){return t("div",{key:i.id,staticClass:"dyn-item"},[0==i.type?t("div",[t("van-field",{attrs:{name:i.finalName,label:i.finalName,required:1==i.required,placeholder:i.finalName,rules:[{required:1==i.required,message:"请填写"+i.finalName}]},model:{value:e.userInfo[i.applyConfigFieldName],callback:function(t){e.$set(e.userInfo,i.applyConfigFieldName,t)},expression:"userInfo[item.applyConfigFieldName]"}})],1):1==i.type?t("div",[t("van-cell",{attrs:{required:1==i.required,title:i.finalName,"is-link":""},on:{click:function(t){return e.showRadio(i)}},model:{value:e.userInfo[i.applyConfigFieldName],callback:function(t){e.$set(e.userInfo,i.applyConfigFieldName,t)},expression:"userInfo[item.applyConfigFieldName]"}})],1):2==i.type?t("div",[t("van-cell",{attrs:{required:1==i.required,title:i.finalName,"is-link":""},nativeOn:{click:function(t){return e.showCheckBox(i)}},model:{value:e.userInfo[i.applyConfigFieldName],callback:function(t){e.$set(e.userInfo,i.applyConfigFieldName,t)},expression:"userInfo[item.applyConfigFieldName]"}})],1):5==i.type?t("div",[t("van-cell",{attrs:{required:1==i.required,title:i.finalName,"is-link":""},nativeOn:{click:function(t){return e.showDate(i)}},model:{value:e.userInfo[i.applyConfigFieldName],callback:function(t){e.$set(e.userInfo,i.applyConfigFieldName,t)},expression:"userInfo[item.applyConfigFieldName]"}})],1):4==i.type?t("div",["area"==i.applyConfigFieldName?t("van-cell",{attrs:{required:1==i.required,title:i.finalName,"is-link":""},on:{click:function(t){return e.showArea(i)}},model:{value:e.userInfo.areaName,callback:function(t){e.$set(e.userInfo,"areaName",t)},expression:"userInfo.areaName"}}):e._e()],1):e._e()])})),e._l(e.extraResult,(function(i,a){return t("div",{key:a,staticClass:"dyn-item"},[0==i.type?t("div",[t("van-field",{attrs:{name:i.finalName,label:i.finalName,required:1==i.required,placeholder:i.finalName,rules:[{required:1==i.required,message:"请填写"+i.finalName}]},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"extraItem.value"}})],1):1==i.type?t("div",[t("van-cell",{attrs:{required:1==i.required,title:i.finalName,"is-link":""},on:{click:function(t){return e.showExtraRadio(i)}},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"extraItem.value"}})],1):2==i.type?t("div",[t("van-cell",{attrs:{required:1==i.required,title:i.finalName,"is-link":""},nativeOn:{click:function(t){return e.showExtraCheckBox(i)}},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"extraItem.value"}})],1):5==i.type?t("div",[t("van-cell",{attrs:{required:1==i.required,title:i.finalName,"is-link":""},nativeOn:{click:function(t){return e.showExtraDate(i)}},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"extraItem.value"}})],1):e._e()])})),e.indexChannel.isVerify?t("div",[t("van-cell",{attrs:{title:e.indexChannel.verifyName?e.indexChannel.verifyName:"审核材料",required:"",rules:[{required:!0,message:"请上传"+(e.indexChannel.verifyName?e.indexChannel.verifyName:"审核材料")}]}},[t("van-uploader",{attrs:{"after-read":e.afterRead,name:"credit","before-read":e.beforeRead,accept:"image/*"}},[e.userInfo.credit?t("van-image",{attrs:{height:"50px",src:e.userInfo.credit,fit:"contain"}}):t("van-icon",{staticStyle:{"margin-left":"5px"},attrs:{slot:"default",name:"add-o",size:"50px"},slot:"default"})],1)],1)],1):e._e(),e.indexChannel.isInvoice?t("div",[t("div",{staticClass:"nav-title",staticStyle:{"margin-top":"8px"}},[t("div",{staticClass:"color"}),t("div",{staticClass:"text"},[e._v("发票信息填写")])]),t("van-cell-group",[t("van-field",{attrs:{required:"",label:"发票抬头",placeholder:"请输入发票抬头",clearable:"",rules:[{required:!0,message:"请填写发票抬头"}]},on:{clear:e.cancel,input:e.autoCompleteList},model:{value:e.userInfo.invoiceName,callback:function(t){e.$set(e.userInfo,"invoiceName",t)},expression:"userInfo.invoiceName"}}),t("van-cell",{directives:[{name:"show",rawName:"v-show",value:e.autoCompleteListView,expression:"autoCompleteListView"}],staticStyle:{height:"62px"},attrs:{title:""}},[e._l(e.invoicelist,(function(i){return t("div",{key:i.id,attrs:{title:i.invoiceName,clearable:""},on:{click:function(t){return e.invoiceChoose(i)}}},[t("div",{staticStyle:{flex:"3"}}),t("div",{staticStyle:{flex:"6",height:"23px",overflow:"hidden","line-height":"22px"}},[e._v(e._s(i.invoiceName))]),t("div",{staticStyle:{"font-size":"12px",color:"#949494","line-height":"17px"}},[e._v(e._s(i.invoiceCode))])])}))],2),t("van-field",{attrs:{label:"纳税人识别号",clearable:"",required:"",placeholder:"请输入纳税人识别号",rules:[{required:!0,message:"请输入纳税人识别号"}]},model:{value:e.userInfo.invoiceCode,callback:function(t){e.$set(e.userInfo,"invoiceCode",t)},expression:"userInfo.invoiceCode"}}),t("van-field",{attrs:{label:"注册地址(专票)",clearable:"",placeholder:"请输入注册地址(专票)"},model:{value:e.userInfo.invoiceAddress,callback:function(t){e.$set(e.userInfo,"invoiceAddress",t)},expression:"userInfo.invoiceAddress"}}),t("van-field",{attrs:{label:"注册电话(专票)",clearable:"",placeholder:"请输入注册电话(专票)"},model:{value:e.userInfo.invoiceMobile,callback:function(t){e.$set(e.userInfo,"invoiceMobile",t)},expression:"userInfo.invoiceMobile"}}),t("van-field",{attrs:{label:"开户银行(专票)",clearable:"",placeholder:"请输入开户银行(专票)"},model:{value:e.userInfo.invoiceBank,callback:function(t){e.$set(e.userInfo,"invoiceBank",t)},expression:"userInfo.invoiceBank"}}),t("van-field",{attrs:{label:"银行账户(专票)",clearable:"",placeholder:"请输入银行账户(专票)"},model:{value:e.userInfo.invoiceAccount,callback:function(t){e.$set(e.userInfo,"invoiceAccount",t)},expression:"userInfo.invoiceAccount"}})],1)],1):e._e(),t("div",{staticStyle:{margin:"16px",display:"flex"}},[t("van-button",{staticStyle:{flex:"1"},attrs:{round:"",block:"",type:"danger"},on:{click:e.cancelApply}},[e._v("取消报名")]),t("van-button",{staticStyle:{flex:"1","margin-left":"10px"},attrs:{round:"",block:"",type:"info","native-type":"submit"},on:{click:e.onSubmit}},[e._v("提交")])],1)],2),t("van-popup",{style:{height:"45%"},attrs:{closeable:"",position:"bottom"},model:{value:e.radioVisible,callback:function(t){e.radioVisible=t},expression:"radioVisible"}},[t("div",{staticClass:"popup-title"},[e._v(e._s(e.chooseResult.finalName))]),t("div",{staticStyle:{"padding-top":"5px"}},[t("van-radio-group",{model:{value:e.userInfo[e.chooseResult.applyConfigFieldName],callback:function(t){e.$set(e.userInfo,e.chooseResult.applyConfigFieldName,t)},expression:"userInfo[chooseResult.applyConfigFieldName]"}},[t("van-cell-group",e._l(e.chooseResult.selectData,(function(i){return t("van-cell",{key:i,attrs:{title:i,clickable:""},on:{click:function(t){return e.choose(i)}},scopedSlots:e._u([{key:"right-icon",fn:function(){return[t("van-radio",{attrs:{name:i}})]},proxy:!0}],null,!0)})})),1)],1)],1)]),t("van-popup",{style:{height:"45%"},attrs:{closeable:"","close-icon":"http://mpjoy.oss-cn-beijing.aliyuncs.com/20211108/186452e1a5de47759e81e0c439343da3.png",position:"bottom"},on:{"click-close-icon":e.chooseConfirm},model:{value:e.checkBoxVisible,callback:function(t){e.checkBoxVisible=t},expression:"checkBoxVisible"}},[t("div",{staticClass:"popup-title"},[e._v(e._s(e.chooseResult.finalName))]),t("div",{staticStyle:{"padding-top":"5px"}},[t("van-checkbox-group",{attrs:{change:"onChange"},model:{value:e.checkBoxResult,callback:function(t){e.checkBoxResult=t},expression:"checkBoxResult"}},[t("van-cell-group",e._l(e.chooseResult.selectData,(function(i,a){return t("van-cell",{key:i,attrs:{title:i,clickable:"","data-index":a},on:{click:e.toggle},scopedSlots:e._u([{key:"right-icon",fn:function(){return[t("van-checkbox",{ref:"checkboxes",refInFor:!0,attrs:{name:i}})]},proxy:!0}],null,!0)})})),1)],1)],1)]),t("van-popup",{style:{height:"45%"},attrs:{closeable:"",position:"bottom"},model:{value:e.extraRadioVisible,callback:function(t){e.extraRadioVisible=t},expression:"extraRadioVisible"}},[t("div",{staticClass:"popup-title"},[e._v(e._s(e.extraChooseResult.finalName))]),t("div",{staticStyle:{"padding-top":"5px"}},[t("van-radio-group",{model:{value:e.extraChooseResult.value,callback:function(t){e.$set(e.extraChooseResult,"value",t)},expression:"extraChooseResult.value"}},[t("van-cell-group",e._l(e.extraChooseResult.selectData,(function(i){return t("van-cell",{key:i,attrs:{title:i,clickable:""},on:{click:function(t){return e.chooseExtra(i)}},scopedSlots:e._u([{key:"right-icon",fn:function(){return[t("van-radio",{attrs:{name:i}})]},proxy:!0}],null,!0)})})),1)],1)],1)]),t("van-popup",{style:{height:"45%"},attrs:{closeable:"","close-icon":"http://mpjoy.oss-cn-beijing.aliyuncs.com/20211108/186452e1a5de47759e81e0c439343da3.png",position:"bottom"},on:{"click-close-icon":e.chooseExtraConfirm},model:{value:e.extraCheckBoxVisible,callback:function(t){e.extraCheckBoxVisible=t},expression:"extraCheckBoxVisible"}},[t("div",{staticClass:"popup-title"},[e._v(e._s(e.extraChooseResult.finalName))]),t("div",{staticStyle:{"padding-top":"5px"}},[t("van-checkbox-group",{attrs:{change:"onChangeExtra"},model:{value:e.extraCheckBoxResult,callback:function(t){e.extraCheckBoxResult=t},expression:"extraCheckBoxResult"}},[t("van-cell-group",e._l(e.extraChooseResult.selectData,(function(i,a){return t("van-cell",{key:i,attrs:{title:i,clickable:"","data-index":a},on:{click:e.toggleExtra},scopedSlots:e._u([{key:"right-icon",fn:function(){return[t("van-checkbox",{ref:"checkboxesExtra",refInFor:!0,attrs:{name:i}})]},proxy:!0}],null,!0)})})),1)],1)],1)]),t("van-calendar",{attrs:{"min-date":e.minDate,"max-date":e.maxDate},on:{confirm:e.dateSelect},model:{value:e.dateVisible,callback:function(t){e.dateVisible=t},expression:"dateVisible"}}),t("van-calendar",{attrs:{"min-date":e.minDate,"max-date":e.maxDate},on:{confirm:e.dateExtraSelect},model:{value:e.extraDateVisible,callback:function(t){e.extraDateVisible=t},expression:"extraDateVisible"}}),t("van-popup",{style:{height:"45%"},attrs:{position:"bottom"},model:{value:e.areaVisible,callback:function(t){e.areaVisible=t},expression:"areaVisible"}},[t("van-area",{attrs:{title:"省市区选择","area-list":e.areaList,value:e.areaCode},on:{cancel:function(t){e.areaVisible=!1},confirm:e.areaSelect}})],1)],1)},n=[],o=(i("6b54"),i("a481"),i("28a5"),i("ac6a"),i("cacf"),i("434d")),s=i("66c7"),l={components:{},data:function(){return{minDate:new Date(2023,0,1),maxDate:new Date(2028,0,31),dateVisible:!1,extraDateVisible:!1,autoCompleteListView:!1,invoicelist:[],activityId:void 0,channelId:void 0,areaVisible:!1,areaCode:"110101",isPay:0,userInfo:{invoiceName:"",invoiceCode:"",invoiceBank:"",invoiceAddress:"",invoiceMobile:"",invoiceAccount:""},channelList:[],applyActivityConfigList:[],extraResult:[],channelVisible:!1,radioVisible:!1,checkBoxVisible:!1,indexChannel:{},chooseResult:{},checkBoxResult:[],extraRadioVisible:!1,extraCheckBoxVisible:!1,extraChooseResult:{},extraCheckBoxResult:[],areaList:o["a"]}},mounted:function(){document.title="报名信息填写",this.activityId=this.$route.query.id,this.getUserActivityInfo()},methods:{showDate:function(e){this.chooseResult=e,this.dateVisible=!0},showExtraDate:function(e){this.extraChooseResult=e,this.extraDateVisible=!0},dateSelect:function(e){this.$set(this.userInfo,this.chooseResult.applyConfigFieldName,s["a"].formatDate.format(new Date(e),"yyyy/MM/dd")),this.dateVisible=!1},dateExtraSelect:function(e){var t=this;console.log(e),this.extraResult.forEach((function(i){i.finalName==t.extraChooseResult.finalName&&(i.value=s["a"].formatDate.format(new Date(e),"yyyy/MM/dd"))})),this.extraDateVisible=!1},autoCompleteList:function(){var e=this;this.userInfo.invoiceName&&this.$fly.get("/pyp/web/invoice/findByInvoiceName/",{invoiceName:this.userInfo.invoiceName}).then((function(t){200==t.code&&(0!=t.result.length?(e.invoicelist=t.result,e.autoCompleteListView=!0):e.invoicelist=[])}))},invoiceChoose:function(e){this.userInfo.invoiceName=e.invoiceName,this.userInfo.invoiceCode=e.invoiceCode,this.userInfo.invoiceAccount=e.invoiceAccount,this.userInfo.invoiceBank=e.invoiceBank,this.userInfo.invoiceAddress=e.invoiceAddress,this.userInfo.invoiceMobile=e.invoiceMobile,this.autoCompleteListView=!1},cancel:function(){this.autoCompleteListView=!1,this.invoicelist=[]},getUserActivityInfo:function(){var e=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/applyUserInfo/".concat(this.activityId)).then((function(t){200==t.code?(e.userInfo=t.activityUserEntity,e.userInfo.applyActivityChannelConfigId&&e.getApplyActivityChannelConfig(e.userInfo.applyActivityChannelConfigId),e.applyActivityConfigList=t.applyActivityConfigEntities,e.extraResult=t.extraConfig,e.applyActivityConfigList.forEach((function(e){(1==e.type||2==e.type)&&(e.selectData=e.selectData.replace(/，/gi,",").split(","))})),e.extraResult&&(console.log(1),e.extraResult.forEach((function(e){(1==e.type||2==e.type)&&(e.selectData=e.selectData.replace(/，/gi,",").split(","))})))):vant.Toast(t.msg)}))},getApplyActivityChannelConfig:function(e){var t=this;this.$fly.get("/pyp/apply/applyactivitychannelconfig/info/".concat(e)).then((function(e){200==e.code&&(t.indexChannel=e.applyActivityChannelConfig)}))},chooseChannel:function(e){this.channelId=e,this.getApplyActivityConfig()},choose:function(e){this.$set(this.userInfo,this.chooseResult.applyConfigFieldName,e),this.radioVisible=!1},showRadio:function(e){this.chooseResult=e,this.radioVisible=!0},onChange:function(e){this.setData({checkBoxResult:e.detail})},toggle:function(e){var t=e.currentTarget.dataset.index;this.$refs.checkboxes[t].toggle()},chooseConfirm:function(){var e="";if(this.checkBoxResult.length>1)for(var t=0;t<this.checkBoxResult.length;t++)t===this.checkBoxResult.length-1?e+=this.checkBoxResult[t]:e=e+this.checkBoxResult[t]+",";else e=this.checkBoxResult[0];this.$set(this.userInfo,this.chooseResult.applyConfigFieldName,e),this.checkBoxVisible=!1},showCheckBox:function(e){this.chooseResult=e;var t=this.userInfo[this.chooseResult.applyConfigFieldName];this.checkBoxResult=t?t.split(","):[],this.checkBoxVisible=!0},chooseExtra:function(e){var t=this;this.extraResult.forEach((function(i){i.finalName==t.extraChooseResult.finalName&&(i.value=e)})),this.extraRadioVisible=!1},showExtraRadio:function(e){this.extraChooseResult=e,this.extraRadioVisible=!0},onChangeExtra:function(e){this.setData({extraCheckBoxResult:e.detail})},toggleExtra:function(e){var t=e.currentTarget.dataset.index;this.$refs.checkboxesExtra[t].toggle()},chooseExtraConfirm:function(){var e=this,t="";if(this.extraCheckBoxResult.length>1)for(var i=0;i<this.extraCheckBoxResult.length;i++)i===this.extraCheckBoxResult.length-1?t+=this.extraCheckBoxResult[i]:t=t+this.extraCheckBoxResult[i]+",";else t=this.extraCheckBoxResult[0];this.extraResult.forEach((function(i){i.finalName==e.extraChooseResult.finalName&&(i.value=t)})),this.extraRadioVisible=!1},showExtraCheckBox:function(e){this.extraChooseResult=e;var t=e.value;this.extraCheckBoxResult=t?t.split(","):[],this.extraCheckBoxVisible=!0},showArea:function(e){this.chooseResult=e,this.areaCode=this.userInfo.area?this.userInfo.area.split(",")[2]:"110101",this.areaVisible=!0},areaSelect:function(e){console.log(e);var t=e[0].code+","+e[1].code+","+e[2].code,i=e[0].name+","+e[1].name+","+e[2].name;this.areaCode=e[2].code,this.$set(this.userInfo,this.chooseResult.applyConfigFieldName,t),this.$set(this.userInfo,"areaName",i),this.areaVisible=!1},getApplyActivityConfig:function(){var e=this;this.$fly.get("/pyp/apply/applyactivityconfig/findByChannelId/".concat(this.channelId)).then((function(t){200==t.code?(e.applyActivityConfigList=t.result,e.applyActivityConfigList.forEach((function(t){1==t.type||2==t.type?t.selectData=t.selectData.replace(/，/gi,",").split(","):3==t.type&&(e.extraResult=JSON.parse(t.extra),e.extraResult.forEach((function(e){(1==e.type||2==e.type)&&(e.selectData=e.selectData.replace(/，/gi,",").split(","))})))}))):vant.Toast(t.msg)}))},onSubmit:function(){var e=this;this.applyActivityConfigList.forEach((function(t){3==t.type||!t.required||e.userInfo[t.applyConfigFieldName]||vant.Toast("请输入"+t.finalName)})),this.extraResult&&this.extraResult.forEach((function(e){e.selectData=e.selectData?e.selectData.toString():null,3==e.type||!e.required||e.value||vant.Toast("请输入"+e.finalName)})),this.userInfo.activityId=this.activityId,this.userInfo.applyActivityChannelConfigId=this.channelId,this.userInfo.applyExtraVos=this.extraResult,this.$fly.post("/pyp/activity/activityuser/update",this.userInfo).then((function(t){t&&200===t.code?(vant.Toast("更新成功"),e.$router.go(-1)):vant.Toast(t.msg)}))},afterRead:function(e,t){var i=this,a=t.name;e.status="uploading",e.message="上传中...";var n=new FormData;n.append("file",e.file),this.$fly.post("/pyp/web/upload",n).then((function(e){e&&200===e.code&&i.$set(i.userInfo,a,e.result)}))},beforeRead:function(e){return!0},cancelApply:function(){var e=this;vant.Dialog.confirm({title:"提示",message:"确认取消报名?"}).then((function(){e.$fly.post("/pyp/web/activity/activityuserapplyorder/cancelOrder",{id:e.userInfo.id}).then((function(t){t&&200===t.code?(vant.Toast("取消成功"),e.$router.go(-1)):vant.Toast(t.msg)}))})).catch((function(){}))}}},c=l,r=(i("6529"),i("2877")),u=Object(r["a"])(c,a,n,!1,null,null,null);t["default"]=u.exports},6529:function(e,t,i){"use strict";i("db6c")},"66c7":function(e,t,i){"use strict";i("4917"),i("a481");var a=/([yMdhsm])(\1*)/g,n="yyyy-MM-dd";function o(e,t){t-=(e+"").length;for(var i=0;i<t;i++)e="0"+e;return e}t["a"]={formatDate:{format:function(e,t){return t=t||n,t.replace(a,(function(t){switch(t.charAt(0)){case"y":return o(e.getFullYear(),t.length);case"M":return o(e.getMonth()+1,t.length);case"d":return o(e.getDate(),t.length);case"w":return e.getDay()+1;case"h":return o(e.getHours(),t.length);case"m":return o(e.getMinutes(),t.length);case"s":return o(e.getSeconds(),t.length)}}))},parse:function(e,t){var i=t.match(a),n=e.match(/(\d)+/g);if(i.length==n.length){for(var o=new Date(1970,0,1),s=0;s<i.length;s++){var l=parseInt(n[s]),c=i[s];switch(c.charAt(0)){case"y":o.setFullYear(l);break;case"M":o.setMonth(l-1);break;case"d":o.setDate(l);break;case"h":o.setHours(l);break;case"m":o.setMinutes(l);break;case"s":o.setSeconds(l);break}}return o}return null},toWeek:function(e){var t=new Date(e).getDay(),i="";switch(t){case 0:i="s";break;case 1:i="m";break;case 2:i="t";break;case 3:i="w";break;case 4:i="t";break;case 5:i="f";break;case 6:i="s";break}return i}},toUserLook:function(e){var t=Math.floor(e/3600%24),i=Math.floor(e/60%60);return t<1?i+"分":t+"时"+i+"分"}}},ac6a:function(e,t,i){for(var a=i("cadf"),n=i("0d58"),o=i("2aba"),s=i("7726"),l=i("32e9"),c=i("84f2"),r=i("2b4c"),u=r("iterator"),d=r("toStringTag"),f=c.Array,h={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},p=n(h),v=0;v<p.length;v++){var m,x=p[v],y=h[x],g=s[x],b=g&&g.prototype;if(b&&(b[u]||l(b,u,f),b[d]||l(b,d,x),c[x]=f,y))for(m in a)b[m]||o(b,m,a[m],!0)}},db6c:function(e,t,i){}}]);
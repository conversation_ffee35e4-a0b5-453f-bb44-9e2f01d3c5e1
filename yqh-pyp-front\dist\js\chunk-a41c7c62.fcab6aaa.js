(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a41c7c62","chunk-37a545c8"],{"1b69":function(t,i,e){"use strict";e.r(i);e("7f7f");var n,o=function(){var t=this,i=t._self._c;return i("div",{staticStyle:{background:"white","margin-bottom":"20px"}},[i("van-row",{attrs:{gutter:"20"}},[i("van-col",{attrs:{span:"16"}},[i("van-swipe",{staticStyle:{width:"100%"},attrs:{autoplay:3e3}},t._l(t.activityInfo.appFileList,(function(t,e){return i("van-swipe-item",{key:e},[i("van-image",{attrs:{width:"100%",src:t.url}})],1)})),1)],1),i("van-col",{attrs:{span:"8"}},[i("div",{staticStyle:{"margin-top":"20px"}},[i("van-cell",{attrs:{title:"会议名称",value:t.activityInfo.name}}),t.activityInfo.address?i("van-cell",{attrs:{title:"会议地点",value:t.activityInfo.address}}):t._e(),i("van-cell",{attrs:{title:"会议开始时间",value:t.activityInfo.startTime}}),i("van-cell",{attrs:{title:"会议结束时间",value:t.activityInfo.endTime}}),i("van-cell",{attrs:{title:"登录信息"},scopedSlots:t._u([{key:"default",fn:function(){return[t.userInfo?i("van-button",{attrs:{type:"primary",size:"small",round:""}},[t._v(t._s(t.userInfo.username)+"("+t._s(t.userInfo.mobile)+")")]):i("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.showLogin}},[t._v("未登录(点击登录)")])]},proxy:!0}])}),"1736999159118508033"!=t.activityId?i("van-cell",{attrs:{title:"报名状态"},scopedSlots:t._u([{key:"default",fn:function(){return[1==t.isPay?i("van-button",{attrs:{type:"primary",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名")]):2==t.isPay?i("van-button",{attrs:{type:"warning",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名未交费")]):i("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.turnApply}},[t._v("未报名")])]},proxy:!0}],null,!1,567203517)}):t._e()],1)])],1),i("van-tabs",{staticClass:"nav",on:{click:t.onClick},model:{value:t.cmsId,callback:function(i){t.cmsId=i},expression:"cmsId"}},t._l(t.cmsList,(function(t){return i("van-tab",{key:t.id,attrs:{title:t.title,name:t.id}})})),1),i("pclogin")],1)},a=[],s=e("ade3"),c=(e("a481"),e("6762"),e("2fdb"),e("cacf")),r=e("7dcb"),l=function(){var t=this,i=t._self._c;return i("van-dialog",{attrs:{title:"登录操作",width:"400px"},on:{confirm:t.login},model:{value:t.showPcLogin,callback:function(i){t.showPcLogin=i},expression:"showPcLogin"}},[i("div",{staticClass:"text-center padding"},[i("van-cell-group",{attrs:{inset:""}},[i("van-field",{attrs:{name:"手机号",label:"手机号",required:"",placeholder:"手机号",rules:[{required:!0,message:"请填写手机号"}]},model:{value:t.mobile,callback:function(i){t.mobile=i},expression:"mobile"}}),"1736999159118508033"!=t.activityId?i("van-field",{attrs:{center:"",clearable:"",maxlength:"6",label:"短信验证码",required:"",placeholder:"请输入短信验证码"},scopedSlots:t._u([{key:"button",fn:function(){return[i("van-button",{attrs:{size:"small",type:"primary",disabled:t.waiting},on:{click:function(i){return t.doSendSmsCode()}}},[t.waiting?i("span",[t._v(t._s(t.waitingTime)+"秒后重发")]):i("span",{staticStyle:{"font-size":"13px"}},[t._v("获取验证码")])])]},proxy:!0}],null,!1,850170227),model:{value:t.code,callback:function(i){t.code=i},expression:"code"}}):t._e()],1)],1)])},u=[],d={name:"pclogin",computed:{showPcLogin:{get:function(){return this.$store.state.user.showPcLogin},set:function(t){this.$store.commit("user/changePcLogin",t)}}},data:function(){return{activityId:void 0,waiting:!1,waitingTime:60,mobile:"",code:""}},mounted:function(){this.activityId=this.$route.query.id},methods:{login:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?this.code||0x181b0f883e693000==this.activityId?/^\d{6}$/.test(this.code)||0x181b0f883e693000==this.activityId?void this.$fly.post("/pyp/web/user/pcLogin",{mobile:this.mobile,code:0x181b0f883e693000==this.activityId?"121212":this.code}).then((function(i){i&&200===i.code?(vant.Toast("登录成功"),t.$store.commit("user/update",i.userInfo),t.showPcLogin=!1,"applyIndex"!=t.$route.name&&"livesDetail"!=t.$route.name||location.reload()):vant.Toast(i.msg)})):(vant.Toast("验证码错误"),!1):(vant.Toast("请输入验证码"),!1):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},doSendSmsCode:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.mobile,activityId:this.activityId}).then((function(i){i&&200===i.code?(t.countdown(),vant.Toast("发送验证码成功")):vant.Toast(i.msg)})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var t=this;this.waiting=!0;var i=window.setInterval((function(){t.waitingTime--,t.waitingTime<0&&(window.clearInterval(i),t.waitingTime=60,t.waiting=!1)}),1e3)}}},m=d,v=e("2877"),f=Object(v["a"])(m,l,u,!1,null,null,null),h=f.exports,y={components:{pclogin:h},data:function(){return{activityId:void 0,bannerIndex:0,cmsId:void 0,cmsList:[],cmsInfo:{},activityInfo:{}}},computed:{userInfo:{get:function(){return this.$store.state.user.userInfo},set:function(t){this.$store.commit("user/update",t)}},isPay:{get:function(){return this.$store.state.apply.isPay},set:function(t){this.$store.commit("apply/update",t)}}},mounted:function(){var t=(new Date).getTime();localStorage.setItem("logAddTime",t+36e5),this.openid=this.$cookie.get("openid"),this.activityId=this.$route.query.id,this.cmsId=sessionStorage.getItem("cmsId")||this.$route.query.cmsId,this.checkLogin(),this.getActivityInfo(),this.activityLogCount()},methods:(n={showLogin:function(){this.$store.commit("user/changePcLogin",!0)},checkLogin:function(){var t=this;this.$fly.get("/pyp/web/user/checkLogin").then((function(i){t.userInfo=i.result,t.userInfo?t.checkApply():t.isPay=0}))},bannerIndexChange:function(t){this.bannerIndex=t},checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(i){200==i.code?(t.isPay=i.isPay,1==t.isPay||2==t.isPay&&vant.Dialog.alert({title:"提示",message:"您有一笔注册费待支付，点击跳转"}).then((function(){t.$router.push({name:"applySuccess",query:{orderId:i.result,id:t.activityId}})}))):vant.Toast(i.msg)}))},changeShowType:function(t){this.$emit("changeShowType",t)},turnApply:function(){var t=this.cmsList.filter((function(t){return t.model&&t.model.includes("applyIndex")}));if(t){var i=t[0];sessionStorage.setItem("cmsId",i.id);var e=i.model.replace("${activityId}",i.activityId);this.$router.push(JSON.parse(e))}else vant.Toast("暂未开启报名")},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(i){if(t.loading=!1,200==i.code){t.activityInfo=i.activity,document.title=t.activityInfo.name;var e=t.activityInfo.startTime,n=new Date(e.replace(/-/g,"/")),o=new Date,a=n.getTime()-o.getTime();t.dateCompare=a>0?a:0,t.getCmsList()}}))},activityLogCount:function(){var t=localStorage.getItem("logAddTime"),i=(new Date).getTime();i>t&&this.$fly.post("/pyp/activity/activityviewlog/count",{activityId:this.activityId,device:r["a"].getVersion()}).then((function(t){}))},getCmsList:function(){var t=this;this.$fly.get("/pyp/cms/cms/findByActivityId/".concat(this.activityId)).then((function(i){t.loading=!1,200==i.code?(t.cmsList=i.result,t.cmsId?t.changeShowType(t.cmsId):t.changeShowType(t.cmsList[0].id)):(vant.Toast(i.msg),t.cmsList=[])}))},onClick:function(t){if(console.log(t),sessionStorage.setItem("cmsId",t),this.cmsInfo=this.cmsList.filter((function(i){return i.id==t}))[0],this.cmsInfo.url&&Object(c["d"])(this.cmsInfo.url))location.href=this.cmsInfo.url;else if(this.cmsInfo.model&&this.isJSON(this.cmsInfo.model)){var i=this.cmsInfo.model.replace("${activityId}",this.cmsInfo.activityId);this.$router.push(JSON.parse(i))}else"cmsIndex"==this.$route.name?this.changeShowType(t):this.$router.push({name:"cmsIndex",query:{id:this.activityId,cmsId:t}})}},Object(s["a"])(n,"bannerIndexChange",(function(t){this.bannerIndex=t})),Object(s["a"])(n,"isJSON",(function(t){try{return JSON.parse(t),!0}catch(i){return!1}})),n)},p=y,g=(e("dd7a"),Object(v["a"])(p,o,a,!1,null,"7bd3d808",null));i["default"]=g.exports},"75b7":function(t,i,e){"use strict";e("c5ff")},"7dcb":function(t,i,e){"use strict";e("a481"),e("4917");i["a"]={getVersion:function(){var t=navigator.userAgent||window.navigator.userAgent,i=/HUAWEI|HONOR/gi,e=/[^;]+(?= Build)/gi,n=/CPU iPhone OS \d[_\d]*/gi,o=/CPU OS \d[_\d]*/gi,a=/Windows NT \d[\.\d]*/gi,s=/Linux x\d[_\d]*/gi,c=/Mac OS X \d[_|\.\d]*/gi;return/Android/gi.test(t)?i.test(t)?t.match(i)[0]+t.match(e)[0]:t.match(e)[0]:/iPhone/gi.test(t)?t.match(n)[0].replace(/CPU iPhone OS/gi,"iPhone").replace(/_/g,"."):/iPad/gi.test(t)?t.match(o)[0].replace(/CPU OS/gi,"iPad").replace(/_/g,"."):/Windows/gi.test(t)?"Windows "+(Math.min(parseInt(t.match(a)[0].replace(/Windows NT /,""))+1,10)<7?"XP":Math.min(parseInt(t.match(a)[0].replace(/Windows NT /,""))+1,10)):/Linux/gi.test(t)?t.match(s)[0]:/Macintosh/gi.test(t)?t.match(c)[0].replace(/_/g,"."):"unknown"}}},ade3:function(t,i,e){"use strict";e.d(i,"a",(function(){return s}));var n=e("53ca");function o(t,i){if("object"!==Object(n["a"])(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var o=e.call(t,i||"default");if("object"!==Object(n["a"])(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===i?String:Number)(t)}function a(t){var i=o(t,"string");return"symbol"===Object(n["a"])(i)?i:String(i)}function s(t,i,e){return i=a(i),i in t?Object.defineProperty(t,i,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[i]=e,t}},c5ff:function(t,i,e){},cad8:function(t,i,e){},d8db:function(t,i,e){"use strict";e.r(i);e("7f7f"),e("6762"),e("2fdb");var n=function(){var t=this,i=t._self._c;return i("div",{staticClass:"pc-container"},[i("pcheader",{on:{changeShowType:t.changeShowType}}),i("div",{staticClass:"content",domProps:{innerHTML:t._s(t.cmsInfo.content||t.cmsInfo.mobileContent)}}),t.activityInfo.type&&t.activityInfo.type.includes("2")&&t.activityBottom[2]?i("div",{staticClass:"download",staticStyle:{"margin-top":"10px"}},[i("van-collapse",{model:{value:t.downActive,callback:function(i){t.downActive=i},expression:"downActive"}},[i("van-collapse-item",{attrs:{title:"文件下载",name:"0"}},t._l(t.activityBottom[2],(function(e){return i("div",{key:e.id},[2==e.type?i("van-cell",{attrs:{title:e.name,value:"点击下载"},on:{click:function(i){return t.download(e.url)}}}):t._e()],1)})),0)],1)],1):t._e(),t.activityInfo.type&&t.activityInfo.type.includes("1")&&t.activityBottom[1]?i("div",{staticStyle:{"margin-top":"10px"}},[i("van-swipe",{attrs:{autoplay:3e3}},t._l(t.activityBottom[1],(function(e,n){return i("div",{key:n},[1==e.type?i("van-swipe-item",[i("van-image",{attrs:{width:"100%",height:"100px",src:e.name},on:{click:function(i){return t.openUrl(t.item.url)}}})],1):t._e()],1)})),0)],1):t._e(),t.activityInfo.type&&t.activityInfo.type.includes("0")&&t.activityBottom[0]?i("div",{staticClass:"bottomdiy",style:{background:t.activityInfo.bottomColor}},t._l(t.activityBottom[0],(function(e,n){return i("div",{key:n},[0==e.type?i("a",{staticClass:"item",attrs:{href:e.url}},[t._v(t._s(e.name))]):t._e()])})),0):t._e()],1)},o=[],a=e("1b69"),s={components:{pcheader:a["default"]},data:function(){return{baseUrl:"",openid:void 0,activityId:void 0,downActive:["0"],cmsList:[],showType:0,activityBottom:{},bannerIndex:0,activityInfo:{},dateCompare:0,cmsId:void 0,cmsInfo:{}}},mounted:function(){this.baseUrl=window.location.origin+window.location.pathname,this.openid=this.$cookie.get("openid"),this.activityId=this.$route.query.id,this.cmsId=this.$route.query.cmsId},methods:{getActivityBottom:function(){var t=this;this.$fly.get("/pyp/web/activity/activitybottom/findByActivity/".concat(this.activityId),{type:this.activityInfo.type}).then((function(i){200==i.code&&(t.activityBottom=i.result)}))},bannerIndexChange:function(t){this.bannerIndex=t},isJSON:function(t){try{return JSON.parse(t),!0}catch(i){return!1}},download:function(t){window.open(t)},openUrl:function(t){t&&window.open(t)},changeShowType:function(t){this.cmsId=t,this.getCmsInfo()},getCmsInfo:function(){var t=this;this.$fly.get("/pyp/cms/cms/info/".concat(this.cmsId)).then((function(i){200==i.code?(t.cmsInfo=i.cms,document.title=t.cmsInfo.title):(vant.Toast(i.msg),t.cmsInfo={})}))}}},c=s,r=(e("75b7"),e("2877")),l=Object(r["a"])(c,n,o,!1,null,"6522f336",null);i["default"]=l.exports},dd7a:function(t,i,e){"use strict";e("cad8")}}]);
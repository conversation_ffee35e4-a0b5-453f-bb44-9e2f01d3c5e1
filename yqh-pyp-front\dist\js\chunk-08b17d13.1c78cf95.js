(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-08b17d13"],{2909:function(t,e,i){"use strict";function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,s=new Array(e);i<e;i++)s[i]=t[i];return s}function n(t){if(Array.isArray(t))return s(t)}function a(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function o(t,e){if(t){if("string"===typeof t)return s(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?s(t,e):void 0}}function r(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(t){return n(t)||a(t)||o(t)||r()}i.d(e,"a",(function(){return c}))},a153:function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t._self._c;return e("div",[e("div",{ref:"message-list",staticClass:"message-list"},[e("van-pull-refresh",{attrs:{disabled:t.finished},on:{refresh:t.getChatMsg},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},t._l(t.chatMsg,(function(i){return e("div",{key:i.uuid},[i.isBack?t._e():e("div",{staticClass:"message-box"},[e("img",{staticClass:"message-img",attrs:{src:i.avatar},on:{click:function(e){return t.taboo(i)},error:function(e){return t.imgError(i)}}}),e("div",{staticClass:"message-item"},[e("div",{staticStyle:{display:"flex","align-items":"center","margin-bottom":"4px"}},[e("div",{staticClass:"message-nick"},[t._v(t._s(i.username))]),i.roleId&&1==i.roleId?e("van-tag",{staticStyle:{"margin-left":"5px"},attrs:{type:"success"}},[t._v("管理员")]):t._e(),e("div",{staticClass:"message-date"},[t._v(t._s(i.createOn))])],1),e("div",{staticClass:"message-container",on:{click:function(e){return t.messageHandler(i)}}},[e("div",{staticClass:"triangle"}),["img"==i.type?e("img",{attrs:{src:i.msg,width:"100px",height:"100px"}}):e("span",{staticClass:"message-text"},[t._v(t._s(i.msg))])]],2)])])])})),0)],1),e("van-field",{staticClass:"tabbar",attrs:{type:"textarea",rows:"1",autosize:t.height,center:"",placeholder:"我也要发言"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[e("div",{staticStyle:{display:"flex"}},[e("van-icon",{attrs:{name:"smile-o"},on:{click:function(e){t.emojiShow=!t.emojiShow}}}),e("van-uploader",{attrs:{"after-read":t.afterRead,"before-read":t.beforeRead,accept:"image/*"}},[e("van-icon",{staticStyle:{"margin-left":"5px"},attrs:{slot:"default",name:"photo-o"},slot:"default"})],1)],1)]},proxy:!0},{key:"button",fn:function(){return[e("van-button",{ref:"mobileInput",staticClass:"send",attrs:{id:"sendInput"},on:{click:t.sendTextMessage}},[t._v("发送")])]},proxy:!0}]),model:{value:t.messageContent,callback:function(e){t.messageContent=e},expression:"messageContent"}}),e("div",{directives:[{name:"show",rawName:"v-show",value:t.emojiShow,expression:"emojiShow"}],staticClass:"emojis"},t._l(t.emoji,(function(i){return e("div",{key:i,staticClass:"emoji",on:{click:function(e){return t.chooseEmoji(i)}}},[t._v("\n      "+t._s(i)+"\n    ")])})),0)],1)},n=[],a=(i("96cf"),i("1da1")),o=(i("6762"),i("2fdb"),i("ac6a"),i("2909")),r=["😀","😂","😃","😄","😅","😆","😉","😊","😋","😎","😍","😘","😗","😚","😇","😐","😑","😶","😏","😣","😥","😮","😯","😪","😫","😴","😌","😛","😜","😝","😒","😓","😔","😕","😲","😷","😖","😞","😟","😤","😢","😭","😦","😧","😨","😬","😰","😱","😳","😵","😡","😠","🍇","🍈","🍉","🍊","🍋","🍌","🍍","🍎","🍏","🍐","🍑","🍒","🍓","🍅","🍆","🌽","🍄","🐁","🐂","🐅","🐇","🐉","🐍","🐎","🐐","🐒","🐓","🐕","🐖","♈","♉","♊","♋","♌","♍","♎","♏","♐","♑","♒","♓","⛎","🌰","🍞","🍖","🍗","🍔","🍟","🍕","🍳","🍲","🍱","🍘","🍙","🍚","🍛","🍜","🍝","🍠","🍢","🍣","🍤","🍥","🍡","🍦","🍧","🍨","🍩","🍪","🎂","🍰","🍫","🍬","🍭","🍮","🍯","🍼","☕","🍵","🍶","🍷","🍸","🍹","🍺","🍻","🍴","🎪","🎭","🎨","🌹","🍀","🍎","💰","📱","🌙","🍁","🍂","🍃","🌷","💎","🔪","🔫","🏀","⚽","⚡","👄","👍","🔥","👦","👧","👨","👩","👴","👵","👶","👱","👮","👲","👳","👷","👸","💂","🎅","👰","👼","💆","💇","🙍","🙎","🙅","🙆","💁","🙋","🙇","🙌","🙏","👤","👥","🚶","🏃","👯","💃","👫","👬","👭","💏","💑","👪","💪","👈","👉","☝","👆","👇","✌","✋","👌","👍","👎","✊","👊","👋","👏","👐","🌍","🌎","🌏","🌐","🌑","🌒","🌓","🌔","🌕","🌖","🌗","🌘","🌙","🌚","🌛","🌜","☀","🌝","🌞","⭐","🌟","🌠","☁","⛅","☔","⚡","❄","🔥","💧","🌊","🚂","🚃","🚄","🚅","🚆","🚇","🚈","🚉","🚊","🚝","🚞","🚋","🚌","🚍","🚎","🚏","🚐","🚑","🚒","🚓","🚔","🚕","🚖","🚗","🚘","🚚","🚛","🚜","🚲","⛽","🚨","🚥","🚦","🚧","⚓","⛵","🚣","🚤","🚢","✈","💺","🚁","🚟","🚠","🚡","🚀"],c={data:function(){return{emoji:Object(o["a"])(r),actions:[{name:"撤回"},{name:"预览图片"}],emojiShow:!1,showActionSheet:!1,timer:"",isShowScrollButtomTips:!1,preScrollHeight:0,loading:!1,finished:!1,dateCompare:0,chatMsg:[],uuid:[],pageIndex:2,pageSize:5,totalPage:0,height:{maxHeight:75},messageContent:"",indexMessage:{}}},props:["pushKey","activityId"],mounted:function(){var t=this;this.firstGetChatMsg(),window.addEventListener("beforeunload",this.clear),this.timer=setInterval((function(){console.log("---------------------定时器执行---------------------"),t.refreshMsg()}),1e4)},updated:function(){this.keepMessageListOnButtom()},beforeDestroy:function(){},methods:{imgError:function(t){},getChatMsg:function(){var t=this;this.$fly.get("/pyp/web/chat/list",{page:this.pageIndex,limit:this.pageSize,pushKey:this.pushKey}).then((function(e){200==e.code?e.list&&e.list.length>0?(e.list.forEach((function(e){t.uuid.includes(e.uuid)||(t.chatMsg.unshift(e),t.uuid.push(e.uuid))})),t.totalPage=e.totalPage,t.pageIndex++,t.loading=!1,t.totalPage<t.pageIndex?t.finished=!0:t.finished=!1):t.finished=!0:(t.chatMsg=[],t.totalPage=0,t.finished=!0)}))},firstGetChatMsg:function(){var t=this;this.$fly.get("/pyp/web/chat/list",{page:1,limit:5,pushKey:this.pushKey}).then((function(e){200==e.code?e.list&&e.list.length>0?e.list.forEach((function(e){t.uuid.includes(e.uuid)||(t.chatMsg.unshift(e),t.uuid.push(e.uuid))})):t.finished=!0:(t.chatMsg=[],t.finished=!0)}))},refreshMsg:function(){var t=this;this.$fly.get("/pyp/web/chat/list",{page:1,limit:5,pushKey:this.pushKey}).then((function(e){200==e.code&&e.list&&e.list.length>0?e.list.forEach((function(e){t.uuid.includes(e.uuid)||(t.chatMsg.push(e),t.uuid.push(e.uuid))})):(t.chatMsg=[],t.finished=!0)}))},handleLine:function(){this.messageContent+="\n"},handleEnter:function(){this.sendTextMessage()},sendTextMessage:function(){var t=this;if(window.scroll(0,0),""===this.messageContent||0===this.messageContent.trim().length)return this.messageContent="",vant.Toast("不能发送空消息"),!1;this.$fly.get("/pyp/web/chat/send",{pushKey:this.pushKey,msg:this.messageContent,activityId:this.activityId}).then((function(e){200==e.code&&(t.emojiShow=!1,t.refreshMsg(),t.$nextTick((function(){var e=t.$refs["message-list"];console.log(e),e.scrollTop=e.scrollHeight})))})),this.messageContent=""},keepMessageListOnButtom:function(){var t=this.$refs["message-list"];t&&(this.preScrollHeight-t.clientHeight-t.scrollTop<20?(this.$nextTick((function(){t.scrollTop=t.scrollHeight+60})),this.isShowScrollButtomTips=!1):this.isShowScrollButtomTips=!0,this.preScrollHeight=t.scrollHeight)},clear:function(){var t=Object(a["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:this.timer=clearInterval(this.timer);case 1:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),select:function(t){0==t?this.back(this.indexMessage.index):1==t&&vant.ImagePreview({images:[this.indexMessage.msg],closeable:!0})},messageHandler:function(t){this.indexMessage=t,"img"==t.type?this.$emit("showImage"):this.back(t.index)},back:function(t){var e=this;vant.Dialog.confirm({title:"提示",message:"确认撤回消息?"}).then((function(){e.$fly.get("/pyp/web/chat/back",{index:t,pushKey:e.pushKey,activityId:e.activityId}).then((function(t){t&&200===t.code?(vant.Toast("撤回成功"),e.chatMsg=[],e.uuid=[],e.pageIndex=1,e.finished=!1,e.refreshMsg()):vant.Toast(t.msg)}))})).catch((function(){}))},taboo:function(t){var e=this;vant.Dialog.confirm({title:"提示",message:"确认禁言该用户?"}).then((function(){e.$fly.get("/pyp/web/chat/taboo",{userId:t.userId,activityId:e.activityId,pushKey:e.pushKey}).then((function(t){t&&200===t.code?(vant.Toast("禁言成功"),e.chatMsg=[],e.uuid=[],e.pageIndex=1,e.finished=!1,e.refreshMsg()):vant.Toast(t.msg)}))})).catch((function(){}))},chooseEmoji:function(t){this.messageContent+=t},afterRead:function(t){var e=this;t.status="uploading",t.message="上传中...";var i=new FormData;i.append("pushKey",this.pushKey),i.append("activityId",this.activityId),i.append("file",t.file),this.$fly.post("/pyp/web/chat/sendImage",i).then((function(t){t&&200===t.code&&(e.refreshMsg(),e.$nextTick((function(){var t=e.$refs["message-list"];console.log(t),t.scrollTop=t.scrollHeight})))}))},beforeRead:function(t){return!0}},destroyed:function(){var t=Object(a["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:console.log("destroyed"),this.clear();case 2:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()},l=c,u=(i("af0b"),i("2877")),h=Object(u["a"])(l,s,n,!1,null,"1ecb2ec0",null);e["default"]=h.exports},ac6a:function(t,e,i){for(var s=i("cadf"),n=i("0d58"),a=i("2aba"),o=i("7726"),r=i("32e9"),c=i("84f2"),l=i("2b4c"),u=l("iterator"),h=l("toStringTag"),f=c.Array,d={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},g=n(d),p=0;p<g.length;p++){var m,y=g[p],v=d[y],b=o[y],S=b&&b.prototype;if(S&&(S[u]||r(S,u,f),S[h]||r(S,h,y),c[y]=f,v))for(m in s)S[m]||a(S,m,s[m],!0)}},af0b:function(t,e,i){"use strict";i("f325")},f325:function(t,e,i){}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e05ad362","chunk-0506e191","chunk-0506e191"],{1148:function(a,e,t){"use strict";var r=t("5926"),i=t("577e"),o=t("1d80"),n=RangeError;a.exports=function(a){var e=i(o(this)),t="",s=r(a);if(s<0||s===1/0)throw new n("Wrong number of repetitions");for(;s>0;(s>>>=1)&&(e+=e))1&s&&(t+=e);return t}},b680:function(a,e,t){"use strict";var r=t("23e7"),i=t("e330"),o=t("5926"),n=t("408a"),s=t("1148"),l=t("d039"),c=RangeError,d=String,m=Math.floor,u=i(s),p=i("".slice),g=i(1..toFixed),h=function(a,e,t){return 0===e?t:e%2===1?h(a,e-1,t*a):h(a*a,e/2,t)},F=function(a){var e=0,t=a;while(t>=4096)e+=12,t/=4096;while(t>=2)e+=1,t/=2;return e},f=function(a,e,t){var r=-1,i=t;while(++r<6)i+=e*a[r],a[r]=i%1e7,i=m(i/1e7)},v=function(a,e){var t=6,r=0;while(--t>=0)r+=a[t],a[t]=m(r/e),r=r%e*1e7},b=function(a){var e=6,t="";while(--e>=0)if(""!==t||0===e||0!==a[e]){var r=d(a[e]);t=""===t?r:t+u("0",7-r.length)+r}return t},k=l((function(){return"0.000"!==g(8e-5,3)||"1"!==g(.9,0)||"1.25"!==g(1.255,2)||"1000000000000000128"!==g(0xde0b6b3a7640080,0)}))||!l((function(){g({})}));r({target:"Number",proto:!0,forced:k},{toFixed:function(a){var e,t,r,i,s=n(this),l=o(a),m=[0,0,0,0,0,0],g="",k="0";if(l<0||l>20)throw new c("Incorrect fraction digits");if(s!==s)return"NaN";if(s<=-1e21||s>=1e21)return d(s);if(s<0&&(g="-",s=-s),s>1e-21)if(e=F(s*h(2,69,1))-69,t=e<0?s*h(2,-e,1):s/h(2,e,1),t*=4503599627370496,e=52-e,e>0){f(m,0,t),r=l;while(r>=7)f(m,1e7,0),r-=7;f(m,h(10,r,1),0),r=e-1;while(r>=23)v(m,1<<23),r-=23;v(m,1<<r),f(m,1,1),v(m,2),k=b(m)}else f(m,0,t),f(m,1<<-e,0),k=b(m)+u("0",l);return l>0?(i=k.length,k=g+(i<=l?"0."+u("0",l-i)+k:p(k,0,i-l)+"."+p(k,i-l))):k=g+k,k}})},ef2b:function(a,e,t){"use strict";t.r(e);t("a4d3"),t("e01a"),t("b0c0");var r=function(){var a=this,e=a._self._c;return e("el-dialog",{attrs:{title:a.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:a.visible},on:{"update:visible":function(e){a.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:a.dataForm,rules:a.dataRule,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"套餐名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"套餐名称"},model:{value:a.dataForm.name,callback:function(e){a.$set(a.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",{attrs:{label:"套餐描述",prop:"description"}},[e("el-input",{attrs:{type:"textarea",rows:3,placeholder:"套餐描述"},model:{value:a.dataForm.description,callback:function(e){a.$set(a.dataForm,"description",e)},expression:"dataForm.description"}})],1),e("el-form-item",{attrs:{label:"套餐类型",prop:"packageType"}},[e("el-radio-group",{on:{change:a.onPackageTypeChange},model:{value:a.dataForm.packageType,callback:function(e){a.$set(a.dataForm,"packageType",e)},expression:"dataForm.packageType"}},[e("el-radio",{attrs:{label:1}},[a._v("充值次数套餐")]),e("el-radio",{attrs:{label:2}},[a._v("创建活动套餐")]),e("el-radio",{attrs:{label:3}},[a._v("活动续费套餐")])],1),e("div",{staticStyle:{color:"#909399","font-size":"12px","margin-top":"5px"}},[a._v(" 充值次数套餐：为现有活动增加使用次数；创建活动套餐：购买后自动创建新活动；活动续费套餐：延长活动有效期 ")])],1),e("el-form-item",{attrs:{label:"充值次数",prop:"countValue"}},[e("el-input-number",{attrs:{min:1,max:1e4,placeholder:"充值次数"},model:{value:a.dataForm.countValue,callback:function(e){a.$set(a.dataForm,"countValue",e)},expression:"dataForm.countValue"}})],1),e("el-form-item",{attrs:{label:"现价(元)",prop:"price"}},[e("el-input-number",{attrs:{precision:2,min:.01,max:9999.99,placeholder:"现价"},on:{change:a.onPriceChange},model:{value:a.dataForm.price,callback:function(e){a.$set(a.dataForm,"price",e)},expression:"dataForm.price"}})],1),e("el-form-item",{attrs:{label:"原价(元)",prop:"originalPrice"}},[e("el-input-number",{attrs:{precision:2,min:0,max:9999.99,placeholder:"原价(可选)"},on:{change:a.onOriginalPriceChange},model:{value:a.dataForm.originalPrice,callback:function(e){a.$set(a.dataForm,"originalPrice",e)},expression:"dataForm.originalPrice"}})],1),e("el-form-item",{attrs:{label:"折扣率",prop:"discountRate"}},[e("el-input-number",{attrs:{precision:2,min:.01,max:1,step:.01,placeholder:"折扣率(0.01-1)"},on:{change:a.onDiscountRateChange},model:{value:a.dataForm.discountRate,callback:function(e){a.$set(a.dataForm,"discountRate",e)},expression:"dataForm.discountRate"}}),e("div",{staticStyle:{color:"#909399","font-size":"12px","margin-top":"5px"}},[a._v(" 例如：0.8表示8折，1表示无折扣。修改折扣率会自动计算现价，修改现价会自动计算折扣率 ")])],1),3!==a.dataForm.packageType?e("el-form-item",{attrs:{label:"有效期(天)",prop:"validDays"}},[e("el-input-number",{attrs:{min:1,max:3650,placeholder:"有效期天数"},model:{value:a.dataForm.validDays,callback:function(e){a.$set(a.dataForm,"validDays",e)},expression:"dataForm.validDays"}})],1):a._e(),3===a.dataForm.packageType?e("el-form-item",{attrs:{label:"续费天数",prop:"renewalDays"}},[e("el-input-number",{attrs:{min:1,max:3650,placeholder:"续费天数"},model:{value:a.dataForm.renewalDays,callback:function(e){a.$set(a.dataForm,"renewalDays",e)},expression:"dataForm.renewalDays"}}),e("div",{staticStyle:{color:"#909399","font-size":"12px","margin-top":"5px"}},[a._v(" 购买此套餐后，活动有效期将延长指定天数 ")])],1):a._e(),e("el-form-item",{attrs:{label:"是否热门",prop:"isHot"}},[e("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:a.dataForm.isHot,callback:function(e){a.$set(a.dataForm,"isHot",e)},expression:"dataForm.isHot"}})],1),e("el-form-item",{attrs:{label:"是否推荐",prop:"isRecommended"}},[e("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:a.dataForm.isRecommended,callback:function(e){a.$set(a.dataForm,"isRecommended",e)},expression:"dataForm.isRecommended"}})],1),e("el-form-item",{attrs:{label:"是否启用",prop:"status"}},[e("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:a.dataForm.status,callback:function(e){a.$set(a.dataForm,"status",e)},expression:"dataForm.status"}})],1),e("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[e("el-input-number",{attrs:{min:0,placeholder:"排序，数值越小越靠前"},model:{value:a.dataForm.sortOrder,callback:function(e){a.$set(a.dataForm,"sortOrder",e)},expression:"dataForm.sortOrder"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){a.visible=!1}}},[a._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return a.dataFormSubmit()}}},[a._v("确定")])],1)],1)},i=[],o=(t("b680"),{data:function(){return{visible:!1,isCalculating:!1,dataForm:{repeatToken:"",id:0,name:"",description:"",packageType:1,countValue:1,price:.01,originalPrice:null,discountRate:1,validDays:365,renewalDays:30,isHot:0,isRecommended:0,status:1,sortOrder:0},dataRule:{name:[{required:!0,message:"套餐名称不能为空",trigger:"blur"}],packageType:[{required:!0,message:"套餐类型不能为空",trigger:"change"}],countValue:[{required:!0,message:"充值次数不能为空",trigger:"blur"}],price:[{required:!0,message:"现价不能为空",trigger:"blur"}],validDays:[{required:!0,message:"有效期不能为空",trigger:"blur"}]}}},methods:{init:function(a){var e=this;this.getToken(),this.dataForm.id=a||0,this.visible=!0,this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.dataForm.id&&e.$http({url:e.$http.adornUrl("/activity/rechargepackage/info/".concat(e.dataForm.id)),method:"get",params:e.$http.adornParams()}).then((function(a){var t=a.data;t&&200===t.code&&(e.dataForm.name=t.rechargePackage.name,e.dataForm.description=t.rechargePackage.description,e.dataForm.packageType=t.rechargePackage.packageType||1,e.dataForm.countValue=t.rechargePackage.countValue,e.dataForm.price=t.rechargePackage.price,e.dataForm.originalPrice=t.rechargePackage.originalPrice,e.dataForm.discountRate=t.rechargePackage.discountRate,e.dataForm.validDays=t.rechargePackage.validDays,e.dataForm.renewalDays=t.rechargePackage.renewalDays||30,e.dataForm.isHot=t.rechargePackage.isHot,e.dataForm.isRecommended=t.rechargePackage.isRecommended,e.dataForm.status=t.rechargePackage.status,e.dataForm.sortOrder=t.rechargePackage.sortOrder)}))}))},getToken:function(){var a=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(e){var t=e.data;t&&200===t.code&&(a.dataForm.repeatToken=t.result)}))},onDiscountRateChange:function(a){var e=this;this.isCalculating||a&&this.dataForm.originalPrice&&this.dataForm.originalPrice>0&&(this.isCalculating=!0,this.dataForm.price=parseFloat((this.dataForm.originalPrice*a).toFixed(2)),this.$nextTick((function(){e.isCalculating=!1})))},onPriceChange:function(a){var e=this;this.isCalculating||a&&this.dataForm.originalPrice&&this.dataForm.originalPrice>0&&(this.isCalculating=!0,this.dataForm.discountRate=parseFloat((a/this.dataForm.originalPrice).toFixed(2)),this.dataForm.discountRate>1&&(this.dataForm.discountRate=1),this.dataForm.discountRate<.01&&(this.dataForm.discountRate=.01),this.$nextTick((function(){e.isCalculating=!1})))},onOriginalPriceChange:function(a){var e=this;this.isCalculating||a&&this.dataForm.discountRate&&this.dataForm.discountRate>0&&(this.isCalculating=!0,this.dataForm.price=parseFloat((a*this.dataForm.discountRate).toFixed(2)),this.$nextTick((function(){e.isCalculating=!1})))},onPackageTypeChange:function(a){3===a?(this.dataForm.countValue=0,this.dataForm.renewalDays=30):(this.dataForm.countValue=1,this.dataForm.validDays=365)},dataFormSubmit:function(){var a=this;this.$refs["dataForm"].validate((function(e){e&&a.$http({url:a.$http.adornUrl("/activity/rechargepackage/".concat(a.dataForm.id?"update":"save")),method:"post",data:a.$http.adornData({repeatToken:a.dataForm.repeatToken,id:a.dataForm.id||void 0,name:a.dataForm.name,description:a.dataForm.description,packageType:a.dataForm.packageType,countValue:a.dataForm.countValue,price:a.dataForm.price,originalPrice:a.dataForm.originalPrice,discountRate:a.dataForm.discountRate,validDays:a.dataForm.validDays,renewalDays:a.dataForm.renewalDays,isHot:a.dataForm.isHot,isRecommended:a.dataForm.isRecommended,status:a.dataForm.status,sortOrder:a.dataForm.sortOrder})}).then((function(e){var t=e.data;t&&200===t.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.visible=!1,a.$emit("refreshDataList")}}):(a.$message.error(t.msg),"不能重复提交"!=t.msg&&a.getToken())}))}))}}}),n=o,s=t("2877"),l=Object(s["a"])(n,r,i,!1,null,null,null);e["default"]=l.exports}}]);
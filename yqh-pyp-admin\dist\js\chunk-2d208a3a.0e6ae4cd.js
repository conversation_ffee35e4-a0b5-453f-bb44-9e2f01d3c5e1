(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d208a3a"],{a63b:function(e,a,t){"use strict";t.r(a);t("b0c0");var r=function(){var e=this,a=e._self._c;return a("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(a){e.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.dataFormSubmit()}}},[a("el-form-item",{attrs:{label:"摘要",prop:"name"}},[a("el-input",{attrs:{placeholder:"摘要"},model:{value:e.dataForm.name,callback:function(a){e.$set(e.dataForm,"name",a)},expression:"dataForm.name"}})],1),a("el-form-item",{attrs:{label:"科目",prop:"priceConfigId"}},[a("el-select",{attrs:{placeholder:"科目",filterable:""},model:{value:e.dataForm.priceConfigId,callback:function(a){e.$set(e.dataForm,"priceConfigId",a)},expression:"dataForm.priceConfigId"}},e._l(e.priceConfig,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){e.visible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},i=[],o={data:function(){return{priceConfig:[],visible:!1,dataForm:{repeatToken:"",id:0,name:"",price:"",type:"",payTime:"",priceConfigId:"",priceBankId:"",balance:"",status:"",tranFlow:"",detNo:"",accNumber:"",accName:"",accBankName:"",matchPrice:"",activityId:"",activitySettleId:""},dataRule:{name:[{required:!0,message:"摘要不能为空",trigger:"blur"}],price:[{required:!0,message:"到款金额不能为空",trigger:"blur"}],type:[{required:!0,message:"付款类型不能为空",trigger:"blur"}],payTime:[{required:!0,message:"付款时间不能为空",trigger:"blur"}],priceConfigId:[{required:!0,message:"科目不能为空",trigger:"blur"}],priceBankId:[{required:!0,message:"银行账号不能为空",trigger:"blur"}],balance:[{required:!0,message:"余额不能为空",trigger:"blur"}],status:[{required:!0,message:"0-未比对，1-已比对不能为空",trigger:"blur"}],tranFlow:[{required:!0,message:"交易流水号不能为空",trigger:"blur"}],detNo:[{required:!0,message:"活存账户明细号不能为空",trigger:"blur"}],accNumber:[{required:!0,message:"对方账号不能为空",trigger:"blur"}],accName:[{required:!0,message:"对方户名不能为空",trigger:"blur"}],accBankName:[{required:!0,message:"对方账户开户行名称不能为空",trigger:"blur"}],matchPrice:[{required:!0,message:"到款金额不能为空",trigger:"blur"}],activityId:[{required:!0,message:"会议ID不能为空",trigger:"blur"}],activitySettleId:[{required:!0,message:"所属会议结算单不能为空",trigger:"blur"}]}}},methods:{init:function(e){var a=this;this.getToken(),this.getPriceConfig(),this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/price/pricewater/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(e){var t=e.data;t&&200===t.code&&(a.dataForm.name=t.priceWater.name,a.dataForm.price=t.priceWater.price,a.dataForm.type=t.priceWater.type,a.dataForm.payTime=t.priceWater.payTime,a.dataForm.priceConfigId=t.priceWater.priceConfigId,a.dataForm.priceBankId=t.priceWater.priceBankId,a.dataForm.balance=t.priceWater.balance,a.dataForm.status=t.priceWater.status,a.dataForm.tranFlow=t.priceWater.tranFlow,a.dataForm.detNo=t.priceWater.detNo,a.dataForm.accNumber=t.priceWater.accNumber,a.dataForm.accName=t.priceWater.accName,a.dataForm.accBankName=t.priceWater.accBankName,a.dataForm.matchPrice=t.priceWater.matchPrice,a.dataForm.activityId=t.priceWater.activityId,a.dataForm.activitySettleId=t.priceWater.activitySettleId)}))}))},getToken:function(){var e=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(a){var t=a.data;t&&200===t.code&&(e.dataForm.repeatToken=t.result)}))},getPriceConfig:function(){var e=this;this.$http({url:this.$http.adornUrl("/price/priceconfig/findAll"),method:"get",params:this.$http.adornParams()}).then((function(a){var t=a.data;t&&200===t.code&&(e.priceConfig=t.result)}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(a){a&&e.$http({url:e.$http.adornUrl("/price/pricewater/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({repeatToken:e.dataForm.repeatToken,id:e.dataForm.id||void 0,name:e.dataForm.name,price:e.dataForm.price,type:e.dataForm.type,payTime:e.dataForm.payTime,priceConfigId:e.dataForm.priceConfigId,priceBankId:e.dataForm.priceBankId,balance:e.dataForm.balance,status:e.dataForm.status,tranFlow:e.dataForm.tranFlow,detNo:e.dataForm.detNo,accNumber:e.dataForm.accNumber,accName:e.dataForm.accName,accBankName:e.dataForm.accBankName,matchPrice:e.dataForm.matchPrice,appid:e.$cookie.get("appid"),activityId:e.dataForm.activityId,activitySettleId:e.dataForm.activitySettleId})}).then((function(a){var t=a.data;t&&200===t.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):(e.$message.error(t.msg),"不能重复提交"!=t.msg&&e.getToken())}))}))}}},c=o,d=t("2877"),n=Object(d["a"])(c,r,i,!1,null,null,null);a["default"]=n.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1311165a"],{"11e9":function(t,a,i){var e=i("52a7"),o=i("4630"),s=i("6821"),n=i("6a99"),r=i("69a8"),c=i("c69a"),l=Object.getOwnPropertyDescriptor;a.f=i("9e1e")?l:function(t,a){if(t=s(t),a=n(a,!0),c)try{return l(t,a)}catch(i){}if(r(t,a))return o(!e.f.call(t,a),t[a])}},"28a5":function(t,a,i){"use strict";var e=i("aae3"),o=i("cb7c"),s=i("ebd6"),n=i("0390"),r=i("9def"),c=i("5f1b"),l=i("520a"),u=i("79e5"),f=Math.min,d=[].push,m="split",h="length",p="lastIndex",v=4294967295,w=!u((function(){RegExp(v,"y")}));i("214f")("split",2,(function(t,a,i,u){var y;return y="c"=="abbc"[m](/(b)*/)[1]||4!="test"[m](/(?:)/,-1)[h]||2!="ab"[m](/(?:ab)*/)[h]||4!="."[m](/(.?)(.?)/)[h]||"."[m](/()()/)[h]>1||""[m](/.?/)[h]?function(t,a){var o=String(this);if(void 0===t&&0===a)return[];if(!e(t))return i.call(o,t,a);var s,n,r,c=[],u=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),f=0,m=void 0===a?v:a>>>0,w=new RegExp(t.source,u+"g");while(s=l.call(w,o)){if(n=w[p],n>f&&(c.push(o.slice(f,s.index)),s[h]>1&&s.index<o[h]&&d.apply(c,s.slice(1)),r=s[0][h],f=n,c[h]>=m))break;w[p]===s.index&&w[p]++}return f===o[h]?!r&&w.test("")||c.push(""):c.push(o.slice(f)),c[h]>m?c.slice(0,m):c}:"0"[m](void 0,0)[h]?function(t,a){return void 0===t&&0===a?[]:i.call(this,t,a)}:i,[function(i,e){var o=t(this),s=void 0==i?void 0:i[a];return void 0!==s?s.call(i,o,e):y.call(String(o),i,e)},function(t,a){var e=u(y,t,this,a,y!==i);if(e.done)return e.value;var l=o(t),d=String(this),m=s(l,RegExp),h=l.unicode,p=(l.ignoreCase?"i":"")+(l.multiline?"m":"")+(l.unicode?"u":"")+(w?"y":"g"),g=new m(w?l:"^(?:"+l.source+")",p),C=void 0===a?v:a>>>0;if(0===C)return[];if(0===d.length)return null===c(g,d)?[d]:[];var b=0,D=0,x=[];while(D<d.length){g.lastIndex=w?D:0;var k,S=c(g,w?d:d.slice(D));if(null===S||(k=f(r(g.lastIndex+(w?0:D)),d.length))===b)D=n(d,D,h);else{if(x.push(d.slice(b,D)),x.length===C)return x;for(var _=1;_<=S.length-1;_++)if(x.push(S[_]),x.length===C)return x;D=b=k}}return x.push(d.slice(b)),x}]}))},2909:function(t,a,i){"use strict";function e(t,a){(null==a||a>t.length)&&(a=t.length);for(var i=0,e=new Array(a);i<a;i++)e[i]=t[i];return e}function o(t){if(Array.isArray(t))return e(t)}function s(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function n(t,a){if(t){if("string"===typeof t)return e(t,a);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?e(t,a):void 0}}function r(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(t){return o(t)||s(t)||n(t)||r()}i.d(a,"a",(function(){return c}))},"456d":function(t,a,i){var e=i("4bf8"),o=i("0d58");i("5eda")("keys",(function(){return function(t){return o(e(t))}}))},"5eda":function(t,a,i){var e=i("5ca1"),o=i("8378"),s=i("79e5");t.exports=function(t,a){var i=(o.Object||{})[t]||Object[t],n={};n[t]=a(i),e(e.S+e.F*s((function(){i(1)})),"Object",n)}},"6ccb":function(t,a,i){"use strict";i("7dcbe")},"7dcbe":function(t,a,i){},"8e6e":function(t,a,i){var e=i("5ca1"),o=i("990b"),s=i("6821"),n=i("11e9"),r=i("f1ae");e(e.S,"Object",{getOwnPropertyDescriptors:function(t){var a,i,e=s(t),c=n.f,l=o(e),u={},f=0;while(l.length>f)i=c(e,a=l[f++]),void 0!==i&&r(u,a,i);return u}})},9093:function(t,a,i){var e=i("ce10"),o=i("e11e").concat("length","prototype");a.f=Object.getOwnPropertyNames||function(t){return e(t,o)}},"990b":function(t,a,i){var e=i("9093"),o=i("2621"),s=i("cb7c"),n=i("7726").Reflect;t.exports=n&&n.ownKeys||function(t){var a=e.f(s(t)),i=o.f;return i?a.concat(i(t)):a}},ac6a:function(t,a,i){for(var e=i("cadf"),o=i("0d58"),s=i("2aba"),n=i("7726"),r=i("32e9"),c=i("84f2"),l=i("2b4c"),u=l("iterator"),f=l("toStringTag"),d=c.Array,m={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},h=o(m),p=0;p<h.length;p++){var v,w=h[p],y=m[w],g=n[w],C=g&&g.prototype;if(C&&(C[u]||r(C,u,d),C[f]||r(C,f,w),c[w]=d,y))for(v in e)C[v]||s(C,v,e[v],!0)}},ade3:function(t,a,i){"use strict";i.d(a,"a",(function(){return n}));var e=i("53ca");function o(t,a){if("object"!==Object(e["a"])(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var o=i.call(t,a||"default");if("object"!==Object(e["a"])(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===a?String:Number)(t)}function s(t){var a=o(t,"string");return"symbol"===Object(e["a"])(a)?a:String(a)}function n(t,a,i){return a=s(a),a in t?Object.defineProperty(t,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[a]=i,t}},ced2:function(t,a,i){"use strict";i.r(a);i("7f7f");var e=function(){var t=this,a=t._self._c;return a("div",{staticClass:"activity-edit-page"},[a("van-nav-bar",{staticClass:"custom-nav-bar",attrs:{title:"修改活动","left-text":"返回","left-arrow":""},on:{"click-left":t.goBack}}),a("div",{staticClass:"progress-indicator"},[a("div",{staticClass:"progress-steps"},[a("div",{staticClass:"step active"},[a("div",{staticClass:"step-icon"},[a("van-icon",{attrs:{name:"info-o"}})],1),a("span",[t._v("基本信息")])]),a("div",{staticClass:"step-line"}),a("div",{staticClass:"step active"},[a("div",{staticClass:"step-icon"},[a("van-icon",{attrs:{name:"photo-o"}})],1),a("span",[t._v("图片设置")])]),a("div",{staticClass:"step-line"}),a("div",{staticClass:"step active"},[a("div",{staticClass:"step-icon"},[a("van-icon",{attrs:{name:"setting-o"}})],1),a("span",[t._v("平台配置")])])])]),a("div",{staticClass:"form-container"},[a("van-form",{ref:"form",staticStyle:{margin:"10px"},on:{submit:t.onSubmit}},[a("div",{staticClass:"form-section",attrs:{id:"basic-info"}},[a("div",{staticClass:"section-header"},[a("div",{staticClass:"section-title"},[a("div",{staticClass:"title-icon"},[a("van-icon",{attrs:{name:"info-o"}})],1),a("div",{staticClass:"title-content"},[a("h3",[t._v("基本信息")]),a("p",[t._v("设置活动的基础信息")])])]),a("div",{staticClass:"section-badge"},[a("van-tag",{attrs:{type:"primary",size:"medium"}},[t._v("必填")])],1)]),a("div",{staticClass:"section-content"},[a("van-field",{staticClass:"custom-field",attrs:{name:"name",label:"活动名称",placeholder:"请输入活动名称",rules:[{required:!0,message:"请输入活动名称"}]},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"edit"}})]},proxy:!0}]),model:{value:t.formData.name,callback:function(a){t.$set(t.formData,"name",a)},expression:"formData.name"}})],1)]),a("div",{staticClass:"form-section",attrs:{id:"title-config"}},[a("div",{staticClass:"section-header"},[a("div",{staticClass:"section-title"},[a("div",{staticClass:"title-icon"},[a("van-icon",{attrs:{name:"magic"}})],1),a("div",{staticClass:"title-content"},[a("h3",[t._v("标题生成配置")]),a("p",[t._v("配置AI生成标题的相关参数")])])]),a("div",{staticClass:"section-badge"},[a("van-tag",{attrs:{type:"warning",size:"medium"}},[t._v("可选")])],1)]),a("div",{staticClass:"section-content"},[a("van-field",{staticClass:"custom-field",attrs:{name:"nameMode",label:"标题生成模式"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"setting-o"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-radio-group",{attrs:{direction:"horizontal"},model:{value:t.formData.nameMode,callback:function(a){t.$set(t.formData,"nameMode",a)},expression:"formData.nameMode"}},[a("van-radio",{staticClass:"custom-radio",attrs:{name:"ai"},scopedSlots:t._u([{key:"icon",fn:function(t){return[a("div",{staticClass:"radio-icon",class:{active:t.checked}},[a("van-icon",{attrs:{name:"magic"}})],1)]}}])},[t._v("\n                  AI生成\n                ")]),a("van-radio",{staticClass:"custom-radio",attrs:{name:"manual"},scopedSlots:t._u([{key:"icon",fn:function(t){return[a("div",{staticClass:"radio-icon",class:{active:t.checked}},[a("van-icon",{attrs:{name:"edit"}})],1)]}}])},[t._v("\n                  手动填写\n                ")])],1)]},proxy:!0}])}),a("van-field",{staticClass:"custom-field",attrs:{name:"defaultName",label:"默认标题",placeholder:"请输入默认标题",maxlength:"50","show-word-limit":""},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"label-o"}})]},proxy:!0}]),model:{value:t.formData.defaultName,callback:function(a){t.$set(t.formData,"defaultName",a)},expression:"formData.defaultName"}}),a("van-field",{staticClass:"custom-field",attrs:{name:"defaultTitle",label:"默认提示词",placeholder:"请输入默认提示词",maxlength:"100","show-word-limit":"",type:"textarea",rows:"2"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"comment-o"}})]},proxy:!0}]),model:{value:t.formData.defaultTitle,callback:function(a){t.$set(t.formData,"defaultTitle",a)},expression:"formData.defaultTitle"}}),a("van-field",{staticClass:"custom-field",attrs:{name:"defaultUserInput",label:"默认用户输入",placeholder:"设置用户自定义补充字段的默认值，如：请在这里补充您的特殊要求或想法",maxlength:"200","show-word-limit":"",type:"textarea",rows:"2"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"edit"}})]},proxy:!0}]),model:{value:t.formData.defaultUserInput,callback:function(a){t.$set(t.formData,"defaultUserInput",a)},expression:"formData.defaultUserInput"}}),a("div",{staticClass:"config-tips"},[a("van-notice-bar",{attrs:{"left-icon":"info-o",text:"AI生成模式：系统将根据默认提示词自动生成标题；手动填写模式：使用默认标题作为固定标题",color:"#1989fa",background:"#ecf5ff",scrollable:!1}})],1)],1)]),a("div",{staticClass:"form-section",attrs:{id:"image-settings"}},[a("div",{staticClass:"section-header"},[a("div",{staticClass:"section-title"},[a("div",{staticClass:"title-icon"},[a("van-icon",{attrs:{name:"photo-o"}})],1),a("div",{staticClass:"title-content"},[a("h3",[t._v("图片设置")]),a("p",[t._v("上传活动相关的图片素材")])])]),a("div",{staticClass:"section-badge"})]),a("div",{staticClass:"section-content"},[a("div",{staticClass:"image-grid"},[a("div",{staticClass:"image-item"},[a("div",{staticClass:"image-header"},[a("van-icon",{attrs:{name:"user-circle-o"}}),a("span",[t._v("Logo")])],1),a("van-field",{staticClass:"image-field",attrs:{name:"logo"},scopedSlots:t._u([{key:"input",fn:function(){return[a("div",{staticClass:"image-upload-field"},[a("van-uploader",{staticClass:"custom-uploader",attrs:{"max-count":1,"after-read":function(a){return t.afterRead(a,"logo")},"before-delete":function(){return t.beforeDelete("logo")}},model:{value:t.logoFileList,callback:function(a){t.logoFileList=a},expression:"logoFileList"}},[a("div",{staticClass:"upload-area"},[a("van-icon",{attrs:{name:"plus"}}),a("span",[t._v("选择Logo")])],1)]),a("div",{staticClass:"upload-tip"},[t._v("建议尺寸：200*200，大小100kb以下")])],1)]},proxy:!0}])})],1),a("div",{staticClass:"image-item"},[a("div",{staticClass:"image-header"},[a("van-icon",{attrs:{name:"photo"}}),a("span",[t._v("主图片")])],1),a("van-field",{staticClass:"image-field",attrs:{name:"mobileBanner"},scopedSlots:t._u([{key:"input",fn:function(){return[a("div",{staticClass:"image-upload-field"},[a("van-uploader",{staticClass:"custom-uploader",attrs:{"max-count":9,multiple:"","after-read":function(a){return t.afterRead(a,"mobileBanner")},"before-delete":function(a){return t.beforeDelete("mobileBanner",a)}},model:{value:t.mobileBannerFileList,callback:function(a){t.mobileBannerFileList=a},expression:"mobileBannerFileList"}},[a("div",{staticClass:"upload-area"},[a("van-icon",{attrs:{name:"plus"}}),a("span",[t._v("选择图片")])],1)]),a("div",{staticClass:"upload-tip"},[t._v("建议尺寸：1920*1080，大小2mb以下")])],1)]},proxy:!0}])})],1),a("div",{staticClass:"image-item"},[a("div",{staticClass:"image-header"},[a("van-icon",{attrs:{name:"wechat",color:"#07c160"}}),a("span",[t._v("公众号图片")])],1),a("van-field",{staticClass:"image-field",attrs:{name:"wechatQrCode"},scopedSlots:t._u([{key:"input",fn:function(){return[a("div",{staticClass:"image-upload-field"},[a("van-uploader",{staticClass:"custom-uploader",attrs:{"max-count":1,"after-read":function(a){return t.afterRead(a,"wechatQrCode")},"before-delete":function(){return t.beforeDelete("wechatQrCode")}},model:{value:t.wechatQrCodeFileList,callback:function(a){t.wechatQrCodeFileList=a},expression:"wechatQrCodeFileList"}},[a("div",{staticClass:"upload-area"},[a("van-icon",{attrs:{name:"plus"}}),a("span",[t._v("选择公众号图片")])],1)]),a("div",{staticClass:"upload-tip"},[t._v("建议尺寸：430*430，大小500kb以下")])],1)]},proxy:!0}])})],1)])])]),a("div",{staticClass:"form-section",attrs:{id:"platform-settings"}},[a("div",{staticClass:"section-header"},[a("div",{staticClass:"section-title"},[a("div",{staticClass:"title-icon"},[a("van-icon",{attrs:{name:"setting-o"}})],1),a("div",{staticClass:"title-content"},[a("h3",[t._v("平台设置")]),a("p",[t._v("配置各平台相关信息")])])]),a("div",{staticClass:"section-badge"})]),a("div",{staticClass:"section-content"},[a("div",{staticClass:"setting-group"},[a("div",{staticClass:"group-title"},[a("van-icon",{attrs:{name:"apps-o"}}),a("span",[t._v("平台类型")])],1),a("div",{staticClass:"type-grid"},[a("div",{staticClass:"type-item"},[a("van-field",{staticClass:"custom-field",attrs:{name:"douyinType",label:"抖音类型"},scopedSlots:t._u([{key:"input",fn:function(){return[a("van-radio-group",{staticClass:"custom-radio-group",attrs:{direction:"horizontal"},model:{value:t.formData.douyinType,callback:function(a){t.$set(t.formData,"douyinType",a)},expression:"formData.douyinType"}},[a("van-radio",{staticClass:"custom-radio",attrs:{name:0}},[t._v("视频")]),a("van-radio",{staticClass:"custom-radio",attrs:{name:1}},[t._v("图文")])],1)]},proxy:!0}])})],1),a("div",{staticClass:"type-item"},[a("van-field",{staticClass:"custom-field",attrs:{name:"xiaohongshuType",label:"小红书类型"},scopedSlots:t._u([{key:"input",fn:function(){return[a("van-radio-group",{staticClass:"custom-radio-group",attrs:{direction:"horizontal"},model:{value:t.formData.xiaohongshuType,callback:function(a){t.$set(t.formData,"xiaohongshuType",a)},expression:"formData.xiaohongshuType"}},[a("van-radio",{staticClass:"custom-radio",attrs:{name:0}},[t._v("视频")]),a("van-radio",{staticClass:"custom-radio",attrs:{name:1}},[t._v("图文")])],1)]},proxy:!0}])})],1)])]),a("div",{staticClass:"setting-group"},[a("div",{staticClass:"group-title"},[a("van-icon",{attrs:{name:"link-o"}}),a("span",[t._v("平台信息")])],1),a("div",{staticClass:"platform-fields"},[a("van-field",{staticClass:"custom-field",attrs:{name:"douyinPoi",label:"抖音POI",placeholder:"请输入抖音POI"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"location-o"}})]},proxy:!0}]),model:{value:t.formData.douyinPoi,callback:function(a){t.$set(t.formData,"douyinPoi",a)},expression:"formData.douyinPoi"}}),a("van-field",{staticClass:"custom-field",attrs:{name:"douyindianping",label:"抖音点评",placeholder:"请输入抖音点评"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"star-o"}})]},proxy:!0}]),model:{value:t.formData.douyindianping,callback:function(a){t.$set(t.formData,"douyindianping",a)},expression:"formData.douyindianping"}}),a("van-field",{staticClass:"custom-field",attrs:{name:"meituan",label:"美团",placeholder:"请输入美团"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"shop-o"}})]},proxy:!0}]),model:{value:t.formData.meituan,callback:function(a){t.$set(t.formData,"meituan",a)},expression:"formData.meituan"}}),a("van-field",{staticClass:"custom-field",attrs:{name:"dazhongdianping",label:"大众点评",placeholder:"请输入大众点评"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"comment-o"}})]},proxy:!0}]),model:{value:t.formData.dazhongdianping,callback:function(a){t.$set(t.formData,"dazhongdianping",a)},expression:"formData.dazhongdianping"}}),a("van-field",{staticClass:"custom-field",attrs:{name:"qiyeweixin",label:"企业微信",placeholder:"请输入企业微信"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"chat-o"}})]},proxy:!0}]),model:{value:t.formData.qiyeweixin,callback:function(a){t.$set(t.formData,"qiyeweixin",a)},expression:"formData.qiyeweixin"}}),a("van-field",{staticClass:"custom-field",attrs:{name:"wifiAccount",label:"WiFi账号",placeholder:"请输入WiFi账号"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("div",{staticClass:"wifi-icon",staticStyle:{color:"#5352ed"}},[a("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor"}},[a("path",{attrs:{d:"M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.07 2.93 1 9zm8 8l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z"}})])])]},proxy:!0}]),model:{value:t.formData.wifiAccount,callback:function(a){t.$set(t.formData,"wifiAccount",a)},expression:"formData.wifiAccount"}}),a("van-field",{staticClass:"custom-field",attrs:{name:"wifiPassword",label:"WiFi密码",placeholder:"请输入WiFi密码"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"lock",color:"#5352ed"}})]},proxy:!0}]),model:{value:t.formData.wifiPassword,callback:function(a){t.$set(t.formData,"wifiPassword",a)},expression:"formData.wifiPassword"}}),a("van-field",{staticClass:"custom-field",attrs:{name:"ctripConfig",label:"携程首页配置",placeholder:"请输入携程首页配置信息"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"location-o",color:"#0066cc"}})]},proxy:!0}]),model:{value:t.formData.ctripConfig,callback:function(a){t.$set(t.formData,"ctripConfig",a)},expression:"formData.ctripConfig"}}),a("van-field",{staticClass:"custom-field",attrs:{name:"ctripReviewConfig",label:"携程点评配置",placeholder:"请输入携程点评配置信息"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"star-o",color:"#0066cc"}})]},proxy:!0}]),model:{value:t.formData.ctripReviewConfig,callback:function(a){t.$set(t.formData,"ctripReviewConfig",a)},expression:"formData.ctripReviewConfig"}}),a("van-field",{staticClass:"custom-field",attrs:{name:"ctripNotesConfig",label:"携程笔记配置",placeholder:"请输入携程笔记配置信息"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"notes-o",color:"#0066cc"}})]},proxy:!0}]),model:{value:t.formData.ctripNotesConfig,callback:function(a){t.$set(t.formData,"ctripNotesConfig",a)},expression:"formData.ctripNotesConfig"}}),a("van-field",{staticClass:"custom-field",attrs:{name:"zhuyeDouyin",label:"抖音主页",placeholder:"请输入抖音主页"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"user-o"}})]},proxy:!0}]),model:{value:t.formData.zhuyeDouyin,callback:function(a){t.$set(t.formData,"zhuyeDouyin",a)},expression:"formData.zhuyeDouyin"}}),a("van-field",{staticClass:"custom-field",attrs:{name:"zhuyeKuaishou",label:"快手主页",placeholder:"请输入快手主页"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"user-circle-o"}})]},proxy:!0}]),model:{value:t.formData.zhuyeKuaishou,callback:function(a){t.$set(t.formData,"zhuyeKuaishou",a)},expression:"formData.zhuyeKuaishou"}})],1)])])]),a("div",{staticClass:"form-section",attrs:{id:"display-settings"}},[a("div",{staticClass:"section-header"},[a("div",{staticClass:"section-title"},[a("div",{staticClass:"title-icon"},[a("van-icon",{attrs:{name:"eye-o"}})],1),a("div",{staticClass:"title-content"},[a("h3",[t._v("显示设置")]),a("p",[t._v("控制各功能模块的显示状态")])])]),a("div",{staticClass:"section-badge"})]),a("div",{staticClass:"section-content"},[a("div",{staticClass:"switch-grid"},[a("div",{staticClass:"switch-group"},[a("div",{staticClass:"group-title"},[a("van-icon",{attrs:{name:"tv-o"}}),a("span",[t._v("平台显示")])],1),a("div",{staticClass:"switch-items"},[a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showDouyin",label:"显示抖音"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"video-o",color:"#ff6b6b"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showDouyin,callback:function(a){t.$set(t.formData,"showDouyin",a)},expression:"formData.showDouyin"}})]},proxy:!0}])})],1),a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showXiaohongshu",label:"显示小红书"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"photo-o",color:"#ff4757"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showXiaohongshu,callback:function(a){t.$set(t.formData,"showXiaohongshu",a)},expression:"formData.showXiaohongshu"}})]},proxy:!0}])})],1),a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showKuaishou",label:"显示快手"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"play-circle-o",color:"#ffa502"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showKuaishou,callback:function(a){t.$set(t.formData,"showKuaishou",a)},expression:"formData.showKuaishou"}})]},proxy:!0}])})],1),a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showShipinhao",label:"显示视频号"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"video",color:"#2ed573"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showShipinhao,callback:function(a){t.$set(t.formData,"showShipinhao",a)},expression:"formData.showShipinhao"}})]},proxy:!0}])})],1)])]),a("div",{staticClass:"switch-group"},[a("div",{staticClass:"group-title"},[a("van-icon",{attrs:{name:"setting-o"}}),a("span",[t._v("功能显示")])],1),a("div",{staticClass:"switch-items"},[a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showDouyindianping",label:"显示抖音点评"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"star-o",color:"#ff6b6b"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showDouyindianping,callback:function(a){t.$set(t.formData,"showDouyindianping",a)},expression:"formData.showDouyindianping"}})]},proxy:!0}])})],1),a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showDazhongdianping",label:"显示大众点评"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"comment-o",color:"#ffa502"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showDazhongdianping,callback:function(a){t.$set(t.formData,"showDazhongdianping",a)},expression:"formData.showDazhongdianping"}})]},proxy:!0}])})],1),a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showMeituandianping",label:"显示美团点评"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"shop-o",color:"#2ed573"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showMeituandianping,callback:function(a){t.$set(t.formData,"showMeituandianping",a)},expression:"formData.showMeituandianping"}})]},proxy:!0}])})],1),a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showQiyeweixin",label:"显示企业微信"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"chat-o",color:"#3742fa"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showQiyeweixin,callback:function(a){t.$set(t.formData,"showQiyeweixin",a)},expression:"formData.showQiyeweixin"}})]},proxy:!0}])})],1),a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showMiniProgram",label:"显示微信小程序"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"wechat",color:"#07c160"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showMiniProgram,callback:function(a){t.$set(t.formData,"showMiniProgram",a)},expression:"formData.showMiniProgram"}})]},proxy:!0}])})],1),a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showWifi",label:"显示WIFI"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("div",{staticClass:"wifi-icon",staticStyle:{color:"#5352ed"}},[a("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor"}},[a("path",{attrs:{d:"M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.07 2.93 1 9zm8 8l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z"}})])])]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showWifi,callback:function(a){t.$set(t.formData,"showWifi",a)},expression:"formData.showWifi"}})]},proxy:!0}])})],1),a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showGuanzhukuaishou",label:"显示关注快手"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"like-o",color:"#ff9ff3"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showGuanzhukuaishou,callback:function(a){t.$set(t.formData,"showGuanzhukuaishou",a)},expression:"formData.showGuanzhukuaishou"}})]},proxy:!0}])})],1),a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showGuanzhudouyin",label:"显示关注抖音"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"good-job-o",color:"#ff3838"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showGuanzhudouyin,callback:function(a){t.$set(t.formData,"showGuanzhudouyin",a)},expression:"formData.showGuanzhudouyin"}})]},proxy:!0}])})],1),a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showShipindianzan",label:"显示视频点赞"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"thumb-circle-o",color:"#ff6348"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showShipindianzan,callback:function(a){t.$set(t.formData,"showShipindianzan",a)},expression:"formData.showShipindianzan"}})]},proxy:!0}])})],1),a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showCtrip",label:"显示携程首页"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"location-o",color:"#0066cc"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showCtrip,callback:function(a){t.$set(t.formData,"showCtrip",a)},expression:"formData.showCtrip"}})]},proxy:!0}])})],1),a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showCtripReview",label:"显示携程点评详情"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"star-o",color:"#0066cc"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showCtripReview,callback:function(a){t.$set(t.formData,"showCtripReview",a)},expression:"formData.showCtripReview"}})]},proxy:!0}])})],1),a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showCtripNotes",label:"显示携程笔记"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"notes-o",color:"#0066cc"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showCtripNotes,callback:function(a){t.$set(t.formData,"showCtripNotes",a)},expression:"formData.showCtripNotes"}})]},proxy:!0}])})],1),a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showGroupBuying",label:"显示团购"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"shopping-cart-o",color:"#ff9500"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showGroupBuying,callback:function(a){t.$set(t.formData,"showGroupBuying",a)},expression:"formData.showGroupBuying"}})]},proxy:!0}])})],1),a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showWechatQr",label:"显示微信公众号二维码"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"qr",color:"#07c160"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showWechatQr,callback:function(a){t.$set(t.formData,"showWechatQr",a)},expression:"formData.showWechatQr"}})]},proxy:!0}])})],1),a("div",{staticClass:"switch-item"},[a("van-field",{staticClass:"switch-field",attrs:{name:"showMyShop",label:"显示我的小店"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"shop-o",color:"#ff9500"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-switch",{staticClass:"custom-switch",attrs:{"active-value":1,"inactive-value":0},model:{value:t.formData.showMyShop,callback:function(a){t.$set(t.formData,"showMyShop",a)},expression:"formData.showMyShop"}})]},proxy:!0}])})],1)])])])])]),t.formData.showMyShop?a("div",{staticClass:"form-section",attrs:{id:"shop-settings"}},[a("div",{staticClass:"section-header"},[a("div",{staticClass:"section-title"},[a("div",{staticClass:"title-icon"},[a("van-icon",{attrs:{name:"shop-o"}})],1),a("div",{staticClass:"title-content"},[a("h3",[t._v("我的小店配置")]),a("p",[t._v("配置商户自己的网页或小程序跳转")])])]),a("div",{staticClass:"section-badge"},[a("van-tag",{attrs:{type:"warning",size:"medium"}},[t._v("必填")])],1)]),a("div",{staticClass:"section-content"},[a("van-field",{staticClass:"custom-field",attrs:{name:"shopType",label:"小店类型"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"apps-o"}})]},proxy:!0},{key:"input",fn:function(){return[a("van-radio-group",{attrs:{direction:"horizontal"},model:{value:t.formData.shopType,callback:function(a){t.$set(t.formData,"shopType",a)},expression:"formData.shopType"}},[a("van-radio",{attrs:{name:0}},[t._v("网页")]),a("van-radio",{attrs:{name:1}},[t._v("小程序")])],1)]},proxy:!0}],null,!1,3205403167)}),0===t.formData.shopType?a("van-field",{staticClass:"custom-field",attrs:{name:"shopUrl",label:"小店网页URL",placeholder:"请输入小店网页URL，如：https://www.example.com/shop",rules:t.formData.showMyShop&&0===t.formData.shopType?[{required:!0,message:"请输入小店网页URL"}]:[]},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"link-o"}})]},proxy:!0}],null,!1,3771187939),model:{value:t.formData.shopUrl,callback:function(a){t.$set(t.formData,"shopUrl",a)},expression:"formData.shopUrl"}}):t._e(),1===t.formData.shopType?[a("van-field",{staticClass:"custom-field",attrs:{name:"shopAppid",label:"小程序AppID",placeholder:"请输入小程序AppID",rules:t.formData.showMyShop&&1===t.formData.shopType?[{required:!0,message:"请输入小程序AppID"}]:[]},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"wechat"}})]},proxy:!0}],null,!1,2848434541),model:{value:t.formData.shopAppid,callback:function(a){t.$set(t.formData,"shopAppid",a)},expression:"formData.shopAppid"}}),a("van-field",{staticClass:"custom-field",attrs:{name:"shopPagePath",label:"小程序页面路径",placeholder:"请输入页面路径，如：pages/shop/index",rules:t.formData.showMyShop&&1===t.formData.shopType?[{required:!0,message:"请输入小程序页面路径"}]:[]},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"location-o"}})]},proxy:!0}],null,!1,809920510),model:{value:t.formData.shopPagePath,callback:function(a){t.$set(t.formData,"shopPagePath",a)},expression:"formData.shopPagePath"}})]:t._e(),a("van-field",{staticClass:"custom-field",attrs:{name:"shopDescription",label:"小店描述",placeholder:"请输入小店描述，如：商户专属店铺"},scopedSlots:t._u([{key:"left-icon",fn:function(){return[a("van-icon",{attrs:{name:"description"}})]},proxy:!0}],null,!1,1209775431),model:{value:t.formData.shopDescription,callback:function(a){t.$set(t.formData,"shopDescription",a)},expression:"formData.shopDescription"}})],2)]):t._e(),a("div",{staticClass:"form-footer"},[a("div",{staticClass:"footer-content"},[a("div",{staticClass:"footer-info"},[a("van-icon",{attrs:{name:"info-o"}}),a("span",[t._v("修改后将立即生效")])],1),a("div",{staticClass:"footer-buttons"},[a("van-button",{staticClass:"cancel-button",attrs:{size:"large"},on:{click:t.goBack}},[t._v("\n              取消\n            ")]),a("van-button",{staticClass:"save-button",attrs:{type:"primary",size:"large",loading:t.saving},on:{click:t.saveActivity}},[t.saving?t._e():a("van-icon",{attrs:{name:"success"}}),t._v("\n              保存修改\n            ")],1)],1)])])])],1)],1)},o=[],s=(i("8e6e"),i("ac6a"),i("456d"),i("ade3")),n=(i("7514"),i("28a5"),i("2909"));function r(t,a){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(t);a&&(e=e.filter((function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable}))),i.push.apply(i,e)}return i}function c(t){for(var a=1;a<arguments.length;a++){var i=null!=arguments[a]?arguments[a]:{};a%2?r(Object(i),!0).forEach((function(a){Object(s["a"])(t,a,i[a])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):r(Object(i)).forEach((function(a){Object.defineProperty(t,a,Object.getOwnPropertyDescriptor(i,a))}))}return t}var l={name:"ActivityEdit",data:function(){return{saving:!1,activityId:null,musicList:[],formData:{name:"",logo:"",mobileBanner:"",background:"",shareUrl:"",musicUrl:"",douyinType:0,xiaohongshuType:1,douyinPoi:"",douyindianping:"",meituan:"",dazhongdianping:"",qiyeweixin:"",zhuyeDouyin:"",zhuyeKuaishou:"",wifiAccount:"",wifiPassword:"",wechatQrCode:"",showDouyin:1,showXiaohongshu:1,showKuaishou:1,showShipinhao:1,showDouyindianping:1,showDazhongdianping:1,showMeituandianping:1,showQiyeweixin:1,showMiniProgram:1,showWifi:1,showGuanzhukuaishou:1,showGuanzhudouyin:1,showShipindianzan:1,nameMode:"ai",defaultName:"",defaultTitle:"",defaultUserInput:"",showCtrip:0,ctripConfig:"",showCtripReview:0,showCtripNotes:0,ctripReviewConfig:"",ctripNotesConfig:"",showWechatQr:0,mobile:"",adminAccount:"",expirationTime:null,shopType:0,shopUrl:"",shopAppid:"",shopPagePath:"",shopDescription:"商户专属店铺",showMyShop:0,showGroupBuying:0},logoFileList:[],mobileBannerFileList:[],backgroundFileList:[],shareUrlFileList:[],wechatQrCodeFileList:[]}},computed:{musicOptions:function(){return[{text:"无",value:""}].concat(Object(n["a"])(this.musicList.map((function(t){return{text:t.name,value:t.url}}))))}},mounted:function(){this.activityId=this.$route.query.id,this.activityId&&this.loadActivityData(),this.loadMusicList()},methods:{goBack:function(){this.$router.go(-1)},loadActivityData:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(a){if(200===a.code){var i=a.activity;t.formData={name:i.name||"",logo:i.logo||"",mobileBanner:i.mobileBanner||"",background:i.background||"",shareUrl:i.shareUrl||"",musicUrl:i.musicUrl||"",douyinType:i.douyinType||0,xiaohongshuType:i.xiaohongshuType||1,douyinPoi:i.douyinPoi||"",douyindianping:i.douyindianping||"",meituan:i.meituan||"",dazhongdianping:i.dazhongdianping||"",qiyeweixin:i.qiyeweixin||"",wifiAccount:i.wifiAccount||"",wifiPassword:i.wifiPassword||"",wechatQrCode:i.wechatQrCode||"",zhuyeDouyin:i.zhuyeDouyin||"",zhuyeKuaishou:i.zhuyeKuaishou||"",showDouyin:void 0!==i.showDouyin?i.showDouyin:1,showXiaohongshu:void 0!==i.showXiaohongshu?i.showXiaohongshu:1,showKuaishou:void 0!==i.showKuaishou?i.showKuaishou:1,showShipinhao:void 0!==i.showShipinhao?i.showShipinhao:1,showDouyindianping:void 0!==i.showDouyindianping?i.showDouyindianping:1,showDazhongdianping:void 0!==i.showDazhongdianping?i.showDazhongdianping:1,showMeituandianping:void 0!==i.showMeituandianping?i.showMeituandianping:1,showQiyeweixin:void 0!==i.showQiyeweixin?i.showQiyeweixin:1,showWifi:void 0!==i.showWifi?i.showWifi:1,showGuanzhukuaishou:void 0!==i.showGuanzhukuaishou?i.showGuanzhukuaishou:1,showGuanzhudouyin:void 0!==i.showGuanzhudouyin?i.showGuanzhudouyin:1,showShipindianzan:void 0!==i.showShipindianzan?i.showShipindianzan:1,nameMode:i.nameMode||"ai",defaultName:i.defaultName||"",defaultTitle:i.defaultTitle||"",defaultUserInput:i.defaultUserInput||"",showCtrip:void 0!==i.showCtrip?i.showCtrip:0,ctripConfig:i.ctripConfig||"",showCtripReview:void 0!==i.showCtripReview?i.showCtripReview:0,showCtripNotes:void 0!==i.showCtripNotes?i.showCtripNotes:0,ctripReviewConfig:i.ctripReviewConfig||"",ctripNotesConfig:i.ctripNotesConfig||"",showWechatQr:void 0!==i.showWechatQr?i.showWechatQr:0,mobile:i.mobile||"",adminAccount:i.adminAccount||"",expirationTime:i.expirationTime||null,shopDescription:i.shopDescription||"商户专属店铺",showGroupBuying:void 0!==i.showGroupBuying?i.showGroupBuying:0},t.initFileList()}else t.$toast.fail(a.msg||"加载活动数据失败")})).catch((function(a){console.error("加载活动数据失败:",a),t.$toast.fail("加载活动数据失败")}))},loadMusicList:function(){var t=this;this.$fly.get("/pyp/sys/sysmusic/findAll").then((function(a){200===a.code&&(t.musicList=a.result||[])})).catch((function(t){console.error("加载音乐列表失败:",t)}))},initFileList:function(){this.formData.logo&&(this.logoFileList=[{url:this.formData.logo,isImage:!0}]),this.formData.mobileBanner&&(this.mobileBannerFileList=this.formData.mobileBanner.split(",").map((function(t){return{url:t.trim(),isImage:!0}})).filter((function(t){return t.url}))),this.formData.background&&(this.backgroundFileList=[{url:this.formData.background,isImage:!0}]),this.formData.shareUrl&&(this.shareUrlFileList=[{url:this.formData.shareUrl,isImage:!0}]),this.formData.wechatQrCode&&(this.wechatQrCodeFileList=[{url:this.formData.wechatQrCode,isImage:!0}])},afterRead:function(t,a){var i=this,e=new FormData;e.append("file",t.file),this.$fly.post("/pyp/web/upload",e).then((function(e){if(200===e.code){var o=e.result;switch(a){case"logo":i.formData.logo=o;break;case"mobileBanner":var s=i.formData.mobileBanner?i.formData.mobileBanner.split(","):[];s.push(o),i.formData.mobileBanner=s.join(",");break;case"background":i.formData.background=o;break;case"shareUrl":i.formData.shareUrl=o;break;case"wechatQrCode":i.formData.wechatQrCode=o;break}t.url=o,i.$toast.success("上传成功")}else i.$toast.fail(e.msg||"上传失败")})).catch((function(t){console.error("上传失败:",t),i.$toast.fail("上传失败")}))},beforeDelete:function(t,a){switch(t){case"logo":this.formData.logo="";break;case"mobileBanner":if(a&&a.url){var i=this.formData.mobileBanner.split(","),e=i.indexOf(a.url);e>-1&&(i.splice(e,1),this.formData.mobileBanner=i.join(","))}break;case"background":this.formData.background="";break;case"shareUrl":this.formData.shareUrl="";break;case"wechatQrCode":this.formData.wechatQrCode="";break}return!0},onMusicConfirm:function(t){this.formData.musicUrl=t},getMusicName:function(t){if(!t)return"";var a=this.musicList.find((function(a){return a.url===t}));return a?a.name:"未知音乐"},playMusic:function(){var t=this;if(this.formData.musicUrl){var a=new Audio(this.formData.musicUrl);a.play().catch((function(){t.$toast.fail("音乐预览失败")})),this.$toast.success("开始播放预览")}},clearMusic:function(){this.formData.musicUrl="",this.$toast.success("已清除背景音乐")},onExpirationTimeConfirm:function(t){this.formData.expirationTime=t},formatExpirationTime:function(t){if(!t)return"";var a=new Date(t);return a.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},onSubmit:function(){this.saveActivity()},saveActivity:function(){var t=this;this.$refs.form.validate().then((function(){t.saving=!0,t.$fly.post("/pyp/web/activity/update",c({id:t.activityId},t.formData)).then((function(a){t.saving=!1,200===a.code?(t.$toast.success("保存成功"),setTimeout((function(){t.goBack()}),1e3)):t.$toast.fail(a.msg||"保存失败")})).catch((function(a){t.saving=!1,console.error("保存失败:",a),t.$toast.fail("保存失败")}))})).catch((function(){t.$toast.fail("请检查表单填写")}))}}},u=l,f=(i("6ccb"),i("2877")),d=Object(f["a"])(u,e,o,!1,null,"a13718b8",null);a["default"]=d.exports},f1ae:function(t,a,i){"use strict";var e=i("86cc"),o=i("4630");t.exports=function(t,a,i){a in t?e.f(t,a,o(0,i)):t[a]=i}}}]);
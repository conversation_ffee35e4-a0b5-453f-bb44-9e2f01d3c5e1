(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d20f394"],{b35b:function(a,t,e){"use strict";e.r(t);var r=function(){var a=this,t=a._self._c;return t("el-dialog",{attrs:{title:a.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:a.visible},on:{"update:visible":function(t){a.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:a.dataForm,rules:a.dataRule,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"会议id",prop:"activityId"}},[t("el-input",{attrs:{placeholder:"会议id"},model:{value:a.dataForm.activityId,callback:function(t){a.$set(a.dataForm,"activityId",t)},expression:"dataForm.activityId"}})],1),t("el-form-item",{attrs:{label:"ip地址",prop:"ipAddr"}},[t("el-input",{attrs:{placeholder:"ip地址"},model:{value:a.dataForm.ipAddr,callback:function(t){a.$set(a.dataForm,"ipAddr",t)},expression:"dataForm.ipAddr"}})],1),t("el-form-item",{attrs:{label:"设备",prop:"device"}},[t("el-input",{attrs:{placeholder:"设备"},model:{value:a.dataForm.device,callback:function(t){a.$set(a.dataForm,"device",t)},expression:"dataForm.device"}})],1),t("el-form-item",{attrs:{label:"mac地址",prop:"macAddr"}},[t("el-input",{attrs:{placeholder:"mac地址"},model:{value:a.dataForm.macAddr,callback:function(t){a.$set(a.dataForm,"macAddr",t)},expression:"dataForm.macAddr"}})],1),t("el-form-item",{attrs:{label:"浏览时长",prop:"count"}},[t("el-input",{attrs:{placeholder:"浏览时长"},model:{value:a.dataForm.count,callback:function(t){a.$set(a.dataForm,"count",t)},expression:"dataForm.count"}})],1),t("el-form-item",{attrs:{label:"房间id",prop:"placeId"}},[t("el-input",{attrs:{placeholder:"房间id"},model:{value:a.dataForm.placeId,callback:function(t){a.$set(a.dataForm,"placeId",t)},expression:"dataForm.placeId"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){a.visible=!1}}},[a._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return a.dataFormSubmit()}}},[a._v("确定")])],1)],1)},d=[],i={data:function(){return{visible:!1,dataForm:{id:0,activityId:"",ipAddr:"",device:"",macAddr:"",count:"",placeId:""},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],ipAddr:[{required:!0,message:"ip地址不能为空",trigger:"blur"}],device:[{required:!0,message:"设备不能为空",trigger:"blur"}],macAddr:[{required:!0,message:"mac地址不能为空",trigger:"blur"}],count:[{required:!0,message:"浏览时长不能为空",trigger:"blur"}],placeId:[{required:!0,message:"房间id不能为空",trigger:"blur"}]}}},methods:{init:function(a){var t=this;this.dataForm.id=a||0,this.visible=!0,this.$nextTick((function(){t.$refs["dataForm"].resetFields(),t.dataForm.id&&t.$http({url:t.$http.adornUrl("/place/placeactivityhourlog/info/".concat(t.dataForm.id)),method:"get",params:t.$http.adornParams()}).then((function(a){var e=a.data;e&&200===e.code&&(t.dataForm.activityId=e.placeActivityHourLog.activityId,t.dataForm.ipAddr=e.placeActivityHourLog.ipAddr,t.dataForm.device=e.placeActivityHourLog.device,t.dataForm.macAddr=e.placeActivityHourLog.macAddr,t.dataForm.createOn=e.placeActivityHourLog.createOn,t.dataForm.createBy=e.placeActivityHourLog.createBy,t.dataForm.updateOn=e.placeActivityHourLog.updateOn,t.dataForm.updateBy=e.placeActivityHourLog.updateBy,t.dataForm.count=e.placeActivityHourLog.count,t.dataForm.placeId=e.placeActivityHourLog.placeId)}))}))},dataFormSubmit:function(){var a=this;this.$refs["dataForm"].validate((function(t){t&&a.$http({url:a.$http.adornUrl("/place/placeactivityhourlog/".concat(a.dataForm.id?"update":"save")),method:"post",data:a.$http.adornData({id:a.dataForm.id||void 0,activityId:a.dataForm.activityId,ipAddr:a.dataForm.ipAddr,device:a.dataForm.device,macAddr:a.dataForm.macAddr,count:a.dataForm.count,placeId:a.dataForm.placeId})}).then((function(t){var e=t.data;e&&200===e.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.visible=!1,a.$emit("refreshDataList")}}):a.$message.error(e.msg)}))}))}}},o=i,c=e("2877"),l=Object(c["a"])(o,r,d,!1,null,null,null);t["default"]=l.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-70576988","chunk-2d0a4b8c","chunk-2d0a4b8c","chunk-58faa667","chunk-70b580ed"],{"02d7":function(t,e,a){"use strict";a.d(e,"d",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"b",(function(){return o})),a.d(e,"a",(function(){return r}));var n=[{key:0,value:"考卷"},{key:1,value:"问卷"}],i=[{key:0,value:"统一时间考试"},{key:1,value:"随时考试"}],o=[{key:0,value:"单选"},{key:1,value:"多选"},{key:2,value:"填空"}],r=[{key:0,value:"未提交"},{key:1,value:"已提交"},{key:2,value:"已通过"},{key:3,value:"未通过"},{key:4,value:"已超时"},{key:5,value:"作废"}]},"083a":function(t,e,a){"use strict";var n=a("0d51"),i=TypeError;t.exports=function(t,e){if(!delete t[e])throw new i("Cannot delete property "+n(e)+" of "+n(t))}},5435:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"参数名",clearable:""},model:{value:t.dataForm.key,callback:function(e){t.$set(t.dataForm,"key",e)},expression:"dataForm.key"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),t.isAuth("exam:examquestion:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("exam:examquestion:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"name","header-align":"center","show-overflow-tooltip":"",align:"center",label:"题目名称"}}),e("el-table-column",{attrs:{prop:"type","header-align":"center",align:"center",label:"题目类型"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-tag",{class:"tag-color tag-color-"+a.row.type,attrs:{type:"primary"}},[t._v(t._s(t.examQuestionType[a.row.type].value))])],1)}}])}),e("el-table-column",{attrs:{prop:"charOptionId","header-align":"center",align:"center",label:"正确答案"}}),e("el-table-column",{attrs:{prop:"points","header-align":"center",align:"center",label:"自定义分值"}}),e("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",label:"创建时间"}}),e("el-table-column",{attrs:{prop:"updateOn","header-align":"center",align:"center",label:"更新时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e()],1)},i=[],o=(a("99af"),a("a15b"),a("d81d"),a("a573"),a("02d7")),r=a("7f00"),d={data:function(){return{examQuestionType:o["b"],dataForm:{key:"",examId:void 0,activityId:void 0},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1}},components:{AddOrUpdate:r["default"]},activated:function(){this.dataForm.activityId=this.$route.query.activityId,this.dataForm.examId=this.$route.query.examId,this.getDataList()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/exam/examquestion/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,key:this.dataForm.key,activityId:this.dataForm.activityId,examId:this.dataForm.examId})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(e.dataForm.activityId,e.dataForm.examId,t)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/exam/examquestion/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))}}},s=d,l=a("2877"),c=Object(l["a"])(s,n,i,!1,null,null,null);e["default"]=c.exports},"7f00":function(t,e,a){"use strict";a.r(e);a("b0c0");var n=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"题目类型",prop:"type"}},[e("el-select",{attrs:{placeholder:"题目类型",filterable:""},on:{change:t.changeTypeHandle},model:{value:t.dataForm.type,callback:function(e){t.$set(t.dataForm,"type",e)},expression:"dataForm.type"}},t._l(t.examQuestionType,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1),e("el-form-item",{attrs:{label:"题目名称",prop:"name"}},[e("tinymce-editor",{ref:"editor",model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",{attrs:{label:"自定义分值",prop:"points"}},[e("el-input",{attrs:{placeholder:"自定义分值"},model:{value:t.dataForm.points,callback:function(e){t.$set(t.dataForm,"points",e)},expression:"dataForm.points"}})],1),2!=t.dataForm.type?e("div",[e("el-form-item",{attrs:{label:"选项"}},[e("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.addOptionHandle(t.options.length)}}},[t._v("添加选项")]),e("el-table",{attrs:{data:t.options}},[e("el-table-column",{attrs:{prop:"optionId","header-align":"center",align:"center",label:"选项"}}),e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"内容"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-input",{attrs:{placeholder:"内容"},model:{value:a.row.name,callback:function(e){t.$set(a.row,"name",e)},expression:"scope.row.name"}})],1)}}],null,!1,2191848166)}),e("el-table-column",{attrs:{"header-align":"center",align:"center",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.deletePaymentHandle(a.row)}}},[t._v("删除")])],1)}}],null,!1,3870104636)})],1)],1),0==t.dataForm.type?e("el-form-item",{attrs:{label:"答案",prop:"charOptionId"}},[e("el-select",{attrs:{placeholder:"请选择类型"},model:{value:t.dataForm.charOptionId,callback:function(e){t.$set(t.dataForm,"charOptionId",e)},expression:"dataForm.charOptionId"}},t._l(t.options,(function(t){return e("el-option",{key:t.optionId,attrs:{label:t.optionId,value:t.optionId}})})),1)],1):e("el-form-item",{attrs:{label:"答案",prop:"charOptionId"}},[e("el-select",{attrs:{multiple:"",placeholder:"请选择类型"},model:{value:t.dataForm.charOptionId,callback:function(e){t.$set(t.dataForm,"charOptionId",e)},expression:"dataForm.charOptionId"}},t._l(t.options,(function(t){return e("el-option",{key:t.optionId,attrs:{label:t.optionId,value:t.optionId}})})),1)],1)],1):e("div",[e("el-form-item",{attrs:{label:"答案",prop:"charOptionId"}},[e("el-input",{attrs:{placeholder:"答案"},model:{value:t.dataForm.charOptionId,callback:function(e){t.$set(t.dataForm,"charOptionId",e)},expression:"dataForm.charOptionId"}})],1)],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},i=[],o=(a("14d9"),a("a434"),a("d3b7"),a("25f0"),a("3ca3"),a("ddb0"),a("02d7")),r={data:function(){return{examQuestionType:o["b"],visible:!1,dataForm:{id:0,activityId:"",examId:"",name:"",type:"",optionId:"",charOptionId:"",points:0},options:[{optionId:"A",name:""}],character:["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],examId:[{required:!0,message:"考卷id不能为空",trigger:"blur"}],name:[{required:!0,message:"题目名称不能为空",trigger:"blur"}],type:[{required:!0,message:"题目类型不能为空",trigger:"blur"}],optionId:[{required:!0,message:"正确答案不能为空",trigger:"blur"}],points:[{required:!0,message:"自定义分值不能为空",trigger:"blur"}]}}},components:{TinymceEditor:function(){return Promise.all([a.e("chunk-2d0e1c2e"),a.e("chunk-03be236c")]).then(a.bind(null,"26dc"))},OssUploader:function(){return a.e("chunk-2d0e97b1").then(a.bind(null,"8e5c"))}},methods:{init:function(t,e,a){var n=this;this.dataForm.activityId=t,this.dataForm.examId=e,this.dataForm.id=a||0,this.visible=!0,this.$nextTick((function(){n.$refs["dataForm"].resetFields(),n.options=[],n.dataForm.id&&(n.$http({url:n.$http.adornUrl("/exam/examquestion/info/".concat(n.dataForm.id)),method:"get",params:n.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(n.dataForm.activityId=e.examQuestion.activityId,n.dataForm.examId=e.examQuestion.examId,n.dataForm.name=e.examQuestion.name,n.dataForm.type=e.examQuestion.type,1==n.dataForm.type?n.dataForm.charOptionId=e.examQuestion.charOptionId.split(","):n.dataForm.charOptionId=e.examQuestion.charOptionId,n.dataForm.points=e.examQuestion.points)})),n.$http({url:n.$http.adornUrl("/exam/examquestionoption/findByQuestionId/".concat(n.dataForm.id)),method:"get",params:n.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(n.options=e.result)})))}))},changeTypeHandle:function(){1==this.dataForm.type?this.dataForm.charOptionId=[]:this.dataForm.charOptionId=""},addOptionHandle:function(t){if(t>=26)return this.$message.error("选项数量已上线"),!1;this.options.push({optionId:this.character[t],name:""}),this.$forceUpdate()},deletePaymentHandle:function(t){var e=this.options.indexOf(t);if(this.options.length<=1)this.$message.error("至少保留一个选项");else if(-1!==e){if(e==this.options.length-1)this.options.splice(e,1);else{if(0==this.dataForm.type)(this.dataForm.charOptionId=this.options[e].id)&&(this.dataForm.charOptionId="");else if(1==this.dataForm.type&&-1!=this.dataForm.charOptionId.indexOf(this.options[e].id))for(var a in this.dataForm.charOptionId.splice(this.dataForm.charOptionId.indexOf(this.options[e].id),1),this.dataForm.charOptionId)this.character.indexOf(this.dataForm.charOptionId[a])>this.character.indexOf(this.options[e].id)&&(this.dataForm.charOptionId[a]=this.character[this.character.indexOf(this.dataForm.charOptionId[a])-1]);for(var n in this.options)n>e&&(this.options[n].optionId=this.character[n-1]);this.options.splice(e,1)}this.$forceUpdate()}},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){if(e){if(2!=t.dataForm.type)for(var a=0;a<t.options.length;a++)if(""==t.options[a].optionId||""==t.options[a].name)return t.$message.error("选项不为空，请填写完整"),!1;1==t.dataForm.type&&(t.dataForm.charOptionId=t.dataForm.charOptionId.toString()),t.$http({url:t.$http.adornUrl("/exam/examquestion/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,activityId:t.dataForm.activityId,examId:t.dataForm.examId,name:t.dataForm.name,type:t.dataForm.type,optionId:t.dataForm.optionId,charOptionId:t.dataForm.charOptionId,points:t.dataForm.points,examQuestionOptionEntities:t.options})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}}))}}},d=r,s=a("2877"),l=Object(s["a"])(d,n,i,!1,null,null,null);e["default"]=l.exports},a15b:function(t,e,a){"use strict";var n=a("23e7"),i=a("e330"),o=a("44ad"),r=a("fc6a"),d=a("a640"),s=i([].join),l=o!==Object,c=l||!d("join",",");n({target:"Array",proto:!0,forced:c},{join:function(t){return s(r(this),void 0===t?",":t)}})},a434:function(t,e,a){"use strict";var n=a("23e7"),i=a("7b0b"),o=a("23cb"),r=a("5926"),d=a("07fa"),s=a("3a34"),l=a("3511"),c=a("65f0"),p=a("8418"),u=a("083a"),m=a("1dde"),h=m("splice"),f=Math.max,g=Math.min;n({target:"Array",proto:!0,forced:!h},{splice:function(t,e){var a,n,m,h,v,b,y=i(this),F=d(y),I=o(t,F),x=arguments.length;for(0===x?a=n=0:1===x?(a=0,n=F-I):(a=x-2,n=g(f(r(e),0),F-I)),l(F+a-n),m=c(y,n),h=0;h<n;h++)v=I+h,v in y&&p(m,h,y[v]);if(m.length=n,a<n){for(h=I;h<F-n;h++)v=h+n,b=h+a,v in y?y[b]=y[v]:u(y,b);for(h=F;h>F-n+a;h--)u(y,h-1)}else if(a>n)for(h=F-n;h>I;h--)v=h+n-1,b=h+a-1,v in y?y[b]=y[v]:u(y,b);for(h=0;h<a;h++)y[h+I]=arguments[h+2];return s(y,F-n+a),m}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var n=a("23e7"),i=a("d024"),o=a("c430");n({target:"Iterator",proto:!0,real:!0,forced:o},{map:i})},d024:function(t,e,a){"use strict";var n=a("c65b"),i=a("59ed"),o=a("825a"),r=a("46c4"),d=a("c5cc"),s=a("9bdd"),l=d((function(){var t=this.iterator,e=o(n(this.next,t)),a=this.done=!!e.done;if(!a)return s(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return o(this),i(t),new l(r(this),{mapper:t})}},d81d:function(t,e,a){"use strict";var n=a("23e7"),i=a("b727").map,o=a("1dde"),r=o("map");n({target:"Array",proto:!0,forced:!r},{map:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})}}]);
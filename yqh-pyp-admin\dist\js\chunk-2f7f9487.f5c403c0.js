(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2f7f9487","chunk-269932be"],{1485:function(a,t,e){"use strict";e("9bb9")},"31f0":function(a,t,e){"use strict";e.r(t);e("99af"),e("b0c0");var s=function(){var a=this,t=a._self._c;return t("el-dialog",{attrs:{title:a.dialogTitle,"close-on-click-modal":!1,visible:a.visible,width:"600px"},on:{"update:visible":function(t){a.visible=t}}},[t("div",{staticClass:"order-info"},[t("h4",[a._v("订单信息")]),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"label"},[a._v("订单号：")]),t("span",{staticClass:"value"},[a._v(a._s(a.orderInfo.orderSn))])])]),t("el-col",{attrs:{span:12}},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"label"},[a._v("订单金额：")]),t("span",{staticClass:"value amount"},[a._v("¥"+a._s(a.orderInfo.amount))])])]),t("el-col",{attrs:{span:12}},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"label"},[a._v("微信用户：")]),t("span",{staticClass:"value"},[a._v(a._s(a.orderInfo.wxUserName))])])]),t("el-col",{attrs:{span:12}},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"label"},[a._v("下单时间：")]),t("span",{staticClass:"value"},[a._v(a._s(a.orderInfo.createOn))])])])],1)],1),"change"===a.mode&&a.orderInfo.salesmanName?t("div",{staticClass:"current-association"},[t("h4",[a._v("当前关联业务员")]),t("div",{staticClass:"current-salesman"},[t("span",{staticClass:"name"},[a._v(a._s(a.orderInfo.salesmanName))]),t("span",{staticClass:"code"},[a._v("（"+a._s(a.orderInfo.salesmanCode)+"）")])])]):a._e(),t("div",{staticClass:"salesman-selection"},[t("h4",[a._v(a._s("change"===a.mode?"选择新业务员":"选择业务员"))]),t("el-form",{ref:"dataForm",attrs:{model:a.dataForm,rules:a.dataRule,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"业务员",prop:"salesmanId"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择业务员",filterable:"",remote:"","remote-method":a.searchSalesmen,loading:a.salesmanLoading},model:{value:a.dataForm.salesmanId,callback:function(t){a.$set(a.dataForm,"salesmanId",t)},expression:"dataForm.salesmanId"}},a._l(a.salesmanOptions,(function(e){return t("el-option",{key:e.id,attrs:{label:"".concat(e.name," (").concat(e.code,")"),value:e.id}},[t("span",{staticStyle:{float:"left"}},[a._v(a._s(e.name))]),t("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[a._v(a._s(e.code))])])})),1)],1),t("el-form-item",{attrs:{label:"关联原因",prop:"reason"}},[t("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入关联原因"},model:{value:a.dataForm.reason,callback:function(t){a.$set(a.dataForm,"reason",t)},expression:"dataForm.reason"}})],1)],1)],1),a.recommendedSalesman?t("div",{staticClass:"recommended-salesman"},[t("h4",[a._v("推荐业务员")]),t("div",{staticClass:"recommendation"},[t("el-alert",{attrs:{title:"推荐关联业务员：".concat(a.recommendedSalesman.name," (").concat(a.recommendedSalesman.code,")"),type:"info",closable:!1}},[t("template",{slot:"description"},[t("p",[a._v("推荐原因：该用户当前绑定的业务员")]),t("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return a.useRecommended()}}},[a._v("使用推荐")])],1)],2)],1)]):a._e(),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){a.visible=!1}}},[a._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return a.dataFormSubmit()}}},[a._v("确定")])],1)])},n=[],i={data:function(){return{visible:!1,mode:"associate",orderInfo:{},dataForm:{salesmanId:"",reason:""},dataRule:{salesmanId:[{required:!0,message:"业务员不能为空",trigger:"change"}],reason:[{required:!0,message:"关联原因不能为空",trigger:"blur"}]},salesmanOptions:[],salesmanLoading:!1,recommendedSalesman:null}},computed:{dialogTitle:function(){return"change"===this.mode?"更换业务员":"关联业务员"}},methods:{init:function(a,t){var e=this;this.orderInfo=a,this.mode=t,this.visible=!0,this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.dataForm.reason="change"===t?"管理员更换业务员":"管理员手动关联",e.getRecommendedSalesman()}))},getRecommendedSalesman:function(){var a=this;this.$http({url:this.$http.adornUrl("/salesman/orderassociation/findSalesmanByUser"),method:"get",params:this.$http.adornParams({userId:this.orderInfo.userId})}).then((function(t){var e=t.data;e&&200===e.code&&e.salesmanId&&a.$http({url:a.$http.adornUrl("/salesman/salesman/info/".concat(e.salesmanId)),method:"get"}).then((function(t){var e=t.data;e&&200===e.code&&(a.recommendedSalesman=e.salesman)}))}))},useRecommended:function(){this.dataForm.salesmanId=this.recommendedSalesman.id,this.salesmanOptions=[this.recommendedSalesman]},searchSalesmen:function(a){var t=this;""!==a?(this.salesmanLoading=!0,this.$http({url:this.$http.adornUrl("/salesman/salesman/search"),method:"get",params:this.$http.adornParams({keyword:a,limit:20})}).then((function(a){var e=a.data;t.salesmanLoading=!1,e&&200===e.code&&(t.salesmanOptions=e.list||[])})).catch((function(){t.salesmanLoading=!1}))):this.salesmanOptions=[]},dataFormSubmit:function(){var a=this;this.$refs["dataForm"].validate((function(t){if(t){var e="change"===a.mode?"/salesman/orderassociation/change":"/salesman/orderassociation/associate";a.$http({url:a.$http.adornUrl(e),method:"post",params:a.$http.adornParams({rechargeRecordId:a.orderInfo.id,salesmanId:a.dataForm.salesmanId,reason:a.dataForm.reason})}).then((function(t){var e=t.data;e&&200===e.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.visible=!1,a.$emit("refreshDataList")}}):a.$message.error(e.msg)}))}}))}}},o=i,r=(e("1485"),e("2877")),l=Object(r["a"])(o,s,n,!1,null,"71e7c8a5",null);t["default"]=l.exports},"363c":function(a,t,e){},"4f8f":function(a,t,e){"use strict";e("363c")},"57a2":function(a,t,e){"use strict";e.r(t);var s=function(){var a=this,t=a._self._c;return t("div",{staticClass:"mod-config"},[t("el-form",{attrs:{inline:!0,model:a.dataForm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&a._k(t.keyCode,"enter",13,t.key,"Enter")?null:a.getDataList()}}},[t("el-form-item",[t("el-input",{attrs:{placeholder:"订单号",clearable:""},model:{value:a.dataForm.orderSn,callback:function(t){a.$set(a.dataForm,"orderSn",t)},expression:"dataForm.orderSn"}})],1),t("el-form-item",[t("el-input",{attrs:{placeholder:"微信用户",clearable:""},model:{value:a.dataForm.wxUserName,callback:function(t){a.$set(a.dataForm,"wxUserName",t)},expression:"dataForm.wxUserName"}})],1),t("el-form-item",[t("el-input",{attrs:{placeholder:"业务员",clearable:""},model:{value:a.dataForm.salesmanName,callback:function(t){a.$set(a.dataForm,"salesmanName",t)},expression:"dataForm.salesmanName"}})],1),t("el-form-item",[t("el-select",{attrs:{placeholder:"关联状态",clearable:""},model:{value:a.dataForm.hasAssociation,callback:function(t){a.$set(a.dataForm,"hasAssociation",t)},expression:"dataForm.hasAssociation"}},[t("el-option",{attrs:{label:"已关联",value:1}}),t("el-option",{attrs:{label:"未关联",value:0}})],1)],1),t("el-form-item",[t("el-button",{on:{click:function(t){return a.getDataList()}}},[a._v("查询")]),t("el-button",{attrs:{type:"primary",disabled:a.dataListSelections.length<=0},on:{click:function(t){return a.batchAssociate()}}},[a._v("批量关联")]),t("el-button",{attrs:{type:"warning",disabled:a.dataListSelections.length<=0},on:{click:function(t){return a.batchDisassociate()}}},[a._v("批量取消关联")]),t("el-button",{attrs:{type:"success"},on:{click:function(t){return a.batchAssociateHistorical()}}},[a._v("关联历史订单")])],1),t("el-form-item",{staticStyle:{float:"right"}},[t("el-button",{attrs:{type:"success"},on:{click:function(t){return a.$router.go(-1)}}},[a._v("返回")])],1)],1),t("div",{staticClass:"stats-overview",staticStyle:{"margin-bottom":"20px"}},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"stats-card"},[t("div",{staticClass:"stats-item"},[t("div",{staticClass:"stats-value"},[a._v(a._s(a.overallStats.totalOrders||0))]),t("div",{staticClass:"stats-label"},[a._v("总订单数")])])])],1),t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"stats-card"},[t("div",{staticClass:"stats-item"},[t("div",{staticClass:"stats-value"},[a._v(a._s(a.overallStats.associatedOrders||0))]),t("div",{staticClass:"stats-label"},[a._v("已关联订单")])])])],1),t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"stats-card"},[t("div",{staticClass:"stats-item"},[t("div",{staticClass:"stats-value"},[a._v(a._s(a.overallStats.unassociatedOrders||0))]),t("div",{staticClass:"stats-label"},[a._v("未关联订单")])])])],1),t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"stats-card"},[t("div",{staticClass:"stats-item"},[t("div",{staticClass:"stats-value"},[a._v(a._s(a.overallStats.associationRate||"0%"))]),t("div",{staticClass:"stats-label"},[a._v("关联率")])])])],1)],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:a.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:a.dataList,border:""},on:{"selection-change":a.selectionChangeHandle}},[t("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),t("el-table-column",{attrs:{prop:"orderSn","header-align":"center",align:"center",label:"订单号"}}),t("el-table-column",{attrs:{prop:"userName","header-align":"center",align:"center",label:"微信用户"},scopedSlots:a._u([{key:"default",fn:function(e){return[t("div",[t("div",[a._v(a._s(e.row.userName))]),t("div",{staticStyle:{color:"#999","font-size":"12px"}},[a._v(a._s(e.row.mobile))])])]}}])}),t("el-table-column",{attrs:{prop:"amount","header-align":"center",align:"center",label:"订单金额"},scopedSlots:a._u([{key:"default",fn:function(e){return[t("span",{staticStyle:{color:"#E6A23C","font-weight":"bold"}},[a._v("¥"+a._s(e.row.amount))])]}}])}),t("el-table-column",{attrs:{prop:"salesmanName","header-align":"center",align:"center",label:"关联业务员"},scopedSlots:a._u([{key:"default",fn:function(e){return[e.row.salesmanId?t("div",[t("div",[a._v(a._s(e.row.salesmanName))]),t("div",{staticStyle:{color:"#999","font-size":"12px"}},[a._v(a._s(e.row.salesmanCode))])]):t("el-tag",{attrs:{type:"danger",size:"mini"}},[a._v("未关联")])]}}])}),t("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",width:"150",label:"下单时间"}}),t("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",label:"订单状态"},scopedSlots:a._u([{key:"default",fn:function(e){return[t("el-tag",{attrs:{type:a.getOrderStatusTagType(e.row.status)}},[a._v(" "+a._s(a.getOrderStatusText(e.row.status))+" ")])]}}])}),t("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"200",label:"操作"},scopedSlots:a._u([{key:"default",fn:function(e){return[e.row.salesmanId?t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return a.changeAssociationHandle(e.row)}}},[a._v("更换业务员")]):t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return a.associateHandle(e.row)}}},[a._v("关联业务员")]),e.row.salesmanId?t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return a.disassociateHandle(e.row)}}},[a._v("取消关联")]):a._e()]}}])})],1),t("el-pagination",{attrs:{"current-page":a.pageIndex,"page-sizes":[10,20,50,100],"page-size":a.pageSize,total:a.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":a.sizeChangeHandle,"current-change":a.currentChangeHandle}}),a.associateVisible?t("associate-salesman",{ref:"associateSalesman",on:{refreshDataList:a.getDataList}}):a._e()],1)},n=[],i=e("31f0"),o={data:function(){return{dataForm:{wxUserId:"",salesmanId:"",orderSn:"",wxUserName:"",salesmanName:"",hasAssociation:"",dateRange:[]},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],associateVisible:!1,overallStats:{}}},components:{AssociateSalesman:i["default"]},activated:function(){this.dataForm.wxUserId=this.$route.query.wxUserId,this.dataForm.salesmanId=this.$route.query.salesmanId,this.getDataList(),this.getOverallStats()},methods:{getDataList:function(){var a=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/salesman/orderassociation/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,wxUserId:this.dataForm.wxUserId,salesmanId:this.dataForm.salesmanId,orderSn:this.dataForm.orderSn,wxUserName:this.dataForm.wxUserName,salesmanName:this.dataForm.salesmanName,hasAssociation:this.dataForm.hasAssociation,startDate:this.dataForm.dateRange&&this.dataForm.dateRange[0],endDate:this.dataForm.dateRange&&this.dataForm.dateRange[1]})}).then((function(t){var e=t.data;e&&200===e.code?(a.dataList=e.page.list,a.totalPage=e.page.totalCount):(a.dataList=[],a.totalPage=0),a.dataListLoading=!1}))},getOverallStats:function(){var a=this;this.$http({url:this.$http.adornUrl("/salesman/orderassociation/stats"),params:this.$http.adornParams({wxUserId:this.dataForm.wxUserId,salesmanId:this.dataForm.salesmanId,startDate:this.dataForm.dateRange&&this.dataForm.dateRange[0],endDate:this.dataForm.dateRange&&this.dataForm.dateRange[1]}),method:"get"}).then((function(t){var e=t.data;e&&200===e.code&&(a.overallStats=e.stats||{})}))},sizeChangeHandle:function(a){this.pageSize=a,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(a){this.pageIndex=a,this.getDataList()},selectionChangeHandle:function(a){this.dataListSelections=a},associateHandle:function(a){var t=this;this.associateVisible=!0,this.$nextTick((function(){t.$refs.associateSalesman.init(a,"associate")}))},changeAssociationHandle:function(a){var t=this;this.associateVisible=!0,this.$nextTick((function(){t.$refs.associateSalesman.init(a,"change")}))},disassociateHandle:function(a){var t=this;this.$confirm("确定要取消此订单的业务员关联吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:t.$http.adornUrl("/salesman/orderassociation/disassociate"),method:"post",params:t.$http.adornParams({rechargeRecordId:a.id})}).then((function(a){var e=a.data;e&&200===e.code?t.$message({message:"取消关联成功",type:"success",duration:1500,onClose:function(){t.getDataList()}}):t.$message.error(e.msg)}))}))},batchAssociate:function(){this.$message.info("批量关联功能开发中...")},batchDisassociate:function(){this.$message.info("批量取消关联功能开发中...")},batchAssociateHistorical:function(){var a=this;this.$confirm("此操作将为所有未关联业务员的历史订单自动关联业务员，是否继续？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.$http({url:a.$http.adornUrl("/salesman/orderassociation/batchAssociateHistorical"),method:"post"}).then((function(t){var e=t.data;e&&200===e.code?a.$message({message:e.message||"关联完成",type:"success",duration:2e3,onClose:function(){a.getDataList(),a.getOverallStats()}}):a.$message.error(e.msg)}))}))},validateConsistency:function(){this.$message.info("验证一致性功能开发中...")},validateOrderHandle:function(a){var t=this;this.$http({url:this.$http.adornUrl("/salesman/orderassociation/validate"),method:"get",params:this.$http.adornParams({rechargeRecordId:a.id})}).then((function(a){var e=a.data;e&&200===e.code?e.valid?t.$message.success(e.message):t.$message.warning(e.message):t.$message.error(e.msg)}))},getOrderStatusText:function(a){var t={0:"待支付",1:"已支付",2:"已取消",3:"已退款"};return t[a]||"未知"},getOrderStatusTagType:function(a){var t={0:"warning",1:"success",2:"info",3:"danger"};return t[a]||""}}},r=o,l=(e("4f8f"),e("2877")),c=Object(l["a"])(r,s,n,!1,null,null,null);t["default"]=c.exports},"9bb9":function(a,t,e){}}]);
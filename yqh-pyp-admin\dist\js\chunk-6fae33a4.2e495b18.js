(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6fae33a4"],{"3a01":function(e,t,a){"use strict";a.r(t);var u=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.dataFormSubmit()}}},[t("el-form-item",{attrs:{label:"类型",prop:"type"}},[t("el-select",{attrs:{placeholder:"类型",filterable:""},model:{value:e.dataForm.type,callback:function(t){e.$set(e.dataForm,"type",t)},expression:"dataForm.type"}},e._l(e.tripType,(function(e){return t("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},l=[],n=a("593c"),r={data:function(){return{times:[],tripType:n["m"],visible:!1,dataForm:{repeatToken:"",id:0,type:0},dataRule:{type:[{required:!0,message:"类型不能为空",trigger:"blur"}]}}},methods:{dateChange:function(e){this.dataForm.inStartDate=e[0],this.dataForm.inEndDate=e[1]},init:function(e,t,a){var u=this;this.getToken(),this.dataForm.id=e||0,this.dataForm.activityGuestId=a||0,this.dataForm.activityId=t||0,this.visible=!0,this.$nextTick((function(){u.$refs["dataForm"].resetFields(),u.dataForm.id&&u.$http({url:u.$http.adornUrl("/activity/activityguesttrip/info/".concat(u.dataForm.id)),method:"get",params:u.$http.adornParams()}).then((function(e){var t=e.data;t&&200===t.code&&(u.dataForm.type=t.activityGuestTrip.type)}))}))},getToken:function(){var e=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm.repeatToken=a.result)}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$http({url:e.$http.adornUrl("/activity/activityguesttrip/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({repeatToken:e.dataForm.repeatToken,id:e.dataForm.id||void 0,type:e.dataForm.type})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):(e.$message.error(a.msg),"不能重复提交"!=a.msg&&e.getToken())}))}))}}},i=r,o=a("2877"),v=Object(o["a"])(i,u,l,!1,null,null,null);t["default"]=v.exports},"593c":function(e,t,a){"use strict";a.d(t,"h",(function(){return u})),a.d(t,"i",(function(){return l})),a.d(t,"n",(function(){return n})),a.d(t,"f",(function(){return r})),a.d(t,"o",(function(){return i})),a.d(t,"c",(function(){return o})),a.d(t,"m",(function(){return v})),a.d(t,"b",(function(){return y})),a.d(t,"g",(function(){return k})),a.d(t,"j",(function(){return d})),a.d(t,"d",(function(){return s})),a.d(t,"k",(function(){return c})),a.d(t,"l",(function(){return m})),a.d(t,"a",(function(){return p})),a.d(t,"e",(function(){return f}));var u=[{key:0,value:"后台签到"},{key:1,value:"微信扫码签到"}],l=[{key:0,value:"未签到"},{key:1,value:"已签到"},{key:2,value:"已签退"}],n=[{key:0,value:"未报名"},{key:1,value:"已报名"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],r=[{key:0,value:"无需"},{key:1,value:"统一"},{key:2,value:"随机"},{key:3,value:"统一(多个)"}],i=[{key:0,value:"未审核"},{key:1,value:"审核通过"},{key:2,value:"审核不通过"}],o=[{key:0,value:"飞机"},{key:1,value:"火车"},{key:2,value:"其他"}],v=[{key:0,value:"会务组出票"},{key:1,value:"自行出票"}],y=[{key:0,value:"主控"},{key:1,value:"学员管理"},{key:2,value:"专家管理"},{key:3,value:"技术"},{key:4,value:"业务员"},{key:5,value:"兼职"}],k=[{key:0,value:"项目收支"},{key:1,value:"服务费"}],d=[{key:0,value:"大会出票"},{key:1,value:"自行购买，凭票大会报销"}],s=[{key:0,value:"未出票"},{key:1,value:"已出票"}],c=[{key:0,value:"已提交"},{key:1,value:"已预订"},{key:2,value:"已支付,待出票"},{key:4,value:"已出票"},{key:5,value:"不能出票待退款"},{key:7,value:"不能出票已退款"},{key:13,value:"(退票)审核不通过交易结束"},{key:15,value:"(退票)申请中"},{key:16,value:"(退票)已退款交易结束"},{key:22,value:"(改签)审核不通过交易结束"},{key:23,value:"(改签)需补款待支付"},{key:26,value:"(改签)无法改签已退款交易结束"},{key:27,value:"(改签)改签成功交易结束"},{key:28,value:"(改签)改签已取消交易结束"},{key:29,value:"(改签)改签处理中"},{key:99,value:"已取消"}],m=[{key:0,value:"已提交"},{key:1,value:"已预订"},{key:2,value:"占位成功"},{key:4,value:"占位失败"},{key:5,value:"已取消"},{key:8,value:"出票成功"},{key:9,value:"出票失败"},{key:31,value:"改签占位成功"},{key:32,value:"改签占位失败"},{key:34,value:"改签确认出票成功"},{key:35,value:"改签确认出票失败"},{key:37,value:"改签已取消"},{key:21,value:"退票成功"},{key:22,value:"退票失败"},{key:23,value:"已退票未退款"}],p=[{key:0,value:"专家信息"},{key:1,value:"专家任务确认"},{key:2,value:"专家行程"},{key:3,value:"专家接送"},{key:4,value:"专家劳务费信息"},{key:5,value:"专家劳务费签字"},{key:6,value:"其他"},{key:7,value:"活动即将过期"},{key:8,value:"活动已过期"},{key:9,value:"活动续费成功"}],f=[{key:0,value:"未读"},{key:1,value:"已读"}]}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-723e68e5"],{"28a5":function(t,e,a){"use strict";var i=a("aae3"),o=a("cb7c"),r=a("ebd6"),n=a("0390"),s=a("9def"),c=a("5f1b"),l=a("520a"),d=a("79e5"),u=Math.min,m=[].push,h="split",v="length",p="lastIndex",f=4294967295,y=!d((function(){RegExp(f,"y")}));a("214f")("split",2,(function(t,e,a,d){var b;return b="c"=="abbc"[h](/(b)*/)[1]||4!="test"[h](/(?:)/,-1)[v]||2!="ab"[h](/(?:ab)*/)[v]||4!="."[h](/(.?)(.?)/)[v]||"."[h](/()()/)[v]>1||""[h](/.?/)[v]?function(t,e){var o=String(this);if(void 0===t&&0===e)return[];if(!i(t))return a.call(o,t,e);var r,n,s,c=[],d=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),u=0,h=void 0===e?f:e>>>0,y=new RegExp(t.source,d+"g");while(r=l.call(y,o)){if(n=y[p],n>u&&(c.push(o.slice(u,r.index)),r[v]>1&&r.index<o[v]&&m.apply(c,r.slice(1)),s=r[0][v],u=n,c[v]>=h))break;y[p]===r.index&&y[p]++}return u===o[v]?!s&&y.test("")||c.push(""):c.push(o.slice(u)),c[v]>h?c.slice(0,h):c}:"0"[h](void 0,0)[v]?function(t,e){return void 0===t&&0===e?[]:a.call(this,t,e)}:a,[function(a,i){var o=t(this),r=void 0==a?void 0:a[e];return void 0!==r?r.call(a,o,i):b.call(String(o),a,i)},function(t,e){var i=d(b,t,this,e,b!==a);if(i.done)return i.value;var l=o(t),m=String(this),h=r(l,RegExp),v=l.unicode,p=(l.ignoreCase?"i":"")+(l.multiline?"m":"")+(l.unicode?"u":"")+(y?"y":"g"),g=new h(y?l:"^(?:"+l.source+")",p),w=void 0===e?f:e>>>0;if(0===w)return[];if(0===m.length)return null===c(g,m)?[m]:[];var F=0,D=0,k=[];while(D<m.length){g.lastIndex=y?D:0;var x,S=c(g,y?m:m.slice(D));if(null===S||(x=u(s(g.lastIndex+(y?0:D)),m.length))===F)D=n(m,D,v);else{if(k.push(m.slice(F,D)),k.length===w)return k;for(var I=1;I<=S.length-1;I++)if(k.push(S[I]),k.length===w)return k;D=F=x}}return k.push(m.slice(F)),k}]}))},"386d":function(t,e,a){"use strict";var i=a("cb7c"),o=a("83a1"),r=a("5f1b");a("214f")("search",1,(function(t,e,a,n){return[function(a){var i=t(this),o=void 0==a?void 0:a[e];return void 0!==o?o.call(a,i):new RegExp(a)[e](String(i))},function(t){var e=n(a,t,this);if(e.done)return e.value;var s=i(t),c=String(this),l=s.lastIndex;o(l,0)||(s.lastIndex=0);var d=r(s,c);return o(s.lastIndex,l)||(s.lastIndex=l),null===d?-1:d.index}]}))},"66c7":function(t,e,a){"use strict";a("4917"),a("a481");var i=/([yMdhsm])(\1*)/g,o="yyyy-MM-dd";function r(t,e){e-=(t+"").length;for(var a=0;a<e;a++)t="0"+t;return t}e["a"]={formatDate:{format:function(t,e){return e=e||o,e.replace(i,(function(e){switch(e.charAt(0)){case"y":return r(t.getFullYear(),e.length);case"M":return r(t.getMonth()+1,e.length);case"d":return r(t.getDate(),e.length);case"w":return t.getDay()+1;case"h":return r(t.getHours(),e.length);case"m":return r(t.getMinutes(),e.length);case"s":return r(t.getSeconds(),e.length)}}))},parse:function(t,e){var a=e.match(i),o=t.match(/(\d)+/g);if(a.length==o.length){for(var r=new Date(1970,0,1),n=0;n<a.length;n++){var s=parseInt(o[n]),c=a[n];switch(c.charAt(0)){case"y":r.setFullYear(s);break;case"M":r.setMonth(s-1);break;case"d":r.setDate(s);break;case"h":r.setHours(s);break;case"m":r.setMinutes(s);break;case"s":r.setSeconds(s);break}}return r}return null},toWeek:function(t){var e=new Date(t).getDay(),a="";switch(e){case 0:a="s";break;case 1:a="m";break;case 2:a="t";break;case 3:a="w";break;case 4:a="t";break;case 5:a="f";break;case 6:a="s";break}return a}},toUserLook:function(t){var e=Math.floor(t/3600%24),a=Math.floor(t/60%60);return e<1?a+"分":e+"时"+a+"分"}}},"761d":function(t,e,a){"use strict";a.r(e);a("7f7f");var i=function(){var t=this,e=t._self._c;return e("div",[t._m(0),e("van-radio-group",{model:{value:t.dataFrom.hotelActivityRoomId,callback:function(e){t.$set(t.dataFrom,"hotelActivityRoomId",e)},expression:"dataFrom.hotelActivityRoomId"}},[e("van-cell-group",t._l(t.dataList,(function(a){return e("van-cell",{key:a.id,attrs:{title:a.name,clickable:""},scopedSlots:t._u([{key:"right-icon",fn:function(){return[e("van-radio",{attrs:{name:a.id},on:{click:function(e){return t.chooseRoom(a)}}})]},proxy:!0}],null,!0)},[a.price>0?e("div",{attrs:{slot:"label"},slot:"label"},[e("div",[t._v("￥"+t._s(a.price)+"元/间"),a.roomRemarks?e("span",[t._v("("+t._s(a.roomRemarks)+")")]):t._e()]),1==a.bedStatus&&a.bedNumber>1?e("div",[t._v("\n            ￥"+t._s(a.bedPrice)+"元/床位"),a.bedRemarks?e("span",[t._v("("+t._s(a.bedRemarks)+")")]):t._e()]):t._e()]):t._e()])})),1)],1),e("van-cell-group",{directives:[{name:"show",rawName:"v-show",value:t.dataFrom.hotelActivityRoomId,expression:"dataFrom.hotelActivityRoomId"}],staticStyle:{"margin-top":"20px"},attrs:{inset:""}},[e("van-cell",{attrs:{required:!0,title:"入住时间",value:t._f("dateFilter")(t.dataFrom.inDate)},on:{click:t.showInDate}}),e("van-cell",{attrs:{required:!0,title:"退房时间",value:t._f("dateFilter")(t.dataFrom.outDate)},on:{click:t.showOutDate}}),1==t.indexRoom.bedStatus&&t.indexRoom.bedNumber>1?e("van-cell",{attrs:{required:!0,title:"入住类型",value:t.dataFrom.roomTypeName},on:{click:t.showType}}):t._e(),t.indexRoom.roomRemarks||t.indexRoom.bedRemarks?e("div",{staticStyle:{color:"red","font-size":"12px","padding-left":"10px"}},[t._v(t._s((t.indexRoom.roomRemarks||"")+(t.indexRoom.bedRemarks||"")))]):t._e(),e("van-field",{attrs:{label:"联系人",required:!0,placeholder:"请输入联系人",rules:[{required:!0,message:"请输入联系人"}]},model:{value:t.dataFrom.contact,callback:function(e){t.$set(t.dataFrom,"contact",e)},expression:"dataFrom.contact"}}),e("van-field",{attrs:{label:"联系方式",required:!0,placeholder:"请输入联系方式",rules:[{required:!0,message:"请输入联系方式"}]},model:{value:t.dataFrom.mobile,callback:function(e){t.$set(t.dataFrom,"mobile",e)},expression:"dataFrom.mobile"}}),"wx0770d56458b33c67"!=t.appid?e("div",[e("van-field",{attrs:{type:"number",label:"数量",required:!0,placeholder:"请输入数量",rules:[{required:!0,message:"请输入数量"}]},on:{input:t.numberChange},model:{value:t.dataFrom.number,callback:function(e){t.$set(t.dataFrom,"number",e)},expression:"dataFrom.number"}}),e("van-field",{attrs:{label:"邮箱",required:!0,placeholder:"请输入邮箱",rules:[{required:!0,message:"请输入邮箱"}]},model:{value:t.dataFrom.email,callback:function(e){t.$set(t.dataFrom,"email",e)},expression:"dataFrom.email"}})],1):t._e()],1),e("van-action-sheet",{attrs:{actions:t.roomType},on:{select:t.typeSelect},model:{value:t.typeShow,callback:function(e){t.typeShow=e},expression:"typeShow"}}),e("van-action-sheet",{model:{value:t.inDateShow,callback:function(e){t.inDateShow=e},expression:"inDateShow"}},[e("van-datetime-picker",{attrs:{type:"date",title:"选择入住时间","min-date":t.minDate,"max-date":t.maxDate},on:{confirm:t.inDateSelect},model:{value:t.inDate,callback:function(e){t.inDate=e},expression:"inDate"}})],1),e("van-action-sheet",{model:{value:t.outDateShow,callback:function(e){t.outDateShow=e},expression:"outDateShow"}},[e("van-datetime-picker",{attrs:{type:"date",title:"选择退房时间","min-date":t.minDate,"max-date":t.maxDate},on:{confirm:t.outDateSelect},model:{value:t.outDate,callback:function(e){t.outDate=e},expression:"outDate"}})],1),e("div",{directives:[{name:"show",rawName:"v-show",value:t.dataFrom.hotelActivityRoomId,expression:"dataFrom.hotelActivityRoomId"}],staticClass:"bottom"},[e("div",{staticStyle:{width:"40%","text-align":"center","font-size":"18px"}},[t._v("总价："),e("span",{staticStyle:{color:"red"}},[t._v("￥"+t._s(t.dataFrom.price)+"元")])]),e("div",{staticStyle:{width:"60%","text-align":"center"}},[e("van-button",{staticStyle:{width:"90%"},attrs:{loading:t.loading,round:"",block:"",type:"info"},on:{click:t.submit}},[t._v("提交订单")])],1)])],1)},o=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"nav-title",staticStyle:{"margin-top":"8px"}},[e("div",{staticClass:"color"}),e("div",{staticClass:"text"},[t._v("酒店房型")])])}],r=(a("a481"),a("386d"),a("28a5"),a("66c7")),n=[{id:0,name:"整间"},{id:1,name:"男床位"},{id:2,name:"女床位"}],s=[{id:0,name:"整间"},{id:1,name:"拼住"}],c={data:function(){return{userInfo:{},loading:!1,appid:"",openid:void 0,dataFrom:{activityId:"",hotelId:"",hotelActivityId:"",hotelActivityRoomId:"",roomType:"",roomTypeName:"",price:0,dayNumber:0,number:1,inDate:"",outDate:"",contact:"",mobile:"",email:"",orderToken:""},roomType:n,roomTypeFjsd:s,minDate:"",maxDate:"",inDate:"",outDate:"",typeShow:!1,inDateShow:!1,outDateShow:!1,indexRoom:{},dataList:[]}},filters:{dateFilter:function(t){return t?r["a"].formatDate.format(new Date(t),"yyyy/MM/dd"):""}},mounted:function(){this.appid=this.$cookie.get("appid"),document.title="选择房型",this.dataFrom.hotelActivityId=this.$route.query.id,this.dataFrom.activityId=this.$route.query.activityId,this.openid=this.$cookie.get("openid"),this.rebuildUrl(),this.getToken(),this.getActivityRoomList(),this.getUserActivityInfo()},methods:{getActivityRoomList:function(){var t=this;this.$fly.get("/pyp/web/hotel/hotelactivityroom/list",{hotelActivityId:this.dataFrom.hotelActivityId,status:1}).then((function(e){200==e.code?t.dataList=e.result:(vant.Toast(e.msg),t.dataList=[])}))},getToken:function(){var t=this;this.$fly.get("/pyp/web/hotel/hotelactivity/createToken").then((function(e){200==e.code&&(t.dataFrom.orderToken=e.result)}))},getUserActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activityuser/findByUserIdAndActivityId",{activityId:this.dataFrom.activityId}).then((function(e){200==e.code&&(t.userInfo=e.result,t.dataFrom.contact=e.result.contact,t.dataFrom.mobile=e.result.mobile,t.dataFrom.email=e.result.email)}))},chooseRoom:function(t){this.indexRoom=t,this.dataFrom.hotelActivityRoomId=t.id,this.dataFrom.hotelId=t.hotelId,this.minDate=new Date(t.inDate),this.maxDate=new Date(t.outDate),1==t.bedNumber?this.$set(this.dataFrom,"roomType",0):this.$set(this.dataFrom,"roomType",""),this.countPrice()},showType:function(){this.typeShow=!0},typeSelect:function(t){this.dataFrom.roomType=t.id,this.dataFrom.roomTypeName=t.name,this.typeShow=!1,this.countPrice()},numberChange:function(){this.countPrice()},showInDate:function(){this.inDateShow=!0},showOutDate:function(){this.outDateShow=!0},inDateSelect:function(){this.dataFrom.inDate=r["a"].formatDate.format(new Date(this.inDate),"yyyy/MM/dd hh:mm:ss"),this.inDateShow=!1,this.countPrice()},outDateSelect:function(){this.dataFrom.outDate=r["a"].formatDate.format(new Date(this.outDate),"yyyy/MM/dd hh:mm:ss"),this.outDateShow=!1,this.countPrice()},countPrice:function(){if(this.outDate&&this.inDate&&null!=this.dataFrom.roomType&&""!==this.dataFrom.roomType){var t=this.outDate.getTime()/1e3-this.inDate.getTime()/1e3;this.dataFrom.dayNumber=parseInt(t/60/60/24),0==this.dataFrom.roomType?this.dataFrom.price=this.dataFrom.dayNumber*this.indexRoom.price*this.dataFrom.number:this.dataFrom.price=this.dataFrom.dayNumber*this.indexRoom.bedPrice*this.dataFrom.number}},submit:function(){var t=this;if(!this.dataFrom.number||this.dataFrom.number<=0)return vant.Toast("请输入正确房间数量"),!1;if(1!=this.dataFrom.roomType&&0!==this.dataFrom.roomType&&2!=this.dataFrom.roomType)return vant.Toast("请选择房型"),!1;if(!this.dataFrom.inDate)return vant.Toast("请选择入住时间"),!1;if(!this.dataFrom.outDate)return vant.Toast("请选择退房时间"),!1;if(!this.dataFrom.contact)return vant.Toast("请输入联系人"),!1;if(!this.dataFrom.mobile)return vant.Toast("请输入联系方式"),!1;if(!this.dataFrom.email&&"wx0770d56458b33c67"!=this.appid)return vant.Toast("请输入联系人邮箱"),!1;if(this.countPrice(),this.dataFrom.dayNumber<=0)return vant.Toast("退房时间必须大于入住时间"),!1;this.loading=!0;var e=this;this.$fly.post("/pyp/web/hotel/hotelactivity/createOrder",this.dataFrom).then((function(a){200==a.code?a.isNeedPay&&!a.turnSuccess?t.$fly.get("/pyp/web/hotel/hotelactivity/pay",{orderId:a.result}).then((function(t){t&&200===t.code?WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:t.result.appId,timeStamp:t.result.timeStamp,nonceStr:t.result.nonceStr,package:t.result.packageValue,signType:t.result.signType,paySign:t.result.paySign},(function(t){if(console.log("开始支付"),"get_brand_wcpay_request:ok"==t.err_msg){var i=window.location.href.split("#")[0];location.href=i+"#/hotel/success?id="+e.activityId+"&orderId="+a.result}else if("get_brand_wcpay_request:cancel"==t.err_msg){i=window.location.href.split("#")[0];location.href=i+"#/hotel/success?id="+e.activityId+"&orderId="+a.result}else{i=window.location.href.split("#")[0];location.href=i+"#/hotel/success?id="+e.activityId+"&orderId="+a.result}})):vant.Toast(t.msg)})):(vant.Toast("酒店预订提交成功"),t.$router.push({name:"hotelSuccess",query:{orderId:a.result,id:t.activityId}})):(vant.Toast(a.msg),t.dataList=[]),t.loading=!1}))},rebuildUrl:function(){var t=window.location,e=t.href,a=t.protocol,i=t.host,o=t.pathname,r=t.search,n=t.hash;console.log(window.location),r=r||"?";var s="".concat(a,"//").concat(i).concat(o).concat(r).concat(n);console.log(s),s!==e&&window.location.replace(s)}}},l=c,d=(a("b2e6"),a("2877")),u=Object(d["a"])(l,i,o,!1,null,"154c89c7",null);e["default"]=u.exports},"83a1":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},b2e6:function(t,e,a){"use strict";a("ea5f")},ea5f:function(t,e,a){}}]);
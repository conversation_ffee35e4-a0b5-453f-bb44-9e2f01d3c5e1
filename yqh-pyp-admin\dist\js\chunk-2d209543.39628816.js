(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d209543"],{a91f:function(r,e,a){"use strict";a.r(e);var t=function(){var r=this,e=r._self._c;return e("el-dialog",{attrs:{title:r.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:r.visible},on:{"update:visible":function(e){r.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:r.dataForm,rules:r.dataRule,"label-width":"120px"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&r._k(e.keyCode,"enter",13,e.key,"Enter")?null:r.dataFormSubmit()}}},[e("el-form-item",{attrs:{label:"到款金额",prop:"price"}},[e("el-input",{attrs:{placeholder:"到款金额"},model:{value:r.dataForm.price,callback:function(e){r.$set(r.dataForm,"price",e)},expression:"dataForm.price"}})],1),e("el-form-item",{attrs:{label:"备注",prop:"remarks"}},[e("el-input",{attrs:{placeholder:"备注"},model:{value:r.dataForm.remarks,callback:function(e){r.$set(r.dataForm,"remarks",e)},expression:"dataForm.remarks"}})],1),e("el-form-item",{attrs:{label:"往来ID",prop:"priceTransformId"}},[e("el-input",{attrs:{placeholder:"往来ID"},model:{value:r.dataForm.priceTransformId,callback:function(e){r.$set(r.dataForm,"priceTransformId",e)},expression:"dataForm.priceTransformId"}})],1),e("el-form-item",{attrs:{label:"银行账户ID",prop:"priceBankId"}},[e("el-input",{attrs:{placeholder:"银行账户ID"},model:{value:r.dataForm.priceBankId,callback:function(e){r.$set(r.dataForm,"priceBankId",e)},expression:"dataForm.priceBankId"}})],1),e("el-form-item",{attrs:{label:"支付时间",prop:"payTime"}},[e("el-date-picker",{attrs:{type:"datetime",placeholder:"请选择支付时间","value-format":"yyyy/MM/dd HH:mm:ss"},model:{value:r.dataForm.payTime,callback:function(e){r.$set(r.dataForm,"payTime",e)},expression:"dataForm.payTime"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){r.visible=!1}}},[r._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return r.dataFormSubmit()}}},[r._v("确定")])],1)],1)},o=[],i={data:function(){return{visible:!1,dataForm:{repeatToken:"",id:0,price:"",remarks:"",priceTransformId:"",priceBankId:"",payTime:""},dataRule:{price:[{required:!0,message:"到款金额不能为空",trigger:"blur"}],remarks:[{required:!0,message:"备注不能为空",trigger:"blur"}],priceTransformId:[{required:!0,message:"往来ID不能为空",trigger:"blur"}],priceBankId:[{required:!0,message:"银行账户ID不能为空",trigger:"blur"}],payTime:[{required:!0,message:"支付时间不能为空",trigger:"blur"}]}}},methods:{init:function(r){var e=this;this.getToken(),this.dataForm.id=r||0,this.visible=!0,this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.dataForm.id&&e.$http({url:e.$http.adornUrl("/price/pricetransformlog/info/".concat(e.dataForm.id)),method:"get",params:e.$http.adornParams()}).then((function(r){var a=r.data;a&&200===a.code&&(e.dataForm.price=a.priceTransformLog.price,e.dataForm.remarks=a.priceTransformLog.remarks,e.dataForm.priceTransformId=a.priceTransformLog.priceTransformId,e.dataForm.priceBankId=a.priceTransformLog.priceBankId,e.dataForm.payTime=a.priceTransformLog.payTime)}))}))},getToken:function(){var r=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(r.dataForm.repeatToken=a.result)}))},dataFormSubmit:function(){var r=this;this.$refs["dataForm"].validate((function(e){e&&r.$http({url:r.$http.adornUrl("/price/pricetransformlog/".concat(r.dataForm.id?"update":"save")),method:"post",data:r.$http.adornData({repeatToken:r.dataForm.repeatToken,id:r.dataForm.id||void 0,price:r.dataForm.price,remarks:r.dataForm.remarks,appid:r.$cookie.get("appid"),priceTransformId:r.dataForm.priceTransformId,priceBankId:r.dataForm.priceBankId,payTime:r.dataForm.payTime})}).then((function(e){var a=e.data;a&&200===a.code?r.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){r.visible=!1,r.$emit("refreshDataList")}}):(r.$message.error(a.msg),"不能重复提交"!=a.msg&&r.getToken())}))}))}}},s=i,n=a("2877"),d=Object(n["a"])(s,t,o,!1,null,null,null);e["default"]=d.exports}}]);
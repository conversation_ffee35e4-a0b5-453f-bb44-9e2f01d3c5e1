(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a9666a32","chunk-2d23718d"],{"7eff":function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.onSearch()}}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"参数名",clearable:""},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.onSearch()}}},[t._v("查询")]),t.isAuth("activity:activitynav:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("activity:activitynav:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"title","header-align":"center",align:"center",label:"标题"}}),e("el-table-column",{attrs:{prop:"longitude","header-align":"center",align:"center",label:"经度"}}),e("el-table-column",{attrs:{prop:"latitude","header-align":"center",align:"center",label:"纬度"}}),e("el-table-column",{attrs:{prop:"paixu","header-align":"center",align:"center",label:"排序"}}),e("el-table-column",{attrs:{prop:"createOn","show-overflow-tooltip":!0,"header-align":"center",align:"center",label:"创建时间"}}),e("el-table-column",{attrs:{prop:"updateOn","show-overflow-tooltip":!0,"header-align":"center",align:"center",label:"更新时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e()],1)},n=[],r=(a("99af"),a("a15b"),a("d81d"),a("a573"),a("fa51")),o={data:function(){return{dataForm:{title:"",activityId:void 0},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1}},components:{AddOrUpdate:r["default"]},activated:function(){this.dataForm.activityId=this.$route.query.activityId,this.getDataList()},methods:{onSearch:function(){this.pageIndex=1,this.getDataList()},getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/activity/activitynav/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,title:this.dataForm.title,activityId:this.dataForm.activityId})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(e.dataForm.activityId,t)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/activity/activitynav/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))}}},l=o,d=a("2877"),s=Object(d["a"])(l,i,n,!1,null,null,null);e["default"]=s.exports},a15b:function(t,e,a){"use strict";var i=a("23e7"),n=a("e330"),r=a("44ad"),o=a("fc6a"),l=a("a640"),d=n([].join),s=r!==Object,c=s||!l("join",",");i({target:"Array",proto:!0,forced:c},{join:function(t){return d(o(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var i=a("23e7"),n=a("d024"),r=a("c430");i({target:"Iterator",proto:!0,real:!0,forced:r},{map:n})},d024:function(t,e,a){"use strict";var i=a("c65b"),n=a("59ed"),r=a("825a"),o=a("46c4"),l=a("c5cc"),d=a("9bdd"),s=l((function(){var t=this.iterator,e=r(i(this.next,t)),a=this.done=!!e.done;if(!a)return d(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return r(this),n(t),new s(o(this),{mapper:t})}},d81d:function(t,e,a){"use strict";var i=a("23e7"),n=a("b727").map,r=a("1dde"),o=r("map");i({target:"Array",proto:!0,forced:!o},{map:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}})},fa51:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.dataFormSubmit()}}},[e("el-form-item",{attrs:{label:"标题",prop:"title"}},[e("el-input",{attrs:{placeholder:"标题"},model:{value:t.dataForm.title,callback:function(e){t.$set(t.dataForm,"title",e)},expression:"dataForm.title"}})],1),e("el-form-item",{attrs:{label:"排序",prop:"paixu"}},[e("el-input",{attrs:{placeholder:"排序"},model:{value:t.dataForm.paixu,callback:function(e){t.$set(t.dataForm,"paixu",e)},expression:"dataForm.paixu"}})],1),e("el-form-item",{attrs:{label:"详细介绍",prop:"content"}},[e("tinymce-editor",{ref:"editor",model:{value:t.dataForm.content,callback:function(e){t.$set(t.dataForm,"content",e)},expression:"dataForm.content"}})],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"经度",prop:"longitude"}},[e("el-input",{attrs:{placeholder:"经度"},model:{value:t.dataForm.longitude,callback:function(e){t.$set(t.dataForm,"longitude",e)},expression:"dataForm.longitude"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"纬度",prop:"latitude"}},[e("el-input",{attrs:{placeholder:"纬度"},model:{value:t.dataForm.latitude,callback:function(e){t.$set(t.dataForm,"latitude",e)},expression:"dataForm.latitude"}})],1)],1)],1),e("a",{staticStyle:{color:"red","margin-left":"50px"},attrs:{target:"_blank",href:"https://lbs.qq.com/tool/getpoint/index.html"}},[t._v("腾讯地图坐标拾取工具")])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},n=[],r=(a("b0c0"),a("d3b7"),a("3ca3"),a("ddb0"),a("7c8d")),o=a.n(r),l={data:function(){return{visible:!1,dataForm:{repeatToken:"",id:0,title:"",activityId:"",paixu:"",longitude:"",latitude:"",color:"",content:""},dataRule:{title:[{required:!0,message:"标题不能为空",trigger:"blur"}],activityId:[{required:!0,message:"活动表id不能为空",trigger:"blur"}],paixu:[{required:!0,message:"排序不能为空",trigger:"blur"}],longitude:[{required:!0,message:"经度不能为空",trigger:"blur"}],latitude:[{required:!0,message:"纬度不能为空",trigger:"blur"}],color:[{required:!0,message:"背景颜色不能为空",trigger:"blur"}],content:[{required:!0,message:"内容不能为空",trigger:"blur"}]}}},components:{TinymceEditor:function(){return Promise.all([a.e("chunk-03be236c"),a.e("chunk-2d0a4b8c")]).then(a.bind(null,"26dc"))}},methods:{init:function(t,e){var a=this;this.getToken(),this.dataForm.id=e||0,this.dataForm.activityId=t,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/activity/activitynav/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.dataForm.title=e.activityNav.title,a.dataForm.activityId=e.activityNav.activityId,a.dataForm.paixu=e.activityNav.paixu,a.dataForm.longitude=e.activityNav.longitude,a.dataForm.latitude=e.activityNav.latitude,a.dataForm.color=e.activityNav.color,a.dataForm.content=e.activityNav.content)}))}))},getToken:function(){var t=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.repeatToken=a.result)}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/activity/activitynav/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({repeatToken:t.dataForm.repeatToken,id:t.dataForm.id||void 0,title:t.dataForm.title,activityId:t.dataForm.activityId,paixu:t.dataForm.paixu,longitude:t.dataForm.longitude,latitude:t.dataForm.latitude,color:t.dataForm.color,content:t.dataForm.content})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):(t.$message.error(a.msg),"不能重复提交"!=a.msg&&t.getToken())}))}))},checkFileSize:function(t){return t.size/1024/1024>6?(this.$message.error("".concat(t.name,"文件大于6MB，请选择小于6MB大小的图片")),!1):!(t.size/1024>100)||new Promise((function(e,a){new o.a(t,{quality:.8,success:function(t){e(t)}})}))},backgroundSuccessHandle:function(t,e,a){t&&200===t.code?(this.dataForm.picUrl=t.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(t.msg)}}},d=l,s=a("2877"),c=Object(s["a"])(d,i,n,!1,null,null,null);e["default"]=c.exports}}]);
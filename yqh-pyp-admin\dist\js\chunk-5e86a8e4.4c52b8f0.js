(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5e86a8e4","chunk-0506e191","chunk-0506e191","chunk-2d21a418"],{1148:function(t,e,a){"use strict";var n=a("5926"),o=a("577e"),s=a("1d80"),i=RangeError;t.exports=function(t){var e=o(s(this)),a="",r=n(t);if(r<0||r===1/0)throw new i("Wrong number of repetitions");for(;r>0;(r>>>=1)&&(e+=e))1&r&&(a+=e);return a}},a15b:function(t,e,a){"use strict";var n=a("23e7"),o=a("e330"),s=a("44ad"),i=a("fc6a"),r=a("a640"),l=o([].join),m=s!==Object,c=m||!r("join",",");n({target:"Array",proto:!0,forced:c},{join:function(t){return l(i(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var n=a("23e7"),o=a("d024"),s=a("c430");n({target:"Iterator",proto:!0,real:!0,forced:s},{map:o})},b680:function(t,e,a){"use strict";var n=a("23e7"),o=a("e330"),s=a("5926"),i=a("408a"),r=a("1148"),l=a("d039"),m=RangeError,c=String,d=Math.floor,u=o(r),p=o("".slice),f=o(1..toFixed),h=function(t,e,a){return 0===e?a:e%2===1?h(t,e-1,a*t):h(t*t,e/2,a)},g=function(t){var e=0,a=t;while(a>=4096)e+=12,a/=4096;while(a>=2)e+=1,a/=2;return e},y=function(t,e,a){var n=-1,o=a;while(++n<6)o+=e*t[n],t[n]=o%1e7,o=d(o/1e7)},v=function(t,e){var a=6,n=0;while(--a>=0)n+=t[a],t[a]=d(n/e),n=n%e*1e7},b=function(t){var e=6,a="";while(--e>=0)if(""!==a||0===e||0!==t[e]){var n=c(t[e]);a=""===a?n:a+u("0",7-n.length)+n}return a},F=l((function(){return"0.000"!==f(8e-5,3)||"1"!==f(.9,0)||"1.25"!==f(1.255,2)||"1000000000000000128"!==f(0xde0b6b3a7640080,0)}))||!l((function(){f({})}));n({target:"Number",proto:!0,forced:F},{toFixed:function(t){var e,a,n,o,r=i(this),l=s(t),d=[0,0,0,0,0,0],f="",F="0";if(l<0||l>20)throw new m("Incorrect fraction digits");if(r!==r)return"NaN";if(r<=-1e21||r>=1e21)return c(r);if(r<0&&(f="-",r=-r),r>1e-21)if(e=g(r*h(2,69,1))-69,a=e<0?r*h(2,-e,1):r/h(2,e,1),a*=4503599627370496,e=52-e,e>0){y(d,0,a),n=l;while(n>=7)y(d,1e7,0),n-=7;y(d,h(10,n,1),0),n=e-1;while(n>=23)v(d,1<<23),n-=23;v(d,1<<n),y(d,1,1),v(d,2),F=b(d)}else y(d,0,a),y(d,1<<-e,0),F=b(d)+u("0",l);return l>0?(o=F.length,F=f+(o<=l?"0."+u("0",l-o)+F:p(F,0,o-l)+"."+p(F,o-l))):F=f+F,F}})},bb79:function(t,e,a){"use strict";a.r(e);a("b0c0");var n=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.dataFormSubmit()}}},[e("el-form-item",{attrs:{label:"业务员",prop:"salesmanId"}},[e("el-select",{attrs:{placeholder:"请选择业务员",filterable:"",disabled:null!==t.presetSalesmanId},model:{value:t.dataForm.salesmanId,callback:function(e){t.$set(t.dataForm,"salesmanId",e)},expression:"dataForm.salesmanId"}},t._l(t.salesmanList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name+"("+t.code+")",value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"佣金类型",prop:"commissionType"}},[e("el-select",{attrs:{placeholder:"请选择佣金类型"},on:{change:t.commissionTypeChange},model:{value:t.dataForm.commissionType,callback:function(e){t.$set(t.dataForm,"commissionType",e)},expression:"dataForm.commissionType"}},t._l(t.commissionTypes,(function(t){return e("el-option",{key:t.code,attrs:{label:t.desc,value:t.code}})})),1)],1),e("el-form-item",{attrs:{label:"计算方式",prop:"calculationType"}},[e("el-select",{attrs:{placeholder:"请选择计算方式"},on:{change:t.calculationTypeChange},model:{value:t.dataForm.calculationType,callback:function(e){t.$set(t.dataForm,"calculationType",e)},expression:"dataForm.calculationType"}},t._l(t.availableCalculationTypes,(function(t){return e("el-option",{key:t.code,attrs:{label:t.desc,value:t.code}})})),1)],1),e("el-form-item",{attrs:{label:"佣金值",prop:"commissionValue"}},[e("el-input-number",{attrs:{precision:4,min:0,max:2===t.dataForm.calculationType?1:999999,placeholder:"请输入佣金值"},model:{value:t.dataForm.commissionValue,callback:function(e){t.$set(t.dataForm,"commissionValue",e)},expression:"dataForm.commissionValue"}}),1===t.dataForm.calculationType?e("span",{staticStyle:{"margin-left":"10px"}},[t._v("元")]):t._e(),2===t.dataForm.calculationType?e("span",{staticStyle:{"margin-left":"10px"}},[t._v("（比例，如0.1表示10%）")]):t._e()],1),e("el-form-item",{attrs:{label:"最小金额",prop:"minAmount"}},[e("el-input-number",{attrs:{precision:2,min:0,placeholder:"最小佣金金额（可选）"},model:{value:t.dataForm.minAmount,callback:function(e){t.$set(t.dataForm,"minAmount",e)},expression:"dataForm.minAmount"}}),e("span",{staticStyle:{"margin-left":"10px"}},[t._v("元")])],1),e("el-form-item",{attrs:{label:"最大金额",prop:"maxAmount"}},[e("el-input-number",{attrs:{precision:2,min:0,placeholder:"最大佣金金额（可选）"},model:{value:t.dataForm.maxAmount,callback:function(e){t.$set(t.dataForm,"maxAmount",e)},expression:"dataForm.maxAmount"}}),e("span",{staticStyle:{"margin-left":"10px"}},[t._v("元")])],1),e("el-form-item",{attrs:{label:"状态",prop:"status"}},[e("el-radio-group",{model:{value:t.dataForm.status,callback:function(e){t.$set(t.dataForm,"status",e)},expression:"dataForm.status"}},[e("el-radio",{attrs:{label:1}},[t._v("启用")]),e("el-radio",{attrs:{label:0}},[t._v("禁用")])],1)],1),e("el-form-item",{attrs:{label:"生效时间",prop:"effectiveDate"}},[e("el-date-picker",{attrs:{type:"datetime",placeholder:"选择生效时间",format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:t.dataForm.effectiveDate,callback:function(e){t.$set(t.dataForm,"effectiveDate",e)},expression:"dataForm.effectiveDate"}})],1),e("el-form-item",{attrs:{label:"失效时间",prop:"expiryDate"}},[e("el-date-picker",{attrs:{type:"datetime",placeholder:"选择失效时间（可选）",format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:t.dataForm.expiryDate,callback:function(e){t.$set(t.dataForm,"expiryDate",e)},expression:"dataForm.expiryDate"}})],1),e("el-form-item",{attrs:{label:"备注",prop:"remarks"}},[e("el-input",{attrs:{type:"textarea",placeholder:"备注信息"},model:{value:t.dataForm.remarks,callback:function(e){t.$set(t.dataForm,"remarks",e)},expression:"dataForm.remarks"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},o=[],s=(a("4de4"),a("d3b7"),a("0643"),a("2382"),{data:function(){return{visible:!1,dataForm:{id:0,salesmanId:"",commissionType:"",calculationType:"",commissionValue:0,minAmount:null,maxAmount:null,status:1,effectiveDate:"",expiryDate:"",remarks:""},salesmanList:[],commissionTypes:[],calculationTypes:[],presetSalesmanId:null,dataRule:{salesmanId:[{required:!0,message:"业务员不能为空",trigger:"change"}],commissionType:[{required:!0,message:"佣金类型不能为空",trigger:"change"}],calculationType:[{required:!0,message:"计算方式不能为空",trigger:"change"}],commissionValue:[{required:!0,message:"佣金值不能为空",trigger:"blur"}]}}},computed:{availableCalculationTypes:function(){return 3===this.dataForm.commissionType?this.calculationTypes.filter((function(t){return 1===t.code})):this.calculationTypes}},methods:{init:function(t,e){var a=this;this.dataForm.id=t||0,this.presetSalesmanId=e||null,this.visible=!0,this.getSalesmanList(),this.getCommissionTypes(),this.getCalculationTypes(),this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.presetSalesmanId&&!a.dataForm.id&&(a.dataForm.salesmanId=a.presetSalesmanId),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/salesman/commission/config/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.dataForm.salesmanId=e.config.salesmanId,a.dataForm.commissionType=e.config.commissionType,a.dataForm.calculationType=e.config.calculationType,a.dataForm.commissionValue=e.config.commissionValue,a.dataForm.minAmount=e.config.minAmount,a.dataForm.maxAmount=e.config.maxAmount,a.dataForm.status=e.config.status,a.dataForm.effectiveDate=e.config.effectiveDate,a.dataForm.expiryDate=e.config.expiryDate,a.dataForm.remarks=e.config.remarks)}))}))},getSalesmanList:function(){var t=this;this.$http({url:this.$http.adornUrl("/salesman/salesman/list"),method:"get",params:this.$http.adornParams({page:1,limit:1e3})}).then((function(e){var a=e.data;a&&200===a.code&&(t.salesmanList=a.page.list)}))},getCommissionTypes:function(){var t=this;this.$http({url:this.$http.adornUrl("/salesman/commission/config/getCommissionTypes"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.commissionTypes=a.commissionTypes)}))},getCalculationTypes:function(){var t=this;this.$http({url:this.$http.adornUrl("/salesman/commission/config/getCalculationTypes"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.calculationTypes=a.calculationTypes)}))},commissionTypeChange:function(){3===this.dataForm.commissionType&&(this.dataForm.calculationType=1)},calculationTypeChange:function(){this.dataForm.commissionValue=0},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){if(e){if(t.dataForm.minAmount&&t.dataForm.maxAmount&&t.dataForm.minAmount>t.dataForm.maxAmount)return void t.$message.error("最小金额不能大于最大金额");if(2===t.dataForm.calculationType&&(t.dataForm.commissionValue<0||t.dataForm.commissionValue>1))return void t.$message.error("百分比佣金值应在0-1之间");t.$http({url:t.$http.adornUrl("/salesman/commission/config/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,salesmanId:t.dataForm.salesmanId,commissionType:t.dataForm.commissionType,calculationType:t.dataForm.calculationType,commissionValue:t.dataForm.commissionValue,minAmount:t.dataForm.minAmount,maxAmount:t.dataForm.maxAmount,status:t.dataForm.status,effectiveDate:t.dataForm.effectiveDate,expiryDate:t.dataForm.expiryDate,remarks:t.dataForm.remarks})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}}))}}}),i=s,r=a("2877"),l=Object(r["a"])(i,n,o,!1,null,null,null);e["default"]=l.exports},d024:function(t,e,a){"use strict";var n=a("c65b"),o=a("59ed"),s=a("825a"),i=a("46c4"),r=a("c5cc"),l=a("9bdd"),m=r((function(){var t=this.iterator,e=s(n(this.next,t)),a=this.done=!!e.done;if(!a)return l(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return s(this),o(t),new m(i(this),{mapper:t})}},d81d:function(t,e,a){"use strict";var n=a("23e7"),o=a("b727").map,s=a("1dde"),i=s("map");n({target:"Array",proto:!0,forced:!i},{map:function(t){return o(this,t,arguments.length>1?arguments[1]:void 0)}})},da61:function(t,e,a){"use strict";a.r(e);a("b680");var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-commission-config"},[t.currentSalesmanName?e("div",{staticClass:"page-header",staticStyle:{"margin-bottom":"20px"}},[e("h3",{staticStyle:{margin:"0",color:"#303133"}},[e("i",{staticClass:"el-icon-user",staticStyle:{"margin-right":"8px"}}),t._v(" "+t._s(t.currentSalesmanName)+" - 佣金配置管理 ")])]):t._e(),e("el-form",{attrs:{inline:!0,model:t.dataForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.getDataList()}}},[t.currentSalesmanId?t._e():e("el-form-item",[e("el-input",{attrs:{placeholder:"业务员姓名",clearable:""},model:{value:t.dataForm.salesmanName,callback:function(e){t.$set(t.dataForm,"salesmanName",e)},expression:"dataForm.salesmanName"}})],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"佣金类型",clearable:""},model:{value:t.dataForm.commissionType,callback:function(e){t.$set(t.dataForm,"commissionType",e)},expression:"dataForm.commissionType"}},[e("el-option",{attrs:{label:"全部",value:""}}),t._l(t.commissionTypes,(function(t){return e("el-option",{key:t.code,attrs:{label:t.desc,value:t.code}})}))],2)],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"状态",clearable:""},model:{value:t.dataForm.status,callback:function(e){t.$set(t.dataForm,"status",e)},expression:"dataForm.status"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"启用",value:1}}),e("el-option",{attrs:{label:"禁用",value:0}})],1)],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]),e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")])],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"salesmanName","header-align":"center",align:"center",label:"业务员"}}),e("el-table-column",{attrs:{prop:"salesmanCode","header-align":"center",align:"center",label:"业务员编号"}}),e("el-table-column",{attrs:{prop:"commissionTypeDesc","header-align":"center",align:"center",label:"佣金类型"}}),e("el-table-column",{attrs:{prop:"calculationTypeDesc","header-align":"center",align:"center",label:"计算方式"}}),e("el-table-column",{attrs:{prop:"commissionValue","header-align":"center",align:"center",label:"佣金值"},scopedSlots:t._u([{key:"default",fn:function(a){return[1===a.row.calculationType?e("span",[t._v("¥"+t._s(a.row.commissionValue))]):e("span",[t._v(t._s((100*a.row.commissionValue).toFixed(2))+"%")])]}}])}),e("el-table-column",{attrs:{prop:"minAmount","header-align":"center",align:"center",label:"最小金额"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.minAmount?e("span",[t._v("¥"+t._s(a.row.minAmount))]):e("span",[t._v("-")])]}}])}),e("el-table-column",{attrs:{prop:"maxAmount","header-align":"center",align:"center",label:"最大金额"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.maxAmount?e("span",[t._v("¥"+t._s(a.row.maxAmount))]):e("span",[t._v("-")])]}}])}),e("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",label:"状态"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:1===a.row.status?"success":"danger"}},[t._v(" "+t._s(1===a.row.status?"启用":"禁用")+" ")])]}}])}),e("el-table-column",{attrs:{prop:"effectiveDate","header-align":"center",align:"center",width:"150",label:"生效时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e()],1)},o=[],s=(a("99af"),a("a15b"),a("d81d"),a("a573"),a("bb79")),i={data:function(){return{dataForm:{salesmanId:"",salesmanName:"",commissionType:"",status:""},currentSalesmanId:null,currentSalesmanName:"",dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,commissionTypes:[]}},components:{AddOrUpdate:s["default"]},activated:function(){this.initPageParams(),this.getDataList(),this.getCommissionTypes()},methods:{initPageParams:function(){this.currentSalesmanId=this.$route.query.salesmanId,this.currentSalesmanName=this.$route.query.salesmanName,this.dataForm.salesmanId=this.currentSalesmanId},getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/salesman/commission/config/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,salesmanId:this.dataForm.salesmanId,salesmanName:this.dataForm.salesmanName,commissionType:this.dataForm.commissionType,status:this.dataForm.status})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},getCommissionTypes:function(){var t=this;this.$http({url:this.$http.adornUrl("/salesman/commission/config/getCommissionTypes"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.commissionTypes=a.commissionTypes)}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(t,e.currentSalesmanId)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/salesman/commission/config/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))}}},r=i,l=a("2877"),m=Object(l["a"])(r,n,o,!1,null,null,null);e["default"]=m.exports}}]);
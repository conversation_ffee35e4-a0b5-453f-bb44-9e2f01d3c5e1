(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-312bb52c"],{1400:function(t,n,e){"use strict";e("afd6")},"54f8":function(t,n,e){"use strict";e.r(n);e("7f7f");var s=function(){var t=this,n=t._self._c;return n("div",{staticClass:"scan-qrcode-page"},[n("div",{staticClass:"header"},[n("van-nav-bar",{attrs:{title:"扫码绑定业务员","left-text":"返回","left-arrow":""},on:{"click-left":function(n){return t.$router.go(-1)}}})],1),n("div",{staticClass:"scan-container"},[t._m(0),n("div",{staticClass:"manual-input"},[n("van-button",{attrs:{type:"primary",size:"large",block:""},on:{click:t.showManualInput}},[t._v("\n        手动输入邀请码\n      ")])],1),n("div",{staticClass:"instructions"},[n("div",{staticClass:"instruction-title"},[t._v("使用说明")]),n("div",{staticClass:"instruction-list"},[n("div",{staticClass:"instruction-item"},[n("van-icon",{attrs:{name:"info-o"}}),n("span",[t._v("请向您的专属业务员获取二维码或邀请码")])],1),n("div",{staticClass:"instruction-item"},[n("van-icon",{attrs:{name:"info-o"}}),n("span",[t._v("绑定后可享受一对一专业服务")])],1),n("div",{staticClass:"instruction-item"},[n("van-icon",{attrs:{name:"info-o"}}),n("span",[t._v("如有问题请联系客服：400-123-4567")])],1)])])]),n("van-dialog",{attrs:{title:"输入邀请码","show-cancel-button":""},on:{confirm:t.bindByInviteCode},model:{value:t.manualInputVisible,callback:function(n){t.manualInputVisible=n},expression:"manualInputVisible"}},[n("div",{staticClass:"manual-input-content"},[n("van-field",{attrs:{placeholder:"请输入6-8位邀请码",maxlength:"8",clearable:"",center:""},model:{value:t.inviteCode,callback:function(n){t.inviteCode=n},expression:"inviteCode"}}),n("div",{staticClass:"input-tips"},[n("p",[t._v("邀请码由业务员提供，通常为6-8位字母数字组合")])])],1)]),n("van-dialog",{attrs:{title:t.bindResult.success?"绑定成功":"绑定失败","show-cancel-button":!1,"confirm-button-text":"确定"},on:{confirm:t.handleResultConfirm},model:{value:t.resultDialogVisible,callback:function(n){t.resultDialogVisible=n},expression:"resultDialogVisible"}},[n("div",{staticClass:"result-content"},[n("div",{staticClass:"result-icon"},[n("van-icon",{attrs:{name:t.bindResult.success?"success":"fail",color:t.bindResult.success?"#07c160":"#ee0a24",size:"48"}})],1),n("div",{staticClass:"result-message"},[n("p",[t._v(t._s(t.bindResult.message))]),t.bindResult.success&&t.bindResult.salesmanInfo?n("div",{staticClass:"salesman-info"},[n("p",[n("strong",[t._v("业务员：")]),t._v(t._s(t.bindResult.salesmanInfo.name))]),n("p",[n("strong",[t._v("编号：")]),t._v(t._s(t.bindResult.salesmanInfo.code))])]):t._e()])])])],1)},i=[function(){var t=this,n=t._self._c;return n("div",{staticClass:"scan-area"},[n("div",{staticClass:"scan-box"},[n("div",{staticClass:"scan-line"})]),n("div",{staticClass:"scan-tips"},[n("p",[t._v("将二维码放入框内，即可自动扫描")])])])}],a=(e("a481"),e("96cf"),e("1da1")),c={name:"ScanQrcode",data:function(){return{manualInputVisible:!1,inviteCode:"",resultDialogVisible:!1,bindResult:{success:!1,message:"",salesmanInfo:null},scannerInitialized:!1}},mounted:function(){this.initScanner()},beforeDestroy:function(){this.destroyScanner()},methods:{initScanner:function(){this.isWechat()?this.initWechatScanner():this.initH5Scanner()},isWechat:function(){return/micromessenger/i.test(navigator.userAgent)},initWechatScanner:function(){var t=this;window.wx?this.startWechatScan():this.loadWechatSDK().then((function(){t.startWechatScan()}))},startWechatScan:function(){var t=this;window.wx.scanQRCode({needResult:1,scanType:["qrCode"],success:function(n){t.handleScanResult(n.resultStr)},fail:function(){t.$toast("扫码失败，请重试")}})},initH5Scanner:function(){this.$toast("请在微信中打开或使用手动输入功能")},loadWechatSDK:function(){var t=this;return new Promise((function(n,e){if(window.wx)n();else{var s=document.createElement("script");s.src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js",s.onload=function(){t.configWechatSDK().then(n).catch(e)},s.onerror=e,document.head.appendChild(s)}}))},configWechatSDK:function(){var t=Object(a["a"])(regeneratorRuntime.mark((function t(){var n,e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$fly.get("/pyp/web/wechat/config",{url:window.location.href});case 3:if(n=t.sent,200!==n.code){t.next=8;break}return e=n.config,window.wx.config({debug:!1,appId:e.appId,timestamp:e.timestamp,nonceStr:e.nonceStr,signature:e.signature,jsApiList:["scanQRCode"]}),t.abrupt("return",new Promise((function(t,n){window.wx.ready(t),window.wx.error(n)})));case 8:t.next=13;break;case 10:throw t.prev=10,t.t0=t["catch"](0),new Error("微信配置失败");case 13:case"end":return t.stop()}}),t,this,[[0,10]])})));function n(){return t.apply(this,arguments)}return n}(),handleScanResult:function(t){t&&this.bindByQrCode(t)},bindByQrCode:function(){var t=Object(a["a"])(regeneratorRuntime.mark((function t(n){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$fly.post("/pyp/web/salesman/binding/bindByQrCode",{qrCodeContent:n});case 3:e=t.sent,200===e.code?this.showBindResult(!0,"绑定成功！",e.binding):this.showBindResult(!1,e.msg||"绑定失败"),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),this.showBindResult(!1,"网络错误，请重试");case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function n(n){return t.apply(this,arguments)}return n}(),showManualInput:function(){this.inviteCode="",this.manualInputVisible=!0},bindByInviteCode:function(){var t=Object(a["a"])(regeneratorRuntime.mark((function t(){var n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.inviteCode.trim()){t.next=3;break}return this.$toast("请输入邀请码"),t.abrupt("return");case 3:return t.prev=3,t.next=6,this.$fly.post("/pyp/web/salesman/binding/bindByInviteCode",{inviteCode:this.inviteCode.trim()});case 6:n=t.sent,this.manualInputVisible=!1,200===n.code?this.showBindResult(!0,"绑定成功！",n.binding):this.showBindResult(!1,n.msg||"绑定失败"),t.next=15;break;case 11:t.prev=11,t.t0=t["catch"](3),this.manualInputVisible=!1,this.showBindResult(!1,"网络错误，请重试");case 15:case"end":return t.stop()}}),t,this,[[3,11]])})));function n(){return t.apply(this,arguments)}return n}(),showBindResult:function(t,n){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;this.bindResult={success:t,message:n,salesmanInfo:e?{name:e.salesmanName,code:e.salesmanCode}:null},this.resultDialogVisible=!0},handleResultConfirm:function(){this.resultDialogVisible=!1,this.bindResult.success?this.$router.replace("/salesman/my-salesman"):this.initScanner()},destroyScanner:function(){this.scannerInitialized=!1}}},r=c,o=(e("1400"),e("2877")),u=Object(o["a"])(r,s,i,!1,null,"69e6eb88",null);n["default"]=u.exports},afd6:function(t,n,e){}}]);
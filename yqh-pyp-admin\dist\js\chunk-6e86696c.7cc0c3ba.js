(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6e86696c","chunk-e05ad362","chunk-0506e191","chunk-0506e191"],{1148:function(e,a,t){"use strict";var r=t("5926"),n=t("577e"),i=t("1d80"),o=RangeError;e.exports=function(e){var a=n(i(this)),t="",l=r(e);if(l<0||l===1/0)throw new o("Wrong number of repetitions");for(;l>0;(l>>>=1)&&(a+=a))1&l&&(t+=a);return t}},a15b:function(e,a,t){"use strict";var r=t("23e7"),n=t("e330"),i=t("44ad"),o=t("fc6a"),l=t("a640"),s=n([].join),c=i!==Object,d=c||!l("join",",");r({target:"Array",proto:!0,forced:d},{join:function(e){return s(o(this),void 0===e?",":e)}})},a573:function(e,a,t){"use strict";t("ab43")},ab43:function(e,a,t){"use strict";var r=t("23e7"),n=t("d024"),i=t("c430");r({target:"Iterator",proto:!0,real:!0,forced:i},{map:n})},b680:function(e,a,t){"use strict";var r=t("23e7"),n=t("e330"),i=t("5926"),o=t("408a"),l=t("1148"),s=t("d039"),c=RangeError,d=String,u=Math.floor,p=n(l),m=n("".slice),g=n(1..toFixed),h=function(e,a,t){return 0===a?t:a%2===1?h(e,a-1,t*e):h(e*e,a/2,t)},f=function(e){var a=0,t=e;while(t>=4096)a+=12,t/=4096;while(t>=2)a+=1,t/=2;return a},v=function(e,a,t){var r=-1,n=t;while(++r<6)n+=a*e[r],e[r]=n%1e7,n=u(n/1e7)},b=function(e,a){var t=6,r=0;while(--t>=0)r+=e[t],e[t]=u(r/a),r=r%a*1e7},F=function(e){var a=6,t="";while(--a>=0)if(""!==t||0===a||0!==e[a]){var r=d(e[a]);t=""===t?r:t+p("0",7-r.length)+r}return t},k=s((function(){return"0.000"!==g(8e-5,3)||"1"!==g(.9,0)||"1.25"!==g(1.255,2)||"1000000000000000128"!==g(0xde0b6b3a7640080,0)}))||!s((function(){g({})}));r({target:"Number",proto:!0,forced:k},{toFixed:function(e){var a,t,r,n,l=o(this),s=i(e),u=[0,0,0,0,0,0],g="",k="0";if(s<0||s>20)throw new c("Incorrect fraction digits");if(l!==l)return"NaN";if(l<=-1e21||l>=1e21)return d(l);if(l<0&&(g="-",l=-l),l>1e-21)if(a=f(l*h(2,69,1))-69,t=a<0?l*h(2,-a,1):l/h(2,a,1),t*=4503599627370496,a=52-a,a>0){v(u,0,t),r=s;while(r>=7)v(u,1e7,0),r-=7;v(u,h(10,r,1),0),r=a-1;while(r>=23)b(u,1<<23),r-=23;b(u,1<<r),v(u,1,1),b(u,2),k=F(u)}else v(u,0,t),v(u,1<<-a,0),k=F(u)+p("0",s);return s>0?(n=k.length,k=g+(n<=s?"0."+p("0",s-n)+k:m(k,0,n-s)+"."+m(k,n-s))):k=g+k,k}})},d024:function(e,a,t){"use strict";var r=t("c65b"),n=t("59ed"),i=t("825a"),o=t("46c4"),l=t("c5cc"),s=t("9bdd"),c=l((function(){var e=this.iterator,a=i(r(this.next,e)),t=this.done=!!a.done;if(!t)return s(e,this.mapper,[a.value,this.counter++],!0)}));e.exports=function(e){return i(this),n(e),new c(o(this),{mapper:e})}},d81d:function(e,a,t){"use strict";var r=t("23e7"),n=t("b727").map,i=t("1dde"),o=i("map");r({target:"Array",proto:!0,forced:!o},{map:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})},e1c8:function(e,a,t){"use strict";t.r(a);t("b0c0"),t("b680");var r=function(){var e=this,a=e._self._c;return a("div",{staticClass:"mod-config"},[a("el-form",{attrs:{inline:!0,model:e.dataForm}},[a("el-form-item",[a("el-input",{attrs:{placeholder:"套餐名称",clearable:""},model:{value:e.dataForm.name,callback:function(a){e.$set(e.dataForm,"name",a)},expression:"dataForm.name"}})],1),a("el-form-item",[a("el-select",{attrs:{placeholder:"套餐类型",clearable:""},model:{value:e.dataForm.packageType,callback:function(a){e.$set(e.dataForm,"packageType",a)},expression:"dataForm.packageType"}},[a("el-option",{attrs:{label:"充值次数套餐",value:1}}),a("el-option",{attrs:{label:"创建活动套餐",value:2}}),a("el-option",{attrs:{label:"活动续费套餐",value:3}})],1)],1),a("el-form-item",[a("el-select",{attrs:{placeholder:"状态",clearable:""},model:{value:e.dataForm.status,callback:function(a){e.$set(e.dataForm,"status",a)},expression:"dataForm.status"}},[a("el-option",{attrs:{label:"启用",value:1}}),a("el-option",{attrs:{label:"禁用",value:0}})],1)],1),a("el-form-item",[a("el-button",{on:{click:function(a){return e.getDataList()}}},[e._v("查询")]),e.isAuth("activity:rechargepackage:save")?a("el-button",{attrs:{type:"primary"},on:{click:function(a){return e.addOrUpdateHandle()}}},[e._v("新增")]):e._e(),e.isAuth("activity:rechargepackage:delete")?a("el-button",{attrs:{type:"danger",disabled:e.dataListSelections.length<=0},on:{click:function(a){return e.deleteHandle()}}},[e._v("批量删除")]):e._e(),a("el-button",{attrs:{type:"warning"},on:{click:function(a){return e.goToCreateActivityPackage()}}},[e._v("购买创建活动套餐")])],1),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"success"},on:{click:function(a){return e.$router.go(-1)}}},[e._v("返回")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:e.dataList,border:""},on:{"selection-change":e.selectionChangeHandle}},[a("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),a("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"套餐名称"}}),a("el-table-column",{attrs:{prop:"packageType","header-align":"center",align:"center",label:"套餐类型"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{type:e.getPackageTypeTagType(t.row.packageType)}},[e._v(" "+e._s(e.getPackageTypeName(t.row.packageType))+" ")])]}}])}),a("el-table-column",{attrs:{prop:"description","header-align":"center",align:"center",label:"套餐描述","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"countValue","header-align":"center",align:"center",label:"充值次数"}}),a("el-table-column",{attrs:{prop:"price","header-align":"center",align:"center",label:"现价(元)"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticStyle:{color:"#f56c6c","font-weight":"bold"}},[e._v("¥"+e._s(t.row.price))])]}}])}),a("el-table-column",{attrs:{prop:"originalPrice","header-align":"center",align:"center",label:"原价(元)"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.originalPrice?a("span",{staticStyle:{"text-decoration":"line-through",color:"#909399"}},[e._v("¥"+e._s(t.row.originalPrice))]):a("span",[e._v("-")])]}}])}),a("el-table-column",{attrs:{prop:"discountRate","header-align":"center",align:"center",label:"折扣"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.discountRate&&t.row.discountRate<1?a("span",[e._v(e._s((10*t.row.discountRate).toFixed(1))+"折")]):a("span",[e._v("-")])]}}])}),a("el-table-column",{attrs:{prop:"validDays","header-align":"center",align:"center",label:"有效期(天)"},scopedSlots:e._u([{key:"default",fn:function(t){return[3!==t.row.packageType?a("span",[e._v(e._s(t.row.validDays||"-"))]):a("span",[e._v("-")])]}}])}),a("el-table-column",{attrs:{prop:"renewalDays","header-align":"center",align:"center",label:"续费天数"},scopedSlots:e._u([{key:"default",fn:function(t){return[3===t.row.packageType?a("span",[e._v(e._s(t.row.renewalDays||"-"))]):a("span",[e._v("-")])]}}])}),a("el-table-column",{attrs:{prop:"isHot","header-align":"center",align:"center",label:"热门"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{type:1==t.row.isHot?"danger":"info"}},[e._v(e._s(1==t.row.isHot?"热门":"普通"))])]}}])}),a("el-table-column",{attrs:{prop:"isRecommended","header-align":"center",align:"center",label:"推荐"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{type:1==t.row.isRecommended?"success":"info"}},[e._v(e._s(1==t.row.isRecommended?"推荐":"普通"))])]}}])}),a("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",label:"状态"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{type:1==t.row.status?"success":"danger"}},[e._v(e._s(1==t.row.status?"启用":"禁用"))])]}}])}),a("el-table-column",{attrs:{prop:"sortOrder","header-align":"center",align:"center",label:"排序"}}),a("el-table-column",{attrs:{"show-overflow-tooltip":"",prop:"createOn","header-align":"center",align:"center",label:"创建时间"}}),a("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.addOrUpdateHandle(t.row.id)}}},[e._v("修改")]),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.deleteHandle(t.row.id)}}},[e._v("删除")])]}}])})],1),a("el-pagination",{attrs:{"current-page":e.pageIndex,"page-sizes":[10,20,50,100],"page-size":e.pageSize,total:e.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle}}),e.addOrUpdateVisible?a("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:e.getDataList}}):e._e()],1)},n=[],i=(t("99af"),t("a15b"),t("d81d"),t("14d9"),t("a573"),t("ef2b")),o={data:function(){return{dataForm:{name:"",packageType:"",status:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1}},components:{AddOrUpdate:i["default"]},activated:function(){this.getDataList()},methods:{getDataList:function(){var e=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/activity/rechargepackage/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,name:this.dataForm.name,packageType:this.dataForm.packageType,status:this.dataForm.status})}).then((function(a){var t=a.data;t&&200===t.code?(e.dataList=t.page.list,e.totalPage=t.page.totalCount):(e.dataList=[],e.totalPage=0),e.dataListLoading=!1}))},sizeChangeHandle:function(e){this.pageSize=e,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(e){this.pageIndex=e,this.getDataList()},selectionChangeHandle:function(e){this.dataListSelections=e},addOrUpdateHandle:function(e){var a=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){a.$refs.addOrUpdate.init(e)}))},deleteHandle:function(e){var a=this,t=e?[e]:this.dataListSelections.map((function(e){return e.id}));this.$confirm("确定对[id=".concat(t.join(","),"]进行[").concat(e?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.$http({url:a.$http.adornUrl("/activity/rechargepackage/delete"),method:"post",data:a.$http.adornData(t,!1)}).then((function(e){var t=e.data;t&&200===t.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.getDataList()}}):a.$message.error(t.msg)}))}))},goToCreateActivityPackage:function(){this.$router.push({name:"create-activity-package-order"})},getPackageTypeName:function(e){switch(e){case 1:return"充值次数套餐";case 2:return"创建活动套餐";case 3:return"活动续费套餐";default:return"未知类型"}},getPackageTypeTagType:function(e){switch(e){case 1:return"primary";case 2:return"warning";case 3:return"success";default:return"info"}}}},l=o,s=t("2877"),c=Object(s["a"])(l,r,n,!1,null,null,null);a["default"]=c.exports},ef2b:function(e,a,t){"use strict";t.r(a);t("a4d3"),t("e01a"),t("b0c0");var r=function(){var e=this,a=e._self._c;return a("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(a){e.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"套餐名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"套餐名称"},model:{value:e.dataForm.name,callback:function(a){e.$set(e.dataForm,"name",a)},expression:"dataForm.name"}})],1),a("el-form-item",{attrs:{label:"套餐描述",prop:"description"}},[a("el-input",{attrs:{type:"textarea",rows:3,placeholder:"套餐描述"},model:{value:e.dataForm.description,callback:function(a){e.$set(e.dataForm,"description",a)},expression:"dataForm.description"}})],1),a("el-form-item",{attrs:{label:"套餐类型",prop:"packageType"}},[a("el-radio-group",{on:{change:e.onPackageTypeChange},model:{value:e.dataForm.packageType,callback:function(a){e.$set(e.dataForm,"packageType",a)},expression:"dataForm.packageType"}},[a("el-radio",{attrs:{label:1}},[e._v("充值次数套餐")]),a("el-radio",{attrs:{label:2}},[e._v("创建活动套餐")]),a("el-radio",{attrs:{label:3}},[e._v("活动续费套餐")])],1),a("div",{staticStyle:{color:"#909399","font-size":"12px","margin-top":"5px"}},[e._v(" 充值次数套餐：为现有活动增加使用次数；创建活动套餐：购买后自动创建新活动；活动续费套餐：延长活动有效期 ")])],1),a("el-form-item",{attrs:{label:"充值次数",prop:"countValue"}},[a("el-input-number",{attrs:{min:1,max:1e4,placeholder:"充值次数"},model:{value:e.dataForm.countValue,callback:function(a){e.$set(e.dataForm,"countValue",a)},expression:"dataForm.countValue"}})],1),a("el-form-item",{attrs:{label:"现价(元)",prop:"price"}},[a("el-input-number",{attrs:{precision:2,min:.01,max:9999.99,placeholder:"现价"},on:{change:e.onPriceChange},model:{value:e.dataForm.price,callback:function(a){e.$set(e.dataForm,"price",a)},expression:"dataForm.price"}})],1),a("el-form-item",{attrs:{label:"原价(元)",prop:"originalPrice"}},[a("el-input-number",{attrs:{precision:2,min:0,max:9999.99,placeholder:"原价(可选)"},on:{change:e.onOriginalPriceChange},model:{value:e.dataForm.originalPrice,callback:function(a){e.$set(e.dataForm,"originalPrice",a)},expression:"dataForm.originalPrice"}})],1),a("el-form-item",{attrs:{label:"折扣率",prop:"discountRate"}},[a("el-input-number",{attrs:{precision:2,min:.01,max:1,step:.01,placeholder:"折扣率(0.01-1)"},on:{change:e.onDiscountRateChange},model:{value:e.dataForm.discountRate,callback:function(a){e.$set(e.dataForm,"discountRate",a)},expression:"dataForm.discountRate"}}),a("div",{staticStyle:{color:"#909399","font-size":"12px","margin-top":"5px"}},[e._v(" 例如：0.8表示8折，1表示无折扣。修改折扣率会自动计算现价，修改现价会自动计算折扣率 ")])],1),3!==e.dataForm.packageType?a("el-form-item",{attrs:{label:"有效期(天)",prop:"validDays"}},[a("el-input-number",{attrs:{min:1,max:3650,placeholder:"有效期天数"},model:{value:e.dataForm.validDays,callback:function(a){e.$set(e.dataForm,"validDays",a)},expression:"dataForm.validDays"}})],1):e._e(),3===e.dataForm.packageType?a("el-form-item",{attrs:{label:"续费天数",prop:"renewalDays"}},[a("el-input-number",{attrs:{min:1,max:3650,placeholder:"续费天数"},model:{value:e.dataForm.renewalDays,callback:function(a){e.$set(e.dataForm,"renewalDays",a)},expression:"dataForm.renewalDays"}}),a("div",{staticStyle:{color:"#909399","font-size":"12px","margin-top":"5px"}},[e._v(" 购买此套餐后，活动有效期将延长指定天数 ")])],1):e._e(),a("el-form-item",{attrs:{label:"是否热门",prop:"isHot"}},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.dataForm.isHot,callback:function(a){e.$set(e.dataForm,"isHot",a)},expression:"dataForm.isHot"}})],1),a("el-form-item",{attrs:{label:"是否推荐",prop:"isRecommended"}},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.dataForm.isRecommended,callback:function(a){e.$set(e.dataForm,"isRecommended",a)},expression:"dataForm.isRecommended"}})],1),a("el-form-item",{attrs:{label:"是否启用",prop:"status"}},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.dataForm.status,callback:function(a){e.$set(e.dataForm,"status",a)},expression:"dataForm.status"}})],1),a("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[a("el-input-number",{attrs:{min:0,placeholder:"排序，数值越小越靠前"},model:{value:e.dataForm.sortOrder,callback:function(a){e.$set(e.dataForm,"sortOrder",a)},expression:"dataForm.sortOrder"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){e.visible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},n=[],i=(t("b680"),{data:function(){return{visible:!1,isCalculating:!1,dataForm:{repeatToken:"",id:0,name:"",description:"",packageType:1,countValue:1,price:.01,originalPrice:null,discountRate:1,validDays:365,renewalDays:30,isHot:0,isRecommended:0,status:1,sortOrder:0},dataRule:{name:[{required:!0,message:"套餐名称不能为空",trigger:"blur"}],packageType:[{required:!0,message:"套餐类型不能为空",trigger:"change"}],countValue:[{required:!0,message:"充值次数不能为空",trigger:"blur"}],price:[{required:!0,message:"现价不能为空",trigger:"blur"}],validDays:[{required:!0,message:"有效期不能为空",trigger:"blur"}]}}},methods:{init:function(e){var a=this;this.getToken(),this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/activity/rechargepackage/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(e){var t=e.data;t&&200===t.code&&(a.dataForm.name=t.rechargePackage.name,a.dataForm.description=t.rechargePackage.description,a.dataForm.packageType=t.rechargePackage.packageType||1,a.dataForm.countValue=t.rechargePackage.countValue,a.dataForm.price=t.rechargePackage.price,a.dataForm.originalPrice=t.rechargePackage.originalPrice,a.dataForm.discountRate=t.rechargePackage.discountRate,a.dataForm.validDays=t.rechargePackage.validDays,a.dataForm.renewalDays=t.rechargePackage.renewalDays||30,a.dataForm.isHot=t.rechargePackage.isHot,a.dataForm.isRecommended=t.rechargePackage.isRecommended,a.dataForm.status=t.rechargePackage.status,a.dataForm.sortOrder=t.rechargePackage.sortOrder)}))}))},getToken:function(){var e=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(a){var t=a.data;t&&200===t.code&&(e.dataForm.repeatToken=t.result)}))},onDiscountRateChange:function(e){var a=this;this.isCalculating||e&&this.dataForm.originalPrice&&this.dataForm.originalPrice>0&&(this.isCalculating=!0,this.dataForm.price=parseFloat((this.dataForm.originalPrice*e).toFixed(2)),this.$nextTick((function(){a.isCalculating=!1})))},onPriceChange:function(e){var a=this;this.isCalculating||e&&this.dataForm.originalPrice&&this.dataForm.originalPrice>0&&(this.isCalculating=!0,this.dataForm.discountRate=parseFloat((e/this.dataForm.originalPrice).toFixed(2)),this.dataForm.discountRate>1&&(this.dataForm.discountRate=1),this.dataForm.discountRate<.01&&(this.dataForm.discountRate=.01),this.$nextTick((function(){a.isCalculating=!1})))},onOriginalPriceChange:function(e){var a=this;this.isCalculating||e&&this.dataForm.discountRate&&this.dataForm.discountRate>0&&(this.isCalculating=!0,this.dataForm.price=parseFloat((e*this.dataForm.discountRate).toFixed(2)),this.$nextTick((function(){a.isCalculating=!1})))},onPackageTypeChange:function(e){3===e?(this.dataForm.countValue=0,this.dataForm.renewalDays=30):(this.dataForm.countValue=1,this.dataForm.validDays=365)},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(a){a&&e.$http({url:e.$http.adornUrl("/activity/rechargepackage/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({repeatToken:e.dataForm.repeatToken,id:e.dataForm.id||void 0,name:e.dataForm.name,description:e.dataForm.description,packageType:e.dataForm.packageType,countValue:e.dataForm.countValue,price:e.dataForm.price,originalPrice:e.dataForm.originalPrice,discountRate:e.dataForm.discountRate,validDays:e.dataForm.validDays,renewalDays:e.dataForm.renewalDays,isHot:e.dataForm.isHot,isRecommended:e.dataForm.isRecommended,status:e.dataForm.status,sortOrder:e.dataForm.sortOrder})}).then((function(a){var t=a.data;t&&200===t.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):(e.$message.error(t.msg),"不能重复提交"!=t.msg&&e.getToken())}))}))}}}),o=i,l=t("2877"),s=Object(l["a"])(o,r,n,!1,null,null,null);a["default"]=s.exports}}]);
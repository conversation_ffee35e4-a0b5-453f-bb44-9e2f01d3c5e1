(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3097ff07","chunk-28b224fe","chunk-49af1aae","chunk-2d0f1183"],{"202c":function(t,e,a){"use strict";a("41490")},"24c5":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"获取直播间流量","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"查询时间"}},[e("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择查询时间","value-format":"yyyy-MM-dd HH:mm:ss"},on:{change:t.dateChange},model:{value:t.dataForm.startTime,callback:function(e){t.$set(t.dataForm,"startTime",e)},expression:"dataForm.startTime"}})],1),e("div",{staticClass:"mod-user"},[e("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"center"}},[e("div",{staticStyle:{"margin-left":"20px","font-size":"24px","font-weight":"600"}},[t._v("直播间流量："+t._s(t.number)+"G")])])])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],n=a("c466"),o={data:function(){return{wxAccount:{},appid:"",number:0,visible:!1,dataForm:{id:0,startTime:"",endTime:"",pushKey:""},dataRule:{startTime:[{required:!0,message:"推流过期时间不能为空",trigger:"blur"}]}}},methods:{init:function(t){var e=this;this.appid=this.$cookie.get("appid"),this.dataForm.id=t,this.visible=!0,this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.getPlaceInfo()}))},dateChange:function(t){this.dataForm.endTime=Object(n["b"])(t,1440,"yyyy-MM-dd hh:mm:ss")},getPlaceInfo:function(){var t=this;this.$http({url:this.$http.adornUrl("/place/placeactivity/info/".concat(this.dataForm.id)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm.pushKey=a.placeActivity.pushKey)}))},getAccountInfo:function(){var t=this;this.$http({url:this.$http.adornUrl("/manage/wxAccount/info/".concat(this.appid)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.wxAccount=a.wxAccount,t.getPlaceInfo())}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/place/placeactivityvideo/getLiveNumber"),method:"get",params:t.$http.adornParams({pushKey:t.dataForm.pushKey,startTime:t.dataForm.startTime,endTime:t.dataForm.endTime})}).then((function(e){var a=e.data;a&&200===a.code?t.number=a.result:t.$message.error(a.msg)}))}))}}},l=o,s=a("2877"),c=Object(s["a"])(l,i,r,!1,null,null,null);e["default"]=c.exports},41490:function(t,e,a){},"466d":function(t,e,a){"use strict";var i=a("c65b"),r=a("d784"),n=a("825a"),o=a("7234"),l=a("50c4"),s=a("577e"),c=a("1d80"),d=a("dc4a"),u=a("8aa5"),m=a("14c3");r("match",(function(t,e,a){return[function(e){var a=c(this),r=o(e)?void 0:d(e,t);return r?i(r,e,a):new RegExp(e)[t](s(a))},function(t){var i=n(this),r=s(t),o=a(e,i,r);if(o.done)return o.value;if(!i.global)return m(i,r);var c=i.unicode;i.lastIndex=0;var d,p=[],v=0;while(null!==(d=m(i,r))){var h=s(d[0]);p[v]=h,""===h&&(i.lastIndex=u(r,l(i.lastIndex),c)),v++}return 0===v?null:p}]}))},"7de9":function(t,e,a){"use strict";a.d(e,"g",(function(){return i})),a.d(e,"f",(function(){return r})),a.d(e,"e",(function(){return n})),a.d(e,"a",(function(){return o})),a.d(e,"b",(function(){return l})),a.d(e,"c",(function(){return s})),a.d(e,"d",(function(){return c}));var i=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],r=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],n=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],o=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],l=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],s=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],c=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]},"9ebc":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"开启推流","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"推流过期时间"}},[e("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime",placeholder:"请选择推流过期时间","value-format":"yyyy/MM/dd HH:mm:ss"},model:{value:t.dataForm.pushExpiredTime,callback:function(e){t.$set(t.dataForm,"pushExpiredTime",e)},expression:"dataForm.pushExpiredTime"}}),e("div",{staticStyle:{color:"red"}},[t._v("设置后，不要轻易修改")])],1),1==t.dataForm.pushStatus?e("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"推流地址"}},[e("el-input",{attrs:{disabled:"true",placeholder:"推流地址",clearable:""},model:{value:t.dataForm.pushUrl,callback:function(e){t.$set(t.dataForm,"pushUrl",e)},expression:"dataForm.pushUrl"}})],1):t._e(),1==t.dataForm.pushStatus?e("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"推流名称"}},[e("el-input",{attrs:{disabled:"true",placeholder:"推流名称",clearable:""},model:{value:t.dataForm.obsPushUrl,callback:function(e){t.$set(t.dataForm,"obsPushUrl",e)},expression:"dataForm.obsPushUrl"}})],1):t._e(),1==t.dataForm.pushStatus?e("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"推流秘钥"}},[e("el-input",{attrs:{disabled:"true",placeholder:"推流秘钥",clearable:""},model:{value:t.dataForm.obsPushSecretKey,callback:function(e){t.$set(t.dataForm,"obsPushSecretKey",e)},expression:"dataForm.obsPushSecretKey"}})],1):t._e(),e("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"拉流地址"}},[e("el-input",{attrs:{placeholder:"拉流地址",clearable:""},model:{value:t.dataForm.playUrl,callback:function(e){t.$set(t.dataForm,"playUrl",e)},expression:"dataForm.playUrl"}})],1),e("div",{staticClass:"mod-user"},[e("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"center"}},[e("vue-qrcode",{attrs:{options:{width:120},value:t.iframeSrc}}),e("div",{staticStyle:{"margin-left":"20px","font-size":"24px","font-weight":"600"}},[t._v("扫码通过手机预览")])],1)])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],n=a("b2e5"),o=a.n(n),l={data:function(){return{wxAccount:{},appid:"",iframeSrc:"",visible:!1,dataForm:{id:0,activityId:"",pushKey:"",pushExpiredTime:"",pushUrl:"",obsPushUrl:"",obsPushSecretKey:"",pushStatus:"",advice:"",playUrl:"",playStatus:"",playRtmpUrl:"",playFlvUrl:"",playUdpUrl:""},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],pushExpiredTime:[{required:!0,message:"推流过期时间不能为空",trigger:"blur"}]}}},components:{VueQrcode:o.a},methods:{init:function(t){var e=this;this.appid=this.$cookie.get("appid"),this.dataForm.id=t,this.visible=!0,this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.getAccountInfo()}))},getPlaceInfo:function(){var t=this;this.$http({url:this.$http.adornUrl("/place/placeactivity/info/".concat(this.dataForm.id)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.dataForm=a.placeActivity,t.iframeSrc=t.wxAccount.baseUrl+"lives/detail?detailId="+t.dataForm.id+"&id="+t.dataForm.activityId,console.log(t.iframeSrc))}))},getAccountInfo:function(){var t=this;this.$http({url:this.$http.adornUrl("/manage/wxAccount/info/".concat(this.appid)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.wxAccount=a.wxAccount,t.getPlaceInfo())}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/place/placeactivity/openPush"),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,pushKey:t.dataForm.pushKey,pushExpiredTime:t.dataForm.pushExpiredTime})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1e3,onClose:function(){t.getPlaceInfo()}}):t.$message.error(a.msg)}))}))}}},s=l,c=a("2877"),d=Object(c["a"])(s,i,r,!1,null,null,null);e["default"]=d.exports},a15b:function(t,e,a){"use strict";var i=a("23e7"),r=a("e330"),n=a("44ad"),o=a("fc6a"),l=a("a640"),s=r([].join),c=n!==Object,d=c||!l("join",",");i({target:"Array",proto:!0,forced:d},{join:function(t){return s(o(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var i=a("23e7"),r=a("d024"),n=a("c430");i({target:"Iterator",proto:!0,real:!0,forced:n},{map:r})},b352:function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("div",{staticStyle:{"text-align":"center",padding:"10px","font-weight":"bold","font-size":"24px"}},[t._v(" 议程配置 ")]),e("el-row",{staticStyle:{"margin-bottom":"30px"},attrs:{gutter:12}},[e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"card",attrs:{shadow:"always"}},[t._v(" 会场管理 ")])],1),e("el-col",{attrs:{span:6},nativeOn:{click:function(e){return t.placeactivitytopic.apply(null,arguments)}}},[e("el-card",{staticClass:"card",attrs:{shadow:"always"}},[t._v(" 主题管理 ")])],1),e("el-col",{attrs:{span:6},nativeOn:{click:function(e){return t.placeactivitytopicschedule.apply(null,arguments)}}},[e("el-card",{staticClass:"card",attrs:{shadow:"always"}},[t._v(" 日程管理 ")])],1),e("el-col",{attrs:{span:6},nativeOn:{click:function(e){return t.activityguest.apply(null,arguments)}}},[e("el-card",{staticClass:"card",attrs:{shadow:"always"}},[t._v(" 嘉宾管理 ")])],1)],1),e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"会场名称",clearable:""},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),t.isAuth("place:placeactivity:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("place:placeactivity:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"会场名称"}}),e("el-table-column",{attrs:{prop:"paixu","header-align":"center",align:"center",label:"排序(越小越前)"}}),e("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",label:"创建时间"}}),e("el-table-column",{attrs:{prop:"updateOn","header-align":"center",align:"center",label:"更新时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"280",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.placeactivitytopic(a.row)}}},[t._v("主题管理")]),t.isAuth("place:placeactivity:live")&&1==a.row.isLive?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.openPush(a.row.id)}}},[t._v("推流")]):t._e(),t.isAuth("place:placeactivity:live")&&1==a.row.isLive?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.placeactivitylive(a.row.id)}}},[t._v("流量")]):t._e(),t.isAuth("place:chat:list")&&1==a.row.isLive?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.openChat(a.row)}}},[t._v("聊天室")]):t._e(),t.isAuth("place:placeactivityvideo:list")&&1==a.row.isLive?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.openVideo(a.row)}}},[t._v("录播")]):t._e(),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e(),t.openpushVisible?e("openpush",{ref:"openpush",on:{refreshDataList:t.getDataList}}):t._e(),t.placeactivityliveVisible?e("placeactivitylive",{ref:"placeactivitylive",on:{refreshDataList:t.getDataList}}):t._e()],1)},r=[],n=(a("99af"),a("a15b"),a("d81d"),a("14d9"),a("a573"),a("c197")),o=a("9ebc"),l=a("24c5"),s={data:function(){return{dataForm:{name:"",activityId:void 0},url:"",dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,openpushVisible:!1,placeactivityliveVisible:!1}},components:{AddOrUpdate:n["default"],openpush:o["default"],placeactivitylive:l["default"]},activated:function(){this.dataForm.activityId=this.$route.query.activityId,this.getDataList()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/place/placeactivity/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,activityId:this.dataForm.activityId,name:this.dataForm.name})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(e.dataForm.activityId,t)}))},openPush:function(t){var e=this;this.openpushVisible=!0,this.$nextTick((function(){e.$refs.openpush.init(t)}))},placeactivitylive:function(t){var e=this;this.placeactivityliveVisible=!0,this.$nextTick((function(){e.$refs.placeactivitylive.init(t)}))},openChat:function(t){this.$router.push({name:"chat",query:{activityId:this.dataForm.activityId,placeId:t.id,pushKey:t.pushKey}})},openVideo:function(t){this.$router.push({name:"placeactivityvideo",query:{activityId:this.dataForm.activityId,placeId:t.id,pushKey:t.pushKey}})},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/place/placeactivity/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},placeactivitytopic:function(t){null!=t.id?this.$router.push({name:"placeactivitytopic",query:{activityId:this.dataForm.activityId,placeId:t.id}}):this.$router.push({name:"placeactivitytopic",query:{activityId:this.dataForm.activityId}})},placeactivitytopicschedule:function(){this.$router.push({name:"placeactivitytopicschedule",query:{activityId:this.dataForm.activityId}})},activityguest:function(){this.$router.push({name:"activityguest",query:{activityId:this.dataForm.activityId}})}}},c=s,d=(a("202c"),a("2877")),u=Object(d["a"])(c,i,r,!1,null,"1653e4fc",null);e["default"]=u.exports},c197:function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"会场名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"会场名称"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",{attrs:{label:"是否直播",prop:"isLive"}},[e("el-switch",{attrs:{width:35,"active-color":"#60CA7E","inactive-color":"#dcdfe6","active-value":1,"inactive-value":0},model:{value:t.dataForm.isLive,callback:function(e){t.$set(t.dataForm,"isLive",e)},expression:"dataForm.isLive"}})],1),t.dataForm.isLive?e("el-form-item",{attrs:{label:"直播状态",prop:"playStatus"}},[e("el-select",{attrs:{placeholder:"直播状态",filterable:""},model:{value:t.dataForm.playStatus,callback:function(e){t.$set(t.dataForm,"playStatus",e)},expression:"dataForm.playStatus"}},t._l(t.playStatus,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1):t._e(),e("el-form-item",{attrs:{label:"日程封面",prop:"cover"}},[e("el-upload",{staticClass:"avatar-uploader",attrs:{"list-type":"picture-card","show-file-list":!1,accept:".jpg, .jpeg, .png, .gif","on-success":t.backgroundSuccessHandle,action:t.url}},[t.dataForm.cover?e("img",{staticClass:"avatar",attrs:{width:"100px",src:t.dataForm.cover}}):e("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),t.dataForm.isLive?e("el-form-item",{attrs:{label:"直播时间"}},[e("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime",placeholder:"请选择直播时间","value-format":"yyyy/MM/dd HH:mm:ss"},model:{value:t.dataForm.liveTime,callback:function(e){t.$set(t.dataForm,"liveTime",e)},expression:"dataForm.liveTime"}})],1):t._e(),t.dataForm.isLive?e("el-form-item",{attrs:{label:"是否显示倒计时",prop:"isCountdown"}},[e("el-select",{attrs:{placeholder:"是否显示倒计时",filterable:""},model:{value:t.dataForm.isCountdown,callback:function(e){t.$set(t.dataForm,"isCountdown",e)},expression:"dataForm.isCountdown"}},t._l(t.yesOrNo,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1):t._e(),t.dataForm.isLive?e("el-form-item",{attrs:{label:"直播间通知",prop:"advice"}},[e("el-input",{attrs:{placeholder:"直播间通知"},model:{value:t.dataForm.advice,callback:function(e){t.$set(t.dataForm,"advice",e)},expression:"dataForm.advice"}})],1):t._e(),t.dataForm.isLive?e("el-form-item",{attrs:{label:"点击数",prop:"pvCount"}},[e("el-input",{attrs:{disabled:"",placeholder:"点击数"},model:{value:t.dataForm.pvCount,callback:function(e){t.$set(t.dataForm,"pvCount",e)},expression:"dataForm.pvCount"}})],1):t._e(),t.dataForm.isLive?e("el-form-item",{attrs:{label:"访问数",prop:"uvCount"}},[e("el-input",{attrs:{disabled:"",placeholder:"访问数"},model:{value:t.dataForm.uvCount,callback:function(e){t.$set(t.dataForm,"uvCount",e)},expression:"dataForm.uvCount"}})],1):t._e(),t.dataForm.isLive?e("el-form-item",{attrs:{label:"虚拟倍数",prop:"mulity"}},[e("el-input",{attrs:{placeholder:"虚拟倍数"},model:{value:t.dataForm.mulity,callback:function(e){t.$set(t.dataForm,"mulity",e)},expression:"dataForm.mulity"}})],1):t._e(),t.dataForm.isLive?e("el-form-item",{attrs:{label:"虚拟点击数",prop:"virtualPvCount"}},[e("el-input",{attrs:{placeholder:"虚拟点击数"},model:{value:t.dataForm.virtualPvCount,callback:function(e){t.$set(t.dataForm,"virtualPvCount",e)},expression:"dataForm.virtualPvCount"}})],1):t._e(),t.dataForm.isLive?e("el-form-item",{attrs:{label:"虚拟访问数",prop:"virtualUvCount"}},[e("el-input",{attrs:{placeholder:"虚拟访问数"},model:{value:t.dataForm.virtualUvCount,callback:function(e){t.$set(t.dataForm,"virtualUvCount",e)},expression:"dataForm.virtualUvCount"}})],1):t._e(),e("el-form-item",{attrs:{label:"排序",prop:"paixu"}},[e("el-input",{attrs:{placeholder:"排序"},model:{value:t.dataForm.paixu,callback:function(e){t.$set(t.dataForm,"paixu",e)},expression:"dataForm.paixu"}})],1),t.dataForm.isLive?e("el-form-item",{attrs:{label:"弹窗通知显示时间"}},[e("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime",placeholder:"弹窗通知显示时间","value-format":"yyyy/MM/dd HH:mm:ss"},model:{value:t.dataForm.notifyTime,callback:function(e){t.$set(t.dataForm,"notifyTime",e)},expression:"dataForm.notifyTime"}})],1):t._e(),t.dataForm.isLive?e("el-form-item",{attrs:{label:"弹窗通知",prop:"notify"}},[e("tinymce-editor",{ref:"editor",model:{value:t.dataForm.notify,callback:function(e){t.$set(t.dataForm,"notify",e)},expression:"dataForm.notify"}})],1):t._e()],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],n=(a("d3b7"),a("3ca3"),a("ddb0"),a("7c8d")),o=a.n(n),l=a("ed56"),s=a("7de9"),c={components:{TinymceEditor:function(){return Promise.all([a.e("chunk-03be236c"),a.e("chunk-2d0a4b8c")]).then(a.bind(null,"26dc"))}},data:function(){return{yesOrNo:s["g"],playStatus:l["c"],visible:!1,url:"",dataForm:{id:0,activityId:"",name:"",isLive:0,playStatus:0,liveTime:"",realityOnline:0,pvCount:0,uvCount:0,virtualPvCount:0,virtualUvCount:0,paixu:0,isCountdown:1,cover:"",imGroupId:"",advice:"",notify:"",notifyTime:"",mulity:5},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],name:[{required:!0,message:"会场名称不能为空",trigger:"blur"}]}}},methods:{init:function(t,e){var a=this;this.url=this.$http.adornUrl("/sys/oss/upload?token=".concat(this.$cookie.get("token"))),this.dataForm.activityId=t,this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/place/placeactivity/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.dataForm=e.placeActivity)}))}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/place/placeactivity/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,activityId:t.dataForm.activityId,name:t.dataForm.name,isLive:t.dataForm.isLive,liveTime:t.dataForm.liveTime,realityOnline:t.dataForm.realityOnline,pvCount:t.dataForm.pvCount,uvCount:t.dataForm.uvCount,virtualPvCount:t.dataForm.virtualPvCount,virtualUvCount:t.dataForm.virtualUvCount,cover:t.dataForm.cover,imGroupId:t.dataForm.imGroupId,advice:t.dataForm.advice,notify:t.dataForm.notify,playStatus:t.dataForm.playStatus,paixu:t.dataForm.paixu,isCountdown:t.dataForm.isCountdown,notifyTime:t.dataForm.notifyTime,mulity:t.dataForm.mulity})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))},checkFileSize:function(t){return t.size/1024/1024>6?(this.$message.error("".concat(t.name,"文件大于6MB，请选择小于6MB大小的图片")),!1):!(t.size/1024>100)||new Promise((function(e,a){new o.a(t,{quality:.8,success:function(t){e(t)}})}))},backgroundSuccessHandle:function(t,e,a){t&&200===t.code?(this.dataForm.cover=t.url,this.$message({message:"上传成功",type:"success"})):this.$message.error(t.msg)}}},d=c,u=a("2877"),m=Object(u["a"])(d,i,r,!1,null,null,null);e["default"]=m.exports},c466:function(t,e,a){"use strict";a.d(e,"c",(function(){return l})),a.d(e,"a",(function(){return s})),a.d(e,"b",(function(){return c}));a("ac1f"),a("466d"),a("5319");var i=/([yMdhsm])(\1*)/g,r="yyyy/MM/dd",n="yyyy/MM/dd hh:mm:ss";function o(t,e){e-=(t+"").length;for(var a=0;a<e;a++)t="0"+t;return t}function l(t,e){return e=e||r,e.replace(i,(function(e){switch(e.charAt(0)){case"y":return o(t.getFullYear(),e.length);case"M":return o(t.getMonth()+1,e.length);case"d":return o(t.getDate(),e.length);case"w":return t.getDay()+1;case"h":return o(t.getHours(),e.length);case"m":return o(t.getMinutes(),e.length);case"s":return o(t.getSeconds(),e.length)}}))}function s(t,e){var a=new Date(t),i=new Date(a.getTime()+24*e*60*60*1e3);return l(i,n)}function c(t,e,a){var i=new Date(t),r=new Date(i.getTime()+60*e*1e3);return l(r,a||n)}},d024:function(t,e,a){"use strict";var i=a("c65b"),r=a("59ed"),n=a("825a"),o=a("46c4"),l=a("c5cc"),s=a("9bdd"),c=l((function(){var t=this.iterator,e=n(i(this.next,t)),a=this.done=!!e.done;if(!a)return s(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return n(this),r(t),new c(o(this),{mapper:t})}},d81d:function(t,e,a){"use strict";var i=a("23e7"),r=a("b727").map,n=a("1dde"),o=n("map");i({target:"Array",proto:!0,forced:!o},{map:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})},ed56:function(t,e,a){"use strict";a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return n}));var i=[{key:0,value:"预告"},{key:1,value:"直播"},{key:2,value:"录播"}],r=[{key:0,value:"未确认"},{key:1,value:"确认通过"},{key:2,value:"确认不通过"}],n=[{key:0,value:"自定义内容"},{key:1,value:"自定义链接"},{key:2,value:"会议日程"},{key:3,value:"会议嘉宾"},{key:4,value:"聊天室"},{key:5,value:"考试&问卷"},{key:6,value:"展商列表"},{key:7,value:"录播视频列表"}]}}]);
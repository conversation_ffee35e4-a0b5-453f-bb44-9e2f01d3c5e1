(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0af72d7d"],{1216:function(t,e,i){},"2e08":function(t,e,i){var a=i("9def"),s=i("9744"),n=i("be13");t.exports=function(t,e,i,o){var r=String(n(t)),c=r.length,l=void 0===i?" ":String(i),d=a(e);if(d<=c||""==l)return r;var v=d-c,m=s.call(l,Math.ceil(v/l.length));return m.length>v&&(m=m.slice(0,v)),o?m+r:r+m}},"598d":function(t,e,i){"use strict";i.r(e);i("7f7f");var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"finished-video"},[e("div",{staticClass:"page-header-bg"}),e("van-nav-bar",{staticClass:"custom-nav-bar",attrs:{title:"成品视频","left-text":"返回","left-arrow":""},on:{"click-left":function(e){return t.$router.go(-1)}}}),e("div",{staticClass:"function-section"},[e("div",{staticClass:"function-bar"},[e("div",{staticClass:"function-item ai-generate",on:{click:function(e){t.showGenerateDialog=!0}}},[e("div",{staticClass:"function-icon"},[e("van-icon",{attrs:{name:"video-o"}})],1),t._m(0),e("van-icon",{staticClass:"function-arrow",attrs:{name:"arrow"}})],1),e("div",{staticClass:"search-compact"},[e("van-search",{staticClass:"compact-search",attrs:{placeholder:"搜索视频...",shape:"round"},on:{search:t.onSearch,clear:t.onSearch},model:{value:t.searchKeyword,callback:function(e){t.searchKeyword=e},expression:"searchKeyword"}})],1)])]),e("div",{staticClass:"video-list"},[t.videoList.length>0?e("div",{staticClass:"list-header"},[e("span",{staticClass:"list-count"},[t._v("共 "+t._s(t.videoList.length)+" 个视频")]),e("van-button",{attrs:{size:"mini",icon:"refresh"},on:{click:t.onSearch}},[t._v("刷新")])],1):t._e(),e("van-list",{staticClass:"custom-list",attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:t.onLoad},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},t._l(t.videoList,(function(i){return e("div",{key:i.id,staticClass:"video-item"},[e("div",{staticClass:"video-preview",on:{click:function(e){return t.previewVideo(i)}}},[e("div",{staticClass:"video-container"},["image"===i.mediaType?e("div",{staticClass:"image-container"},[i.mediaUrl?e("img",{staticClass:"image-element",attrs:{src:i.mediaUrl,alt:"成品图片"}}):e("div",{staticClass:"no-image"},[e("van-icon",{attrs:{name:"photo-o",size:"40"}}),e("p",[t._v("图片处理中...")])],1),e("div",{staticClass:"image-overlay"},[i.imageCount>1?e("div",{staticClass:"image-count"},[t._v("\n                  "+t._s(i.imageCount)+"张\n                ")]):t._e()])]):e("div",{staticClass:"video-container"},[i.mediaUrl?e("video",{staticClass:"video-element",attrs:{src:i.mediaUrl,preload:"metadata",poster:i.coverUrl}}):e("div",{staticClass:"no-video"},[e("div",{staticClass:"processing-animation"},[e("van-loading",{attrs:{size:"24px",color:"#1989fa"}})],1),e("p",{staticClass:"processing-text"},[t._v("视频处理中...")])]),e("div",{staticClass:"play-overlay"},[e("div",{staticClass:"play-button"},[e("van-icon",{attrs:{name:"play",size:"24"}})],1)]),i.duration?e("div",{staticClass:"video-duration"},[t._v("\n                "+t._s(t.formatDuration(i.duration))+"\n              ")]):t._e()])])]),e("div",{staticClass:"video-info"},[e("div",{staticClass:"video-header"},[e("h3",{staticClass:"video-title"},[t._v(t._s(i.name))]),e("div",{staticClass:"video-badges"},[e("van-tag",{staticClass:"video-tag",attrs:{type:"video"===i.mediaType?"success":"primary",size:"small"}},[t._v("\n                "+t._s("video"===i.mediaType?"成品视频":"成品图片")+"\n              ")]),i.platform?e("van-tag",{staticClass:"platform-tag",attrs:{color:"#667eea",size:"small"}},[t._v("\n                "+t._s(t.getPlatformName(i.platform))+"\n              ")]):t._e(),i.useCount>0?e("van-tag",{attrs:{color:"#f39c12",size:"small"}},[t._v("\n                热门\n              ")]):t._e()],1)]),e("div",{staticClass:"video-meta"},[e("div",{staticClass:"meta-item"},[e("van-icon",{attrs:{name:"description"}}),e("span",[t._v(t._s(t.formatFileSize(i.fileSize)))])],1),e("div",{staticClass:"meta-item"},[e("van-icon",{attrs:{name:"clock-o"}}),e("span",[t._v(t._s(t.formatDuration(i.duration)))])],1),e("div",{staticClass:"meta-item"},[e("van-icon",{attrs:{name:"eye-o"}}),e("span",[t._v(t._s(i.useCount||0)+" 次使用")])],1)]),e("div",{staticClass:"video-actions"},[e("van-button",{attrs:{size:"small",type:"primary",disabled:!i.mediaUrl,icon:"play-circle-o"},on:{click:function(e){return t.previewVideo(i)}}},[t._v("\n              预览\n            ")]),e("van-button",{attrs:{size:"small",type:"danger",icon:"delete-o"},on:{click:function(e){return t.deleteVideo(i)}}},[t._v("\n              删除\n            ")])],1)])])})),0),t.loading||0!==t.videoList.length?t._e():e("div",{staticClass:"empty-state"},[e("div",{staticClass:"empty-content"},[e("van-icon",{staticClass:"empty-icon",attrs:{name:"video-o",size:"80"}}),e("h3",{staticClass:"empty-title"},[t._v("暂无成品视频")]),e("p",{staticClass:"empty-desc"},[t._v("开始创建您的第一个成品视频吧")]),e("div",{staticClass:"empty-actions"},[e("van-button",{attrs:{type:"primary",icon:"video-o"},on:{click:function(e){t.showGenerateDialog=!0}}},[t._v("\n            AI生成视频\n          ")]),e("van-button",{attrs:{icon:"plus"},on:{click:function(e){t.showUploadDialog=!0}}},[t._v("\n            上传视频\n          ")])],1)],1)])],1),e("van-dialog",{staticClass:"custom-dialog generate-dialog",attrs:{title:"","show-cancel-button":"","confirm-button-text":"立即生成",width:"90%"},on:{confirm:t.handleGenerate},model:{value:t.showGenerateDialog,callback:function(e){t.showGenerateDialog=e},expression:"showGenerateDialog"}},[e("div",{staticClass:"generate-form"},[e("div",{staticClass:"dialog-header"},[e("div",{staticClass:"header-icon"},[e("van-icon",{attrs:{name:"video-o",size:"32"}})],1),e("h3",{staticClass:"dialog-title"},[t._v("AI智能生成")]),e("p",{staticClass:"dialog-subtitle"},[t._v("为不同平台创建专业的成品内容")])]),e("div",{staticClass:"generate-config"},[e("van-field",{staticClass:"custom-field",attrs:{readonly:"",clickable:"",label:"选择平台",value:t.selectedPlatformName,placeholder:"请选择目标平台",required:"","right-icon":"arrow-down"},on:{click:function(e){t.showPlatformPicker=!0}}}),e("van-field",{staticClass:"custom-field",attrs:{readonly:"",clickable:"",label:"内容类型",value:t.selectedMediaTypeName,placeholder:"请选择内容类型",required:"","right-icon":"arrow-down"},on:{click:function(e){t.showMediaTypePicker=!0}}}),"image"===t.generateForm.mediaType?e("van-field",{staticClass:"custom-field",attrs:{label:"图片数量",type:"number",placeholder:"请输入图片数量"},model:{value:t.generateForm.imageCount,callback:function(e){t.$set(t.generateForm,"imageCount",e)},expression:"generateForm.imageCount"}}):t._e()],1),e("div",{staticClass:"generate-features"},["video"===t.generateForm.mediaType?e("div",{staticClass:"feature-item"},[e("van-icon",{staticClass:"feature-icon",attrs:{name:"video-o"}}),e("div",{staticClass:"feature-content"},[e("span",{staticClass:"feature-title"},[t._v("智能剪辑")]),e("span",{staticClass:"feature-desc"},[t._v("自动识别精彩片段")])])],1):t._e(),"video"===t.generateForm.mediaType?e("div",{staticClass:"feature-item"},[e("van-icon",{staticClass:"feature-icon",attrs:{name:"music-o"}}),e("div",{staticClass:"feature-content"},[e("span",{staticClass:"feature-title"},[t._v("配乐配音")]),e("span",{staticClass:"feature-desc"},[t._v("智能匹配背景音乐")])])],1):t._e(),"image"===t.generateForm.mediaType?e("div",{staticClass:"feature-item"},[e("van-icon",{staticClass:"feature-icon",attrs:{name:"photo-o"}}),e("div",{staticClass:"feature-content"},[e("span",{staticClass:"feature-title"},[t._v("智能选图")]),e("span",{staticClass:"feature-desc"},[t._v("从素材库精选图片")])])],1):t._e(),e("div",{staticClass:"feature-item"},[e("van-icon",{staticClass:"feature-icon",attrs:{name:"star-o"}}),e("div",{staticClass:"feature-content"},[e("span",{staticClass:"feature-title"},[t._v("文案匹配")]),e("span",{staticClass:"feature-desc"},[t._v("关联对应平台文案")])])],1)]),e("div",{staticClass:"generate-tips"},[e("div",{staticClass:"tip-header"},[e("van-icon",{attrs:{name:"info-o"}}),e("span",[t._v("温馨提示")])],1),e("ul",{staticClass:"tip-list"},["video"===t.generateForm.mediaType?e("li",[t._v("AI将基于您的活动素材自动生成成品视频")]):t._e(),"image"===t.generateForm.mediaType?e("li",[t._v("AI将从图片素材中精选内容生成成品图片")]):t._e(),e("li",[t._v("生成过程需要3-5分钟，请耐心等待")]),e("li",[t._v("生成完成后会在列表中显示")])])])])]),e("van-popup",{attrs:{position:"bottom"},model:{value:t.showPlatformPicker,callback:function(e){t.showPlatformPicker=e},expression:"showPlatformPicker"}},[e("van-picker",{attrs:{columns:t.platformColumns,"show-toolbar":"",title:"选择平台"},on:{confirm:t.onPlatformConfirm,cancel:function(e){t.showPlatformPicker=!1}}})],1),e("van-popup",{attrs:{position:"bottom"},model:{value:t.showMediaTypePicker,callback:function(e){t.showMediaTypePicker=e},expression:"showMediaTypePicker"}},[e("van-picker",{attrs:{columns:t.mediaTypeColumns,"show-toolbar":"",title:"选择内容类型"},on:{confirm:t.onMediaTypeConfirm,cancel:function(e){t.showMediaTypePicker=!1}}})],1),e("van-dialog",{staticClass:"custom-dialog upload-dialog",attrs:{title:"","show-cancel-button":"","confirm-button-loading":t.uploading,"confirm-button-text":"开始上传",width:"90%"},on:{confirm:t.confirmUpload},model:{value:t.showUploadDialog,callback:function(e){t.showUploadDialog=e},expression:"showUploadDialog"}},[e("div",{staticClass:"upload-form"},[e("div",{staticClass:"dialog-header"},[e("div",{staticClass:"header-icon upload-icon"},[e("van-icon",{attrs:{name:"plus",size:"32"}})],1),e("h3",{staticClass:"dialog-title"},[t._v("上传成品视频")]),e("p",{staticClass:"dialog-subtitle"},[t._v("导入您的本地视频文件")])]),e("van-field",{staticClass:"custom-field",attrs:{label:"批次名称",placeholder:"请输入批次名称（可选）"},model:{value:t.uploadForm.name,callback:function(e){t.$set(t.uploadForm,"name",e)},expression:"uploadForm.name"}}),e("div",{staticClass:"upload-section"},[e("div",{staticClass:"upload-header"},[e("span",{staticClass:"upload-title"},[t._v("选择视频文件")]),e("span",{staticClass:"upload-count"},[t._v(t._s(t.fileList.length)+"/3")])]),e("van-uploader",{staticClass:"custom-uploader",attrs:{"max-count":3,"after-read":t.afterRead,"before-delete":t.beforeDelete,accept:"video/*","max-size":524288e3,multiple:"","preview-size":80,"upload-text":"选择视频"},on:{oversize:t.onOversize},model:{value:t.fileList,callback:function(e){t.fileList=e},expression:"fileList"}})],1),e("div",{staticClass:"upload-tips"},[e("div",{staticClass:"tip-header"},[e("van-icon",{attrs:{name:"info-o"}}),e("span",[t._v("上传须知")])],1),e("div",{staticClass:"tip-grid"},[e("div",{staticClass:"tip-item"},[e("van-icon",{staticClass:"tip-icon",attrs:{name:"video-o"}}),e("div",{staticClass:"tip-content"},[e("span",{staticClass:"tip-title"},[t._v("支持格式")]),e("span",{staticClass:"tip-desc"},[t._v("MP4、AVI、MOV、WMV、FLV")])])],1),e("div",{staticClass:"tip-item"},[e("van-icon",{staticClass:"tip-icon",attrs:{name:"description"}}),e("div",{staticClass:"tip-content"},[e("span",{staticClass:"tip-title"},[t._v("文件大小")]),e("span",{staticClass:"tip-desc"},[t._v("单个文件不超过500MB")])])],1),e("div",{staticClass:"tip-item"},[e("van-icon",{staticClass:"tip-icon",attrs:{name:"apps-o"}}),e("div",{staticClass:"tip-content"},[e("span",{staticClass:"tip-title"},[t._v("数量限制")]),e("span",{staticClass:"tip-desc"},[t._v("最多可选择3个文件")])])],1),e("div",{staticClass:"tip-item"},[e("van-icon",{staticClass:"tip-icon",attrs:{name:"star-o"}}),e("div",{staticClass:"tip-content"},[e("span",{staticClass:"tip-title"},[t._v("推荐内容")]),e("span",{staticClass:"tip-desc"},[t._v("完整的宣传视频")])])],1)])]),t.uploadProgress.length>0?e("div",{staticClass:"upload-progress"},[e("div",{staticClass:"progress-header"},[e("van-icon",{attrs:{name:"clock-o"}}),e("span",{staticClass:"progress-title"},[t._v("上传进度")])],1),t._l(t.uploadProgress,(function(i,a){return e("div",{key:a,staticClass:"progress-item"},[e("div",{staticClass:"progress-info"},[e("span",{staticClass:"progress-name"},[t._v(t._s(i.name))]),e("span",{staticClass:"progress-status"},[t._v(t._s(i.status))])]),e("van-progress",{attrs:{percentage:i.progress,color:i.color,"stroke-width":"6"}})],1)}))],2):t._e()],1)]),e("van-dialog",{staticClass:"custom-dialog preview-dialog",attrs:{title:"","show-confirm-button":!1,"show-cancel-button":"","cancel-button-text":"关闭",width:"95%"},model:{value:t.showPreviewDialog,callback:function(e){t.showPreviewDialog=e},expression:"showPreviewDialog"}},[t.currentPreviewItem?e("div",{staticClass:"preview-container"},[e("div",{staticClass:"preview-header"},[e("van-icon",{attrs:{name:"video"===t.currentPreviewItem.mediaType?"play-circle-o":"photo-o",size:"24"}}),e("span",{staticClass:"preview-title"},[t._v("\n          "+t._s("video"===t.currentPreviewItem.mediaType?"视频预览":"图片预览")+"\n        ")]),e("span",{staticClass:"preview-subtitle"},[t._v(t._s(t.currentPreviewItem.name))])],1),"video"===t.currentPreviewItem.mediaType?e("div",{staticClass:"video-wrapper"},[t.currentPreviewItem.mediaUrl?e("video",{staticClass:"preview-video",attrs:{src:t.currentPreviewItem.mediaUrl,controls:"",autoplay:"",controlslist:"nodownload"}}):e("div",{staticClass:"no-preview"},[e("van-icon",{attrs:{name:"video-o",size:"48"}}),e("p",[t._v("视频处理中，暂无预览")])],1)]):e("div",{staticClass:"image-wrapper"},[t.previewImages.length>0?e("van-swipe",{staticClass:"preview-swipe",attrs:{autoplay:0,"show-indicators":t.previewImages.length>1,"indicator-color":"white"},on:{change:t.onSwipeChange}},t._l(t.previewImages,(function(i,a){return e("van-swipe-item",{key:a,staticClass:"swipe-item"},[e("img",{staticClass:"preview-image",attrs:{src:i.mediaUrl,alt:"图片 ".concat(a+1)},on:{load:t.onImageLoaded}})])})),1):e("div",{staticClass:"no-preview"},[e("van-icon",{attrs:{name:"photo-o",size:"48"}}),e("p",[t._v("图片处理中，暂无预览")])],1),t.previewImages.length>1?e("div",{staticClass:"image-counter"},[t._v("\n          "+t._s(t.currentImageIndex+1)+" / "+t._s(t.previewImages.length)+"\n        ")]):t._e()],1),e("div",{staticClass:"preview-info"},[e("div",{staticClass:"info-item"},[e("span",{staticClass:"info-label"},[t._v("平台:")]),e("span",{staticClass:"info-value"},[t._v(t._s(t.getPlatformName(t.currentPreviewItem.platform)))])]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"info-label"},[t._v("类型:")]),e("span",{staticClass:"info-value"},[t._v(t._s("video"===t.currentPreviewItem.mediaType?"视频":"图片"))])]),"image"===t.currentPreviewItem.mediaType?e("div",{staticClass:"info-item"},[e("span",{staticClass:"info-label"},[t._v("数量:")]),e("span",{staticClass:"info-value"},[t._v(t._s(t.currentPreviewItem.imageCount||1)+"张")])]):t._e()]),t.currentTextInfo?e("div",{staticClass:"text-info"},[e("div",{staticClass:"text-content"},[t.currentTextInfo.title?e("div",{staticClass:"text-item"},[e("div",{staticClass:"text-label"},[e("span",[t._v("标题")]),e("van-button",{staticClass:"copy-btn",attrs:{size:"mini",type:"primary",plain:""},on:{click:function(e){return t.copyText(t.currentTextInfo.title)}}},[t._v("\n                复制\n              ")])],1),e("div",{staticClass:"text-value"},[t._v(t._s(t.currentTextInfo.title))])]):t._e(),t.currentTextInfo.promptKeyword?e("div",{staticClass:"text-item"},[e("div",{staticClass:"text-label"},[e("span",[t._v("提示词")]),e("van-button",{staticClass:"copy-btn",attrs:{size:"mini",type:"primary",plain:""},on:{click:function(e){return t.copyText(t.currentTextInfo.promptKeyword)}}},[t._v("\n                复制\n              ")])],1),e("div",{staticClass:"text-value"},[t._v(t._s(t.currentTextInfo.promptKeyword))])]):t._e(),t.currentTextInfo.content?e("div",{staticClass:"text-item"},[e("div",{staticClass:"text-label"},[e("span",[t._v("文案内容")]),e("van-button",{staticClass:"copy-btn",attrs:{size:"mini",type:"primary",plain:""},on:{click:function(e){return t.copyText(t.currentTextInfo.content)}}},[t._v("\n                复制\n              ")])],1),e("div",{staticClass:"text-value content-text"},[t._v(t._s(t.currentTextInfo.content))])]):t._e(),t.currentTextInfo.topics?e("div",{staticClass:"text-item"},[e("div",{staticClass:"text-label"},[e("span",[t._v("话题标签")]),e("van-button",{staticClass:"copy-btn",attrs:{size:"mini",type:"primary",plain:""},on:{click:function(e){return t.copyText(t.currentTextInfo.topics)}}},[t._v("\n                复制\n              ")])],1),e("div",{staticClass:"text-value topics-text"},[t._v(t._s(t.currentTextInfo.topics))])]):t._e(),e("div",{staticClass:"copy-all-section"},[e("van-button",{attrs:{type:"info",size:"small",block:"",icon:"notes-o"},on:{click:t.copyAllText}},[t._v("\n              一键复制全部文案\n            ")])],1)])]):t.loadingTextInfo?e("div",{staticClass:"loading-text"},[e("van-loading",{attrs:{size:"16px"}}),e("span",[t._v("加载文案信息中...")])],1):t._e()]):t._e()]),e("div",{staticClass:"safe-area-bottom"})],1)},s=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"function-content"},[e("span",{staticClass:"function-title"},[t._v("AI生成视频")])])}],n=(i("6b54"),i("f576"),i("6762"),i("2fdb"),i("7514"),{name:"FinishedVideo",data:function(){return{activityId:null,fileList:[],videoList:[],loading:!1,finished:!1,page:1,pageSize:10,searchKeyword:"",showGenerateDialog:!1,showUploadDialog:!1,showPreviewDialog:!1,previewVideoUrl:"",uploading:!1,currentPreviewItem:null,previewImages:[],currentImageIndex:0,currentTextInfo:null,loadingTextInfo:!1,generateForm:{name:"",platform:"douyin",mediaType:"video",imageCount:3},platformOptions:[],platformColumns:[],mediaTypeColumns:[],showPlatformPicker:!1,showMediaTypePicker:!1,uploadForm:{name:""},uploadProgress:[]}},computed:{selectedPlatformName:function(){var t=this,e=this.platformOptions.find((function(e){return e.code===t.generateForm.platform}));return e?e.name:""},selectedMediaTypeName:function(){return"video"===this.generateForm.mediaType?"视频":"image"===this.generateForm.mediaType?"图片":""}},mounted:function(){document.title="成品视频";var t=this.$route.query.activityId;if(t)this.activityId=t;else{var e=this.$store.state.activity.selectedActivityId;e&&(this.activityId=e)}if(!this.activityId)return this.$toast.fail("活动ID不能为空，请先选择活动"),void this.$router.push({name:"index"});this.loadPlatformConfigs(),this.loadVideoList()},methods:{loadPlatformConfigs:function(){var t=this;this.$fly.get("/pyp/web/activity/video/platforms").then((function(e){200===e.code&&(t.platformOptions=e.platforms||[],t.platformColumns=t.platformOptions.map((function(t){return{text:t.name,value:t.code}})))})).catch((function(t){console.error("加载平台配置失败:",t)}))},onPlatformConfirm:function(t){this.generateForm.platform=t.value,this.showPlatformPicker=!1,this.loadMediaTypes(t.value)},loadMediaTypes:function(t){var e=this;t&&this.$fly.get("/pyp/web/activity/video/platforms/".concat(t,"/mediaTypes")).then((function(t){if(200===t.code){var i=t.mediaTypes||[];e.mediaTypeColumns=i.map((function(t){return{text:t.name,value:t.code}}));var a=i.map((function(t){return t.code}));a.includes(e.generateForm.mediaType)||(e.generateForm.mediaType=a[0]||"video")}})).catch((function(t){console.error("加载媒体类型失败:",t)}))},onMediaTypeConfirm:function(t){var e=this;if(this.generateForm.mediaType=t.value,this.showMediaTypePicker=!1,"image"===t.value){var i=this.platformOptions.find((function(t){return t.code===e.generateForm.platform}));i&&i.defaultImageCount&&(this.generateForm.imageCount=i.defaultImageCount)}},onSearch:function(){this.page=1,this.videoList=[],this.finished=!1,this.loadVideoList()},onLoad:function(){this.loadVideoList()},loadVideoList:function(){var t=this;this.loading=!0;var e={page:this.page,limit:this.pageSize,activityId:this.activityId,type:1};this.searchKeyword&&(e.name=this.searchKeyword),this.$fly.get("/pyp/web/activity/activityvideo/list",e).then((function(e){if(t.loading=!1,200===e.code){var i=e.page.list||[];1===t.page?t.videoList=i:t.videoList=t.videoList.concat(i),t.page++,t.finished=t.videoList.length>=e.page.totalCount}else t.$toast.fail(e.msg||"获取视频列表失败"),t.finished=!0})).catch((function(){t.loading=!1,t.$toast.fail("获取视频列表失败"),t.finished=!0}))},handleGenerate:function(){var t=this;if(this.generateForm.platform)if(this.generateForm.mediaType){var e=this.$toast.loading({message:"正在生成中...",forbidClick:!0,duration:0}),i={activityId:this.activityId,platform:this.generateForm.platform,mediaType:this.generateForm.mediaType,name:this.generateForm.name};"image"===this.generateForm.mediaType&&(i.imageCount=this.generateForm.imageCount);var a="video"===this.generateForm.mediaType?"/pyp/web/activity/activityvideo/submitVideoEdit":"/pyp/web/activity/video/generateImages",s="video"===this.generateForm.mediaType?"get":"post";this.$fly[s](a,i).then((function(i){if(e.clear(),200===i.code){var a="video"===t.generateForm.mediaType?"视频":"图片";t.$toast.success({message:"� 成品".concat(a,"生成任务已提交！\n后台正在处理中，请稍后在列表中查看结果"),duration:3e3}),t.showGenerateDialog=!1,t.resetGenerateForm(),t.onSearch()}else t.$toast.fail(i.msg||"生成失败")})).catch((function(i){e.clear(),console.error("生成失败:",i),t.$toast.fail("生成失败")}))}else this.$toast.fail("请选择内容类型");else this.$toast.fail("请选择平台")},resetGenerateForm:function(){this.generateForm={name:"",platform:"",mediaType:"video",imageCount:3}},getPlatformName:function(t){var e=this.platformOptions.find((function(e){return e.code===t}));return e?e.name:t},afterRead:function(t){console.log("上传文件:",t)},onOversize:function(){this.$toast.fail("文件大小不能超过100MB")},confirmUpload:function(){var t=this;this.uploadForm.name?0!==this.fileList.length?(this.$toast.loading("上传中..."),setTimeout((function(){t.$toast.success("上传成功"),t.showUploadDialog=!1,t.uploadForm={name:""},t.fileList=[],t.onSearch()}),2e3)):this.$toast.fail("请选择视频文件"):this.$toast.fail("请输入视频名称")},previewVideo:function(t){if(this.currentPreviewItem=t,this.currentTextInfo=null,this.loadingTextInfo=!1,"video"===t.mediaType){if(!t.mediaUrl)return void this.$toast.fail("视频还在处理中，请稍后再试");this.previewVideoUrl=t.mediaUrl}else this.loadPreviewImages(t.id);this.loadTextInfo(t.activityTextId),this.showPreviewDialog=!0},loadPreviewImages:function(t){var e=this;this.$fly.get("/pyp/web/activity/video/images",{videoId:t}).then((function(t){200===t.code?(e.previewImages=t.images||[],e.currentImageIndex=0,0===e.previewImages.length&&e.currentPreviewItem.mediaUrl&&(e.previewImages=[{mediaUrl:e.currentPreviewItem.mediaUrl}])):(e.$toast.fail("加载图片失败"),e.previewImages=[])})).catch((function(t){console.error("加载预览图片失败:",t),e.currentPreviewItem.mediaUrl?e.previewImages=[{mediaUrl:e.currentPreviewItem.mediaUrl}]:e.previewImages=[]}))},onSwipeChange:function(t){this.currentImageIndex=t},onImageLoaded:function(){},downloadContent:function(){if("video"===this.currentPreviewItem.mediaType)this.currentPreviewItem.mediaUrl?window.open(this.currentPreviewItem.mediaUrl,"_blank"):this.$toast.fail("视频还在处理中，无法下载");else if(this.previewImages.length>0){var t=this.previewImages[this.currentImageIndex];t&&t.mediaUrl?window.open(t.mediaUrl,"_blank"):this.$toast.fail("图片无法下载")}},shareContent:function(){this.$toast("分享功能开发中")},deleteContent:function(){var t=this;this.$dialog.confirm({title:"确认删除",message:"确定要删除这个".concat("video"===this.currentPreviewItem.mediaType?"视频":"图片","吗？")}).then((function(){t.deleteVideo(t.currentPreviewItem),t.showPreviewDialog=!1})).catch((function(){}))},loadTextInfo:function(t){var e=this;t?(this.loadingTextInfo=!0,this.$fly.get("/pyp/web/activity/text/info",{textId:t}).then((function(t){e.loadingTextInfo=!1,200===t.code?e.currentTextInfo=t.textInfo||null:(e.currentTextInfo=null,console.error("加载文案信息失败:",t.msg))})).catch((function(t){e.loadingTextInfo=!1,e.currentTextInfo=null,console.error("加载文案信息失败:",t)}))):this.currentTextInfo=null},copyText:function(t){var e=this;t?navigator.clipboard&&window.isSecureContext?navigator.clipboard.writeText(t).then((function(){e.$toast.success("复制成功")})).catch((function(){e.fallbackCopyText(t)})):this.fallbackCopyText(t):this.$toast.fail("没有可复制的内容")},fallbackCopyText:function(t){try{var e=document.createElement("textarea");e.value=t,e.style.position="fixed",e.style.left="-999999px",e.style.top="-999999px",document.body.appendChild(e),e.focus(),e.select();var i=document.execCommand("copy");document.body.removeChild(e),i?this.$toast.success("复制成功"):this.$toast.fail("复制失败，请手动复制")}catch(a){this.$toast.fail("复制失败，请手动复制")}},copyAllText:function(){if(this.currentTextInfo){var t="";this.currentTextInfo.title&&(t+="标题：".concat(this.currentTextInfo.title,"\n\n")),this.currentTextInfo.promptKeyword&&(t+="提示词：".concat(this.currentTextInfo.promptKeyword,"\n\n")),this.currentTextInfo.content&&(t+="文案内容：".concat(this.currentTextInfo.content,"\n\n")),this.currentTextInfo.topics&&(t+="话题标签：".concat(this.currentTextInfo.topics,"\n\n")),t.trim()?this.copyText(t.trim()):this.$toast.fail("没有可复制的文案内容")}else this.$toast.fail("没有可复制的文案")},editVideo:function(t){this.$toast("编辑视频功能待开发"),console.log("编辑视频:",t)},deleteVideo:function(t){var e=this;this.$dialog.confirm({title:"确认删除",message:"确定要删除这个成品视频吗？"}).then((function(){e.$fly.post("/pyp/web/activity/activityvideo/delete",[t.id]).then((function(t){200===t.code?(e.$toast.success("删除成功"),e.onSearch()):e.$toast.fail(t.msg||"删除失败")})).catch((function(){e.$toast.fail("删除失败")}))})).catch((function(){}))},formatFileSize:function(t){if(!t)return"0 B";var e=["B","KB","MB","GB"],i=0;while(t>=1024&&i<e.length-1)t/=1024,i++;return"".concat(t.toFixed(1)," ").concat(e[i])},formatDuration:function(t){if(!t)return"00:00";var e=Math.floor(t/60),i=Math.floor(t%60);return"".concat(e.toString().padStart(2,"0"),":").concat(i.toString().padStart(2,"0"))}}}),o=n,r=(i("acac"),i("2877")),c=Object(r["a"])(o,a,s,!1,null,"b90f66da",null);e["default"]=c.exports},9744:function(t,e,i){"use strict";var a=i("4588"),s=i("be13");t.exports=function(t){var e=String(s(this)),i="",n=a(t);if(n<0||n==1/0)throw RangeError("Count can't be negative");for(;n>0;(n>>>=1)&&(e+=e))1&n&&(i+=e);return i}},acac:function(t,e,i){"use strict";i("1216")},f576:function(t,e,i){"use strict";var a=i("5ca1"),s=i("2e08"),n=i("a25f"),o=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(n);a(a.P+a.F*o,"String",{padStart:function(t){return s(this,t,arguments.length>1?arguments[1]:void 0,!0)}})}}]);
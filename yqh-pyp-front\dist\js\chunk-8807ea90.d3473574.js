(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8807ea90"],{2909:function(t,s,e){"use strict";function a(t,s){(null==s||s>t.length)&&(s=t.length);for(var e=0,a=new Array(s);e<s;e++)a[e]=t[e];return a}function i(t){if(Array.isArray(t))return a(t)}function r(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function n(t,s){if(t){if("string"===typeof t)return a(t,s);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?a(t,s):void 0}}function o(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(t){return i(t)||r(t)||n(t)||o()}e.d(s,"a",(function(){return c}))},"8bff":function(t,s,e){"use strict";e("fc13")},fb18:function(t,s,e){"use strict";e.r(s);var a=function(){var t=this,s=t._self._c;return s("div",{staticClass:"my-customers-page"},[s("van-nav-bar",{staticClass:"custom-nav-bar",attrs:{title:"我的客户","left-text":"返回","left-arrow":""},on:{"click-left":function(s){return t.$router.go(-1)}}}),s("div",{staticClass:"search-section"},[s("van-search",{attrs:{placeholder:"搜索客户昵称或手机号",shape:"round"},on:{search:t.onSearch,clear:t.onClear},model:{value:t.searchKeyword,callback:function(s){t.searchKeyword=s},expression:"searchKeyword"}})],1),s("div",{staticClass:"filter-section"},[s("van-dropdown-menu",{attrs:{"active-color":"#1989fa"}},[s("van-dropdown-item",{attrs:{options:t.statusOptions},on:{change:t.onFilterChange},model:{value:t.filterStatus,callback:function(s){t.filterStatus=s},expression:"filterStatus"}}),s("van-dropdown-item",{attrs:{options:t.typeOptions},on:{change:t.onFilterChange},model:{value:t.filterType,callback:function(s){t.filterType=s},expression:"filterType"}}),s("van-dropdown-item",{attrs:{options:t.sortOptions},on:{change:t.onFilterChange},model:{value:t.sortType,callback:function(s){t.sortType=s},expression:"sortType"}})],1)],1),s("div",{staticClass:"stats-section"},[s("div",{staticClass:"stats-card"},[s("div",{staticClass:"stat-item"},[s("div",{staticClass:"stat-icon customers"},[s("van-icon",{attrs:{name:"friends-o",size:"20"}})],1),s("div",{staticClass:"stat-content"},[s("div",{staticClass:"stat-value"},[t._v(t._s(t.customerStats.totalCustomers||0))]),s("div",{staticClass:"stat-label"},[t._v("总客户数")])])]),s("div",{staticClass:"stat-item"},[s("div",{staticClass:"stat-icon active"},[s("van-icon",{attrs:{name:"fire-o",size:"20"}})],1),s("div",{staticClass:"stat-content"},[s("div",{staticClass:"stat-value"},[t._v(t._s(t.customerStats.activeCustomers||0))]),s("div",{staticClass:"stat-label"},[t._v("活跃客户")])])]),s("div",{staticClass:"stat-item"},[s("div",{staticClass:"stat-icon new"},[s("van-icon",{attrs:{name:"add-o",size:"20"}})],1),s("div",{staticClass:"stat-content"},[s("div",{staticClass:"stat-value"},[t._v(t._s(t.customerStats.newCustomers||0))]),s("div",{staticClass:"stat-label"},[t._v("本月新增")])])]),s("div",{staticClass:"stat-item"},[s("div",{staticClass:"stat-icon amount"},[s("van-icon",{attrs:{name:"gold-coin-o",size:"20"}})],1),s("div",{staticClass:"stat-content"},[s("div",{staticClass:"stat-value"},[t._v("¥"+t._s(t.customerStats.totalAmount||0))]),s("div",{staticClass:"stat-label"},[t._v("总消费额")])])])])]),s("div",{staticClass:"customer-list"},[s("van-list",{attrs:{finished:t.finished,"finished-text":"没有更多了","loading-text":"加载中...","error-text":"请求失败，点击重新加载"},on:{load:t.onLoad},model:{value:t.loading,callback:function(s){t.loading=s},expression:"loading"}},t._l(t.customerList,(function(e){return s("div",{key:e.id,staticClass:"customer-item",on:{click:function(s){return t.viewCustomerDetail(e)}}},[s("div",{staticClass:"customer-avatar"},[e.avatar?s("van-image",{attrs:{src:e.avatar,round:"",width:"50",height:"50",fit:"cover","show-loading":!1}}):s("div",{staticClass:"default-avatar"},[t._v("\n            "+t._s(e.nickname?e.nickname.charAt(0):"客")+"\n          ")]),s("div",{staticClass:"status-indicator",class:t.getStatusClass(e.status)})],1),s("div",{staticClass:"customer-info"},[s("div",{staticClass:"customer-basic"},[s("div",{staticClass:"customer-header"},[s("div",{staticClass:"customer-name"},[t._v(t._s(e.nickname||"未知客户"))]),s("div",{staticClass:"customer-status"},[s("van-tag",{attrs:{type:t.getStatusTagType(e.status),size:"small",round:""}},[t._v("\n                  "+t._s(t.getStatusText(e.status))+"\n                ")])],1)]),s("div",{staticClass:"customer-details"},[s("div",{staticClass:"detail-item"},[s("van-icon",{attrs:{name:"phone-o",size:"14",color:"#969799"}}),s("span",[t._v(t._s(e.mobile||"未绑定手机"))])],1),s("div",{staticClass:"detail-item"},[s("van-icon",{attrs:{name:"clock-o",size:"14",color:"#969799"}}),s("span",[t._v(t._s(t.formatTime(e.bindingTime)))])],1)])]),s("div",{staticClass:"customer-stats-section"},[s("div",{staticClass:"customer-stats"},[s("div",{staticClass:"stat-item"},[s("van-icon",{attrs:{name:"orders-o",size:"14",color:"#1989fa"}}),s("span",{staticClass:"stat-text"},[s("span",{staticClass:"stat-label"},[t._v("订单")]),s("span",{staticClass:"stat-value"},[t._v(t._s(e.orderCount||0))])])],1),s("div",{staticClass:"stat-item"},[s("van-icon",{attrs:{name:"gold-coin-o",size:"14",color:"#ff976a"}}),s("span",{staticClass:"stat-text"},[s("span",{staticClass:"stat-label"},[t._v("消费")]),s("span",{staticClass:"stat-value amount"},[t._v("¥"+t._s(e.totalAmount||0))])])],1),s("div",{staticClass:"stat-item"},[s("van-icon",{attrs:{name:"gem-o",size:"14",color:"#07c160"}}),s("span",{staticClass:"stat-text"},[s("span",{staticClass:"stat-label"},[t._v("佣金")]),s("span",{staticClass:"stat-value commission"},[t._v("¥"+t._s(e.commission||0))])])],1)])])]),s("div",{staticClass:"customer-actions"},[s("van-icon",{attrs:{name:"arrow",size:"16",color:"#c8c9cc"}})],1)])})),0),0!==t.customerList.length||t.loading?t._e():s("van-empty",{attrs:{description:"暂无客户数据",image:"https://img.yzcdn.cn/vant/custom-empty-image.png"}})],1),s("van-popup",{style:{height:"85%"},attrs:{position:"bottom",round:"",closeable:"","close-icon-position":"top-right"},model:{value:t.detailVisible,callback:function(s){t.detailVisible=s},expression:"detailVisible"}},[t.selectedCustomer?s("div",{staticClass:"detail-popup"},[s("div",{staticClass:"popup-header"},[s("div",{staticClass:"customer-profile"},[s("div",{staticClass:"profile-avatar"},[t.selectedCustomer.avatar?s("van-image",{attrs:{src:t.selectedCustomer.avatar,round:"",width:"60",height:"60",fit:"cover"}}):s("div",{staticClass:"default-avatar large"},[t._v("\n              "+t._s(t.selectedCustomer.nickname?t.selectedCustomer.nickname.charAt(0):"客")+"\n            ")]),s("div",{staticClass:"status-indicator",class:t.getStatusClass(t.selectedCustomer.status)})],1),s("div",{staticClass:"profile-info"},[s("div",{staticClass:"profile-name"},[t._v(t._s(t.selectedCustomer.nickname||"未知客户"))]),s("div",{staticClass:"profile-mobile"},[t._v(t._s(t.selectedCustomer.mobile||"未绑定手机"))]),s("van-tag",{attrs:{type:t.getStatusTagType(t.selectedCustomer.status),size:"small",round:""}},[t._v("\n              "+t._s(t.getStatusText(t.selectedCustomer.status))+"\n            ")])],1)])]),s("div",{staticClass:"detail-content"},[s("div",{staticClass:"detail-section"},[s("div",{staticClass:"section-title"},[s("van-icon",{attrs:{name:"contact",size:"16",color:"#1989fa"}}),t._v("\n            基本信息\n          ")],1),s("div",{staticClass:"info-grid"},[s("div",{staticClass:"info-item"},[s("div",{staticClass:"info-icon"},[s("van-icon",{attrs:{name:"user-o",size:"16",color:"#1989fa"}})],1),s("div",{staticClass:"info-content"},[s("div",{staticClass:"info-label"},[t._v("昵称")]),s("div",{staticClass:"info-value"},[t._v(t._s(t.selectedCustomer.nickname||"未知"))])])]),s("div",{staticClass:"info-item"},[s("div",{staticClass:"info-icon"},[s("van-icon",{attrs:{name:"phone-o",size:"16",color:"#07c160"}})],1),s("div",{staticClass:"info-content"},[s("div",{staticClass:"info-label"},[t._v("手机号")]),s("div",{staticClass:"info-value"},[t._v(t._s(t.selectedCustomer.mobile||"未绑定"))])])]),s("div",{staticClass:"info-item"},[s("div",{staticClass:"info-icon"},[s("van-icon",{attrs:{name:"clock-o",size:"16",color:"#ff976a"}})],1),s("div",{staticClass:"info-content"},[s("div",{staticClass:"info-label"},[t._v("绑定时间")]),s("div",{staticClass:"info-value"},[t._v(t._s(t.formatTime(t.selectedCustomer.bindingTime)))])])]),s("div",{staticClass:"info-item"},[s("div",{staticClass:"info-icon"},[s("van-icon",{attrs:{name:"link-o",size:"16",color:"#9c26b0"}})],1),s("div",{staticClass:"info-content"},[s("div",{staticClass:"info-label"},[t._v("绑定方式")]),s("div",{staticClass:"info-value"},[t._v(t._s(t.getBindingTypeText(t.selectedCustomer.bindingType)))])])])])]),s("div",{staticClass:"detail-section"},[s("div",{staticClass:"section-title"},[s("van-icon",{attrs:{name:"chart-trending-o",size:"16",color:"#1989fa"}}),t._v("\n            消费统计\n          ")],1),s("div",{staticClass:"stats-grid"},[s("div",{staticClass:"stat-card orders"},[s("div",{staticClass:"stat-icon"},[s("van-icon",{attrs:{name:"orders-o",size:"20"}})],1),s("div",{staticClass:"stat-value"},[t._v(t._s(t.selectedCustomer.orderCount||0))]),s("div",{staticClass:"stat-label"},[t._v("订单数量")])]),s("div",{staticClass:"stat-card amount"},[s("div",{staticClass:"stat-icon"},[s("van-icon",{attrs:{name:"gold-coin-o",size:"20"}})],1),s("div",{staticClass:"stat-value"},[t._v("¥"+t._s(t.selectedCustomer.totalAmount||0))]),s("div",{staticClass:"stat-label"},[t._v("消费总额")])]),s("div",{staticClass:"stat-card commission"},[s("div",{staticClass:"stat-icon"},[s("van-icon",{attrs:{name:"gem-o",size:"20"}})],1),s("div",{staticClass:"stat-value"},[t._v("¥"+t._s(t.selectedCustomer.commission||0))]),s("div",{staticClass:"stat-label"},[t._v("获得佣金")])])])]),s("div",{staticClass:"detail-actions"},[s("van-button",{attrs:{type:"primary",block:"",round:"",loading:t.ordersLoading,icon:"orders-o"},on:{click:t.viewCustomerOrders}},[t._v("\n            查看订单\n          ")]),s("van-button",{staticStyle:{"margin-top":"12px"},attrs:{block:"",round:"",icon:"phone-o"},on:{click:t.contactCustomer}},[t._v("\n            联系客户\n          ")])],1)])]):t._e()]),s("van-popup",{style:{height:"90%"},attrs:{position:"bottom",round:"",closeable:"","close-icon-position":"top-right"},model:{value:t.ordersVisible,callback:function(s){t.ordersVisible=s},expression:"ordersVisible"}},[s("div",{staticClass:"orders-popup"},[s("div",{staticClass:"popup-header"},[s("div",{staticClass:"popup-title"},[s("van-icon",{attrs:{name:"orders-o",size:"18",color:"#1989fa"}}),t._v("\n          客户订单\n        ")],1),s("div",{staticClass:"customer-name"},[t._v(t._s(t.selectedCustomer?t.selectedCustomer.nickname:""))])]),s("div",{staticClass:"orders-content"},[s("div",{staticClass:"orders-filter"},[s("van-tabs",{attrs:{color:"#1989fa","title-active-color":"#1989fa"},on:{change:t.onOrderFilterChange},model:{value:t.orderStatusFilter,callback:function(s){t.orderStatusFilter=s},expression:"orderStatusFilter"}},[s("van-tab",{attrs:{title:"全部",name:"all"}}),s("van-tab",{attrs:{title:"待支付",name:"pending"}}),s("van-tab",{attrs:{title:"已支付",name:"paid"}}),s("van-tab",{attrs:{title:"已取消",name:"cancelled"}})],1)],1),s("div",{staticClass:"orders-list"},[s("van-list",{attrs:{finished:t.ordersFinished,"finished-text":"没有更多订单了"},on:{load:t.loadCustomerOrders},model:{value:t.ordersLoading,callback:function(s){t.ordersLoading=s},expression:"ordersLoading"}},t._l(t.ordersList,(function(e){return s("div",{key:e.id,staticClass:"order-item"},[s("div",{staticClass:"order-header"},[s("div",{staticClass:"order-info"},[s("div",{staticClass:"order-number"},[t._v("订单号："+t._s(e.orderSn))]),s("div",{staticClass:"order-time"},[t._v(t._s(t.formatTime(e.createTime)))])]),s("div",{staticClass:"order-status"},[s("van-tag",{attrs:{type:t.getOrderStatusTagType(e.status),size:"small",round:""}},[t._v("\n                    "+t._s(t.getOrderStatusText(e.status))+"\n                  ")])],1)]),s("div",{staticClass:"order-content"},[s("div",{staticClass:"order-detail"},[s("div",{staticClass:"detail-row"},[s("span",{staticClass:"label"},[t._v("订单类型：")]),s("span",{staticClass:"value"},[t._v(t._s(t.getOrderTypeText(e.rechargeType)))])]),e.countValue?s("div",{staticClass:"detail-row"},[s("span",{staticClass:"label"},[t._v("充值次数：")]),s("span",{staticClass:"value"},[t._v(t._s(e.countValue)+"次")])]):t._e(),s("div",{staticClass:"detail-row"},[s("span",{staticClass:"label"},[t._v("订单金额：")]),s("span",{staticClass:"value amount"},[t._v("¥"+t._s(e.amount||0))])]),e.payAmount&&1===e.status?s("div",{staticClass:"detail-row"},[s("span",{staticClass:"label"},[t._v("实付金额：")]),s("span",{staticClass:"value paid"},[t._v("¥"+t._s(e.payAmount||0))])]):t._e()])])])})),0),0!==t.ordersList.length||t.ordersLoading?t._e():s("van-empty",{attrs:{description:"暂无订单数据",image:"https://img.yzcdn.cn/vant/custom-empty-image.png"}})],1)])])])],1)},i=[],r=e("2909"),n=(e("96cf"),e("1da1")),o={name:"MyCustomers",data:function(){return{activityId:null,searchKeyword:"",filterStatus:0,filterType:0,sortType:0,statusOptions:[{text:"全部状态",value:0},{text:"有效绑定",value:1},{text:"已失效",value:2}],typeOptions:[{text:"全部类型",value:0},{text:"二维码绑定",value:1},{text:"邀请链接",value:2},{text:"邀请码",value:3}],sortOptions:[{text:"绑定时间",value:0},{text:"消费金额",value:1},{text:"订单数量",value:2}],customerList:[],customerStats:{},loading:!1,finished:!1,page:1,detailVisible:!1,selectedCustomer:null,ordersVisible:!1,ordersLoading:!1,ordersFinished:!1,ordersList:[],ordersPage:1,orderStatusFilter:"all"}},created:function(){this.activityId=this.$route.query.activityId,this.loadCustomerStats(),this.onLoad()},methods:{onLoad:function(){var t=Object(n["a"])(regeneratorRuntime.mark((function t(){var s,e,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!this.loading){t.next=2;break}return t.abrupt("return");case 2:return this.loading=!0,t.prev=3,t.next=6,this.$fly.get("/pyp/web/salesman/getCustomerBindings",{page:this.page,limit:10,keyword:this.searchKeyword,status:this.filterStatus,bindingType:this.filterType,sortType:this.sortType});case 6:s=t.sent,200===s.code?(e=(s.result||[]).map((function(t){return{id:t.id,wxUserId:t.wxUserId,nickname:t.wxUserName||"未知客户",mobile:t.wxUserMobile||"未绑定手机",avatar:t.wxUserAvatar||"",status:t.status,bindingTime:t.bindingTime,bindingType:t.bindingType,orderCount:t.orderCount||0,totalAmount:t.totalAmount||"0.00",commission:t.commission||"0.00"}})),1===this.page?this.customerList=e:(a=this.customerList).push.apply(a,Object(r["a"])(e)),this.page++,this.finished=e.length<10):(this.$toast(s.msg||"加载失败"),this.finished=!0),t.next=13;break;case 10:t.prev=10,t.t0=t["catch"](3),console.error("加载客户列表失败:",t.t0);case 13:return t.prev=13,this.loading=!1,t.finish(13);case 16:case"end":return t.stop()}}),t,this,[[3,10,13,16]])})));function s(){return t.apply(this,arguments)}return s}(),onSearch:function(){this.resetList(),this.onLoad()},onClear:function(){this.searchKeyword="",this.resetList(),this.onLoad()},onFilterChange:function(){this.resetList(),this.onLoad()},resetList:function(){this.customerList=[],this.page=1,this.finished=!1},viewCustomerDetail:function(t){this.selectedCustomer=t,this.detailVisible=!0},viewCustomerOrders:function(){this.ordersVisible=!0,this.ordersList=[],this.ordersPage=1,this.ordersFinished=!1,this.loadCustomerOrders()},loadCustomerOrders:function(){var t=Object(n["a"])(regeneratorRuntime.mark((function t(){var s,e,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!this.ordersLoading&&this.selectedCustomer){t.next=2;break}return t.abrupt("return");case 2:return this.ordersLoading=!0,t.prev=3,t.next=6,this.$fly.get("/pyp/web/salesman/getCustomerOrders",{wxUserId:this.selectedCustomer.wxUserId,page:this.ordersPage,limit:10,status:"all"===this.orderStatusFilter?"":this.getOrderStatusValue(this.orderStatusFilter)});case 6:s=t.sent,200===s.code?(e=(s.result||[]).map((function(t){return{id:t.id,orderSn:t.orderSn,rechargeType:t.rechargeType,countValue:t.countValue,amount:t.amount,payAmount:t.payAmount,status:t.status,createTime:t.createTime||t.createOn,payTime:t.payTime}})),1===this.ordersPage?this.ordersList=e:(a=this.ordersList).push.apply(a,Object(r["a"])(e)),this.ordersPage++,this.ordersFinished=e.length<10):(this.$toast(s.msg||"加载订单失败"),this.ordersFinished=!0),t.next=14;break;case 10:t.prev=10,t.t0=t["catch"](3),console.error("加载客户订单失败:",t.t0),1===this.ordersPage&&(this.ordersList=[{id:1,orderSn:"RC20250118001",rechargeType:1,countValue:100,amount:"99.00",payAmount:"99.00",status:1,createTime:"2025-01-18 10:30:00",payTime:"2025-01-18 10:31:00"},{id:2,orderSn:"RC20250117002",rechargeType:2,countValue:50,amount:"49.00",payAmount:"49.00",status:1,createTime:"2025-01-17 15:20:00",payTime:"2025-01-17 15:21:00"}],this.ordersFinished=!0);case 14:return t.prev=14,this.ordersLoading=!1,t.finish(14);case 17:case"end":return t.stop()}}),t,this,[[3,10,14,17]])})));function s(){return t.apply(this,arguments)}return s}(),onOrderFilterChange:function(){this.ordersList=[],this.ordersPage=1,this.ordersFinished=!1,this.loadCustomerOrders()},contactCustomer:function(){this.selectedCustomer.mobile&&"未绑定手机"!==this.selectedCustomer.mobile?window.location.href="tel:".concat(this.selectedCustomer.mobile):this.$toast("客户未绑定手机号")},loadCustomerStats:function(){var t=Object(n["a"])(regeneratorRuntime.mark((function t(){var s,e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$fly.get("/pyp/salesman/wxuserbinding/customerStats");case 3:s=t.sent,200===s.code?(e=s.stats||{},this.customerStats={totalCustomers:e.totalCustomers||0,activeCustomers:e.activeCustomers||0,newCustomers:e.todayBindings||0,totalAmount:e.totalAmount||"0.00"}):console.log("获取客户统计失败:",s.msg),t.next=12;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("获取客户统计失败:",t.t0),this.customerStats={totalCustomers:156,activeCustomers:142,newCustomers:23,totalAmount:"45680.50"},console.log("使用模拟统计数据");case 12:case"end":return t.stop()}}),t,this,[[0,7]])})));function s(){return t.apply(this,arguments)}return s}(),getStatusText:function(t){var s={1:"有效",2:"已失效"};return s[t]||"未知"},getStatusTagType:function(t){var s={1:"success",2:"danger"};return s[t]||"default"},getBindingTypeText:function(t){var s={1:"二维码绑定",2:"邀请链接",3:"邀请码绑定"};return s[t]||"未知"},getStatusClass:function(t){var s={1:"active",2:"inactive"};return s[t]||"inactive"},formatTime:function(t){if(!t)return"未知时间";try{var s=new Date(t),e=new Date,a=e-s,i=Math.floor(a/864e5);return 0===i?"今天":1===i?"昨天":i<7?"".concat(i,"天前"):s.toLocaleDateString()}catch(r){return t}},getOrderStatusText:function(t){var s={0:"待支付",1:"已支付",2:"已取消",3:"已退款"};return s[t]||"未知"},getOrderStatusTagType:function(t){var s={0:"warning",1:"success",2:"danger",3:"default"};return s[t]||"default"},getOrderTypeText:function(t){var s={1:"套餐充值",2:"自定义充值",3:"系统赠送",4:"创建活动套餐"};return s[t]||"未知类型"},getOrderStatusValue:function(t){var s={pending:0,paid:1,cancelled:2};return s[t]}}},c=o,l=(e("8bff"),e("2877")),d=Object(l["a"])(c,a,i,!1,null,"72fe4b10",null);s["default"]=d.exports},fc13:function(t,s,e){}}]);
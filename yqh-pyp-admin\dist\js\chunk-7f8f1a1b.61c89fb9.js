(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7f8f1a1b"],{"17ba":function(t,i,n){"use strict";n("2c68")},"2c68":function(t,i,n){},b0f4:function(t,i,n){"use strict";n.r(i);n("b0c0");var a=function(){var t=this,i=t._self._c;return i("div",{staticStyle:{"text-align":"center"},attrs:{id:"printResult"}},[i("div",{staticStyle:{width:"100%"}},[i("div",{staticStyle:{"font-size":"50px","margin-top":"50px","font-weight":"800"}},[t._v(t._s(t.activityInfo.name))]),i("vue-qrcode",{attrs:{options:{width:800},value:t.wxAccount.baseUrl+"apply/sign?id="+t.activityId}}),i("div",{staticStyle:{"font-size":"40px","font-weight":"800"}},[t._v("扫描此二维码，进行签到")])],1)])},c=[],o=n("b2e5"),e=n.n(o),s={data:function(){return{wxAccount:{},activityInfo:{},appid:"",activityId:"",baseUrl:"",visible:!1}},components:{VueQrcode:e.a},created:function(){this.appid=this.$cookie.get("appid"),this.activityId=this.$route.query.activityId,this.getActivity(),this.getAccountInfo()},methods:{getActivity:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activity/info/".concat(this.activityId)),method:"get",params:this.$http.adornParams()}).then((function(i){var n=i.data;n&&200===n.code&&(t.activityInfo=n.activity)}))},getAccountInfo:function(){var t=this;this.$http({url:this.$http.adornUrl("/manage/wxAccount/info/".concat(this.appid)),method:"get",params:this.$http.adornParams()}).then((function(i){var n=i.data;n&&200===n.code&&(t.wxAccount=n.wxAccount)}))}}},r=s,d=(n("17ba"),n("2877")),u=Object(d["a"])(r,a,c,!1,null,null,null);i["default"]=u.exports}}]);
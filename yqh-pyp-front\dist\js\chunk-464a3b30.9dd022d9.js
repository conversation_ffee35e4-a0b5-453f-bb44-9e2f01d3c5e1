(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-464a3b30"],{"2a8b":function(t,i,e){"use strict";e("fab1")},"5df3":function(t,i,e){"use strict";var a=e("02f4")(!0);e("01f9")(String,"String",(function(t){this._t=String(t),this._i=0}),(function(){var t,i=this._t,e=this._i;return e>=i.length?{value:void 0,done:!0}:(t=a(i,e),this._i+=t.length,{value:t,done:!1})}))},"86b9":function(t,i,e){"use strict";e.r(i);e("7f7f");var a,s=function(){var t=this,i=t._self._c;return i("div",{staticClass:"image-materials"},[i("van-nav-bar",{attrs:{title:"图片素材","left-text":"返回","left-arrow":""},on:{"click-left":function(i){return t.$router.go(-1)}}}),i("div",{staticClass:"function-section"},[i("div",{staticClass:"section-content"},[t._m(0),i("div",{staticClass:"action-buttons"},[i("van-button",{staticClass:"upload-btn",attrs:{type:"primary",size:"large",icon:"plus"},on:{click:function(i){t.showUploadDialog=!0}}},[t._v("\n          上传图片素材\n        ")])],1),i("div",{staticClass:"search-filter-bar"},[i("van-search",{staticClass:"search-input",attrs:{placeholder:"搜索图片名称...",shape:"round"},on:{search:t.onSearch,clear:t.onSearch},model:{value:t.searchKeyword,callback:function(i){t.searchKeyword=i},expression:"searchKeyword"}}),i("van-button",{staticClass:"filter-btn",attrs:{icon:"filter-o"},on:{click:function(i){t.showFilter=!t.showFilter}}})],1)])]),i("div",{staticClass:"image-list"},[t.imageList.length>0?i("div",{staticClass:"stats-bar"},[i("div",{staticClass:"stats-info"},[i("span",{staticClass:"total-count"},[t._v("共 "+t._s(t.imageList.length)+" 张图片")])]),i("div",{staticClass:"list-actions"},[i("van-button",{attrs:{size:"mini",icon:"refresh",plain:""},on:{click:t.onSearch}},[t._v("刷新")])],1)]):t._e(),i("van-list",{staticClass:"custom-list",attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:t.onLoad},model:{value:t.loading,callback:function(i){t.loading=i},expression:"loading"}},[i("div",{staticClass:"image-grid"},t._l(t.imageList,(function(e){return i("div",{key:e.id,staticClass:"image-card"},[i("div",{staticClass:"card-preview",on:{click:function(i){return t.previewImage(e)}}},[i("div",{staticClass:"preview-container"},[i("van-image",{staticClass:"preview-image",attrs:{src:e.mediaUrl||e.url,fit:"cover",width:"100%",height:"200px","loading-icon":"photo","error-icon":"photo-fail"}}),i("div",{staticClass:"image-overlay"},[i("div",{staticClass:"overlay-actions"},[i("van-button",{attrs:{size:"mini",icon:"eye-o",round:""},on:{click:function(i){return i.stopPropagation(),t.previewImage(e)}}},[t._v("预览")]),i("van-button",{attrs:{size:"mini",icon:"more-o",round:""},on:{click:function(i){return i.stopPropagation(),t.showImageActions(e)}}},[t._v("更多")])],1)]),i("div",{staticClass:"image-status"},[i("van-tag",{attrs:{size:"mini",type:"primary"}},[t._v("素材")])],1)],1)]),i("div",{staticClass:"card-content"},[i("div",{staticClass:"card-header"},[i("h4",{staticClass:"card-title"},[t._v(t._s(e.name))]),i("van-icon",{staticClass:"more-icon",attrs:{name:"more-o"},on:{click:function(i){return t.showImageActions(e)}}})],1),i("div",{staticClass:"card-meta"},[i("div",{staticClass:"meta-row"},[i("span",{staticClass:"meta-item"},[i("van-icon",{attrs:{name:"folder-o",size:"12"}}),t._v("\n                  "+t._s(t.formatFileSize(e.fileSize))+"\n                ")],1),i("span",{staticClass:"meta-item"},[i("van-icon",{attrs:{name:"eye-o",size:"12"}}),t._v("\n                  "+t._s(e.useCount||0)+" 次使用\n                ")],1)])]),i("div",{staticClass:"card-actions"},[i("van-button",{attrs:{size:"mini",type:"primary",icon:"eye-o"},on:{click:function(i){return t.previewImage(e)}}},[t._v("\n                预览\n              ")]),i("van-button",{attrs:{size:"mini",type:"danger",icon:"delete-o"},on:{click:function(i){return t.deleteImage(e)}}},[t._v("\n                删除\n              ")])],1)])])})),0)]),t.loading||0!==t.imageList.length?t._e():i("div",{staticClass:"empty-state"},[i("div",{staticClass:"empty-content"},[i("van-icon",{staticClass:"empty-icon",attrs:{name:"photo-o",size:"80"}}),i("h3",{staticClass:"empty-title"},[t._v("暂无图片素材")]),i("p",{staticClass:"empty-desc"},[t._v("上传您的第一张图片素材，开始创作之旅")]),i("div",{staticClass:"empty-actions"},[i("van-button",{attrs:{type:"primary",icon:"plus"},on:{click:function(i){t.showUploadDialog=!0}}},[t._v("\n            上传图片素材\n          ")])],1)],1)])],1),i("van-dialog",{staticClass:"custom-dialog upload-dialog",attrs:{title:"","show-cancel-button":"","confirm-button-loading":t.uploading,"confirm-button-text":"开始上传",width:"90%"},on:{confirm:t.confirmUpload},model:{value:t.showUploadDialog,callback:function(i){t.showUploadDialog=i},expression:"showUploadDialog"}},[i("div",{staticClass:"upload-form"},[i("div",{staticClass:"dialog-header"},[i("div",{staticClass:"header-icon upload-icon"},[i("van-icon",{attrs:{name:"plus",size:"32"}})],1),i("h3",{staticClass:"dialog-title"},[t._v("上传图片素材")]),i("p",{staticClass:"dialog-desc"},[t._v("批量上传您的图片素材，支持多种格式")])]),i("van-field",{staticClass:"form-field",attrs:{label:"批次名称",placeholder:"请输入批次名称（可选）"},model:{value:t.uploadForm.name,callback:function(i){t.$set(t.uploadForm,"name",i)},expression:"uploadForm.name"}}),i("div",{staticClass:"upload-section"},[i("div",{staticClass:"upload-header"},[i("span",{staticClass:"upload-title"},[t._v("选择图片文件")]),i("span",{staticClass:"upload-count"},[t._v(t._s(t.fileList.length)+"/20")])]),i("van-uploader",{staticClass:"custom-uploader",attrs:{"max-count":20,"after-read":t.afterRead,"before-delete":t.beforeDelete,accept:"image/*","max-size":10485760,multiple:"","preview-size":80,"upload-text":"选择图片"},on:{oversize:t.onOversize},model:{value:t.fileList,callback:function(i){t.fileList=i},expression:"fileList"}})],1),i("div",{staticClass:"upload-tips"},[i("div",{staticClass:"tips-header"},[i("van-icon",{attrs:{name:"info-o"}}),i("span",[t._v("上传说明")])],1),i("div",{staticClass:"tips-content"},[i("div",{staticClass:"tip-item"},[i("span",{staticClass:"tip-label"},[t._v("支持格式：")]),i("span",{staticClass:"tip-value"},[t._v("JPG、PNG、GIF、WEBP")])]),i("div",{staticClass:"tip-item"},[i("span",{staticClass:"tip-label"},[t._v("文件大小：")]),i("span",{staticClass:"tip-value"},[t._v("单个文件不超过10MB")])]),i("div",{staticClass:"tip-item"},[i("span",{staticClass:"tip-label"},[t._v("数量限制：")]),i("span",{staticClass:"tip-value"},[t._v("最多可选择20个文件")])])])]),t.uploadProgress.length>0?i("div",{staticClass:"upload-progress"},[i("div",{staticClass:"progress-header"},[i("van-icon",{attrs:{name:"clock-o"}}),i("span",[t._v("上传进度")])],1),t._l(t.uploadProgress,(function(e,a){return i("div",{key:a,staticClass:"progress-item"},[i("div",{staticClass:"progress-info"},[i("span",{staticClass:"progress-name"},[t._v(t._s(e.name))]),i("span",{staticClass:"progress-status"},[t._v(t._s(e.status))])]),i("van-progress",{attrs:{percentage:e.progress,color:e.color,"stroke-width":"6"}})],1)}))],2):t._e()],1)]),i("van-image-preview",{attrs:{images:t.previewImages,"start-position":t.previewIndex},model:{value:t.showPreview,callback:function(i){t.showPreview=i},expression:"showPreview"}})],1)},n=[function(){var t=this,i=t._self._c;return i("div",{staticClass:"page-intro"},[i("h2",{staticClass:"page-title"},[t._v("图片素材管理")]),i("p",{staticClass:"page-desc"},[t._v("管理您的图片素材，支持批量上传和在线预览")])])}],o=e("ade3"),r=(e("20d6"),e("f559"),e("5df3"),e("ac6a"),e("96cf"),e("1da1")),c={name:"ImageMaterials",data:function(){return{activityId:null,fileList:[],imageList:[],loading:!1,finished:!1,page:1,pageSize:20,searchKeyword:"",showUploadDialog:!1,showPreview:!1,uploading:!1,previewImages:[],previewIndex:0,showFilter:!1,viewMode:"grid",uploadForm:{name:""},uploadProgress:[]}},mounted:function(){var t=this.$route.query.activityId;if(t)this.activityId=t;else{var i=this.$store.state.activity.selectedActivityId;i&&(this.activityId=i)}if(!this.activityId)return this.$toast.fail("活动ID不能为空，请先选择活动"),void this.$router.push({name:"index"});this.loadImageList()},methods:(a={onSearch:function(){this.page=1,this.imageList=[],this.finished=!1,this.loadImageList()},onLoad:function(){this.loadImageList()},toggleViewMode:function(){this.viewMode="grid"===this.viewMode?"list":"grid"},getTotalSize:function(){var t=this.imageList.reduce((function(t,i){return t+(i.fileSize||0)}),0);return this.formatFileSize(t)},editImage:function(t){this.$toast("编辑功能开发中")},deleteImage:function(t){var i=this;this.$dialog.confirm({title:"确认删除",message:'确定要删除图片"'.concat(t.name,'"吗？')}).then((function(){i.$fly.delete("/pyp/web/activity/activityimage/".concat(t.id)).then((function(t){200===t.code?(i.$toast.success("删除成功"),i.onSearch()):i.$toast.fail(t.message||"删除失败")})).catch((function(t){i.$toast.fail("删除失败")}))})).catch((function(){}))},loadImageList:function(){var t=this;this.loading=!0;var i={page:this.page,limit:this.pageSize,activityId:this.activityId};this.searchKeyword&&(i.name=this.searchKeyword),this.$fly.get("/pyp/web/activity/activityimage/list",i).then((function(i){if(t.loading=!1,200===i.code){var e=i.page.list||[];1===t.page?t.imageList=e:t.imageList=t.imageList.concat(e),t.page++,t.finished=t.imageList.length>=i.page.totalCount}else t.$toast.fail(i.msg||"获取图片列表失败"),t.finished=!0})).catch((function(){t.loading=!1,t.$toast.fail("获取图片列表失败"),t.finished=!0}))},afterRead:function(t){console.log("选择文件:",t)},beforeDelete:function(t,i){var e=this;return new Promise((function(t){e.$dialog.confirm({title:"确认删除",message:"确定要删除这个文件吗？"}).then((function(){t(!0)})).catch((function(){t(!1)}))}))},onOversize:function(){this.$toast.fail("文件大小不能超过10MB")},confirmUpload:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var i,e,a=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(0!==this.fileList.length){t.next=3;break}return this.$toast.fail("请选择图片文件"),t.abrupt("return");case 3:return this.uploading=!0,this.uploadProgress=[],t.prev=5,this.fileList.forEach((function(t,i){a.uploadProgress.push({name:t.file?t.file.name:"图片".concat(i+1),progress:0,status:"准备上传",color:"#1989fa"})})),i=this.fileList.map((function(t,i){return a.uploadSingleFile(t,i)})),t.next=10,Promise.all(i);case 10:return e=t.sent,t.next=13,this.saveUploadedImages(e);case 13:this.$toast.success("成功上传 ".concat(e.length," 个图片")),this.showUploadDialog=!1,this.resetUploadForm(),this.onSearch(),t.next=23;break;case 19:t.prev=19,t.t0=t["catch"](5),console.error("上传失败:",t.t0),this.$toast.fail("上传失败，请重试");case 23:return t.prev=23,this.uploading=!1,t.finish(23);case 26:case"end":return t.stop()}}),t,this,[[5,19,23,26]])})));function i(){return t.apply(this,arguments)}return i}(),uploadSingleFile:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(i,e){var a=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise((function(t,s){var n=i.file,o=new FormData;a.uploadProgress[e].status="上传中",a.uploadProgress[e].progress=10,n.type.startsWith("image/"),o.append("file",n),a.performUpload(o,e,t,s)})));case 1:case"end":return t.stop()}}),t)})));function i(i,e){return t.apply(this,arguments)}return i}(),performUpload:function(t,i,e,a){var s=this;this.$fly.post("/pyp/web/upload",t).then((function(a){if(!a||200!==a.code)throw new Error(a.msg||"上传失败");s.uploadProgress[i].progress=100,s.uploadProgress[i].status="上传成功",s.uploadProgress[i].color="#52c41a",e({url:a.result,name:s.uploadForm.name||"图片".concat(i+1),fileSize:t.get("file").size})})).catch((function(t){s.uploadProgress[i].progress=0,s.uploadProgress[i].status="上传失败",s.uploadProgress[i].color="#ff4444",a(t)}))},saveUploadedImages:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(i){var e,a=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e=i.map((function(t,i){var e={activityId:a.activityId,name:t.name,mediaUrl:t.url,fileSize:t.fileSize,useCount:0};return a.$fly.post("/pyp/web/activity/activityimage/save",e)})),t.next=3,Promise.all(e);case 3:case"end":return t.stop()}}),t)})));function i(i){return t.apply(this,arguments)}return i}(),resetUploadForm:function(){this.uploadForm={name:""},this.fileList=[],this.uploadProgress=[]},previewImage:function(t){this.previewImages=this.imageList.map((function(t){return t.mediaUrl||t.url})),this.previewIndex=this.imageList.findIndex((function(i){return i.id===t.id})),this.showPreview=!0},showImageActions:function(t){var i=this;this.$actionSheet({actions:[{name:"预览图片",callback:function(){return i.previewImage(t)}},{name:"编辑图片",callback:function(){return i.editImage(t)}},{name:"删除图片",color:"#ee0a24",callback:function(){return i.deleteImage(t)}}]})}},Object(o["a"])(a,"editImage",(function(t){this.$toast("编辑图片功能待开发"),console.log("编辑图片:",t)})),Object(o["a"])(a,"deleteImage",(function(t){var i=this;this.$dialog.confirm({title:"确认删除",message:"确定要删除这张图片吗？"}).then((function(){i.$fly.post("/pyp/web/activity/activityimage/delete",[t.id]).then((function(t){200===t.code?(i.$toast.success("删除成功"),i.onSearch()):i.$toast.fail(t.msg||"删除失败")})).catch((function(){i.$toast.fail("删除失败")}))})).catch((function(){}))})),Object(o["a"])(a,"formatFileSize",(function(t){if(!t)return"0 B";var i=["B","KB","MB","GB"],e=0;while(t>=1024&&e<i.length-1)t/=1024,e++;return"".concat(t.toFixed(1)," ").concat(i[e])})),a)},l=c,u=(e("2a8b"),e("2877")),d=Object(u["a"])(l,s,n,!1,null,"c46d7270",null);i["default"]=d.exports},ac6a:function(t,i,e){for(var a=e("cadf"),s=e("0d58"),n=e("2aba"),o=e("7726"),r=e("32e9"),c=e("84f2"),l=e("2b4c"),u=l("iterator"),d=l("toStringTag"),p=c.Array,f={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},v=s(f),m=0;m<v.length;m++){var h,g=v[m],w=f[g],C=o[g],y=C&&C.prototype;if(y&&(y[u]||r(y,u,p),y[d]||r(y,d,g),c[g]=p,w))for(h in a)y[h]||n(y,h,a[h],!0)}},ade3:function(t,i,e){"use strict";e.d(i,"a",(function(){return o}));var a=e("53ca");function s(t,i){if("object"!==Object(a["a"])(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var s=e.call(t,i||"default");if("object"!==Object(a["a"])(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===i?String:Number)(t)}function n(t){var i=s(t,"string");return"symbol"===Object(a["a"])(i)?i:String(i)}function o(t,i,e){return i=n(i),i in t?Object.defineProperty(t,i,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[i]=e,t}},f559:function(t,i,e){"use strict";var a=e("5ca1"),s=e("9def"),n=e("d2c8"),o="startsWith",r=""[o];a(a.P+a.F*e("5147")(o),"String",{startsWith:function(t){var i=n(this,t,o),e=s(Math.min(arguments.length>1?arguments[1]:void 0,i.length)),a=String(t);return r?r.call(i,a,e):i.slice(e,e+a.length)===a}})},fab1:function(t,i,e){}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4ed02f7e","chunk-24a71a58","chunk-2d0b6eab"],{"02d7":function(t,e,a){"use strict";a.d(e,"d",(function(){return i})),a.d(e,"c",(function(){return r})),a.d(e,"b",(function(){return n})),a.d(e,"a",(function(){return o}));var i=[{key:0,value:"考卷"},{key:1,value:"问卷"}],r=[{key:0,value:"统一时间考试"},{key:1,value:"随时考试"}],n=[{key:0,value:"单选"},{key:1,value:"多选"},{key:2,value:"填空"}],o=[{key:0,value:"未提交"},{key:1,value:"已提交"},{key:2,value:"已通过"},{key:3,value:"未通过"},{key:4,value:"已超时"},{key:5,value:"作废"}]},"1ecd":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"答题情况","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.examActivityUserOptionEntities,border:""}},[e("el-table-column",{attrs:{prop:"questionName","show-overflow-tooltip":"","header-align":"center",align:"center",label:"题目名称"}}),e("el-table-column",{attrs:{prop:"optionName","header-align":"center",align:"center",label:"作答结果"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{style:a.row.optionName!=a.row.realOptionName?"color: red":"color: green"},[t._v(" "+t._s(a.row.optionName)+" ")])}}])}),e("el-table-column",{attrs:{prop:"realOptionName","header-align":"center",align:"center",label:"正确答案"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{style:a.row.optionName!=a.row.realOptionName?"color: red":"color: green"},[t._v(" "+t._s(a.row.realOptionName)+" ")])}}])})],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("确定")])],1)],1)},r=[],n={data:function(){return{visible:!1,id:"",examActivityUserOptionEntities:[]}},methods:{init:function(t){var e=this;this.id=t||0,this.visible=!0,this.$http({url:this.$http.adornUrl("/exam/examactivityuseroption/findByExamActivityUserId/".concat(this.id)),method:"get"}).then((function(t){var a=t.data;a&&200===a.code&&(e.examActivityUserOptionEntities=a.result)}))}}},o=n,s=a("2877"),l=Object(s["a"])(o,i,r,!1,null,null,null);e["default"]=l.exports},"4e0f":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"姓名",clearable:""},model:{value:t.dataForm.contact,callback:function(e){t.$set(t.dataForm,"contact",e)},expression:"dataForm.contact"}})],1),e("el-form-item",[e("el-input",{attrs:{placeholder:"联系方式",clearable:""},model:{value:t.dataForm.mobile,callback:function(e){t.$set(t.dataForm,"mobile",e)},expression:"dataForm.mobile"}})],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"答题状态",filterable:""},model:{value:t.dataForm.status,callback:function(e){t.$set(t.dataForm,"status",e)},expression:"dataForm.status"}},[e("el-option",{attrs:{label:"全部",value:""}}),t._l(t.examActivityUserStatus,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})}))],2)],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),t.isAuth("exam:examactivityuser:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("activity:activityuserapplyorder:save")?e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.exportHandle()}}},[t._v("导出")]):t._e(),t.isAuth("exam:examactivityuser:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"activityName","header-align":"center",align:"center",label:"会议名称"}}),e("el-table-column",{attrs:{prop:"examName","header-align":"center",align:"center",label:"考卷名称"}}),e("el-table-column",{attrs:{prop:"activityUserName","header-align":"center",align:"center",label:"姓名"}}),e("el-table-column",{attrs:{prop:"activityUserMobile","header-align":"center",align:"center",label:"联系方式"}}),e("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",label:"考试状态"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-tag",{class:"tag-color tag-color-"+a.row.status,attrs:{type:"primary"}},[t._v(t._s(t.examActivityUserStatus[a.row.status].value))])],1)}}])}),e("el-table-column",{attrs:{prop:"points","header-align":"center",align:"center",label:"得分"}}),e("el-table-column",{attrs:{prop:"passPoints","header-align":"center",align:"center",label:"及格分数"}}),e("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",label:"创建时间"}}),e("el-table-column",{attrs:{prop:"updateOn","header-align":"center",align:"center",label:"更新时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.optionHandle(a.row.id)}}},[t._v("答题情况")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e(),t.showanswerVisible?e("showanswer",{ref:"showanswer"}):t._e()],1)},r=[],n=(a("99af"),a("a15b"),a("d81d"),a("a573"),a("02d7")),o=a("6738"),s=a("1ecd"),l={data:function(){return{examActivityUserStatus:n["a"],dataForm:{contact:"",mobile:"",status:"",examId:void 0,activityId:void 0},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,showanswerVisible:!1}},components:{AddOrUpdate:o["default"],showanswer:s["default"]},activated:function(){this.dataForm.activityId=this.$route.query.activityId,this.dataForm.examId=this.$route.query.examId,this.getDataList()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/exam/examactivityuser/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,contact:this.dataForm.contact,mobile:this.dataForm.mobile,activityId:this.dataForm.activityId,status:this.dataForm.status,examId:this.dataForm.examId})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(e.dataForm.activityId,e.dataForm.examId,t)}))},optionHandle:function(t){var e=this;this.showanswerVisible=!0,this.$nextTick((function(){e.$refs.showanswer.init(t)}))},exportHandle:function(){var t=this.$http.adornUrl("/exam/examactivityuser/export?"+["token="+this.$cookie.get("token"),"page=1","limit=65535","mobile="+this.dataForm.mobile,"contact="+this.dataForm.contact,"examId="+this.dataForm.examId,"activityId="+this.dataForm.activityId,"status="+this.dataForm.status].join("&"));window.open(t)},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/exam/examactivityuser/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))}}},d=l,c=a("2877"),u=Object(c["a"])(d,i,r,!1,null,null,null);e["default"]=u.exports},6738:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"}},[e("el-form-item",{attrs:{label:"考试状态",prop:"status"}},[e("el-select",{attrs:{placeholder:"考试状态",filterable:""},model:{value:t.dataForm.status,callback:function(e){t.$set(t.dataForm,"status",e)},expression:"dataForm.status"}},t._l(t.examActivityUserStatus,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},r=[],n=a("02d7"),o={data:function(){return{examActivityUserStatus:n["a"],visible:!1,dataForm:{id:0,activityId:"",examId:"",activityUserId:"",userId:"",status:"",points:"",passPoints:""},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],examId:[{required:!0,message:"考卷id不能为空",trigger:"blur"}],activityUserId:[{required:!0,message:"用户活动表id不能为空",trigger:"blur"}],status:[{required:!0,message:"考试状态不能为空",trigger:"blur"}],points:[{required:!0,message:"得分不能为空",trigger:"blur"}],passPoints:[{required:!0,message:"及格分数不能为空",trigger:"blur"}]}}},methods:{init:function(t,e,a){var i=this;this.dataForm.activityId=t,this.dataForm.examId=e,this.dataForm.id=a||0,this.visible=!0,this.$nextTick((function(){i.$refs["dataForm"].resetFields(),i.dataForm.id&&i.$http({url:i.$http.adornUrl("/exam/examactivityuser/info/".concat(i.dataForm.id)),method:"get",params:i.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(i.dataForm.activityId=e.examActivityUser.activityId,i.dataForm.examId=e.examActivityUser.examId,i.dataForm.activityUserId=e.examActivityUser.activityUserId,i.dataForm.userId=e.examActivityUser.userId,i.dataForm.status=e.examActivityUser.status,i.dataForm.points=e.examActivityUser.points,i.dataForm.passPoints=e.examActivityUser.passPoints)}))}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/exam/examactivityuser/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,activityId:t.dataForm.activityId,examId:t.dataForm.examId,activityUserId:t.dataForm.activityUserId,userId:t.dataForm.userId,status:t.dataForm.status,points:t.dataForm.points,passPoints:t.dataForm.passPoints})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))}}},s=o,l=a("2877"),d=Object(l["a"])(s,i,r,!1,null,null,null);e["default"]=d.exports},a15b:function(t,e,a){"use strict";var i=a("23e7"),r=a("e330"),n=a("44ad"),o=a("fc6a"),s=a("a640"),l=r([].join),d=n!==Object,c=d||!s("join",",");i({target:"Array",proto:!0,forced:c},{join:function(t){return l(o(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var i=a("23e7"),r=a("d024"),n=a("c430");i({target:"Iterator",proto:!0,real:!0,forced:n},{map:r})},d024:function(t,e,a){"use strict";var i=a("c65b"),r=a("59ed"),n=a("825a"),o=a("46c4"),s=a("c5cc"),l=a("9bdd"),d=s((function(){var t=this.iterator,e=n(i(this.next,t)),a=this.done=!!e.done;if(!a)return l(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return n(this),r(t),new d(o(this),{mapper:t})}},d81d:function(t,e,a){"use strict";var i=a("23e7"),r=a("b727").map,n=a("1dde"),o=n("map");i({target:"Array",proto:!0,forced:!o},{map:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})}}]);
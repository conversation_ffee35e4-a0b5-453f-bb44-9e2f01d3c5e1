(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-350df5ea","chunk-37a545c8"],{"1b69":function(t,e,i){"use strict";i.r(e);i("7f7f");var s,a=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{background:"white","margin-bottom":"20px"}},[e("van-row",{attrs:{gutter:"20"}},[e("van-col",{attrs:{span:"16"}},[e("van-swipe",{staticStyle:{width:"100%"},attrs:{autoplay:3e3}},t._l(t.activityInfo.appFileList,(function(t,i){return e("van-swipe-item",{key:i},[e("van-image",{attrs:{width:"100%",src:t.url}})],1)})),1)],1),e("van-col",{attrs:{span:"8"}},[e("div",{staticStyle:{"margin-top":"20px"}},[e("van-cell",{attrs:{title:"会议名称",value:t.activityInfo.name}}),t.activityInfo.address?e("van-cell",{attrs:{title:"会议地点",value:t.activityInfo.address}}):t._e(),e("van-cell",{attrs:{title:"会议开始时间",value:t.activityInfo.startTime}}),e("van-cell",{attrs:{title:"会议结束时间",value:t.activityInfo.endTime}}),e("van-cell",{attrs:{title:"登录信息"},scopedSlots:t._u([{key:"default",fn:function(){return[t.userInfo?e("van-button",{attrs:{type:"primary",size:"small",round:""}},[t._v(t._s(t.userInfo.username)+"("+t._s(t.userInfo.mobile)+")")]):e("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.showLogin}},[t._v("未登录(点击登录)")])]},proxy:!0}])}),"1736999159118508033"!=t.activityId?e("van-cell",{attrs:{title:"报名状态"},scopedSlots:t._u([{key:"default",fn:function(){return[1==t.isPay?e("van-button",{attrs:{type:"primary",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名")]):2==t.isPay?e("van-button",{attrs:{type:"warning",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名未交费")]):e("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.turnApply}},[t._v("未报名")])]},proxy:!0}],null,!1,567203517)}):t._e()],1)])],1),e("van-tabs",{staticClass:"nav",on:{click:t.onClick},model:{value:t.cmsId,callback:function(e){t.cmsId=e},expression:"cmsId"}},t._l(t.cmsList,(function(t){return e("van-tab",{key:t.id,attrs:{title:t.title,name:t.id}})})),1),e("pclogin")],1)},n=[],r=i("ade3"),c=(i("a481"),i("6762"),i("2fdb"),i("cacf")),o=i("7dcb"),l=function(){var t=this,e=t._self._c;return e("van-dialog",{attrs:{title:"登录操作",width:"400px"},on:{confirm:t.login},model:{value:t.showPcLogin,callback:function(e){t.showPcLogin=e},expression:"showPcLogin"}},[e("div",{staticClass:"text-center padding"},[e("van-cell-group",{attrs:{inset:""}},[e("van-field",{attrs:{name:"手机号",label:"手机号",required:"",placeholder:"手机号",rules:[{required:!0,message:"请填写手机号"}]},model:{value:t.mobile,callback:function(e){t.mobile=e},expression:"mobile"}}),"1736999159118508033"!=t.activityId?e("van-field",{attrs:{center:"",clearable:"",maxlength:"6",label:"短信验证码",required:"",placeholder:"请输入短信验证码"},scopedSlots:t._u([{key:"button",fn:function(){return[e("van-button",{attrs:{size:"small",type:"primary",disabled:t.waiting},on:{click:function(e){return t.doSendSmsCode()}}},[t.waiting?e("span",[t._v(t._s(t.waitingTime)+"秒后重发")]):e("span",{staticStyle:{"font-size":"13px"}},[t._v("获取验证码")])])]},proxy:!0}],null,!1,850170227),model:{value:t.code,callback:function(e){t.code=e},expression:"code"}}):t._e()],1)],1)])},u=[],d={name:"pclogin",computed:{showPcLogin:{get:function(){return this.$store.state.user.showPcLogin},set:function(t){this.$store.commit("user/changePcLogin",t)}}},data:function(){return{activityId:void 0,waiting:!1,waitingTime:60,mobile:"",code:""}},mounted:function(){this.activityId=this.$route.query.id},methods:{login:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?this.code||0x181b0f883e693000==this.activityId?/^\d{6}$/.test(this.code)||0x181b0f883e693000==this.activityId?void this.$fly.post("/pyp/web/user/pcLogin",{mobile:this.mobile,code:0x181b0f883e693000==this.activityId?"121212":this.code}).then((function(e){e&&200===e.code?(vant.Toast("登录成功"),t.$store.commit("user/update",e.userInfo),t.showPcLogin=!1,"applyIndex"!=t.$route.name&&"livesDetail"!=t.$route.name||location.reload()):vant.Toast(e.msg)})):(vant.Toast("验证码错误"),!1):(vant.Toast("请输入验证码"),!1):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},doSendSmsCode:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.mobile,activityId:this.activityId}).then((function(e){e&&200===e.code?(t.countdown(),vant.Toast("发送验证码成功")):vant.Toast(e.msg)})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var t=this;this.waiting=!0;var e=window.setInterval((function(){t.waitingTime--,t.waitingTime<0&&(window.clearInterval(e),t.waitingTime=60,t.waiting=!1)}),1e3)}}},v=d,m=i("2877"),f=Object(m["a"])(v,l,u,!1,null,null,null),h=f.exports,p={components:{pclogin:h},data:function(){return{activityId:void 0,bannerIndex:0,cmsId:void 0,cmsList:[],cmsInfo:{},activityInfo:{}}},computed:{userInfo:{get:function(){return this.$store.state.user.userInfo},set:function(t){this.$store.commit("user/update",t)}},isPay:{get:function(){return this.$store.state.apply.isPay},set:function(t){this.$store.commit("apply/update",t)}}},mounted:function(){var t=(new Date).getTime();localStorage.setItem("logAddTime",t+36e5),this.openid=this.$cookie.get("openid"),this.activityId=this.$route.query.id,this.cmsId=sessionStorage.getItem("cmsId")||this.$route.query.cmsId,this.checkLogin(),this.getActivityInfo(),this.activityLogCount()},methods:(s={showLogin:function(){this.$store.commit("user/changePcLogin",!0)},checkLogin:function(){var t=this;this.$fly.get("/pyp/web/user/checkLogin").then((function(e){t.userInfo=e.result,t.userInfo?t.checkApply():t.isPay=0}))},bannerIndexChange:function(t){this.bannerIndex=t},checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(e){200==e.code?(t.isPay=e.isPay,1==t.isPay||2==t.isPay&&vant.Dialog.alert({title:"提示",message:"您有一笔注册费待支付，点击跳转"}).then((function(){t.$router.push({name:"applySuccess",query:{orderId:e.result,id:t.activityId}})}))):vant.Toast(e.msg)}))},changeShowType:function(t){this.$emit("changeShowType",t)},turnApply:function(){var t=this.cmsList.filter((function(t){return t.model&&t.model.includes("applyIndex")}));if(t){var e=t[0];sessionStorage.setItem("cmsId",e.id);var i=e.model.replace("${activityId}",e.activityId);this.$router.push(JSON.parse(i))}else vant.Toast("暂未开启报名")},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(e){if(t.loading=!1,200==e.code){t.activityInfo=e.activity,document.title=t.activityInfo.name;var i=t.activityInfo.startTime,s=new Date(i.replace(/-/g,"/")),a=new Date,n=s.getTime()-a.getTime();t.dateCompare=n>0?n:0,t.getCmsList()}}))},activityLogCount:function(){var t=localStorage.getItem("logAddTime"),e=(new Date).getTime();e>t&&this.$fly.post("/pyp/activity/activityviewlog/count",{activityId:this.activityId,device:o["a"].getVersion()}).then((function(t){}))},getCmsList:function(){var t=this;this.$fly.get("/pyp/cms/cms/findByActivityId/".concat(this.activityId)).then((function(e){t.loading=!1,200==e.code?(t.cmsList=e.result,t.cmsId?t.changeShowType(t.cmsId):t.changeShowType(t.cmsList[0].id)):(vant.Toast(e.msg),t.cmsList=[])}))},onClick:function(t){if(console.log(t),sessionStorage.setItem("cmsId",t),this.cmsInfo=this.cmsList.filter((function(e){return e.id==t}))[0],this.cmsInfo.url&&Object(c["d"])(this.cmsInfo.url))location.href=this.cmsInfo.url;else if(this.cmsInfo.model&&this.isJSON(this.cmsInfo.model)){var e=this.cmsInfo.model.replace("${activityId}",this.cmsInfo.activityId);this.$router.push(JSON.parse(e))}else"cmsIndex"==this.$route.name?this.changeShowType(t):this.$router.push({name:"cmsIndex",query:{id:this.activityId,cmsId:t}})}},Object(r["a"])(s,"bannerIndexChange",(function(t){this.bannerIndex=t})),Object(r["a"])(s,"isJSON",(function(t){try{return JSON.parse(t),!0}catch(e){return!1}})),s)},g=p,y=(i("dd7a"),Object(m["a"])(g,a,n,!1,null,"7bd3d808",null));e["default"]=y.exports},2909:function(t,e,i){"use strict";function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,s=new Array(e);i<e;i++)s[i]=t[i];return s}function a(t){if(Array.isArray(t))return s(t)}function n(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function r(t,e){if(t){if("string"===typeof t)return s(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?s(t,e):void 0}}function c(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function o(t){return a(t)||n(t)||r(t)||c()}i.d(e,"a",(function(){return o}))},"71f4":function(t,e,i){},"7dcb":function(t,e,i){"use strict";i("a481"),i("4917");e["a"]={getVersion:function(){var t=navigator.userAgent||window.navigator.userAgent,e=/HUAWEI|HONOR/gi,i=/[^;]+(?= Build)/gi,s=/CPU iPhone OS \d[_\d]*/gi,a=/CPU OS \d[_\d]*/gi,n=/Windows NT \d[\.\d]*/gi,r=/Linux x\d[_\d]*/gi,c=/Mac OS X \d[_|\.\d]*/gi;return/Android/gi.test(t)?e.test(t)?t.match(e)[0]+t.match(i)[0]:t.match(i)[0]:/iPhone/gi.test(t)?t.match(s)[0].replace(/CPU iPhone OS/gi,"iPhone").replace(/_/g,"."):/iPad/gi.test(t)?t.match(a)[0].replace(/CPU OS/gi,"iPad").replace(/_/g,"."):/Windows/gi.test(t)?"Windows "+(Math.min(parseInt(t.match(n)[0].replace(/Windows NT /,""))+1,10)<7?"XP":Math.min(parseInt(t.match(n)[0].replace(/Windows NT /,""))+1,10)):/Linux/gi.test(t)?t.match(r)[0]:/Macintosh/gi.test(t)?t.match(c)[0].replace(/_/g,"."):"unknown"}}},"80cd":function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"orders-page"},[t.isMobilePhone?t._e():e("pcheader"),e("div",{staticClass:"custom-navbar"},[e("van-icon",{staticClass:"nav-back",attrs:{name:"arrow-left"},on:{click:function(e){return t.$router.go(-1)}}}),e("span",{staticClass:"nav-title"},[t._v("订单管理")]),e("div",{staticClass:"nav-actions"},[e("van-icon",{staticClass:"nav-action",attrs:{name:"filter-o"},on:{click:function(e){t.showFilter=!0}}})],1)],1),e("div",{staticClass:"content-section"},[e("div",{staticClass:"stats-overview"},[e("div",{staticClass:"stats-card"},[e("div",{staticClass:"card-header"},[e("div",{staticClass:"header-icon"},[e("van-icon",{attrs:{name:"orders-o",size:"20"}})],1),t._m(0)]),e("div",{staticClass:"stats-grid"},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.orderStats.totalOrders||0))]),e("div",{staticClass:"stat-label"},[t._v("总订单")])]),e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-number"},[t._v("¥"+t._s((t.orderStats.totalAmount||0).toFixed(2)))]),e("div",{staticClass:"stat-label"},[t._v("总金额")])]),e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.orderStats.todayOrders||0))]),e("div",{staticClass:"stat-label"},[t._v("今日订单")])]),e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-number"},[t._v("¥"+t._s((t.orderStats.monthAmount||0).toFixed(2)))]),e("div",{staticClass:"stat-label"},[t._v("本月业绩")])])])])]),e("div",{staticClass:"filter-tabs"},[e("div",{staticClass:"filter-tab",class:{active:"all"===t.activeFilter},on:{click:function(e){return t.setFilter("all")}}},[t._v("\n        全部\n      ")]),e("div",{staticClass:"filter-tab",class:{active:"my"===t.activeFilter},on:{click:function(e){return t.setFilter("my")}}},[t._v("\n        我的订单\n      ")]),e("div",{staticClass:"filter-tab",class:{active:"team"===t.activeFilter},on:{click:function(e){return t.setFilter("team")}}},[t._v("\n        团队订单\n      ")])]),e("div",{staticClass:"orders-list"},t._l(t.filteredOrders,(function(i){return e("div",{key:i.id,staticClass:"order-card",on:{click:function(e){return t.viewOrderDetails(i)}}},[e("div",{staticClass:"order-header"},[e("div",{staticClass:"order-info"},[e("div",{staticClass:"order-number"},[t._v("订单号："+t._s(i.orderSn||"ORD"+i.id))]),e("div",{staticClass:"order-time"},[t._v(t._s(t.formatDate(i.createOn)))])]),e("div",{staticClass:"order-status"},[e("van-tag",{attrs:{type:t.getStatusType(i.status),size:"mini"}},[t._v("\n              "+t._s(t.getStatusText(i.status))+"\n            ")])],1)]),e("div",{staticClass:"order-content"},[e("div",{staticClass:"product-info"},[e("div",{staticClass:"product-name"},[t._v(t._s(t.getProductName(i)))]),e("div",{staticClass:"product-spec"},[t._v(t._s(i.packageName||"充值套餐"))])]),e("div",{staticClass:"order-amount"},[e("div",{staticClass:"amount"},[t._v("¥"+t._s((i.payAmount||0).toFixed(2)))]),e("div",{staticClass:"quantity"},[t._v("x"+t._s(i.countValue||1))])])]),e("div",{staticClass:"order-footer"},[e("div",{staticClass:"salesman-info"},[e("van-icon",{attrs:{name:"manager-o",size:"12"}}),e("span",[t._v(t._s(i.salesmanName||"未知"))])],1),i.commissionAmount?e("div",{staticClass:"commission-info"},[e("span",{staticClass:"commission-label"},[t._v("佣金：")]),e("span",{staticClass:"commission-amount"},[t._v("¥"+t._s((i.commissionAmount||0).toFixed(2)))])]):t._e()])])})),0),t.hasMore?e("div",{staticClass:"load-more"},[e("van-button",{attrs:{type:"default",size:"small",loading:t.loading,block:""},on:{click:t.loadMore}},[t._v("\n        "+t._s(t.loading?"加载中...":"加载更多")+"\n      ")])],1):t._e(),0!==t.filteredOrders.length||t.loading?t._e():e("div",{staticClass:"empty-state"},[e("van-empty",{attrs:{description:"暂无订单数据"}})],1)]),e("van-popup",{attrs:{position:"bottom",round:""},model:{value:t.showFilter,callback:function(e){t.showFilter=e},expression:"showFilter"}},[e("div",{staticClass:"filter-popup"},[e("div",{staticClass:"filter-header"},[e("div",{staticClass:"header-left"},[e("div",{staticClass:"filter-icon"},[e("van-icon",{attrs:{name:"filter-o",size:"20"}})],1),e("h3",[t._v("筛选条件")])]),e("div",{staticClass:"header-right"},[e("van-icon",{staticClass:"close-btn",attrs:{name:"cross"},on:{click:function(e){t.showFilter=!1}}})],1)]),e("div",{staticClass:"filter-content"},[e("div",{staticClass:"filter-section"},[e("div",{staticClass:"section-title"},[e("van-icon",{attrs:{name:"clock-o",size:"16"}}),e("h4",[t._v("时间范围")])],1),e("div",{staticClass:"filter-options"},[e("div",{staticClass:"filter-option",class:{active:"all"===t.filterTime},on:{click:function(e){t.filterTime="all"}}},[e("span",[t._v("全部")]),"all"===t.filterTime?e("van-icon",{staticClass:"check-icon",attrs:{name:"success"}}):t._e()],1),e("div",{staticClass:"filter-option",class:{active:"today"===t.filterTime},on:{click:function(e){t.filterTime="today"}}},[e("span",[t._v("今天")]),"today"===t.filterTime?e("van-icon",{staticClass:"check-icon",attrs:{name:"success"}}):t._e()],1),e("div",{staticClass:"filter-option",class:{active:"week"===t.filterTime},on:{click:function(e){t.filterTime="week"}}},[e("span",[t._v("本周")]),"week"===t.filterTime?e("van-icon",{staticClass:"check-icon",attrs:{name:"success"}}):t._e()],1),e("div",{staticClass:"filter-option",class:{active:"month"===t.filterTime},on:{click:function(e){t.filterTime="month"}}},[e("span",[t._v("本月")]),"month"===t.filterTime?e("van-icon",{staticClass:"check-icon",attrs:{name:"success"}}):t._e()],1)])]),e("div",{staticClass:"filter-section"},[e("div",{staticClass:"section-title"},[e("van-icon",{attrs:{name:"orders-o",size:"16"}}),e("h4",[t._v("订单状态")])],1),e("div",{staticClass:"filter-options"},[e("div",{staticClass:"filter-option",class:{active:"all"===t.filterStatus},on:{click:function(e){t.filterStatus="all"}}},[e("span",[t._v("全部")]),"all"===t.filterStatus?e("van-icon",{staticClass:"check-icon",attrs:{name:"success"}}):t._e()],1),e("div",{staticClass:"filter-option",class:{active:"0"===t.filterStatus},on:{click:function(e){t.filterStatus="0"}}},[e("span",[t._v("待支付")]),"0"===t.filterStatus?e("van-icon",{staticClass:"check-icon",attrs:{name:"success"}}):t._e()],1),e("div",{staticClass:"filter-option",class:{active:"1"===t.filterStatus},on:{click:function(e){t.filterStatus="1"}}},[e("span",[t._v("已支付")]),"1"===t.filterStatus?e("van-icon",{staticClass:"check-icon",attrs:{name:"success"}}):t._e()],1)])])]),e("div",{staticClass:"filter-actions"},[e("van-button",{staticClass:"reset-btn",attrs:{size:"large",round:""},on:{click:t.resetFilter}},[e("van-icon",{attrs:{name:"replay",size:"16"}}),t._v("\n          重置\n        ")],1),e("van-button",{staticClass:"apply-btn",attrs:{size:"large",type:"primary",round:""},on:{click:t.applyFilter}},[e("van-icon",{attrs:{name:"success",size:"16"}}),t._v("\n          确定\n        ")],1)],1)])])],1)},a=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"header-text"},[e("h3",[t._v("订单概览")]),e("p",[t._v("团队订单统计")])])}],n=i("2909"),r=(i("96cf"),i("1da1")),c=i("cacf"),o=i("1b69"),l={components:{pcheader:o["default"]},data:function(){return{isMobilePhone:Object(c["c"])(),loading:!1,orders:[],orderStats:{totalOrders:0,totalAmount:0,todayOrders:0,monthAmount:0},activeFilter:"all",showFilter:!1,filterTime:"all",filterStatus:"all",page:1,pageSize:20,hasMore:!0,currentSalesmanId:null}},computed:{filteredOrders:function(){var t=this,e=this.orders;return"my"===this.activeFilter?e=e.filter((function(e){return t.isMyOrder(e)})):"team"===this.activeFilter&&(e=e.filter((function(e){return!t.isMyOrder(e)}))),e}},mounted:function(){var t=this;document.title="订单管理",this.getCurrentSalesmanInfo().then((function(){t.loadOrders(),t.loadOrderStats()}))},methods:{loadOrders:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e,i,s,a,r=arguments;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e=r.length>0&&void 0!==r[0]&&r[0],t.prev=1,this.loading=!0,i={page:e?this.page+1:1,limit:this.pageSize,timeRange:this.filterTime,status:this.filterStatus,type:this.activeFilter},t.next=6,this.$fly.get("/pyp/web/salesman/orders",i);case 6:s=t.sent,200===s.code?(a=s.page.list||s.orders||[],e?(this.orders=[].concat(Object(n["a"])(this.orders),Object(n["a"])(a)),this.page++):(this.orders=a,this.page=1),this.hasMore=a.length===this.pageSize):vant.Toast(s.msg||"加载失败"),t.next=15;break;case 10:t.prev=10,t.t0=t["catch"](1),console.error("加载订单列表失败:",t.t0),e||(this.orders=[],this.page=1,this.hasMore=!1),vant.Toast("接口未实现，显示模拟数据");case 15:return t.prev=15,this.loading=!1,t.finish(15);case 18:case"end":return t.stop()}}),t,this,[[1,10,15,18]])})));function e(){return t.apply(this,arguments)}return e}(),loadOrderStats:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$fly.get("/pyp/web/salesman/orderStats");case 3:e=t.sent,200===e.code&&(this.orderStats=e.stats||{totalOrders:0,totalAmount:0,todayOrders:0,monthAmount:0}),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("加载订单统计失败:",t.t0),this.orderStats={};case 11:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),setFilter:function(t){this.activeFilter=t,this.loadOrders()},loadMore:function(){this.loadOrders(!0)},viewOrderDetails:function(t){vant.Dialog.alert({title:"订单详情",message:"\n          订单号：".concat(t.orderSn||"ORD"+t.id,"\n          产品：").concat(this.getProductName(t),"\n          套餐：").concat(t.packageName||"充值套餐","\n          金额：¥").concat((t.payAmount||0).toFixed(2),"\n          数量：").concat(t.countValue||1,"\n          状态：").concat(this.getStatusText(t.status),"\n          业务员：").concat(t.salesmanName||"未知","\n          创建时间：").concat(this.formatDate(t.createOn),"\n        ")})},applyFilter:function(){this.showFilter=!1,this.loadOrders()},resetFilter:function(){this.filterTime="all",this.filterStatus="all"},getStatusType:function(t){switch(t){case 0:return"warning";case 1:return"primary";case 2:return"danger";case 3:return"info";case 4:return"warning";default:return"default"}},getStatusText:function(t){switch(t){case 0:return"待支付";case 1:return"已支付";case 2:return"已取消";case 3:return"已退款";case 4:return"退款中";default:return"未知"}},formatDate:function(t){if(!t)return"";var e=new Date(t);return e.toLocaleDateString("zh-CN")+" "+e.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"})},getProductName:function(t){return 4===t.rechargeType?"创建活动套餐":1===t.rechargeType||2===t.rechargeType?"充值套餐":3===t.rechargeType?"赠送套餐":"充值套餐"},isMyOrder:function(t){return!this.currentSalesmanId||t.salesmanId===this.currentSalesmanId},getCurrentSalesmanInfo:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$fly.get("/pyp/web/salesman/checkSalesmanStatus");case 3:e=t.sent,200===e.code&&e.isSalesman&&(this.currentSalesmanId=e.salesman.id),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("获取业务员信息失败:",t.t0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}()}},u=l,d=(i("de00"),i("2877")),v=Object(d["a"])(u,s,a,!1,null,"a94136ba",null);e["default"]=v.exports},ade3:function(t,e,i){"use strict";i.d(e,"a",(function(){return r}));var s=i("53ca");function a(t,e){if("object"!==Object(s["a"])(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var a=i.call(t,e||"default");if("object"!==Object(s["a"])(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function n(t){var e=a(t,"string");return"symbol"===Object(s["a"])(e)?e:String(e)}function r(t,e,i){return e=n(e),e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}},cad8:function(t,e,i){},dd7a:function(t,e,i){"use strict";i("cad8")},de00:function(t,e,i){"use strict";i("71f4")}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5328719e"],{"0a0a":function(t,e,i){},"11e9":function(t,e,i){var a=i("52a7"),n=i("4630"),r=i("6821"),s=i("6a99"),c=i("69a8"),o=i("c69a"),l=Object.getOwnPropertyDescriptor;e.f=i("9e1e")?l:function(t,e){if(t=r(t),e=s(e,!0),o)try{return l(t,e)}catch(i){}if(c(t,e))return n(!a.f.call(t,e),t[e])}},"456d":function(t,e,i){var a=i("4bf8"),n=i("0d58");i("5eda")("keys",(function(){return function(t){return n(a(t))}}))},"5dbc":function(t,e,i){var a=i("d3f4"),n=i("8b97").set;t.exports=function(t,e,i){var r,s=e.constructor;return s!==i&&"function"==typeof s&&(r=s.prototype)!==i.prototype&&a(r)&&n&&n(t,r),t}},"5eda":function(t,e,i){var a=i("5ca1"),n=i("8378"),r=i("79e5");t.exports=function(t,e){var i=(n.Object||{})[t]||Object[t],s={};s[t]=e(i),a(a.S+a.F*r((function(){i(1)})),"Object",s)}},"66c7":function(t,e,i){"use strict";i("4917"),i("a481");var a=/([yMdhsm])(\1*)/g,n="yyyy-MM-dd";function r(t,e){e-=(t+"").length;for(var i=0;i<e;i++)t="0"+t;return t}e["a"]={formatDate:{format:function(t,e){return e=e||n,e.replace(a,(function(e){switch(e.charAt(0)){case"y":return r(t.getFullYear(),e.length);case"M":return r(t.getMonth()+1,e.length);case"d":return r(t.getDate(),e.length);case"w":return t.getDay()+1;case"h":return r(t.getHours(),e.length);case"m":return r(t.getMinutes(),e.length);case"s":return r(t.getSeconds(),e.length)}}))},parse:function(t,e){var i=e.match(a),n=t.match(/(\d)+/g);if(i.length==n.length){for(var r=new Date(1970,0,1),s=0;s<i.length;s++){var c=parseInt(n[s]),o=i[s];switch(o.charAt(0)){case"y":r.setFullYear(c);break;case"M":r.setMonth(c-1);break;case"d":r.setDate(c);break;case"h":r.setHours(c);break;case"m":r.setMinutes(c);break;case"s":r.setSeconds(c);break}}return r}return null},toWeek:function(t){var e=new Date(t).getDay(),i="";switch(e){case 0:i="s";break;case 1:i="m";break;case 2:i="t";break;case 3:i="w";break;case 4:i="t";break;case 5:i="f";break;case 6:i="s";break}return i}},toUserLook:function(t){var e=Math.floor(t/3600%24),i=Math.floor(t/60%60);return e<1?i+"分":e+"时"+i+"分"}}},"8b97":function(t,e,i){var a=i("d3f4"),n=i("cb7c"),r=function(t,e){if(n(t),!a(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,a){try{a=i("9b43")(Function.call,i("11e9").f(Object.prototype,"__proto__").set,2),a(t,[]),e=!(t instanceof Array)}catch(n){e=!0}return function(t,i){return r(t,i),e?t.__proto__=i:a(t,i),t}}({},!1):void 0),check:r}},"8e6e":function(t,e,i){var a=i("5ca1"),n=i("990b"),r=i("6821"),s=i("11e9"),c=i("f1ae");a(a.S,"Object",{getOwnPropertyDescriptors:function(t){var e,i,a=r(t),o=s.f,l=n(a),u={},f=0;while(l.length>f)i=o(a,e=l[f++]),void 0!==i&&c(u,e,i);return u}})},9093:function(t,e,i){var a=i("ce10"),n=i("e11e").concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return a(t,n)}},"990b":function(t,e,i){var a=i("9093"),n=i("2621"),r=i("cb7c"),s=i("7726").Reflect;t.exports=s&&s.ownKeys||function(t){var e=a.f(r(t)),i=n.f;return i?e.concat(i(t)):e}},a8fc:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"plane-select"},[e("div",{staticClass:"date-nav"},[e("van-button",{attrs:{plain:"",size:"small",disabled:t.isLoading},on:{click:function(e){return t.switchDay(-1)}}},[t._v("前一天")]),e("div",{staticClass:"current-date"},[t._v(t._s(t.inDate))]),e("van-button",{attrs:{plain:"",size:"small",disabled:t.isLoading},on:{click:function(e){return t.switchDay(1)}}},[t._v("后一天")])],1),t.isLoading?e("div",{staticClass:"loading-overlay"},[e("van-loading",{attrs:{type:"spinner",color:"#1989fa"}}),e("div",{staticClass:"loading-text"},[t._v("航班信息加载中...")])],1):t._e(),t.isLoading||t.showCabins?t._e():[t.flights.length>0?e("div",{staticClass:"flight-list"},t._l(t.flights,(function(i){return e("div",{key:i.flightNo,staticClass:"flight-item",on:{click:function(e){return t.showFlightCabins(i)}}},[e("div",{staticClass:"time-row"},[e("div",{staticClass:"time-info"},[e("span",{staticClass:"departure-time"},[t._v(t._s(i.fromDateTime))]),e("span",{staticClass:"airport-name"},[t._v(t._s(i.fromAirportName))])]),e("div",{staticClass:"flight-duration"},[e("div",{staticClass:"duration-line"}),e("span",{staticClass:"duration-text"},[t._v(t._s(i.flyDuration)+"时")])]),e("div",{staticClass:"time-info text-right"},[e("span",{staticClass:"arrival-time"},[t._v(t._s(i.toDateTime))]),e("span",{staticClass:"airport-name"},[t._v(t._s(i.toAirportName))])])]),e("div",{staticClass:"flight-detail"},[e("div",{staticClass:"flight-number"},[e("span",[t._v(t._s(i.flightNo))]),e("span",{staticClass:"company-name"},[t._v(t._s(i.airlineCompany))])]),e("div",{staticClass:"price-preview"},[e("van-icon",{attrs:{name:"arrow"}})],1)])])})),0):e("van-empty",{attrs:{description:"暂无航班信息"}})],!t.isLoading&&t.showCabins?[e("div",{staticClass:"cabin-header"},[e("van-icon",{staticClass:"back-icon",attrs:{name:"arrow-left"},on:{click:t.backToFlightList}}),e("div",{staticClass:"selected-flight-info"},[e("div",{staticClass:"time-row"},[e("div",{staticClass:"time-info"},[e("span",{staticClass:"departure-time"},[t._v(t._s(t.currentFlight.fromDateTime))]),e("span",{staticClass:"airport-name"},[t._v(t._s(t.currentFlight.fromAirportName))])]),e("div",{staticClass:"flight-duration"},[e("div",{staticClass:"duration-line"}),e("span",{staticClass:"duration-text"},[t._v(t._s(t.currentFlight.flyDuration)+"时")])]),e("div",{staticClass:"time-info text-right"},[e("span",{staticClass:"arrival-time"},[t._v(t._s(t.currentFlight.toDateTime))]),e("span",{staticClass:"airport-name"},[t._v(t._s(t.currentFlight.toAirportName))])])]),e("div",{staticClass:"flight-number"},[e("span",[t._v(t._s(t.currentFlight.flightNo))]),e("span",{staticClass:"company-name"},[t._v(t._s(t.currentFlight.airlineCompany))])])])],1),e("div",{staticClass:"cabin-list"},t._l(t.currentFlight.cabins,(function(i){return e("div",{key:i.cabinBookPara,staticClass:"cabin-item",class:{"selected-item":t.selectedFlightItem.cabinBookPara===i.cabinBookPara},on:{click:function(e){return t.selectFlightItem(i)}}},[e("div",{staticClass:"cabin-info"},[e("div",{staticClass:"cabin-left"},[e("span",{staticClass:"cabin-name"},[t._v(t._s(i.cabinName))]),i.cabinDesc?e("span",{staticClass:"cabin-desc"},[t._v(t._s(i.cabinDesc))]):t._e()]),e("div",{staticClass:"cabin-right"})])])})),0)]:t._e(),e("div",{staticClass:"bottom-buttons"},[e("van-button",{attrs:{plain:"",block:""},on:{click:t.goBack}},[t._v("取消")]),e("van-button",{attrs:{type:"primary",block:"",disabled:!t.selectedFlightItem.cabinBookPara},on:{click:t.confirmSelection}},[t._v("确定")])],1)],2)},n=[],r=(i("8e6e"),i("456d"),i("a481"),i("ade3")),s=(i("ac6a"),i("c5f6"),i("66c7"));function c(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,a)}return i}function o(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?c(Object(i),!0).forEach((function(e){Object(r["a"])(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):c(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}var l={name:"PlaneSelect",data:function(){return{isLoading:!1,inDate:"",startCityCode:"",endCityCode:"",flights:[],showCabins:!1,currentFlight:{},selectedFlightItem:{},tripInfo:{}}},mounted:function(){this.tripInfo=JSON.parse(this.$route.query.tripInfo||"{}"),this.inDate=this.tripInfo.inDate?s["a"].formatDate.format(new Date(this.tripInfo.inDate),"yyyy/MM/dd"):s["a"].formatDate.format(new Date,"yyyy/MM/dd"),this.startCityCode=this.tripInfo.startCityCode||"",this.endCityCode=this.tripInfo.endCityCode||"",this.loadFlights()},methods:{goBack:function(){this.showCabins?this.backToFlightList():this.$router.back()},switchDay:function(t){var e=new Date(this.inDate);e.setDate(e.getDate()+t),this.inDate=s["a"].formatDate.format(e,"yyyy/MM/dd"),this.loadFlights(),this.showCabins=!1},loadFlights:function(){var t=this;this.startCityCode&&this.endCityCode?(this.isLoading=!0,this.flights=[],this.$fly.get("/pyp/panhe/searchPlane",{fromCity:this.startCityCode,toCity:this.endCityCode,fromDate:this.inDate}).then((function(e){t.isLoading=!1,e&&200===e.code?t.flights=e.result||[]:vant.Toast(e.msg||"获取航班信息失败")})).catch((function(){t.isLoading=!1,vant.Toast("网络错误，请重试")}))):vant.Toast("请选择出发地和目的地")},getLowestPrice:function(t){if(!t.cabins||0===t.cabins.length)return"暂无";var e=Number.MAX_VALUE;return t.cabins.forEach((function(t){t.cabinPrice&&t.cabinPrice.adultSalePrice<e&&(e=t.cabinPrice.adultSalePrice)})),e===Number.MAX_VALUE?"暂无":e},showFlightCabins:function(t){this.currentFlight=t,this.showCabins=!0,this.selectedFlightItem={}},backToFlightList:function(){this.showCabins=!1,this.currentFlight={},this.selectedFlightItem={}},selectFlightItem:function(t){this.selectedFlightItem=t},confirmSelection:function(){if(this.selectedFlightItem.cabinBookPara){var t=o(o({},this.tripInfo),{},{inNumber:this.currentFlight.flightNo,startCityCode:this.currentFlight.fromAirportCode,endCityCode:this.currentFlight.toAirportCode,inStartPlace:this.currentFlight.fromAirportName,inStartTerminal:this.currentFlight.fromTerminal,inEndTerminal:this.currentFlight.toTerminal,inEndPlace:this.currentFlight.toAirportName,inStartDate:(this.currentFlight.fromDateTime+":00").replaceAll("-","/"),inEndDate:(this.currentFlight.toDateTime+":00").replaceAll("-","/"),cabinBookPara:this.selectedFlightItem.cabinBookPara,cabinCode:this.selectedFlightItem.cabinCode,price:this.selectedFlightItem.cabinPrice.adultSalePrice,isBuy:0,type:0,typeName:"单程"});this.saveTrip(t)}else vant.Toast("请选择舱位")},saveTrip:function(t){var e=this;this.isLoading=!0;var i=t.id?"/pyp/web/activity/activityguest/updateTrip":"/pyp/web/activity/activityguest/saveTrip";this.$fly.post(i,t).then((function(i){e.isLoading=!1,i&&200===i.code?(vant.Toast("保存成功"),e.$router.replace({path:"/schedules/expertTrip",query:{detailId:t.activityGuestId}})):vant.Toast(i.msg||"保存失败")})).catch((function(){e.isLoading=!1,vant.Toast("网络错误，请重试")}))}}},u=l,f=(i("d538"),i("2877")),h=Object(f["a"])(u,a,n,!1,null,"6d66759c",null);e["default"]=h.exports},aa77:function(t,e,i){var a=i("5ca1"),n=i("be13"),r=i("79e5"),s=i("fdef"),c="["+s+"]",o="​",l=RegExp("^"+c+c+"*"),u=RegExp(c+c+"*$"),f=function(t,e,i){var n={},c=r((function(){return!!s[t]()||o[t]()!=o})),l=n[t]=c?e(h):s[t];i&&(n[i]=l),a(a.P+a.F*c,"String",n)},h=f.trim=function(t,e){return t=String(n(t)),1&e&&(t=t.replace(l,"")),2&e&&(t=t.replace(u,"")),t};t.exports=f},ac6a:function(t,e,i){for(var a=i("cadf"),n=i("0d58"),r=i("2aba"),s=i("7726"),c=i("32e9"),o=i("84f2"),l=i("2b4c"),u=l("iterator"),f=l("toStringTag"),h=o.Array,d={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},p=n(d),g=0;g<p.length;g++){var v,b=p[g],m=d[b],y=s[b],C=y&&y.prototype;if(C&&(C[u]||c(C,u,h),C[f]||c(C,f,b),o[b]=h,m))for(v in a)C[v]||r(C,v,a[v],!0)}},ade3:function(t,e,i){"use strict";i.d(e,"a",(function(){return s}));var a=i("53ca");function n(t,e){if("object"!==Object(a["a"])(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!==Object(a["a"])(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function r(t){var e=n(t,"string");return"symbol"===Object(a["a"])(e)?e:String(e)}function s(t,e,i){return e=r(e),e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}},c5f6:function(t,e,i){"use strict";var a=i("7726"),n=i("69a8"),r=i("2d95"),s=i("5dbc"),c=i("6a99"),o=i("79e5"),l=i("9093").f,u=i("11e9").f,f=i("86cc").f,h=i("aa77").trim,d="Number",p=a[d],g=p,v=p.prototype,b=r(i("2aeb")(v))==d,m="trim"in String.prototype,y=function(t){var e=c(t,!1);if("string"==typeof e&&e.length>2){e=m?e.trim():h(e,3);var i,a,n,r=e.charCodeAt(0);if(43===r||45===r){if(i=e.charCodeAt(2),88===i||120===i)return NaN}else if(48===r){switch(e.charCodeAt(1)){case 66:case 98:a=2,n=49;break;case 79:case 111:a=8,n=55;break;default:return+e}for(var s,o=e.slice(2),l=0,u=o.length;l<u;l++)if(s=o.charCodeAt(l),s<48||s>n)return NaN;return parseInt(o,a)}}return+e};if(!p(" 0o1")||!p("0b1")||p("+0x1")){p=function(t){var e=arguments.length<1?0:t,i=this;return i instanceof p&&(b?o((function(){v.valueOf.call(i)})):r(i)!=d)?s(new g(y(e)),i,p):y(e)};for(var C,_=i("9e1e")?l(g):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),w=0;_.length>w;w++)n(g,C=_[w])&&!n(p,C)&&f(p,C,u(g,C));p.prototype=v,v.constructor=p,i("2aba")(a,d,p)}},d538:function(t,e,i){"use strict";i("0a0a")},f1ae:function(t,e,i){"use strict";var a=i("86cc"),n=i("4630");t.exports=function(t,e,i){e in t?a.f(t,e,n(0,i)):t[e]=i}},fdef:function(t,e){t.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-56c603ba"],{"11e9":function(t,e,a){var i=a("52a7"),n=a("4630"),r=a("6821"),s=a("6a99"),c=a("69a8"),o=a("c69a"),u=Object.getOwnPropertyDescriptor;e.f=a("9e1e")?u:function(t,e){if(t=r(t),e=s(e,!0),o)try{return u(t,e)}catch(a){}if(c(t,e))return n(!i.f.call(t,e),t[e])}},"456d":function(t,e,a){var i=a("4bf8"),n=a("0d58");a("5eda")("keys",(function(){return function(t){return n(i(t))}}))},"49e7":function(t,e,a){},"5dbc":function(t,e,a){var i=a("d3f4"),n=a("8b97").set;t.exports=function(t,e,a){var r,s=e.constructor;return s!==a&&"function"==typeof s&&(r=s.prototype)!==a.prototype&&i(r)&&n&&n(t,r),t}},"5eda":function(t,e,a){var i=a("5ca1"),n=a("8378"),r=a("79e5");t.exports=function(t,e){var a=(n.Object||{})[t]||Object[t],s={};s[t]=e(a),i(i.S+i.F*r((function(){a(1)})),"Object",s)}},"66c7":function(t,e,a){"use strict";a("4917"),a("a481");var i=/([yMdhsm])(\1*)/g,n="yyyy-MM-dd";function r(t,e){e-=(t+"").length;for(var a=0;a<e;a++)t="0"+t;return t}e["a"]={formatDate:{format:function(t,e){return e=e||n,e.replace(i,(function(e){switch(e.charAt(0)){case"y":return r(t.getFullYear(),e.length);case"M":return r(t.getMonth()+1,e.length);case"d":return r(t.getDate(),e.length);case"w":return t.getDay()+1;case"h":return r(t.getHours(),e.length);case"m":return r(t.getMinutes(),e.length);case"s":return r(t.getSeconds(),e.length)}}))},parse:function(t,e){var a=e.match(i),n=t.match(/(\d)+/g);if(a.length==n.length){for(var r=new Date(1970,0,1),s=0;s<a.length;s++){var c=parseInt(n[s]),o=a[s];switch(o.charAt(0)){case"y":r.setFullYear(c);break;case"M":r.setMonth(c-1);break;case"d":r.setDate(c);break;case"h":r.setHours(c);break;case"m":r.setMinutes(c);break;case"s":r.setSeconds(c);break}}return r}return null},toWeek:function(t){var e=new Date(t).getDay(),a="";switch(e){case 0:a="s";break;case 1:a="m";break;case 2:a="t";break;case 3:a="w";break;case 4:a="t";break;case 5:a="f";break;case 6:a="s";break}return a}},toUserLook:function(t){var e=Math.floor(t/3600%24),a=Math.floor(t/60%60);return e<1?a+"分":e+"时"+a+"分"}}},"8b97":function(t,e,a){var i=a("d3f4"),n=a("cb7c"),r=function(t,e){if(n(t),!i(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,i){try{i=a("9b43")(Function.call,a("11e9").f(Object.prototype,"__proto__").set,2),i(t,[]),e=!(t instanceof Array)}catch(n){e=!0}return function(t,a){return r(t,a),e?t.__proto__=a:i(t,a),t}}({},!1):void 0),check:r}},"8e6e":function(t,e,a){var i=a("5ca1"),n=a("990b"),r=a("6821"),s=a("11e9"),c=a("f1ae");i(i.S,"Object",{getOwnPropertyDescriptors:function(t){var e,a,i=r(t),o=s.f,u=n(i),l={},f=0;while(u.length>f)a=o(i,e=u[f++]),void 0!==a&&c(l,e,a);return l}})},9093:function(t,e,a){var i=a("ce10"),n=a("e11e").concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return i(t,n)}},"990b":function(t,e,a){var i=a("9093"),n=a("2621"),r=a("cb7c"),s=a("7726").Reflect;t.exports=s&&s.ownKeys||function(t){var e=i.f(r(t)),a=n.f;return a?e.concat(a(t)):e}},"9ef6":function(t,e,a){"use strict";a("49e7")},aa77:function(t,e,a){var i=a("5ca1"),n=a("be13"),r=a("79e5"),s=a("fdef"),c="["+s+"]",o="​",u=RegExp("^"+c+c+"*"),l=RegExp(c+c+"*$"),f=function(t,e,a){var n={},c=r((function(){return!!s[t]()||o[t]()!=o})),u=n[t]=c?e(d):s[t];a&&(n[a]=u),i(i.P+i.F*c,"String",n)},d=f.trim=function(t,e){return t=String(n(t)),1&e&&(t=t.replace(u,"")),2&e&&(t=t.replace(l,"")),t};t.exports=f},ac6a:function(t,e,a){for(var i=a("cadf"),n=a("0d58"),r=a("2aba"),s=a("7726"),c=a("32e9"),o=a("84f2"),u=a("2b4c"),l=u("iterator"),f=u("toStringTag"),d=o.Array,p={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},v=n(p),h=0;h<v.length;h++){var y,b=v[h],m=p[b],g=s[b],T=g&&g.prototype;if(T&&(T[l]||c(T,l,d),T[f]||c(T,f,b),o[b]=d,m))for(y in i)T[y]||r(T,y,i[y],!0)}},ade3:function(t,e,a){"use strict";a.d(e,"a",(function(){return s}));var i=a("53ca");function n(t,e){if("object"!==Object(i["a"])(t)||null===t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var n=a.call(t,e||"default");if("object"!==Object(i["a"])(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function r(t){var e=n(t,"string");return"symbol"===Object(i["a"])(e)?e:String(e)}function s(t,e,a){return e=r(e),e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}},aec9:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"train-select"},[e("div",{staticClass:"date-nav"},[e("van-button",{attrs:{plain:"",size:"small",disabled:t.isLoading},on:{click:function(e){return t.switchDay(-1)}}},[t._v("前一天")]),e("div",{staticClass:"current-date"},[t._v(t._s(t.inDate))]),e("van-button",{attrs:{plain:"",size:"small",disabled:t.isLoading},on:{click:function(e){return t.switchDay(1)}}},[t._v("后一天")])],1),t.isLoading?e("div",{staticClass:"loading-overlay"},[e("van-loading",{attrs:{type:"spinner",color:"#1989fa"}}),e("div",{staticClass:"loading-text"},[t._v("火车票信息加载中...")])],1):t._e(),t.isLoading||t.showSeats?t._e():[t.trains.length>0?e("div",{staticClass:"train-list"},t._l(t.trains,(function(a){return e("div",{key:a.trainCode,staticClass:"train-item",on:{click:function(e){return t.showTrainSeats(a)}}},[e("div",{staticClass:"time-row"},[e("div",{staticClass:"time-info"},[e("span",{staticClass:"departure-time"},[t._v(t._s(a.fromDateTime))]),e("span",{staticClass:"station-name"},[t._v(t._s(a.fromStation))])]),e("div",{staticClass:"train-duration"},[e("div",{staticClass:"duration-line"}),e("span",{staticClass:"duration-text"},[t._v(t._s(a.runTime)+"时")])]),e("div",{staticClass:"time-info text-right"},[e("span",{staticClass:"arrival-time"},[t._v(t._s(a.toDateTime))]),e("span",{staticClass:"station-name"},[t._v(t._s(a.toStation))])])]),e("div",{staticClass:"train-detail"},[e("div",{staticClass:"train-number"},[e("span",[t._v(t._s(a.trainCode))]),e("span",{staticClass:"train-type"},[t._v(t._s(a.trainType||"普通列车"))])]),e("div",{staticClass:"price-preview"},[e("van-icon",{attrs:{name:"arrow"}})],1)])])})),0):e("van-empty",{attrs:{description:"暂无火车票信息"}})],!t.isLoading&&t.showSeats?[e("div",{staticClass:"seat-header"},[e("van-icon",{staticClass:"back-icon",attrs:{name:"arrow-left"},on:{click:t.backToTrainList}}),e("div",{staticClass:"selected-train-info"},[e("div",{staticClass:"time-row"},[e("div",{staticClass:"time-info"},[e("span",{staticClass:"departure-time"},[t._v(t._s(t.currentTrain.fromDateTime))]),e("span",{staticClass:"station-name"},[t._v(t._s(t.currentTrain.fromStation))])]),e("div",{staticClass:"train-duration"},[e("div",{staticClass:"duration-line"}),e("span",{staticClass:"duration-text"},[t._v(t._s(t.currentTrain.runTime)+"时")])]),e("div",{staticClass:"time-info text-right"},[e("span",{staticClass:"arrival-time"},[t._v(t._s(t.currentTrain.toDateTime))]),e("span",{staticClass:"station-name"},[t._v(t._s(t.currentTrain.toStation))])])]),e("div",{staticClass:"train-number"},[e("span",[t._v(t._s(t.currentTrain.trainCode))]),e("span",{staticClass:"train-type"},[t._v(t._s(t.currentTrain.trainType||"普通列车"))])])])],1),e("div",{staticClass:"seat-list"},t._l(t.currentTrain.Seats,(function(a){return e("div",{key:a.seatType,staticClass:"seat-item",class:{"selected-item":t.selectedSeat.seatType===a.seatType},on:{click:function(e){return t.selectSeat(a)}}},[e("div",{staticClass:"seat-info"},[e("div",{staticClass:"seat-left"},[e("span",{staticClass:"seat-name"},[t._v(t._s(a.seatTypeName))]),e("span",{staticClass:"seat-desc"},[t._v("剩余座位："+t._s(a.leftTicketNum))])]),e("div",{staticClass:"seat-right"})])])})),0)]:t._e(),e("div",{staticClass:"bottom-buttons"},[e("van-button",{attrs:{plain:"",block:""},on:{click:t.goBack}},[t._v("取消")]),e("van-button",{attrs:{type:"primary",block:"",disabled:!t.selectedSeat.seatType},on:{click:t.confirmSelection}},[t._v("确定")])],1)],2)},n=[],r=(a("8e6e"),a("456d"),a("a481"),a("ade3")),s=(a("ac6a"),a("c5f6"),a("66c7"));function c(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function o(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?c(Object(a),!0).forEach((function(e){Object(r["a"])(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):c(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}var u={name:"TrainSelect",data:function(){return{isLoading:!1,inDate:"",inStartPlace:"",inEndPlace:"",trains:[],showSeats:!1,currentTrain:{},selectedSeat:{},tripInfo:{}}},mounted:function(){this.tripInfo=JSON.parse(this.$route.query.tripInfo||"{}"),this.inDate=this.tripInfo.inDate?s["a"].formatDate.format(new Date(this.tripInfo.inDate),"yyyy/MM/dd"):s["a"].formatDate.format(new Date,"yyyy/MM/dd"),this.inStartPlace=this.tripInfo.inStartPlace||"",this.inEndPlace=this.tripInfo.inEndPlace||"",this.loadTrains()},methods:{goBack:function(){this.showSeats?this.backToTrainList():this.$router.back()},switchDay:function(t){var e=new Date(this.inDate);e.setDate(e.getDate()+t),this.inDate=s["a"].formatDate.format(e,"yyyy/MM/dd"),this.loadTrains(),this.showSeats=!1},loadTrains:function(){var t=this;this.inStartPlace&&this.inEndPlace?(this.isLoading=!0,this.trains=[],this.$fly.get("/pyp/panhe/searchTrain",{fromCity:this.inStartPlace,toCity:this.inEndPlace,fromDate:this.inDate}).then((function(e){t.isLoading=!1,e&&200===e.code?t.trains=e.result||[]:vant.Toast(e.msg||"获取火车票信息失败")})).catch((function(){t.isLoading=!1,vant.Toast("网络错误，请重试")}))):vant.Toast("请选择出发地和目的地")},getLowestPrice:function(t){if(!t.Seats||0===t.Seats.length)return"暂无";var e=Number.MAX_VALUE;return t.Seats.forEach((function(t){t.ticketPrice<e&&(e=t.ticketPrice)})),e===Number.MAX_VALUE?"暂无":e},showTrainSeats:function(t){this.currentTrain=t,this.showSeats=!0,this.selectedSeat={}},backToTrainList:function(){this.showSeats=!1,this.currentTrain={},this.selectedSeat={}},selectSeat:function(t){this.selectedSeat=t},confirmSelection:function(){if(this.selectedSeat.seatType){var t=o(o({},this.tripInfo),{},{inNumber:this.currentTrain.trainCode,inStartPlace:this.currentTrain.fromStation,inEndPlace:this.currentTrain.toStation,inStartDate:(this.currentTrain.fromDateTime+":00").replaceAll("-","/"),inEndDate:(this.currentTrain.toDateTime+":00").replaceAll("-","/"),cabinBookPara:this.selectedSeat.seatType,cabinCode:this.selectedSeat.seatTypeName,price:this.selectedSeat.ticketPrice,isBuy:0,type:0,typeName:"单程"});this.saveTrip(t)}else vant.Toast("请选择座位")},saveTrip:function(t){var e=this;this.isLoading=!0;var a=t.id?"/pyp/web/activity/activityguest/updateTrip":"/pyp/web/activity/activityguest/saveTrip";this.$fly.post(a,t).then((function(a){e.isLoading=!1,a&&200===a.code?(vant.Toast("保存成功"),e.$router.replace({path:"/schedules/expertTrip",query:{detailId:t.activityGuestId}})):vant.Toast(a.msg||"保存失败")})).catch((function(){e.isLoading=!1,vant.Toast("网络错误，请重试")}))}}},l=u,f=(a("9ef6"),a("2877")),d=Object(f["a"])(l,i,n,!1,null,"7f836178",null);e["default"]=d.exports},c5f6:function(t,e,a){"use strict";var i=a("7726"),n=a("69a8"),r=a("2d95"),s=a("5dbc"),c=a("6a99"),o=a("79e5"),u=a("9093").f,l=a("11e9").f,f=a("86cc").f,d=a("aa77").trim,p="Number",v=i[p],h=v,y=v.prototype,b=r(a("2aeb")(y))==p,m="trim"in String.prototype,g=function(t){var e=c(t,!1);if("string"==typeof e&&e.length>2){e=m?e.trim():d(e,3);var a,i,n,r=e.charCodeAt(0);if(43===r||45===r){if(a=e.charCodeAt(2),88===a||120===a)return NaN}else if(48===r){switch(e.charCodeAt(1)){case 66:case 98:i=2,n=49;break;case 79:case 111:i=8,n=55;break;default:return+e}for(var s,o=e.slice(2),u=0,l=o.length;u<l;u++)if(s=o.charCodeAt(u),s<48||s>n)return NaN;return parseInt(o,i)}}return+e};if(!v(" 0o1")||!v("0b1")||v("+0x1")){v=function(t){var e=arguments.length<1?0:t,a=this;return a instanceof v&&(b?o((function(){y.valueOf.call(a)})):r(a)!=p)?s(new h(g(e)),a,v):g(e)};for(var T,S=a("9e1e")?u(h):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),_=0;S.length>_;_++)n(h,T=S[_])&&!n(v,T)&&f(v,T,l(h,T));v.prototype=y,y.constructor=v,a("2aba")(i,p,v)}},f1ae:function(t,e,a){"use strict";var i=a("86cc"),n=a("4630");t.exports=function(t,e,a){e in t?i.f(t,e,n(0,a)):t[e]=a}},fdef:function(t,e){t.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-35980132"],{"34ae":function(e,a,t){"use strict";t.d(a,"b",(function(){return r})),t.d(a,"c",(function(){return o})),t.d(a,"a",(function(){return i})),t.d(a,"d",(function(){return l}));var r=[{key:0,value:"整间"},{key:1,value:"男床位"},{key:2,value:"女床位"}],o=[{key:0,value:"整间"},{key:1,value:"拼住"},{key:2,value:"拼住"}],i=[{key:0,value:"已取消"},{key:1,value:"已入住"}],l=[{key:0,value:"未开启"},{key:1,value:"已开启"}]},"7cab":function(e,a,t){"use strict";t.r(a);t("b0c0");var r=function(){var e=this,a=e._self._c;return a("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(a){e.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"房型名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"房型名称"},model:{value:e.dataForm.name,callback:function(a){e.$set(e.dataForm,"name",a)},expression:"dataForm.name"}})],1),e.dataForm.id?e._e():a("el-form-item",{attrs:{label:"库存数量",prop:"stockNumber"}},[a("el-input",{attrs:{placeholder:"库存数量"},model:{value:e.dataForm.stockNumber,callback:function(a){e.$set(e.dataForm,"stockNumber",a)},expression:"dataForm.stockNumber"}},[a("template",{slot:"append"},[e._v(" 间 ")])],2)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"销售状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"订单状态",filterable:""},model:{value:e.dataForm.status,callback:function(a){e.$set(e.dataForm,"status",a)},expression:"dataForm.status"}},e._l(e.saleStatus,(function(e){return a("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"床位销售状态",prop:"bedStatus"}},[a("el-select",{attrs:{disabled:e.dataForm.bedNumber<2,placeholder:"床位销售状态",filterable:""},model:{value:e.dataForm.bedStatus,callback:function(a){e.$set(e.dataForm,"bedStatus",a)},expression:"dataForm.bedStatus"}},e._l(e.saleStatus,(function(e){return a("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"单价",prop:"price"}},[a("el-input",{attrs:{placeholder:"单价"},on:{change:e.handleChange},model:{value:e.dataForm.price,callback:function(a){e.$set(e.dataForm,"price",a)},expression:"dataForm.price"}},[a("template",{slot:"append"},[e._v("RMB/间 ")])],2)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"床位价格",prop:"bedPrice"}},[a("el-input",{attrs:{placeholder:"床位价格"},model:{value:e.dataForm.bedPrice,callback:function(a){e.$set(e.dataForm,"bedPrice",a)},expression:"dataForm.bedPrice"}},[a("template",{slot:"append"},[e._v(" RMB/床位 ")])],2)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"床位数量",prop:"bedNumber"}},[a("el-input",{attrs:{placeholder:"床位数量"},on:{change:e.handleChange},model:{value:e.dataForm.bedNumber,callback:function(a){e.$set(e.dataForm,"bedNumber",a)},expression:"dataForm.bedNumber"}},[a("template",{slot:"append"},[e._v(" 床位/间 ")])],2)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序",prop:"orderBy"}},[a("el-input",{attrs:{placeholder:"排序"},model:{value:e.dataForm.orderBy,callback:function(a){e.$set(e.dataForm,"orderBy",a)},expression:"dataForm.orderBy"}},[a("template",{slot:"append"},[e._v(" 值越小越靠前 ")])],2)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"房间备注",prop:"roomRemarks"}},[a("el-input",{attrs:{placeholder:"房间备注"},model:{value:e.dataForm.roomRemarks,callback:function(a){e.$set(e.dataForm,"roomRemarks",a)},expression:"dataForm.roomRemarks"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"床位备注",prop:"bedRemarks"}},[a("el-input",{attrs:{placeholder:"床位备注"},model:{value:e.dataForm.bedRemarks,callback:function(a){e.$set(e.dataForm,"bedRemarks",a)},expression:"dataForm.bedRemarks"}})],1)],1)],1),e.dataForm.price>0?a("el-form-item",{attrs:{label:"微信支付",prop:"isWechatPay"}},[a("el-select",{model:{value:e.dataForm.isWechatPay,callback:function(a){e.$set(e.dataForm,"isWechatPay",a)},expression:"dataForm.isWechatPay"}},e._l(e.yesOrNo,(function(e){return a("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1):e._e(),e.dataForm.price>0?a("el-form-item",{attrs:{label:"支付宝支付",prop:"isAliPay"}},[a("el-select",{model:{value:e.dataForm.isAliPay,callback:function(a){e.$set(e.dataForm,"isAliPay",a)},expression:"dataForm.isAliPay"}},e._l(e.yesOrNo,(function(e){return a("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1):e._e(),e.dataForm.price>0?a("el-form-item",{attrs:{label:"银行转账",prop:"isBankTransfer"}},[a("el-select",{model:{value:e.dataForm.isBankTransfer,callback:function(a){e.$set(e.dataForm,"isBankTransfer",a)},expression:"dataForm.isBankTransfer"}},e._l(e.yesOrNo,(function(e){return a("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1):e._e(),1==e.dataForm.isBankTransfer?a("el-form-item",{attrs:{label:"银行转账信息",prop:"bankTransferNotify"}},[a("tinymce-editor",{ref:"editor",model:{value:e.dataForm.bankTransferNotify,callback:function(a){e.$set(e.dataForm,"bankTransferNotify",a)},expression:"dataForm.bankTransferNotify"}})],1):e._e()],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){e.visible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},o=[],i=(t("d9e2"),t("d3b7"),t("3ca3"),t("ddb0"),t("61f7")),l=t("7de9"),n=t("34ae"),d={components:{TinymceEditor:function(){return Promise.all([t.e("chunk-2d0e1c2e"),t.e("chunk-03be236c"),t.e("chunk-2d0a4b8c")]).then(t.bind(null,"26dc"))}},data:function(){var e=function(e,a,t){Object(i["d"])(a)?t():t(new Error("必须为数字"))},a=function(e,a,t){Object(i["b"])(a)?t():t(new Error("必须为数字"))};return{yesOrNo:l["g"],visible:!1,saleStatus:n["d"],activityInfo:{},dataForm:{id:0,name:"",activityId:"",hotelId:"",hotelActivityId:"",status:0,orderBy:0,bedStatus:0,price:0,stockNumber:0,bedPrice:0,bedNumber:1,isWechatPay:0,isAliPay:0,isBankTransfer:0,bankTransferNotify:"",roomRemarks:"",bedRemarks:""},dataRule:{name:[{required:!0,message:"房型名称不能为空",trigger:"blur"}],status:[{required:!0,message:"销售状态不能为空",trigger:"blur"}],bedStatus:[{required:!0,message:"床位销售状态不能为空",trigger:"blur"}],price:[{required:!0,message:"单价不能为空",trigger:"blur"},{validator:a,trigger:"blur"}],bedPrice:[{required:!0,message:"床位价格不能为空",trigger:"blur"},{validator:a,trigger:"blur"}],bedNumber:[{required:!0,message:"床位数量不能为空",trigger:"blur"},{validator:e,trigger:"blur"}],stockNumber:[{required:!0,message:"库存数量不能为空",trigger:"blur"},{validator:e,trigger:"blur"}]}}},methods:{init:function(e,a,t,r){var o=this;this.dataForm.id=e||0,this.dataForm.activityId=a,this.dataForm.hotelActivityId=t,this.dataForm.hotelId=r,this.visible=!0,this.$nextTick((function(){o.$refs["dataForm"].resetFields(),o.dataForm.id&&o.$http({url:o.$http.adornUrl("/hotel/hotelactivityroom/info/".concat(o.dataForm.id)),method:"get",params:o.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(o.dataForm.name=a.hotelActivityRoom.name,o.dataForm.activityId=a.hotelActivityRoom.activityId,o.dataForm.hotelId=a.hotelActivityRoom.hotelId,o.dataForm.hotelActivityId=a.hotelActivityRoom.hotelActivityId,o.dataForm.status=a.hotelActivityRoom.status,o.dataForm.orderBy=a.hotelActivityRoom.orderBy,o.dataForm.bedStatus=a.hotelActivityRoom.bedStatus,o.dataForm.price=a.hotelActivityRoom.price,o.dataForm.bedPrice=a.hotelActivityRoom.bedPrice,o.dataForm.bedNumber=a.hotelActivityRoom.bedNumber,o.dataForm.inDate=a.hotelActivityRoom.inDate,o.dataForm.outDate=a.hotelActivityRoom.outDate,o.dataForm.bankTransferNotify=a.hotelActivityRoom.bankTransferNotify,o.dataForm.isWechatPay=a.hotelActivityRoom.isWechatPay,o.dataForm.isAliPay=a.hotelActivityRoom.isAliPay,o.dataForm.isBankTransfer=a.hotelActivityRoom.isBankTransfer,o.dataForm.roomRemarks=a.hotelActivityRoom.roomRemarks,o.dataForm.bedRemarks=a.hotelActivityRoom.bedRemarks)}))}))},handleChange:function(){this.dataForm.bedNumber<2&&(this.dataForm.bedStatus=0),this.dataForm.bedPrice=this.dataForm.price/this.dataForm.bedNumber},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(a){a&&e.$http({url:e.$http.adornUrl("/hotel/hotelactivityroom/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,name:e.dataForm.name,activityId:e.dataForm.activityId,hotelId:e.dataForm.hotelId,hotelActivityId:e.dataForm.hotelActivityId,stockNumber:e.dataForm.stockNumber,status:e.dataForm.status,orderBy:e.dataForm.orderBy,bedStatus:e.dataForm.bedStatus,price:e.dataForm.price,bedPrice:e.dataForm.bedPrice,bedNumber:e.dataForm.bedNumber,bankTransferNotify:e.dataForm.bankTransferNotify,isWechatPay:e.dataForm.isWechatPay,isAliPay:e.dataForm.isAliPay,isBankTransfer:e.dataForm.isBankTransfer,roomRemarks:e.dataForm.roomRemarks,bedRemarks:e.dataForm.bedRemarks})}).then((function(a){var t=a.data;t&&200===t.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(t.msg)}))}))},getActivity:function(){var e=this;this.$http({url:this.$http.adornUrl("/activity/activity/info/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(a){var t=a.data;t&&200===t.code&&(e.activityInfo=t.activity)}))}}},s=d,m=t("2877"),u=Object(m["a"])(s,r,o,!1,null,null,null);a["default"]=u.exports},"7de9":function(e,a,t){"use strict";t.d(a,"g",(function(){return r})),t.d(a,"f",(function(){return o})),t.d(a,"e",(function(){return i})),t.d(a,"a",(function(){return l})),t.d(a,"b",(function(){return n})),t.d(a,"c",(function(){return d})),t.d(a,"d",(function(){return s}));var r=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],o=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],i=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],l=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],n=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],d=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],s=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-cefe4872","chunk-0506e191","chunk-0506e191"],{1148:function(e,t,a){"use strict";var r=a("5926"),n=a("577e"),o=a("1d80"),i=RangeError;e.exports=function(e){var t=n(o(this)),a="",l=r(e);if(l<0||l===1/0)throw new i("Wrong number of repetitions");for(;l>0;(l>>>=1)&&(t+=t))1&l&&(a+=t);return a}},"421f":function(e,t,a){"use strict";a.r(t);a("b680");var r=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:"退款名额管理 - ".concat(e.channelName),visible:e.visible,width:"80%","close-on-click-modal":!1,"close-on-press-escape":!1},on:{"update:visible":function(t){e.visible=t}}},[t("el-card",{staticClass:"quota-settings",staticStyle:{"margin-bottom":"20px"}},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("名额设置")]),t("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:e.refreshQuotaSettings}},[e._v("刷新")])],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form",{ref:"quotaForm",attrs:{model:e.quotaForm,rules:e.quotaRules,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"启用名额控制",prop:"enabled"}},[t("el-switch",{attrs:{"active-text":"启用","inactive-text":"禁用"},model:{value:e.quotaForm.enabled,callback:function(t){e.$set(e.quotaForm,"enabled",t)},expression:"quotaForm.enabled"}})],1),e.quotaForm.enabled?t("el-form-item",{attrs:{label:"退款名额",prop:"refundQuota"}},[t("el-input-number",{attrs:{min:0,max:9999,placeholder:"请输入退款名额"},model:{value:e.quotaForm.refundQuota,callback:function(t){e.$set(e.quotaForm,"refundQuota",t)},expression:"quotaForm.refundQuota"}})],1):e._e(),t("el-form-item",[t("el-button",{attrs:{type:"primary",loading:e.quotaSaving},on:{click:e.saveQuotaSettings}},[e._v("保存设置")]),t("el-button",{attrs:{type:"warning",loading:e.batchUpdating},on:{click:e.batchUpdatePermissions}},[e._v("重新分配权限")])],1)],1)],1),t("el-col",{attrs:{span:12}},[t("div",{staticClass:"quota-stats"},[t("h4",[e._v("名额使用统计")]),t("el-row",{attrs:{gutter:10}},[t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-value"},[e._v(e._s(e.quotaStats.total_quota||0))]),t("div",{staticClass:"stat-label"},[e._v("总名额")])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-value"},[e._v(e._s(e.quotaStats.used_quota||0))]),t("div",{staticClass:"stat-label"},[e._v("已使用")])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-value"},[e._v(e._s(e.quotaStats.available_quota||0))]),t("div",{staticClass:"stat-label"},[e._v("可用")])])])],1),t("el-row",{staticStyle:{"margin-top":"10px"},attrs:{gutter:10}},[t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-value"},[e._v(e._s(e.quotaStats.assigned_orders||0))]),t("div",{staticClass:"stat-label"},[e._v("有权限订单")])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-value"},[e._v(e._s(e.quotaStats.refunded_orders||0))]),t("div",{staticClass:"stat-label"},[e._v("已退款订单")])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-value"},[e._v(e._s(e.quotaStats.total_paid_orders||0))]),t("div",{staticClass:"stat-label"},[e._v("总已支付订单")])])])],1)],1)])],1)],1),t("el-tabs",{on:{"tab-click":e.handleTabClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[t("el-tab-pane",{attrs:{label:"有权限订单",name:"eligible"}},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.eligibleLoading,expression:"eligibleLoading"}],staticStyle:{width:"100%"},attrs:{data:e.eligibleOrders,border:""}},[t("el-table-column",{attrs:{prop:"order_sn","header-align":"center",align:"center",label:"订单号",width:"180"}}),t("el-table-column",{attrs:{prop:"user_name","header-align":"center",align:"center",label:"用户"}}),t("el-table-column",{attrs:{prop:"user_mobile","header-align":"center",align:"center",label:"手机号"}}),t("el-table-column",{attrs:{prop:"salesman_name","header-align":"center",align:"center",label:"业务员"}}),t("el-table-column",{attrs:{prop:"pay_amount","header-align":"center",align:"center",label:"支付金额"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v("¥"+e._s((a.row.pay_amount||0).toFixed(2)))])]}}])}),t("el-table-column",{attrs:{prop:"quota_sequence","header-align":"center",align:"center",label:"权限序号",width:"80"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{attrs:{size:"small",type:"success"}},[e._v(e._s(a.row.quota_sequence))])]}}])}),t("el-table-column",{attrs:{prop:"create_on","header-align":"center",align:"center",label:"下单时间",width:"160"}}),t("el-table-column",{attrs:{"header-align":"center",align:"center",label:"操作",width:"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.releasePermission(a.row)}}},[e._v("释放权限")])]}}])})],1)],1),t("el-tab-pane",{attrs:{label:"使用记录",name:"records"}},[t("div",{staticStyle:{"margin-bottom":"10px"}},[t("el-form",{attrs:{inline:!0,model:e.recordForm}},[t("el-form-item",{attrs:{label:"订单号"}},[t("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"订单号",clearable:""},model:{value:e.recordForm.orderSn,callback:function(t){e.$set(e.recordForm,"orderSn",t)},expression:"recordForm.orderSn"}})],1),t("el-form-item",{attrs:{label:"操作类型"}},[t("el-select",{staticStyle:{width:"120px"},attrs:{placeholder:"操作类型",clearable:""},model:{value:e.recordForm.actionType,callback:function(t){e.$set(e.recordForm,"actionType",t)},expression:"recordForm.actionType"}},[t("el-option",{attrs:{label:"分配权限",value:"1"}}),t("el-option",{attrs:{label:"释放权限",value:"2"}})],1)],1),t("el-form-item",[t("el-button",{on:{click:e.getRecordList}},[e._v("查询")]),t("el-button",{on:{click:e.resetRecordForm}},[e._v("重置")])],1)],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.recordLoading,expression:"recordLoading"}],staticStyle:{width:"100%"},attrs:{data:e.recordList,border:""}},[t("el-table-column",{attrs:{prop:"order_sn","header-align":"center",align:"center",label:"订单号",width:"180"}}),t("el-table-column",{attrs:{prop:"user_name","header-align":"center",align:"center",label:"用户"}}),t("el-table-column",{attrs:{prop:"salesman_name","header-align":"center",align:"center",label:"业务员"}}),t("el-table-column",{attrs:{prop:"action_type","header-align":"center",align:"center",label:"操作类型",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{attrs:{type:1===a.row.action_type?"success":"warning",size:"small"}},[e._v(" "+e._s(1===a.row.action_type?"分配权限":"释放权限")+" ")])]}}])}),t("el-table-column",{attrs:{prop:"quota_sequence","header-align":"center",align:"center",label:"序号",width:"80"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.quota_sequence?t("span",[e._v(e._s(a.row.quota_sequence))]):t("span",[e._v("-")])]}}])}),t("el-table-column",{attrs:{prop:"create_time","header-align":"center",align:"center",label:"操作时间",width:"160"}}),t("el-table-column",{attrs:{prop:"remarks","header-align":"center",align:"center",label:"备注"}})],1),t("el-pagination",{attrs:{"current-page":e.recordPageIndex,"page-sizes":[10,20,50,100],"page-size":e.recordPageSize,total:e.recordTotalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.recordSizeChangeHandle,"current-change":e.recordCurrentChangeHandle}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("关闭")])],1)],1)},n=[],o=(a("b0c0"),{data:function(){return{visible:!1,channelId:null,channelName:"",activeTab:"eligible",quotaForm:{enabled:!1,refundQuota:0},quotaRules:{refundQuota:[{required:!0,message:"请输入退款名额",trigger:"blur"},{type:"number",min:0,message:"退款名额不能小于0",trigger:"blur"}]},quotaSaving:!1,batchUpdating:!1,quotaStats:{},eligibleOrders:[],eligibleLoading:!1,recordForm:{orderSn:"",actionType:""},recordList:[],recordPageIndex:1,recordPageSize:10,recordTotalPage:0,recordLoading:!1}},methods:{init:function(e,t){var a=this;this.channelId=e,this.channelName=t,this.visible=!0,this.activeTab="eligible",this.$nextTick((function(){a.refreshQuotaSettings(),a.getEligibleOrders()}))},refreshQuotaSettings:function(){var e=this;this.$http({url:this.$http.adornUrl("/channel/refund-permission/quota-usage/".concat(this.channelId)),method:"get"}).then((function(t){var a=t.data;a&&200===a.code&&(e.quotaStats=a.data||{},e.$http({url:e.$http.adornUrl("/channel/channel/info/".concat(e.channelId)),method:"get"}).then((function(t){var a=t.data;if(a&&200===a.code){var r=a.channel;e.quotaForm.enabled=1===r.refundQuotaEnabled,e.quotaForm.refundQuota=r.refundQuota||0}})))}))},saveQuotaSettings:function(){var e=this;this.$refs.quotaForm.validate((function(t){t&&(e.quotaSaving=!0,e.$http({url:e.$http.adornUrl("/channel/refund-permission/update-quota"),method:"post",data:e.$http.adornData({channelId:e.channelId,refundQuota:e.quotaForm.refundQuota,enabled:e.quotaForm.enabled})}).then((function(t){var a=t.data;e.quotaSaving=!1,a&&200===a.code?(e.$message.success("设置保存成功"),e.refreshQuotaSettings(),e.getEligibleOrders(),e.$emit("refreshDataList")):e.$message.error(a.msg||"保存失败")})).catch((function(){e.quotaSaving=!1})))}))},batchUpdatePermissions:function(){var e=this;this.$confirm("确定要重新分配该渠道的退款权限吗？这将清除现有权限分配并重新计算。","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.batchUpdating=!0,e.$http({url:e.$http.adornUrl("/channel/refund-permission/batch-update/".concat(e.channelId)),method:"post"}).then((function(t){var a=t.data;e.batchUpdating=!1,a&&200===a.code?(e.$message.success("权限重新分配成功"),e.refreshQuotaSettings(),e.getEligibleOrders(),e.$emit("refreshDataList")):e.$message.error(a.msg||"重新分配失败")})).catch((function(){e.batchUpdating=!1}))}))},getEligibleOrders:function(){var e=this;this.eligibleLoading=!0,this.$http({url:this.$http.adornUrl("/channel/refund-permission/eligible-orders/".concat(this.channelId)),method:"get"}).then((function(t){var a=t.data;e.eligibleLoading=!1,a&&200===a.code&&(e.eligibleOrders=a.data||[])})).catch((function(){e.eligibleLoading=!1}))},releasePermission:function(e){var t=this;this.$confirm("确定要释放订单 ".concat(e.order_sn," 的退款权限吗？"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:t.$http.adornUrl("/channel/refund-permission/release-permission/".concat(e.order_id)),method:"post"}).then((function(e){var a=e.data;a&&200===a.code?(t.$message.success("权限释放成功"),t.refreshQuotaSettings(),t.getEligibleOrders(),t.$emit("refreshDataList")):t.$message.error(a.msg||"释放失败")}))}))},handleTabClick:function(e){"records"===e.name&&this.getRecordList()},getRecordList:function(){var e=this;this.recordLoading=!0,this.$http({url:this.$http.adornUrl("/channel/refund-permission/quota-records"),method:"get",params:this.$http.adornParams({page:this.recordPageIndex,limit:this.recordPageSize,channelId:this.channelId,orderSn:this.recordForm.orderSn,actionType:this.recordForm.actionType})}).then((function(t){var a=t.data;e.recordLoading=!1,a&&200===a.code&&(e.recordList=a.page.list,e.recordTotalPage=a.page.totalCount)})).catch((function(){e.recordLoading=!1}))},resetRecordForm:function(){this.recordForm.orderSn="",this.recordForm.actionType="",this.recordPageIndex=1,this.getRecordList()},recordSizeChangeHandle:function(e){this.recordPageSize=e,this.recordPageIndex=1,this.getRecordList()},recordCurrentChangeHandle:function(e){this.recordPageIndex=e,this.getRecordList()}}}),i=o,l=(a("8ae0"),a("2877")),s=Object(l["a"])(i,r,n,!1,null,"bda983d0",null);t["default"]=s.exports},"8ae0":function(e,t,a){"use strict";a("c671")},b680:function(e,t,a){"use strict";var r=a("23e7"),n=a("e330"),o=a("5926"),i=a("408a"),l=a("1148"),s=a("d039"),c=RangeError,d=String,u=Math.floor,h=n(l),g=n("".slice),p=n(1..toFixed),m=function(e,t,a){return 0===t?a:t%2===1?m(e,t-1,a*e):m(e*e,t/2,a)},b=function(e){var t=0,a=e;while(a>=4096)t+=12,a/=4096;while(a>=2)t+=1,a/=2;return t},f=function(e,t,a){var r=-1,n=a;while(++r<6)n+=t*e[r],e[r]=n%1e7,n=u(n/1e7)},v=function(e,t){var a=6,r=0;while(--a>=0)r+=e[a],e[a]=u(r/t),r=r%t*1e7},_=function(e){var t=6,a="";while(--t>=0)if(""!==a||0===t||0!==e[t]){var r=d(e[t]);a=""===a?r:a+h("0",7-r.length)+r}return a},q=s((function(){return"0.000"!==p(8e-5,3)||"1"!==p(.9,0)||"1.25"!==p(1.255,2)||"1000000000000000128"!==p(0xde0b6b3a7640080,0)}))||!s((function(){p({})}));r({target:"Number",proto:!0,forced:q},{toFixed:function(e){var t,a,r,n,l=i(this),s=o(e),u=[0,0,0,0,0,0],p="",q="0";if(s<0||s>20)throw new c("Incorrect fraction digits");if(l!==l)return"NaN";if(l<=-1e21||l>=1e21)return d(l);if(l<0&&(p="-",l=-l),l>1e-21)if(t=b(l*m(2,69,1))-69,a=t<0?l*m(2,-t,1):l/m(2,t,1),a*=4503599627370496,t=52-t,t>0){f(u,0,a),r=s;while(r>=7)f(u,1e7,0),r-=7;f(u,m(10,r,1),0),r=t-1;while(r>=23)v(u,1<<23),r-=23;v(u,1<<r),f(u,1,1),v(u,2),q=_(u)}else f(u,0,a),f(u,1<<-t,0),q=_(u)+h("0",s);return s>0?(n=q.length,q=p+(n<=s?"0."+h("0",s-n)+q:g(q,0,n-s)+"."+g(q,n-s))):q=p+q,q}})},c671:function(e,t,a){}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7d939a42"],{"28a5":function(t,i,e){"use strict";var a=e("aae3"),s=e("cb7c"),n=e("ebd6"),r=e("0390"),c=e("9def"),l=e("5f1b"),u=e("520a"),o=e("79e5"),d=Math.min,p=[].push,h="split",f="length",g="lastIndex",v=4294967295,y=!o((function(){RegExp(v,"y")}));e("214f")("split",2,(function(t,i,e,o){var m;return m="c"=="abbc"[h](/(b)*/)[1]||4!="test"[h](/(?:)/,-1)[f]||2!="ab"[h](/(?:ab)*/)[f]||4!="."[h](/(.?)(.?)/)[f]||"."[h](/()()/)[f]>1||""[h](/.?/)[f]?function(t,i){var s=String(this);if(void 0===t&&0===i)return[];if(!a(t))return e.call(s,t,i);var n,r,c,l=[],o=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),d=0,h=void 0===i?v:i>>>0,y=new RegExp(t.source,o+"g");while(n=u.call(y,s)){if(r=y[g],r>d&&(l.push(s.slice(d,n.index)),n[f]>1&&n.index<s[f]&&p.apply(l,n.slice(1)),c=n[0][f],d=r,l[f]>=h))break;y[g]===n.index&&y[g]++}return d===s[f]?!c&&y.test("")||l.push(""):l.push(s.slice(d)),l[f]>h?l.slice(0,h):l}:"0"[h](void 0,0)[f]?function(t,i){return void 0===t&&0===i?[]:e.call(this,t,i)}:e,[function(e,a){var s=t(this),n=void 0==e?void 0:e[i];return void 0!==n?n.call(e,s,a):m.call(String(s),e,a)},function(t,i){var a=o(m,t,this,i,m!==e);if(a.done)return a.value;var u=s(t),p=String(this),h=n(u,RegExp),f=u.unicode,g=(u.ignoreCase?"i":"")+(u.multiline?"m":"")+(u.unicode?"u":"")+(y?"y":"g"),b=new h(y?u:"^(?:"+u.source+")",g),_=void 0===i?v:i>>>0;if(0===_)return[];if(0===p.length)return null===l(b,p)?[p]:[];var T=0,I=0,S=[];while(I<p.length){b.lastIndex=y?I:0;var w,x=l(b,y?p:p.slice(I));if(null===x||(w=d(c(b.lastIndex+(y?0:I)),p.length))===T)I=r(p,I,f);else{if(S.push(p.slice(T,I)),S.length===_)return S;for(var C=1;C<=x.length-1;C++)if(S.push(x[C]),S.length===_)return S;I=T=w}}return S.push(p.slice(T)),S}]}))},5850:function(t,i,e){"use strict";e("aefc")},"5b0c":function(t,i,e){"use strict";e.r(i);e("a481"),e("7f7f");var a,s=function(){var t=this,i=t._self._c;return i("div",{class:t.isMobilePhone?"":"pc-container"},[t.isMobilePhone?t._e():i("pcheader"),i("van-card",{staticStyle:{background:"white"},attrs:{thumb:t.guestInfo.avatar?t.guestInfo.avatar:"http://mpjoy.oss-cn-beijing.aliyuncs.com/20240917/d9301e81ab674112b3acf4a84aff4e96.png"}},[i("div",{staticStyle:{"font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.guestInfo.name))]),i("div",{staticStyle:{"padding-top":"10px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[t.guestInfo.unit?i("van-tag",{staticStyle:{margin:"5px 10px 5px 0px"},attrs:{size:"medium",round:"",type:"primary",plain:""}},[t._v(t._s(t.guestInfo.unit))]):t._e(),t.guestInfo.duties?i("van-tag",{staticStyle:{margin:"5px 10px 5px 0px"},attrs:{size:"medium",round:"",type:"warning",plain:""}},[t._v(t._s(t.guestInfo.duties))]):t._e()],1)]),t._m(0),t.trip&&t.trip.length>0?i("div",t._l(t.trip,(function(e,a){return i("div",{key:a,staticClass:"flight-card",on:{click:function(i){return t.viewTripDetail(e)}}},[i("div",{staticClass:"flight-top"},[i("div",{staticClass:"flight-top-name"},[i("img",{staticClass:"flight-icon",attrs:{src:0==e.inType?"http://mpjoy.oss-cn-beijing.aliyuncs.com/20241022/ee05423e609347e39d0cb38f9424e0ac.png":"http://mpjoy.oss-cn-beijing.aliyuncs.com/20241022/4da10defd13b4f9ea1716a38d43b9e6b.png"}}),i("span",{staticClass:"flight-no"},[t._v(" "+t._s(e.inNumber)+"-"+t._s(e.inDate))])]),i("div",{staticClass:"flight-status-tags"},[i("van-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:1==e.isBuy?"success":"warning",round:"",size:"medium"}},[t._v("\n                        "+t._s(t.isBuy[e.isBuy]?t.isBuy[e.isBuy].name:"未出票")+"\n                    ")]),i("img",{staticClass:"flight-top-arow",attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/20241022/54aa75f6bee64249b317e1f2796f354f.png"}})],1)]),i("div",{staticClass:"flight-middle"},[i("div",{staticClass:"flight-start"},[i("div",{staticClass:"flight-start-time"},[t._v(" "+t._s(t._f("timeFilter")(e.inStartDate))+" ")]),i("div",{staticClass:"flight-start-a"},[t._v(" "+t._s(e.inStartPlace+(0==e.inType?"-"+e.inStartTerminal:""))+" ")])]),i("div",{staticClass:"flight-status"},[0==e.inType?i("div",{staticClass:"flight-status-show",class:{"plane-type":0==e.inType,"train-type":1==e.inType}},[t._v("\n                        "+t._s(t._f("tripPlaneStatusFilter")(e.orderStatus))+"\n                    ")]):i("div",{staticClass:"flight-status-show",class:{"plane-type":0==e.inType,"train-type":1==e.inType}},[t._v("\n                        "+t._s(t._f("tripTrainStatusFilter")(e.orderStatus))+"\n                    ")]),t._m(1,!0)]),i("div",{staticClass:"flight-end"},[i("div",{staticClass:"flight-end-time"},[t._v(" "+t._s(t._f("timeFilter")(e.inEndDate))+" ")]),i("div",{staticClass:"flight-end-a"},[t._v(" "+t._s(e.inEndPlace+(0==e.inType?"-"+e.inEndTerminal:""))+" ")])])]),0==e.inType&&4==e.orderStatus&&0==e.isCha?i("div",{staticClass:"flight-actions"},[i("van-button",{attrs:{type:"primary",size:"small"},on:{click:function(i){return i.stopPropagation(),t.handleChangeTicket(e)}}},[t._v("申请改签")])],1):t._e()])})),0):i("div",{staticClass:"empty-trip"},[i("van-empty",{attrs:{description:"暂无行程信息"}})],1),i("div",{staticStyle:{margin:"16px"}},[i("van-button",{attrs:{round:"",block:"",type:"info"},on:{click:t.addTrip}},[t._v("新增行程")])],1),i("div",{staticStyle:{margin:"16px"}},[i("van-button",{attrs:{round:"",block:"",type:"primary"},on:{click:function(i){return t.$router.replace({path:"/schedules/expertIndex",query:{detailId:t.id}})}}},[t._v("返回上一页面")])],1)],1)},n=[function(){var t=this,i=t._self._c;return i("div",{staticClass:"nav-title",staticStyle:{"margin-top":"8px"}},[i("div",{staticClass:"color"}),i("div",{staticClass:"text"},[t._v("行程信息")])])},function(){var t=this,i=t._self._c;return i("div",{staticClass:"flight-status-arow1"},[i("div",{staticClass:"flight-status-arow2"})])}],r=e("ade3"),c=(e("6762"),e("28a5"),e("66c7")),l=e("cacf"),u=e("1b69"),o=e("7de9"),d={components:{pcheader:u["default"]},data:function(){return{isBuy:o["d"],tripPlaneStatus:o["g"],tripTrainStatus:o["h"],areaCode:"",guestGoType:o["b"],doType:o["a"],tripType:o["i"],loading:!1,tripTypeShow:!1,areaShow:!1,dateShow:!1,guestGoTypeShow:!1,isMobilePhone:Object(l["c"])(),openid:void 0,activityId:void 0,id:void 0,guestInfo:{},trip:[],activityInfo:{},minDate:new Date(2024,0,1),maxDate:new Date(2030,10,1),currentDate:new Date,startShow:!1,start:"",endShow:!1,end:"",minHour:"",tripForm:{id:"",inType:0,inTypeName:"",type:0,typeName:"",inDate:"",inNumber:"",inEndPlace:"",inStartPlace:"",inEndDate:"",inStartDate:"",activityGuestId:"",activityId:"",isBuy:0,price:0,doType:0,image:""}}},filters:{timeFilter:function(t){return t?c["a"].formatDate.format(new Date(t),"hh:mm"):""},tripPlaneStatusFilter:function(t){var i=o["g"].filter((function(i){return i.key===t}));if(i.length>=1)return i[0].value},tripTrainStatusFilter:function(t){var i=o["h"].filter((function(i){return i.key===t}));if(i.length>=1)return i[0].value}},mounted:function(){this.id=this.$route.query.detailId,this.tripForm.activityGuestId=this.$route.query.detailId,this.openid=this.$cookie.get("openid"),this.getActivityList(),this.getTopicAndSchedule()},methods:(a={viewTripDetail:function(t){this.$router.push({path:"/schedules/expertTripDetail",query:{detailId:this.id,tripId:t.id}})},handleChangeTicket:function(t){event.stopPropagation(),this.$router.push({path:"/schedules/expertTripChange",query:{detailId:this.id,tripId:t.id}})},isImage:function(t){var i=["jpg","jpeg","png","gif","bmp","webp"],e=t.split(".").pop().toLowerCase();return i.includes(e)},preImage:function(t){vant.ImagePreview({images:[t],closeable:!0})},getStatusTagType:function(t){var i=t.orderStatus;return void 0===i?"default":0===i?"primary":1===i?"success":2===i?"danger":"default"},editTrip:function(t){this.$router.push({path:"/schedules/expertTripEdit",query:{detailId:this.id,tripId:t.id}})}},Object(r["a"])(a,"editTrip",(function(t){this.$router.push({path:"/schedules/expertTripEdit",query:{detailId:this.id,tripId:t.id}})})),Object(r["a"])(a,"getTopicAndSchedule",(function(){var t=this;this.$fly.get("/pyp/web/activity/activityguest/getTrip/".concat(this.id)).then((function(i){t.loading=!1,200==i.code?t.trip=i.result:(vant.Toast(i.msg),t.trip=[])}))})),Object(r["a"])(a,"getActivityList",(function(){var t=this;this.$fly.get("/pyp/web/activity/activityguest/getById/".concat(this.id)).then((function(i){200==i.code?(t.guestInfo=i.result,t.activityId=i.result.activityId,t.tripForm.activityId=i.result.activityId):(vant.Toast(i.msg),t.guestInfo={})}))})),Object(r["a"])(a,"addTrip",(function(){this.$router.push({path:"/schedules/expertTripEdit",query:{detailId:this.id}})})),a)},p=d,h=(e("5850"),e("2877")),f=Object(h["a"])(p,s,n,!1,null,"7a4993ad",null);i["default"]=f.exports},aefc:function(t,i,e){}}]);
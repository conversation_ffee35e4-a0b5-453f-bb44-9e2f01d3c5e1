(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2dbb0ede","chunk-35980132","chunk-2d0bdd69"],{"2e66":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"库存时间",prop:"stockDate"}},[e("el-date-picker",{staticStyle:{windth:"100%"},attrs:{disabled:"",type:"date","value-format":"yyyy/MM/dd",placeholder:"库存时间"},model:{value:t.dataForm.stockDate,callback:function(e){t.$set(t.dataForm,"stockDate",e)},expression:"dataForm.stockDate"}})],1),e("el-form-item",{attrs:{label:"价格",prop:"price"}},[e("el-input",{attrs:{placeholder:"库存时间对应价格"},model:{value:t.dataForm.price,callback:function(e){t.$set(t.dataForm,"price",e)},expression:"dataForm.price"}},[e("template",{slot:"append"},[t._v(" RMB/间 ")])],2)],1),e("el-form-item",{attrs:{label:"总库存",prop:"number"}},[e("el-input",{attrs:{placeholder:"总库存"},model:{value:t.dataForm.number,callback:function(e){t.$set(t.dataForm,"number",e)},expression:"dataForm.number"}},[e("template",{slot:"append"},[t._v(" 间 ")])],2)],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},o=[],i=(a("d9e2"),a("61f7")),n={data:function(){var t=function(t,e,a){Object(i["d"])(e)?a():a(new Error("必须为数字"))},e=function(t,e,a){Object(i["b"])(e)?a():a(new Error("必须为数字"))};return{visible:!1,hotelActivityRoom:{},dataForm:{id:0,hotelActivityRoomId:"",activityId:"",hotelId:"",hotelActivityId:"",stockDate:"",price:"",bedPrice:"",number:0,alreadyNumber:0,spareNumber:0},dataRule:{stockDate:[{required:!0,message:"库存时间不能为空",trigger:"blur"}],price:[{required:!0,message:"库存时间对应价格不能为空",trigger:"blur"},{validator:e,trigger:"blur"}],number:[{required:!0,message:"总库存不能为空",trigger:"blur"},{validator:t,trigger:"blur"}]}}},methods:{init:function(t,e,a){var r=this;this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){r.$refs["dataForm"].resetFields(),r.dataForm.id?r.$http({url:r.$http.adornUrl("/hotel/hotelactivityroomstock/info/".concat(r.dataForm.id)),method:"get",params:r.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(r.dataForm.hotelActivityRoomId=e.hotelActivityRoomStock.hotelActivityRoomId,r.dataForm.activityId=e.hotelActivityRoomStock.activityId,r.dataForm.hotelId=e.hotelActivityRoomStock.hotelId,r.dataForm.hotelActivityId=e.hotelActivityRoomStock.hotelActivityId,r.dataForm.stockDate=e.hotelActivityRoomStock.stockDate,r.dataForm.price=e.hotelActivityRoomStock.price,r.dataForm.bedPrice=e.hotelActivityRoomStock.bedPrice,r.dataForm.number=e.hotelActivityRoomStock.number,r.dataForm.alreadyNumber=e.hotelActivityRoomStock.alreadyNumber,r.dataForm.spareNumber=e.hotelActivityRoomStock.spareNumber,r.dataForm.createOn=e.hotelActivityRoomStock.createOn,r.dataForm.createBy=e.hotelActivityRoomStock.createBy,r.dataForm.updateOn=e.hotelActivityRoomStock.updateOn,r.dataForm.updateBy=e.hotelActivityRoomStock.updateBy)})):(r.dataForm.hotelActivityRoomId=e,r.dataForm.stockDate=a,r.getRoomInfo())}))},getRoomInfo:function(){var t=this;this.$http({url:this.$http.adornUrl("/hotel/hotelactivityroom/info/".concat(this.dataForm.hotelActivityRoomId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.hotelActivityRoom=a.hotelActivityRoom,t.dataForm.activityId=a.hotelActivityRoom.activityId,t.dataForm.hotelId=a.hotelActivityRoom.hotelId,t.dataForm.hotelActivityId=a.hotelActivityRoom.hotelActivityId,t.dataForm.price=a.hotelActivityRoom.price,t.dataForm.bedPrice=a.hotelActivityRoom.bedPrice)}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/hotel/hotelactivityroomstock/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,hotelActivityRoomId:t.dataForm.hotelActivityRoomId,activityId:t.dataForm.activityId,hotelId:t.dataForm.hotelId,hotelActivityId:t.dataForm.hotelActivityId,stockDate:t.dataForm.stockDate,price:t.dataForm.price,bedPrice:t.dataForm.bedPrice,number:t.dataForm.number,alreadyNumber:t.dataForm.alreadyNumber,spareNumber:t.dataForm.spareNumber})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))}}},l=n,d=a("2877"),c=Object(d["a"])(l,r,o,!1,null,null,null);e["default"]=c.exports},"34ae":function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i})),a.d(e,"d",(function(){return n}));var r=[{key:0,value:"整间"},{key:1,value:"男床位"},{key:2,value:"女床位"}],o=[{key:0,value:"整间"},{key:1,value:"拼住"},{key:2,value:"拼住"}],i=[{key:0,value:"已取消"},{key:1,value:"已入住"}],n=[{key:0,value:"未开启"},{key:1,value:"已开启"}]},"466d":function(t,e,a){"use strict";var r=a("c65b"),o=a("d784"),i=a("825a"),n=a("7234"),l=a("50c4"),d=a("577e"),c=a("1d80"),s=a("dc4a"),u=a("8aa5"),m=a("14c3");o("match",(function(t,e,a){return[function(e){var a=c(this),o=n(e)?void 0:s(e,t);return o?r(o,e,a):new RegExp(e)[t](d(a))},function(t){var r=i(this),o=d(t),n=a(e,r,o);if(n.done)return n.value;if(!r.global)return m(r,o);var c=r.unicode;r.lastIndex=0;var s,h=[],p=0;while(null!==(s=m(r,o))){var v=d(s[0]);h[p]=v,""===v&&(r.lastIndex=u(o,l(r.lastIndex),c)),p++}return 0===p?null:h}]}))},"7cab":function(t,e,a){"use strict";a.r(e);a("b0c0");var r=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"房型名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"房型名称"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),t.dataForm.id?t._e():e("el-form-item",{attrs:{label:"库存数量",prop:"stockNumber"}},[e("el-input",{attrs:{placeholder:"库存数量"},model:{value:t.dataForm.stockNumber,callback:function(e){t.$set(t.dataForm,"stockNumber",e)},expression:"dataForm.stockNumber"}},[e("template",{slot:"append"},[t._v(" 间 ")])],2)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"销售状态",prop:"status"}},[e("el-select",{attrs:{placeholder:"订单状态",filterable:""},model:{value:t.dataForm.status,callback:function(e){t.$set(t.dataForm,"status",e)},expression:"dataForm.status"}},t._l(t.saleStatus,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"床位销售状态",prop:"bedStatus"}},[e("el-select",{attrs:{disabled:t.dataForm.bedNumber<2,placeholder:"床位销售状态",filterable:""},model:{value:t.dataForm.bedStatus,callback:function(e){t.$set(t.dataForm,"bedStatus",e)},expression:"dataForm.bedStatus"}},t._l(t.saleStatus,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"单价",prop:"price"}},[e("el-input",{attrs:{placeholder:"单价"},on:{change:t.handleChange},model:{value:t.dataForm.price,callback:function(e){t.$set(t.dataForm,"price",e)},expression:"dataForm.price"}},[e("template",{slot:"append"},[t._v("RMB/间 ")])],2)],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"床位价格",prop:"bedPrice"}},[e("el-input",{attrs:{placeholder:"床位价格"},model:{value:t.dataForm.bedPrice,callback:function(e){t.$set(t.dataForm,"bedPrice",e)},expression:"dataForm.bedPrice"}},[e("template",{slot:"append"},[t._v(" RMB/床位 ")])],2)],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"床位数量",prop:"bedNumber"}},[e("el-input",{attrs:{placeholder:"床位数量"},on:{change:t.handleChange},model:{value:t.dataForm.bedNumber,callback:function(e){t.$set(t.dataForm,"bedNumber",e)},expression:"dataForm.bedNumber"}},[e("template",{slot:"append"},[t._v(" 床位/间 ")])],2)],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"排序",prop:"orderBy"}},[e("el-input",{attrs:{placeholder:"排序"},model:{value:t.dataForm.orderBy,callback:function(e){t.$set(t.dataForm,"orderBy",e)},expression:"dataForm.orderBy"}},[e("template",{slot:"append"},[t._v(" 值越小越靠前 ")])],2)],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"房间备注",prop:"roomRemarks"}},[e("el-input",{attrs:{placeholder:"房间备注"},model:{value:t.dataForm.roomRemarks,callback:function(e){t.$set(t.dataForm,"roomRemarks",e)},expression:"dataForm.roomRemarks"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"床位备注",prop:"bedRemarks"}},[e("el-input",{attrs:{placeholder:"床位备注"},model:{value:t.dataForm.bedRemarks,callback:function(e){t.$set(t.dataForm,"bedRemarks",e)},expression:"dataForm.bedRemarks"}})],1)],1)],1),t.dataForm.price>0?e("el-form-item",{attrs:{label:"微信支付",prop:"isWechatPay"}},[e("el-select",{model:{value:t.dataForm.isWechatPay,callback:function(e){t.$set(t.dataForm,"isWechatPay",e)},expression:"dataForm.isWechatPay"}},t._l(t.yesOrNo,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1):t._e(),t.dataForm.price>0?e("el-form-item",{attrs:{label:"支付宝支付",prop:"isAliPay"}},[e("el-select",{model:{value:t.dataForm.isAliPay,callback:function(e){t.$set(t.dataForm,"isAliPay",e)},expression:"dataForm.isAliPay"}},t._l(t.yesOrNo,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1):t._e(),t.dataForm.price>0?e("el-form-item",{attrs:{label:"银行转账",prop:"isBankTransfer"}},[e("el-select",{model:{value:t.dataForm.isBankTransfer,callback:function(e){t.$set(t.dataForm,"isBankTransfer",e)},expression:"dataForm.isBankTransfer"}},t._l(t.yesOrNo,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1):t._e(),1==t.dataForm.isBankTransfer?e("el-form-item",{attrs:{label:"银行转账信息",prop:"bankTransferNotify"}},[e("tinymce-editor",{ref:"editor",model:{value:t.dataForm.bankTransferNotify,callback:function(e){t.$set(t.dataForm,"bankTransferNotify",e)},expression:"dataForm.bankTransferNotify"}})],1):t._e()],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},o=[],i=(a("d9e2"),a("d3b7"),a("3ca3"),a("ddb0"),a("61f7")),n=a("7de9"),l=a("34ae"),d={components:{TinymceEditor:function(){return Promise.all([a.e("chunk-2d0e1c2e"),a.e("chunk-03be236c"),a.e("chunk-2d0a4b8c")]).then(a.bind(null,"26dc"))}},data:function(){var t=function(t,e,a){Object(i["d"])(e)?a():a(new Error("必须为数字"))},e=function(t,e,a){Object(i["b"])(e)?a():a(new Error("必须为数字"))};return{yesOrNo:n["g"],visible:!1,saleStatus:l["d"],activityInfo:{},dataForm:{id:0,name:"",activityId:"",hotelId:"",hotelActivityId:"",status:0,orderBy:0,bedStatus:0,price:0,stockNumber:0,bedPrice:0,bedNumber:1,isWechatPay:0,isAliPay:0,isBankTransfer:0,bankTransferNotify:"",roomRemarks:"",bedRemarks:""},dataRule:{name:[{required:!0,message:"房型名称不能为空",trigger:"blur"}],status:[{required:!0,message:"销售状态不能为空",trigger:"blur"}],bedStatus:[{required:!0,message:"床位销售状态不能为空",trigger:"blur"}],price:[{required:!0,message:"单价不能为空",trigger:"blur"},{validator:e,trigger:"blur"}],bedPrice:[{required:!0,message:"床位价格不能为空",trigger:"blur"},{validator:e,trigger:"blur"}],bedNumber:[{required:!0,message:"床位数量不能为空",trigger:"blur"},{validator:t,trigger:"blur"}],stockNumber:[{required:!0,message:"库存数量不能为空",trigger:"blur"},{validator:t,trigger:"blur"}]}}},methods:{init:function(t,e,a,r){var o=this;this.dataForm.id=t||0,this.dataForm.activityId=e,this.dataForm.hotelActivityId=a,this.dataForm.hotelId=r,this.visible=!0,this.$nextTick((function(){o.$refs["dataForm"].resetFields(),o.dataForm.id&&o.$http({url:o.$http.adornUrl("/hotel/hotelactivityroom/info/".concat(o.dataForm.id)),method:"get",params:o.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(o.dataForm.name=e.hotelActivityRoom.name,o.dataForm.activityId=e.hotelActivityRoom.activityId,o.dataForm.hotelId=e.hotelActivityRoom.hotelId,o.dataForm.hotelActivityId=e.hotelActivityRoom.hotelActivityId,o.dataForm.status=e.hotelActivityRoom.status,o.dataForm.orderBy=e.hotelActivityRoom.orderBy,o.dataForm.bedStatus=e.hotelActivityRoom.bedStatus,o.dataForm.price=e.hotelActivityRoom.price,o.dataForm.bedPrice=e.hotelActivityRoom.bedPrice,o.dataForm.bedNumber=e.hotelActivityRoom.bedNumber,o.dataForm.inDate=e.hotelActivityRoom.inDate,o.dataForm.outDate=e.hotelActivityRoom.outDate,o.dataForm.bankTransferNotify=e.hotelActivityRoom.bankTransferNotify,o.dataForm.isWechatPay=e.hotelActivityRoom.isWechatPay,o.dataForm.isAliPay=e.hotelActivityRoom.isAliPay,o.dataForm.isBankTransfer=e.hotelActivityRoom.isBankTransfer,o.dataForm.roomRemarks=e.hotelActivityRoom.roomRemarks,o.dataForm.bedRemarks=e.hotelActivityRoom.bedRemarks)}))}))},handleChange:function(){this.dataForm.bedNumber<2&&(this.dataForm.bedStatus=0),this.dataForm.bedPrice=this.dataForm.price/this.dataForm.bedNumber},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/hotel/hotelactivityroom/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,name:t.dataForm.name,activityId:t.dataForm.activityId,hotelId:t.dataForm.hotelId,hotelActivityId:t.dataForm.hotelActivityId,stockNumber:t.dataForm.stockNumber,status:t.dataForm.status,orderBy:t.dataForm.orderBy,bedStatus:t.dataForm.bedStatus,price:t.dataForm.price,bedPrice:t.dataForm.bedPrice,bedNumber:t.dataForm.bedNumber,bankTransferNotify:t.dataForm.bankTransferNotify,isWechatPay:t.dataForm.isWechatPay,isAliPay:t.dataForm.isAliPay,isBankTransfer:t.dataForm.isBankTransfer,roomRemarks:t.dataForm.roomRemarks,bedRemarks:t.dataForm.bedRemarks})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))},getActivity:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activity/info/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.activityInfo=a.activity)}))}}},c=d,s=a("2877"),u=Object(s["a"])(c,r,o,!1,null,null,null);e["default"]=u.exports},"7de9":function(t,e,a){"use strict";a.d(e,"g",(function(){return r})),a.d(e,"f",(function(){return o})),a.d(e,"e",(function(){return i})),a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return l})),a.d(e,"c",(function(){return d})),a.d(e,"d",(function(){return c}));var r=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],o=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],i=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],n=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],l=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],d=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],c=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]},a15b:function(t,e,a){"use strict";var r=a("23e7"),o=a("e330"),i=a("44ad"),n=a("fc6a"),l=a("a640"),d=o([].join),c=i!==Object,s=c||!l("join",",");r({target:"Array",proto:!0,forced:s},{join:function(t){return d(n(this),void 0===t?",":t)}})},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var r=a("23e7"),o=a("d024"),i=a("c430");r({target:"Iterator",proto:!0,real:!0,forced:i},{map:o})},c466:function(t,e,a){"use strict";a.d(e,"c",(function(){return l})),a.d(e,"a",(function(){return d})),a.d(e,"b",(function(){return c}));a("ac1f"),a("466d"),a("5319");var r=/([yMdhsm])(\1*)/g,o="yyyy/MM/dd",i="yyyy/MM/dd hh:mm:ss";function n(t,e){e-=(t+"").length;for(var a=0;a<e;a++)t="0"+t;return t}function l(t,e){return e=e||o,e.replace(r,(function(e){switch(e.charAt(0)){case"y":return n(t.getFullYear(),e.length);case"M":return n(t.getMonth()+1,e.length);case"d":return n(t.getDate(),e.length);case"w":return t.getDay()+1;case"h":return n(t.getHours(),e.length);case"m":return n(t.getMinutes(),e.length);case"s":return n(t.getSeconds(),e.length)}}))}function d(t,e){var a=new Date(t),r=new Date(a.getTime()+24*e*60*60*1e3);return l(r,i)}function c(t,e,a){var r=new Date(t),o=new Date(r.getTime()+60*e*1e3);return l(o,a||i)}},d024:function(t,e,a){"use strict";var r=a("c65b"),o=a("59ed"),i=a("825a"),n=a("46c4"),l=a("c5cc"),d=a("9bdd"),c=l((function(){var t=this.iterator,e=i(r(this.next,t)),a=this.done=!!e.done;if(!a)return d(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return i(this),o(t),new c(n(this),{mapper:t})}},d81d:function(t,e,a){"use strict";var r=a("23e7"),o=a("b727").map,i=a("1dde"),n=i("map");r({target:"Array",proto:!0,forced:!n},{map:function(t){return o(this,t,arguments.length>1?arguments[1]:void 0)}})},e6bd:function(t,e,a){"use strict";a.r(e);a("b0c0");var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("div",{staticStyle:{"text-align":"center",padding:"20px","font-weight":"bold","font-size":"28px"}},[t._v(t._s(t.hotelActivityInfo.hotelName)+"的房型列表")]),e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"房型名称",clearable:""},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),t.isAuth("hotel:hotelactivityroom:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle(void 0,t.dataForm.activityId,t.dataForm.hotelActivityId,t.hotelActivityInfo.hotelId)}}},[t._v("新增")]):t._e(),t.isAuth("hotel:hotelactivityroom:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{"default-expand-all":!0,data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"expand"},scopedSlots:t._u([{key:"default",fn:function(a){return a.row.hotelActivityRoomStockEntities?[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:a.row.hotelActivityRoomStockEntities,border:""}},[e("el-table-column",{attrs:{prop:"stockDate","header-align":"center",align:"center",label:"库存日期"}}),e("el-table-column",{attrs:{prop:"number","header-align":"center",align:"center",label:"总库存"}}),e("el-table-column",{attrs:{prop:"spareNumber","header-align":"center",align:"center",label:"剩余库存"}}),e("el-table-column",{attrs:{prop:"alreadyNumber","header-align":"center",align:"center",label:"已售库存"}}),e("el-table-column",{attrs:{prop:"price","header-align":"center",align:"center",label:"单价"}}),e("el-table-column",{attrs:{prop:"bedPrice","header-align":"center",align:"center",label:"床位价格"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"200",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(r){return[0==r.$index?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.stockBeforeHandle(a.row.id,r.row.stockDate)}}},[t._v("往前加一天")]):t._e(),r.$index==a.row.hotelActivityRoomStockEntities.length-1?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.stockAfterHandle(a.row.id,r.row.stockDate)}}},[t._v("往后加一天")]):t._e(),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.stockUpdateHandle(r.row.id)}}},[t._v("修改")]),r.$index==a.row.hotelActivityRoomStockEntities.length-1||0==r.$index?e("el-button",{staticStyle:{color:"red"},attrs:{type:"text",size:"small"},on:{click:function(e){return t.stockDeleteHandle(r.row.id)}}},[t._v("删除")]):t._e()]}}],null,!0)})],1)]:void 0}}],null,!0)}),e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"房型名称"}}),e("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",label:"销售状态"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-tag",{class:"tag-color tag-color-"+a.row.status,attrs:{type:"primary"}},[t._v(t._s(t._f("statusFilter")(a.row.status)))])],1)}}])}),e("el-table-column",{attrs:{prop:"bedStatus","header-align":"center",align:"center",label:"床位销售状态"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-tag",{class:"tag-color tag-color-"+a.row.bedStatus,attrs:{type:"primary"}},[t._v(t._s(t._f("statusFilter")(a.row.bedStatus)))])],1)}}])}),e("el-table-column",{attrs:{prop:"price","header-align":"center",align:"center",label:"单价"}}),e("el-table-column",{attrs:{prop:"bedPrice","header-align":"center",align:"center",label:"床位价格"}}),e("el-table-column",{attrs:{prop:"bedNumber","header-align":"center",align:"center",label:"床位数量"}}),e("el-table-column",{attrs:{prop:"inDate","header-align":"center",align:"center",label:"库存开始时间"}}),e("el-table-column",{attrs:{prop:"outDate","header-align":"center",align:"center",label:"库存结束时间"}}),e("el-table-column",{attrs:{prop:"orderBy","header-align":"center",align:"center",label:"排序"}}),e("el-table-column",{attrs:{"show-overflow-tooltip":"",prop:"createOn","header-align":"center",align:"center",label:"创建时间"}}),e("el-table-column",{attrs:{"show-overflow-tooltip":"",prop:"updateOn","header-align":"center",align:"center",label:"更新时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.number(a.row.id)}}},[t._v("房号管理")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id,t.dataForm.activityId,t.dataForm.hotelActivityId,t.hotelActivityInfo.hotelId)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e(),t.stockAddOrUpdateVisible?e("stock-add-or-update",{ref:"stockAddOrUpdate",on:{refreshDataList:t.getDataList}}):t._e()],1)},o=[],i=(a("99af"),a("4de4"),a("a15b"),a("d81d"),a("14d9"),a("d3b7"),a("0643"),a("2382"),a("a573"),a("7cab")),n=a("2e66"),l=a("34ae"),d=a("c466"),c={data:function(){return{dataForm:{name:"",activityId:"",hotelActivityId:""},dataList:[],hotelActivityInfo:{},pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,stockAddOrUpdateVisible:!1}},components:{AddOrUpdate:i["default"],StockAddOrUpdate:n["default"]},activated:function(){this.dataForm.activityId=this.$route.query.activityId,this.dataForm.hotelActivityId=this.$route.query.hotelActivityId,this.getDataList(),this.getHotelActivityInfo()},filters:{statusFilter:function(t){var e=l["d"].filter((function(e){return e.key===t}));if(e.length>=1)return e[0].value}},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/hotel/hotelactivityroom/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,activityId:this.dataForm.activityId,hotelActivityId:this.dataForm.hotelActivityId,name:this.dataForm.name})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t,e,a,r){var o=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){o.$refs.addOrUpdate.init(t,e,a,r)}))},stockUpdateHandle:function(t){var e=this;this.stockAddOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.stockAddOrUpdate.init(t,void 0,void 0)}))},stockBeforeHandle:function(t,e){var a=this,r=Object(d["a"])(e,-1);this.stockAddOrUpdateVisible=!0,this.$nextTick((function(){a.$refs.stockAddOrUpdate.init(void 0,t,r)}))},stockAfterHandle:function(t,e){var a=this,r=Object(d["a"])(e,1);this.stockAddOrUpdateVisible=!0,this.$nextTick((function(){a.$refs.stockAddOrUpdate.init(void 0,t,r)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/hotel/hotelactivityroom/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},stockDeleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定删除操作操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/hotel/hotelactivityroomstock/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},getHotelActivityInfo:function(){var t=this;this.$http({url:this.$http.adornUrl("/hotel/hotelactivity/info/".concat(this.dataForm.hotelActivityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.hotelActivityInfo=a.hotelActivity)}))},number:function(t){this.$router.push({name:"hotelactivityroomnumber",query:{activityId:this.dataForm.activityId,hotelActivityRoomId:t}})}}},s=c,u=a("2877"),m=Object(u["a"])(s,r,o,!1,null,null,null);e["default"]=m.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-cf5b3cc8","chunk-60feecd6","chunk-0506e191","chunk-0506e191"],{"0ccb":function(t,a,e){"use strict";var n=e("e330"),s=e("50c4"),l=e("577e"),i=e("1148"),r=e("1d80"),o=n(i),d=n("".slice),c=Math.ceil,m=function(t){return function(a,e,n){var i,m,u=l(r(a)),p=s(e),h=u.length,f=void 0===n?" ":l(n);return p<=h||""===f?u:(i=p-h,m=o(f,c(i/f.length)),m.length>i&&(m=d(m,0,i)),t?u+m:m+u)}};t.exports={start:m(!1),end:m(!0)}},1148:function(t,a,e){"use strict";var n=e("5926"),s=e("577e"),l=e("1d80"),i=RangeError;t.exports=function(t){var a=s(l(this)),e="",r=n(t);if(r<0||r===1/0)throw new i("Wrong number of repetitions");for(;r>0;(r>>>=1)&&(a+=a))1&r&&(e+=a);return e}},"11d1":function(t,a,e){},"498a":function(t,a,e){"use strict";var n=e("23e7"),s=e("58a8").trim,l=e("c8d2");n({target:"String",proto:!0,forced:l("trim")},{trim:function(){return s(this)}})},"4d90":function(t,a,e){"use strict";var n=e("23e7"),s=e("0ccb").start,l=e("9a0c");n({target:"String",proto:!0,forced:l},{padStart:function(t){return s(this,t,arguments.length>1?arguments[1]:void 0)}})},"7bfa":function(t,a,e){"use strict";e.r(a);e("b0c0");var n=function(){var t=this,a=t._self._c;return a("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(a){t.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&t._k(a.keyCode,"enter",13,a.key,"Enter")?null:t.dataFormSubmit()}}},[a("el-form-item",{attrs:{label:"姓名",prop:"name"}},[a("el-input",{attrs:{placeholder:"业务员姓名"},model:{value:t.dataForm.name,callback:function(a){t.$set(t.dataForm,"name",a)},expression:"dataForm.name"}})],1),a("el-form-item",{attrs:{label:"编号",prop:"code"}},[a("el-input",{attrs:{placeholder:"业务员编号"},model:{value:t.dataForm.code,callback:function(a){t.$set(t.dataForm,"code",a)},expression:"dataForm.code"}},[a("el-button",{attrs:{slot:"append",loading:t.generating},on:{click:t.generateCode},slot:"append"},[t._v(" "+t._s(t.generating?"生成中":"自动生成")+" ")])],1)],1),a("el-form-item",{attrs:{label:"手机号",prop:"mobile"}},[a("el-input",{attrs:{placeholder:"手机号"},model:{value:t.dataForm.mobile,callback:function(a){t.$set(t.dataForm,"mobile",a)},expression:"dataForm.mobile"}})],1),a("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[a("el-input",{attrs:{placeholder:"邮箱"},model:{value:t.dataForm.email,callback:function(a){t.$set(t.dataForm,"email",a)},expression:"dataForm.email"}})],1),a("el-form-item",{attrs:{label:"所属渠道",prop:"channelId"}},[a("el-select",{attrs:{placeholder:"请选择所属渠道",clearable:""},model:{value:t.dataForm.channelId,callback:function(a){t.$set(t.dataForm,"channelId",a)},expression:"dataForm.channelId"}},t._l(t.channelList,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),a("el-form-item",{attrs:{label:"部门",prop:"department"}},[a("el-input",{attrs:{placeholder:"部门"},model:{value:t.dataForm.department,callback:function(a){t.$set(t.dataForm,"department",a)},expression:"dataForm.department"}})],1),a("el-form-item",{attrs:{label:"职位",prop:"position"}},[a("el-input",{attrs:{placeholder:"职位"},model:{value:t.dataForm.position,callback:function(a){t.$set(t.dataForm,"position",a)},expression:"dataForm.position"}})],1),a("el-form-item",{attrs:{label:"上级业务员",prop:"parentId"}},[a("el-select",{attrs:{placeholder:"请选择上级业务员",clearable:""},model:{value:t.dataForm.parentId,callback:function(a){t.$set(t.dataForm,"parentId",a)},expression:"dataForm.parentId"}},t._l(t.parentSalesmanList,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:t.dataForm.status,callback:function(a){t.$set(t.dataForm,"status",a)},expression:"dataForm.status"}},[a("el-radio",{attrs:{label:0}},[t._v("禁用")]),a("el-radio",{attrs:{label:1}},[t._v("启用")])],1)],1),a("el-form-item",{attrs:{label:"标签",prop:"tags"}},[a("el-input",{attrs:{placeholder:"标签，多个用逗号分隔"},model:{value:t.dataForm.tags,callback:function(a){t.$set(t.dataForm,"tags",a)},expression:"dataForm.tags"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remarks"}},[a("el-input",{attrs:{type:"textarea",placeholder:"备注信息"},model:{value:t.dataForm.remarks,callback:function(a){t.$set(t.dataForm,"remarks",a)},expression:"dataForm.remarks"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){t.visible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},s=[],l=(e("99af"),e("4de4"),e("d3b7"),e("4d90"),e("0643"),e("2382"),{data:function(){return{visible:!1,dataForm:{id:0,name:"",code:"",mobile:"",email:"",channelId:"",department:"",position:"",parentId:"",status:1,tags:"",remarks:""},channelList:[],parentSalesmanList:[],generating:!1,dataRule:{name:[{required:!0,message:"业务员姓名不能为空",trigger:"blur"}],code:[{required:!0,message:"业务员编号不能为空",trigger:"blur"}],mobile:[{required:!0,message:"手机号不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确",trigger:"blur"}],email:[{type:"email",message:"邮箱格式不正确",trigger:"blur"}],channelId:[{required:!0,message:"请选择所属渠道",trigger:"change"}]}}},methods:{init:function(t){var a=this;this.dataForm.id=t||0,this.visible=!0,this.getChannelList(),this.getParentSalesmanList(),this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/channel/salesman/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code&&(a.dataForm.name=e.salesman.name,a.dataForm.code=e.salesman.code,a.dataForm.mobile=e.salesman.mobile,a.dataForm.email=e.salesman.email,a.dataForm.channelId=e.salesman.channelId,a.dataForm.department=e.salesman.department,a.dataForm.position=e.salesman.position,a.dataForm.parentId=e.salesman.parentId,a.dataForm.status=e.salesman.status,a.dataForm.tags=e.salesman.tags,a.dataForm.remarks=e.salesman.remarks)}))}))},getChannelList:function(){var t=this;this.$http({url:this.$http.adornUrl("/channel/channel/select"),method:"get",params:this.$http.adornParams()}).then((function(a){var e=a.data;e&&200===e.code&&(t.channelList=e.channelList||[])}))},getParentSalesmanList:function(){var t=this;this.$http({url:this.$http.adornUrl("/salesman/salesman/findByAppid"),method:"get",params:this.$http.adornParams()}).then((function(a){var e=a.data;e&&200===e.code&&(t.parentSalesmanList=e.result||[],t.dataForm.id&&(t.parentSalesmanList=t.parentSalesmanList.filter((function(a){return a.id!==t.dataForm.id}))))}))},generateCode:function(){var t=this;this.generating=!0;var a=new Date,e=a.getFullYear(),n=String(a.getMonth()+1).padStart(2,"0"),s=String(a.getDate()).padStart(2,"0"),l=String(Math.floor(1e4*Math.random())).padStart(4,"0"),i="YW".concat(e).concat(n).concat(s).concat(l);this.$http({url:this.$http.adornUrl("/channel/salesman/checkCode"),method:"get",params:this.$http.adornParams({code:i,excludeId:this.dataForm.id||null})}).then((function(a){var e=a.data;t.generating=!1,e&&200===e.code&&e.exists?t.generateCode():(t.dataForm.code=i,t.$message.success("编号生成成功"))})).catch((function(){t.generating=!1,t.dataForm.code=i,t.$message.success("编号生成成功")}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(a){a&&t.$http({url:t.$http.adornUrl("/channel/salesman/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,name:t.dataForm.name,code:t.dataForm.code,mobile:t.dataForm.mobile,email:t.dataForm.email,channelId:t.dataForm.channelId,department:t.dataForm.department,position:t.dataForm.position,parentId:t.dataForm.parentId||null,status:t.dataForm.status,tags:t.dataForm.tags,remarks:t.dataForm.remarks})}).then((function(a){var e=a.data;e&&200===e.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(e.msg)}))}))}}}),i=l,r=e("2877"),o=Object(r["a"])(i,n,s,!1,null,null,null);a["default"]=o.exports},"9a0c":function(t,a,e){"use strict";var n=e("b5db");t.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(n)},a15b:function(t,a,e){"use strict";var n=e("23e7"),s=e("e330"),l=e("44ad"),i=e("fc6a"),r=e("a640"),o=s([].join),d=l!==Object,c=d||!r("join",",");n({target:"Array",proto:!0,forced:c},{join:function(t){return o(i(this),void 0===t?",":t)}})},a573:function(t,a,e){"use strict";e("ab43")},ab43:function(t,a,e){"use strict";var n=e("23e7"),s=e("d024"),l=e("c430");n({target:"Iterator",proto:!0,real:!0,forced:l},{map:s})},b680:function(t,a,e){"use strict";var n=e("23e7"),s=e("e330"),l=e("5926"),i=e("408a"),r=e("1148"),o=e("d039"),d=RangeError,c=String,m=Math.floor,u=s(r),p=s("".slice),h=s(1..toFixed),f=function(t,a,e){return 0===a?e:a%2===1?f(t,a-1,e*t):f(t*t,a/2,e)},g=function(t){var a=0,e=t;while(e>=4096)a+=12,e/=4096;while(e>=2)a+=1,e/=2;return a},v=function(t,a,e){var n=-1,s=e;while(++n<6)s+=a*t[n],t[n]=s%1e7,s=m(s/1e7)},b=function(t,a){var e=6,n=0;while(--e>=0)n+=t[e],t[e]=m(n/a),n=n%a*1e7},_=function(t){var a=6,e="";while(--a>=0)if(""!==e||0===a||0!==t[a]){var n=c(t[a]);e=""===e?n:e+u("0",7-n.length)+n}return e},F=o((function(){return"0.000"!==h(8e-5,3)||"1"!==h(.9,0)||"1.25"!==h(1.255,2)||"1000000000000000128"!==h(0xde0b6b3a7640080,0)}))||!o((function(){h({})}));n({target:"Number",proto:!0,forced:F},{toFixed:function(t){var a,e,n,s,r=i(this),o=l(t),m=[0,0,0,0,0,0],h="",F="0";if(o<0||o>20)throw new d("Incorrect fraction digits");if(r!==r)return"NaN";if(r<=-1e21||r>=1e21)return c(r);if(r<0&&(h="-",r=-r),r>1e-21)if(a=g(r*f(2,69,1))-69,e=a<0?r*f(2,-a,1):r/f(2,a,1),e*=4503599627370496,a=52-a,a>0){v(m,0,e),n=o;while(n>=7)v(m,1e7,0),n-=7;v(m,f(10,n,1),0),n=a-1;while(n>=23)b(m,1<<23),n-=23;b(m,1<<n),v(m,1,1),b(m,2),F=_(m)}else v(m,0,e),v(m,1<<-a,0),F=_(m)+u("0",o);return o>0?(s=F.length,F=h+(s<=o?"0."+u("0",o-s)+F:p(F,0,s-o)+"."+p(F,s-o))):F=h+F,F}})},bbc09:function(t,a,e){"use strict";e("11d1")},c8d2:function(t,a,e){"use strict";var n=e("5e77").PROPER,s=e("d039"),l=e("5899"),i="​᠎";t.exports=function(t){return s((function(){return!!l[t]()||i[t]()!==i||n&&l[t].name!==t}))}},d024:function(t,a,e){"use strict";var n=e("c65b"),s=e("59ed"),l=e("825a"),i=e("46c4"),r=e("c5cc"),o=e("9bdd"),d=r((function(){var t=this.iterator,a=l(n(this.next,t)),e=this.done=!!a.done;if(!e)return o(t,this.mapper,[a.value,this.counter++],!0)}));t.exports=function(t){return l(this),s(t),new d(i(this),{mapper:t})}},d5b5:function(t,a,e){"use strict";e.r(a);e("b0c0"),e("b680");var n=function(){var t=this,a=t._self._c;return a("div",{staticClass:"mod-channel-salesman"},[a("el-form",{attrs:{inline:!0,model:t.dataForm},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&t._k(a.keyCode,"enter",13,a.key,"Enter")?null:t.getDataList()}}},[a("el-form-item",[a("el-input",{attrs:{placeholder:"业务员姓名",clearable:""},model:{value:t.dataForm.name,callback:function(a){t.$set(t.dataForm,"name",a)},expression:"dataForm.name"}})],1),a("el-form-item",[a("el-input",{attrs:{placeholder:"手机号",clearable:""},model:{value:t.dataForm.mobile,callback:function(a){t.$set(t.dataForm,"mobile",a)},expression:"dataForm.mobile"}})],1),a("el-form-item",[a("el-button",{on:{click:function(a){return t.getDataList()}}},[t._v("查询")]),t.isAuth("channel:salesman:save")?a("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),t.isAuth("channel:salesman:delete")?a("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(a){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[a("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),a("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"姓名"}}),a("el-table-column",{attrs:{prop:"code","header-align":"center",align:"center",label:"编号"}}),a("el-table-column",{attrs:{prop:"mobile","header-align":"center",align:"center",label:"手机号"}}),a("el-table-column",{attrs:{prop:"channelName","header-align":"center",align:"center",label:"所属渠道"}}),a("el-table-column",{attrs:{prop:"department","header-align":"center",align:"center",label:"部门"}}),a("el-table-column",{attrs:{prop:"position","header-align":"center",align:"center",label:"职位"}}),a("el-table-column",{attrs:{prop:"parentName","header-align":"center",align:"center",label:"上级业务员"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.parentName||"无"))])]}}])}),a("el-table-column",{attrs:{prop:"level","header-align":"center",align:"center",label:"层级"}}),a("el-table-column",{attrs:{prop:"totalOrders","header-align":"center",align:"center",label:"订单数"}}),a("el-table-column",{attrs:{prop:"totalAmount","header-align":"center",align:"center",label:"销售额"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v("¥"+t._s((e.row.totalAmount||0).toFixed(2)))])]}}])}),a("el-table-column",{attrs:{prop:"totalCommission","header-align":"center",align:"center",label:"佣金"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v("¥"+t._s((e.row.totalCommission||0).toFixed(2)))])]}}])}),a("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",label:"状态"},scopedSlots:t._u([{key:"default",fn:function(e){return[0===e.row.status?a("el-tag",{attrs:{size:"small",type:"danger"}},[t._v("禁用")]):a("el-tag",{attrs:{size:"small",type:"success"}},[t._v("启用")])]}}])}),a("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",width:"180",label:"创建时间"}}),a("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"200",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[t.isAuth("channel:salesman:update")?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.addOrUpdateHandle(e.row.id)}}},[t._v("修改")]):t._e(),t.isAuth("channel:salesman:delete")?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.deleteHandle(e.row.id)}}},[t._v("删除")]):t._e(),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.viewDetailsHandle(e.row)}}},[t._v("详情")]),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.commissionConfigHandle(e.row.id,e.row.name)}}},[t._v("佣金配置")])]}}])})],1),a("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.addOrUpdateVisible?a("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e(),a("el-dialog",{staticClass:"clean-dialog",attrs:{title:"业务员详情",visible:t.detailsDialogVisible,width:"70%","close-on-click-modal":!1},on:{"update:visible":function(a){t.detailsDialogVisible=a}}},[a("div",{staticClass:"header-info"},[a("div",{staticClass:"left-info"},[a("h3",{staticClass:"name"},[t._v(t._s(t.selectedSalesman.name))]),a("div",{staticClass:"meta-info"},[a("span",{staticClass:"code"},[t._v(t._s(t.selectedSalesman.code))]),a("el-divider",{attrs:{direction:"vertical"}}),a("span",{staticClass:"channel"},[t._v(t._s(t.selectedSalesman.channelName||"未分配渠道"))]),a("el-divider",{attrs:{direction:"vertical"}}),a("el-tag",{attrs:{type:1===t.selectedSalesman.status?"success":"danger",size:"mini"}},[t._v(" "+t._s(1===t.selectedSalesman.status?"启用":"禁用")+" ")])],1)]),a("div",{staticClass:"right-info"},[a("el-button",{attrs:{size:"small"},on:{click:function(a){return t.editSalesman(t.selectedSalesman)}}},[t._v("编辑")])],1)]),a("el-row",{staticClass:"data-cards",attrs:{gutter:15}},[a("el-col",{attrs:{span:6}},[a("div",{staticClass:"data-card"},[a("div",{staticClass:"number"},[t._v(t._s(t.selectedSalesman.totalOrders||0))]),a("div",{staticClass:"label"},[t._v("总订单")])])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"data-card"},[a("div",{staticClass:"number"},[t._v("¥"+t._s((t.selectedSalesman.totalAmount||0).toFixed(0)))]),a("div",{staticClass:"label"},[t._v("销售额")])])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"data-card"},[a("div",{staticClass:"number"},[t._v("¥"+t._s((t.selectedSalesman.totalCommission||0).toFixed(0)))]),a("div",{staticClass:"label"},[t._v("佣金")])])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"data-card"},[a("div",{staticClass:"number"},[t._v(t._s(t.selectedSalesman.childrenCount||0))]),a("div",{staticClass:"label"},[t._v("下级")])])])],1),a("div",{staticClass:"detail-section"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("div",{staticClass:"info-group"},[a("h4",[t._v("基本信息")]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("手机号：")]),a("span",{staticClass:"value"},[t._v(t._s(t.selectedSalesman.mobile))])]),t.selectedSalesman.email?a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("邮箱：")]),a("span",{staticClass:"value"},[t._v(t._s(t.selectedSalesman.email))])]):t._e(),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("部门：")]),a("span",{staticClass:"value"},[t._v(t._s(t.selectedSalesman.department||"未分配"))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("职位：")]),a("span",{staticClass:"value"},[t._v(t._s(t.selectedSalesman.position||"业务员"))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("上级：")]),a("span",{staticClass:"value"},[t._v(t._s(t.selectedSalesman.parentName||"无"))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("层级：")]),a("span",{staticClass:"value"},[t._v("第"+t._s(t.selectedSalesman.level||1)+"级")])])])]),a("el-col",{attrs:{span:12}},[a("div",{staticClass:"info-group"},[a("h4",[t._v("业绩详情")]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("活动订单：")]),a("span",{staticClass:"value"},[t._v(t._s(t.selectedSalesman.activityOrders||0)+"笔")])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("充值订单：")]),a("span",{staticClass:"value"},[t._v(t._s(t.selectedSalesman.rechargeOrders||0)+"笔")])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("佣金比例：")]),a("span",{staticClass:"value"},[t._v(" "+t._s(t.selectedSalesman.totalAmount>0?(t.selectedSalesman.totalCommission/t.selectedSalesman.totalAmount*100).toFixed(2)+"%":"0%")+" ")])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("创建时间：")]),a("span",{staticClass:"value"},[t._v(t._s(t.selectedSalesman.createOn))])]),t.selectedSalesman.tags?a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("标签：")]),a("span",{staticClass:"value"},t._l(t.getTagList(t.selectedSalesman.tags),(function(e){return a("el-tag",{key:e,staticStyle:{"margin-right":"5px"},attrs:{size:"mini"}},[t._v(" "+t._s(e)+" ")])})),1)]):t._e()])])],1),t.selectedSalesman.remarks?a("div",{staticClass:"info-group"},[a("h4",[t._v("备注")]),a("div",{staticClass:"remarks"},[t._v(t._s(t.selectedSalesman.remarks))])]):t._e()],1)],1)],1)},s=[],l=(e("99af"),e("4de4"),e("a15b"),e("d81d"),e("14d9"),e("d3b7"),e("498a"),e("0643"),e("2382"),e("a573"),e("7bfa")),i={components:{AddOrUpdate:l["default"]},data:function(){return{dataForm:{name:"",mobile:"",channelId:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],channelList:[],addOrUpdateVisible:!1,detailsDialogVisible:!1,selectedSalesman:{},activeDetailTab:"basic"}},activated:function(){this.dataForm.channelId=this.$route.query.channelId,this.getDataList()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/channel/salesman/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,name:this.dataForm.name,mobile:this.dataForm.mobile,channelId:this.dataForm.channelId})}).then((function(a){var e=a.data;e&&200===e.code?(t.dataList=e.page.list,t.totalPage=e.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},getChannelList:function(){var t=this;this.$http({url:this.$http.adornUrl("/channel/channel/select"),method:"get",params:this.$http.adornParams()}).then((function(a){var e=a.data;e&&200===e.code&&(t.channelList=e.channelList||[])}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){var a=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){a.$refs.addOrUpdate.init(t)}))},viewDetailsHandle:function(t){this.selectedSalesman=t,this.activeDetailTab="basic",this.detailsDialogVisible=!0},editSalesman:function(t){this.detailsDialogVisible=!1,this.addOrUpdateHandle(t.id)},getLevelTagType:function(t){return t<=1?"success":t<=3?"primary":t<=5?"warning":"danger"},getTagList:function(t){return t?t.split(",").map((function(t){return t.trim()})).filter((function(t){return t})):[]},deleteHandle:function(t){var a=this,e=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(e.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.$http({url:a.$http.adornUrl("/channel/salesman/delete"),method:"post",data:a.$http.adornData(e,!1)}).then((function(t){var e=t.data;e&&200===e.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.getDataList()}}):a.$message.error(e.msg)}))}))},commissionConfigHandle:function(t,a){this.$router.push({path:"/salesman-commission-config",query:{salesmanId:t,salesmanName:a}})}}},r=i,o=(e("bbc09"),e("2877")),d=Object(o["a"])(r,n,s,!1,null,"6da25a5d",null);a["default"]=d.exports},d81d:function(t,a,e){"use strict";var n=e("23e7"),s=e("b727").map,l=e("1dde"),i=l("map");n({target:"Array",proto:!0,forced:!i},{map:function(t){return s(this,t,arguments.length>1?arguments[1]:void 0)}})}}]);
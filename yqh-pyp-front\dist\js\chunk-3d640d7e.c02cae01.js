(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3d640d7e"],{"0dfe":function(t,e,n){"use strict";n("a5ae")},a5ae:function(t,e,n){},e7ca:function(t,e,n){"use strict";n.r(e);var o=function(){var t=this,e=t._self._c;return e("div",[t.merchantInfo.paramValue?e("div",{staticClass:"content",domProps:{innerHTML:t._s(t.merchantInfo.paramValue)},on:{click:function(e){return t.showImg(e)}}}):e("van-empty",{attrs:{description:"暂无信息"}})],1)},a=[],c={components:{},data:function(){return{openid:void 0,merchantInfo:{}}},mounted:function(){document.title="公司介绍",this.$wxShare(this.$cookie.get("accountName"),this.$cookie.get("logo"),this.$cookie.get("slog")),this.getCmsInfo()},methods:{getCmsInfo:function(){var t=this;this.$fly.get("/pyp/web/config/findParamKey",{paramKey:"company"}).then((function(e){200==e.code?t.merchantInfo=e.result:(vant.Toast(e.msg),t.merchantInfo={})}))},showImg:function(t){"IMG"==t.target.tagName&&t.target.src&&vant.ImagePreview({images:[t.target.src],closeable:!0})}}},i=c,s=(n("0dfe"),n("2877")),r=Object(s["a"])(i,o,a,!1,null,"7b00f260",null);e["default"]=r.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-bd164e16","chunk-0506e191","chunk-0506e191"],{"0ccb":function(t,e,a){"use strict";var s=a("e330"),n=a("50c4"),i=a("577e"),l=a("1148"),r=a("1d80"),o=s(l),c=s("".slice),d=Math.ceil,m=function(t){return function(e,a,s){var l,m,u=i(r(e)),p=n(a),h=u.length,v=void 0===s?" ":i(s);return p<=h||""===v?u:(l=p-h,m=o(v,d(l/v.length)),m.length>l&&(m=c(m,0,l)),t?u+m:m+u)}};t.exports={start:m(!1),end:m(!0)}},1148:function(t,e,a){"use strict";var s=a("5926"),n=a("577e"),i=a("1d80"),l=RangeError;t.exports=function(t){var e=n(i(this)),a="",r=s(t);if(r<0||r===1/0)throw new l("Wrong number of repetitions");for(;r>0;(r>>>=1)&&(e+=e))1&r&&(a+=e);return a}},"13d5":function(t,e,a){"use strict";var s=a("23e7"),n=a("d58f").left,i=a("a640"),l=a("1212"),r=a("9adc"),o=!r&&l>79&&l<83,c=o||!i("reduce");s({target:"Array",proto:!0,forced:c},{reduce:function(t){var e=arguments.length;return n(this,t,e,e>1?arguments[1]:void 0)}})},"4d90":function(t,e,a){"use strict";var s=a("23e7"),n=a("0ccb").start,i=a("9a0c");s({target:"String",proto:!0,forced:i},{padStart:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}})},"6f2d":function(t,e,a){"use strict";a("d7f1")},9485:function(t,e,a){"use strict";var s=a("23e7"),n=a("2266"),i=a("59ed"),l=a("825a"),r=a("46c4"),o=TypeError;s({target:"Iterator",proto:!0,real:!0},{reduce:function(t){l(this),i(t);var e=r(this),a=arguments.length<2,s=a?void 0:arguments[1],c=0;if(n(e,(function(e){a?(a=!1,s=e):s=t(s,e,c),c++}),{IS_RECORD:!0}),a)throw new o("Reduce of empty iterator with no initial value");return s}})},"9a0c":function(t,e,a){"use strict";var s=a("b5db");t.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(s)},"9d4a":function(t,e,a){"use strict";a("9485")},a573:function(t,e,a){"use strict";a("ab43")},ab43:function(t,e,a){"use strict";var s=a("23e7"),n=a("d024"),i=a("c430");s({target:"Iterator",proto:!0,real:!0,forced:i},{map:n})},b680:function(t,e,a){"use strict";var s=a("23e7"),n=a("e330"),i=a("5926"),l=a("408a"),r=a("1148"),o=a("d039"),c=RangeError,d=String,m=Math.floor,u=n(r),p=n("".slice),h=n(1..toFixed),v=function(t,e,a){return 0===e?a:e%2===1?v(t,e-1,a*t):v(t*t,e/2,a)},g=function(t){var e=0,a=t;while(a>=4096)e+=12,a/=4096;while(a>=2)e+=1,a/=2;return e},f=function(t,e,a){var s=-1,n=a;while(++s<6)n+=e*t[s],t[s]=n%1e7,n=m(n/1e7)},b=function(t,e){var a=6,s=0;while(--a>=0)s+=t[a],t[a]=m(s/e),s=s%e*1e7},_=function(t){var e=6,a="";while(--e>=0)if(""!==a||0===e||0!==t[e]){var s=d(t[e]);a=""===a?s:a+u("0",7-s.length)+s}return a},C=o((function(){return"0.000"!==h(8e-5,3)||"1"!==h(.9,0)||"1.25"!==h(1.255,2)||"1000000000000000128"!==h(0xde0b6b3a7640080,0)}))||!o((function(){h({})}));s({target:"Number",proto:!0,forced:C},{toFixed:function(t){var e,a,s,n,r=l(this),o=i(t),m=[0,0,0,0,0,0],h="",C="0";if(o<0||o>20)throw new c("Incorrect fraction digits");if(r!==r)return"NaN";if(r<=-1e21||r>=1e21)return d(r);if(r<0&&(h="-",r=-r),r>1e-21)if(e=g(r*v(2,69,1))-69,a=e<0?r*v(2,-e,1):r/v(2,e,1),a*=4503599627370496,e=52-e,e>0){f(m,0,a),s=o;while(s>=7)f(m,1e7,0),s-=7;f(m,v(10,s,1),0),s=e-1;while(s>=23)b(m,1<<23),s-=23;b(m,1<<s),f(m,1,1),b(m,2),C=_(m)}else f(m,0,a),f(m,1<<-e,0),C=_(m)+u("0",o);return o>0?(n=C.length,C=h+(n<=o?"0."+u("0",o-n)+C:p(C,0,n-o)+"."+p(C,n-o))):C=h+C,C}})},d024:function(t,e,a){"use strict";var s=a("c65b"),n=a("59ed"),i=a("825a"),l=a("46c4"),r=a("c5cc"),o=a("9bdd"),c=r((function(){var t=this.iterator,e=i(s(this.next,t)),a=this.done=!!e.done;if(!a)return o(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return i(this),n(t),new c(l(this),{mapper:t})}},d58f:function(t,e,a){"use strict";var s=a("59ed"),n=a("7b0b"),i=a("44ad"),l=a("07fa"),r=TypeError,o="Reduce of empty array with no initial value",c=function(t){return function(e,a,c,d){var m=n(e),u=i(m),p=l(m);if(s(a),0===p&&c<2)throw new r(o);var h=t?p-1:0,v=t?-1:1;if(c<2)while(1){if(h in u){d=u[h],h+=v;break}if(h+=v,t?h<0:p<=h)throw new r(o)}for(;t?h>=0:p>h;h+=v)h in u&&(d=a(d,u[h],h,m));return d}};t.exports={left:c(!1),right:c(!0)}},d7f1:function(t,e,a){},d81d:function(t,e,a){"use strict";var s=a("23e7"),n=a("b727").map,i=a("1dde"),l=i("map");s({target:"Array",proto:!0,forced:!l},{map:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}})},f0ca:function(t,e,a){"use strict";a.r(e);a("a4d3"),a("e01a"),a("b680");var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-commission-record"},[e("el-row",{staticStyle:{"margin-bottom":"20px"},attrs:{gutter:20}},[e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v(t._s(t.stats.totalRecords||0))]),e("div",{staticClass:"stats-label"},[t._v("总记录数")])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v("¥"+t._s((t.stats.totalAmount||0).toFixed(2)))]),e("div",{staticClass:"stats-label"},[t._v("总佣金金额")])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v("¥"+t._s((t.stats.unsettledAmount||0).toFixed(2)))]),e("div",{staticClass:"stats-label"},[t._v("待结算金额")])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v("¥"+t._s((t.stats.settledAmount||0).toFixed(2)))]),e("div",{staticClass:"stats-label"},[t._v("已结算金额")])])])],1)],1),e("el-form",{attrs:{inline:!0,model:t.dataForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.getDataList()}}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"业务员姓名",clearable:""},model:{value:t.dataForm.salesmanName,callback:function(e){t.$set(t.dataForm,"salesmanName",e)},expression:"dataForm.salesmanName"}})],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"佣金类型",clearable:""},model:{value:t.dataForm.commissionType,callback:function(e){t.$set(t.dataForm,"commissionType",e)},expression:"dataForm.commissionType"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"创建活动佣金",value:1}}),e("el-option",{attrs:{label:"充值次数佣金",value:2}}),e("el-option",{attrs:{label:"用户转发佣金",value:3}})],1)],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"结算状态",clearable:""},model:{value:t.dataForm.settlementStatus,callback:function(e){t.$set(t.dataForm,"settlementStatus",e)},expression:"dataForm.settlementStatus"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"未结算",value:0}}),e("el-option",{attrs:{label:"已结算",value:1}}),e("el-option",{attrs:{label:"已取消",value:2}})],1)],1),e("el-form-item",[e("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),e("el-button",{on:{click:function(e){return t.getStats()}}},[t._v("刷新统计")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.exportData()}}},[t._v("导出")]),e("el-button",{attrs:{type:"warning",disabled:0===t.dataListSelections.length},on:{click:function(e){return t.batchSettlement()}}},[t._v("批量结算")])],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"salesmanName","header-align":"center",align:"center",label:"业务员"}}),e("el-table-column",{attrs:{prop:"commissionTypeDesc","header-align":"center",align:"center",label:"佣金类型"}}),e("el-table-column",{attrs:{prop:"commissionAmount","header-align":"center",align:"center",label:"佣金金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" ¥"+t._s(e.row.commissionAmount.toFixed(2))+" ")]}}])}),e("el-table-column",{attrs:{prop:"orderAmount","header-align":"center",align:"center",label:"订单金额"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.orderAmount?e("span",[t._v("¥"+t._s(a.row.orderAmount.toFixed(2)))]):e("span",[t._v("-")])]}}])}),e("el-table-column",{attrs:{prop:"commissionRate","header-align":"center",align:"center",label:"佣金比例"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.commissionRate?e("span",[t._v(t._s((100*a.row.commissionRate).toFixed(2))+"%")]):e("span",[t._v("-")])]}}])}),e("el-table-column",{attrs:{prop:"settlementStatusDesc","header-align":"center",align:"center",label:"结算状态"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:t.getStatusTagType(a.row.settlementStatus)}},[t._v(" "+t._s(a.row.settlementStatusDesc)+" ")])]}}])}),e("el-table-column",{attrs:{prop:"settlementBatchNo","header-align":"center",align:"center",label:"结算批次"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.settlementBatchNo?e("span",[t._v(t._s(a.row.settlementBatchNo))]):e("span",[t._v("-")])]}}])}),e("el-table-column",{attrs:{prop:"businessTime","header-align":"center",align:"center",width:"150",label:"业务时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.viewDetails(a.row)}}},[t._v("详情")]),0===a.row.settlementStatus?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.singleSettlement(a.row)}}},[t._v("结算")]):t._e()]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),e("el-dialog",{staticClass:"commission-detail-dialog",attrs:{title:"佣金记录详情",visible:t.detailsDialogVisible,width:"70%"},on:{"update:visible":function(e){t.detailsDialogVisible=e}}},[e("div",{staticClass:"detail-content"},[e("el-card",{staticClass:"detail-card",attrs:{shadow:"never"}},[e("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[e("i",{staticClass:"el-icon-user"}),e("span",[t._v("基本信息")])]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("业务员：")]),e("span",{staticClass:"value"},[t._v(t._s(t.selectedRecord.salesmanName||"-"))])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("业务员编号：")]),e("span",{staticClass:"value"},[t._v(t._s(t.selectedRecord.salesmanCode||"-"))])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("用户：")]),e("span",{staticClass:"value"},[t._v(t._s(t.selectedRecord.userName||"-"))])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("活动：")]),e("span",{staticClass:"value"},[t._v(t._s(t.selectedRecord.activityName||"-"))])])])],1)],1),e("el-card",{staticClass:"detail-card",attrs:{shadow:"never"}},[e("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[e("i",{staticClass:"el-icon-money"}),e("span",[t._v("佣金信息")])]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("佣金类型：")]),e("span",{staticClass:"value"},[t._v(t._s(t.selectedRecord.commissionTypeDesc||"-"))])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("计算方式：")]),e("span",{staticClass:"value"},[t._v(t._s(t.selectedRecord.calculationTypeDesc||"-"))])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("佣金金额：")]),e("span",{staticClass:"value amount"},[t._v("¥"+t._s((t.selectedRecord.commissionAmount||0).toFixed(2)))])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("订单金额：")]),t.selectedRecord.orderAmount?e("span",{staticClass:"value amount"},[t._v("¥"+t._s(t.selectedRecord.orderAmount.toFixed(2)))]):e("span",{staticClass:"value"},[t._v("-")])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("佣金比例：")]),t.selectedRecord.commissionRate?e("span",{staticClass:"value"},[t._v(t._s((100*t.selectedRecord.commissionRate).toFixed(2))+"%")]):e("span",{staticClass:"value"},[t._v("-")])])])],1)],1),e("el-card",{staticClass:"detail-card",attrs:{shadow:"never"}},[e("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[e("i",{staticClass:"el-icon-finished"}),e("span",[t._v("结算信息")])]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("结算状态：")]),e("el-tag",{attrs:{type:t.getStatusTagType(t.selectedRecord.settlementStatus),size:"medium"}},[t._v(" "+t._s(t.selectedRecord.settlementStatusDesc)+" ")])],1)]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("结算批次：")]),e("span",{staticClass:"value"},[t._v(t._s(t.selectedRecord.settlementBatchNo||"-"))])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("结算时间：")]),e("span",{staticClass:"value"},[t._v(t._s(t.selectedRecord.settlementTime||"-"))])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("业务时间：")]),e("span",{staticClass:"value"},[t._v(t._s(t.selectedRecord.businessTime||"-"))])])]),e("el-col",{attrs:{span:24}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("结算备注：")]),e("span",{staticClass:"value"},[t._v(t._s(t.selectedRecord.settlementRemarks||"-"))])])])],1)],1),e("el-card",{staticClass:"detail-card",attrs:{shadow:"never"}},[e("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[e("i",{staticClass:"el-icon-info"}),e("span",[t._v("其他信息")])]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("创建时间：")]),e("span",{staticClass:"value"},[t._v(t._s(t.selectedRecord.createOn||"-"))])])]),e("el-col",{attrs:{span:24}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("描述：")]),e("span",{staticClass:"value"},[t._v(t._s(t.selectedRecord.description||"-"))])])])],1)],1)],1)]),e("el-dialog",{attrs:{title:"批量结算",visible:t.batchSettlementDialogVisible,width:"50%"},on:{"update:visible":function(e){t.batchSettlementDialogVisible=e}}},[e("el-form",{ref:"settlementForm",attrs:{model:t.settlementForm,rules:t.settlementRules,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"结算备注",prop:"remarks"}},[e("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入结算备注"},model:{value:t.settlementForm.remarks,callback:function(e){t.$set(t.settlementForm,"remarks",e)},expression:"settlementForm.remarks"}})],1),e("el-form-item",{attrs:{label:"选中记录"}},[e("span",[t._v("共选中 "+t._s(t.dataListSelections.length)+" 条记录，总金额：¥"+t._s(t.selectedTotalAmount.toFixed(2)))])])],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.batchSettlementDialogVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary",loading:t.settlementLoading},on:{click:function(e){return t.confirmBatchSettlement()}}},[t._v("确定结算")])],1)],1)],1)},n=[],i=(a("99af"),a("4de4"),a("d81d"),a("13d5"),a("d3b7"),a("25f0"),a("4d90"),a("0643"),a("2382"),a("a573"),a("9d4a"),{data:function(){return{salesmanId:"",salesmanName:"",dataForm:{salesmanId:"",salesmanName:"",commissionType:"",settlementStatus:""},dateRange:[],dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],stats:{},detailsDialogVisible:!1,selectedRecord:{},batchSettlementDialogVisible:!1,settlementLoading:!1,settlementForm:{remarks:""},settlementRules:{remarks:[{required:!0,message:"请输入结算备注",trigger:"blur"}]}}},computed:{selectedTotalAmount:function(){return this.dataListSelections.reduce((function(t,e){return t+(e.commissionAmount||0)}),0)}},activated:function(){this.salesmanId=this.$route.query.salesmanId,this.salesmanName=this.$route.query.salesmanName,this.getDataList(),this.getStats()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0;var e={page:this.pageIndex,limit:this.pageSize,salesmanId:this.salesmanId,salesmanName:this.dataForm.salesmanName,commissionType:this.dataForm.commissionType,settlementStatus:this.dataForm.settlementStatus};this.dateRange&&2===this.dateRange.length&&(e.startTime=this.dateRange[0],e.endTime=this.dateRange[1]),this.$http({url:this.$http.adornUrl("/salesman/commission/record/list"),method:"get",params:this.$http.adornParams(e)}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},getStats:function(){var t=this,e={salesmanId:this.salesmanId,salesmanName:this.dataForm.salesmanName,commissionType:this.dataForm.commissionType,settlementStatus:this.dataForm.settlementStatus};this.dateRange&&2===this.dateRange.length&&(e.startTime=this.dateRange[0],e.endTime=this.dateRange[1]),this.$http({url:this.$http.adornUrl("/salesman/commission/record/stats"),method:"get",params:this.$http.adornParams(e)}).then((function(e){var a=e.data;a&&200===a.code&&(t.stats=a.stats)}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},viewDetails:function(t){this.selectedRecord=t,this.detailsDialogVisible=!0},getStatusTagType:function(t){switch(t){case 0:return"warning";case 1:return"success";case 2:return"danger";default:return"info"}},exportData:function(){this.$message.info("导出功能开发中...")},singleSettlement:function(t){var e=this;this.$confirm("确定要结算业务员 ".concat(t.salesmanName," 的这条佣金记录吗？"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.doSingleSettlement(t)})).catch((function(){}))},doSingleSettlement:function(t){var e=this,a=this.$loading({lock:!0,text:"结算中...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});this.$http({url:this.$http.adornUrl("/salesman/commission/record/batchUpdateSettlement"),method:"post",data:this.$http.adornData({recordIds:[t.id],settlementStatus:1,settlementBatchNo:this.generateBatchNo(),settlementRemarks:"单独结算"})}).then((function(t){var s=t.data;a.close(),s&&200===s.code?(e.$message.success("结算成功"),e.getDataList(),e.getStats()):e.$message.error(s.msg||"结算失败")})).catch((function(){a.close(),e.$message.error("结算失败")}))},batchSettlement:function(){var t=this.dataListSelections.filter((function(t){return 0===t.settlementStatus}));0!==t.length?t.length===this.dataListSelections.length?(this.settlementForm.remarks="",this.batchSettlementDialogVisible=!0):this.$message.warning("选中的记录中包含已结算的记录，请重新选择"):this.$message.warning("请选择未结算的记录")},confirmBatchSettlement:function(){var t=this;this.$refs.settlementForm.validate((function(e){e&&t.doBatchSettlement()}))},doBatchSettlement:function(){var t=this;this.settlementLoading=!0;var e=this.dataListSelections.map((function(t){return t.id}));this.$http({url:this.$http.adornUrl("/salesman/commission/record/batchUpdateSettlement"),method:"post",data:this.$http.adornData({recordIds:e,settlementStatus:1,settlementBatchNo:this.generateBatchNo(),settlementRemarks:this.settlementForm.remarks})}).then((function(e){var a=e.data;t.settlementLoading=!1,a&&200===a.code?(t.$message.success("批量结算成功，共处理 ".concat(a.updateCount," 条记录")),t.batchSettlementDialogVisible=!1,t.getDataList(),t.getStats()):t.$message.error(a.msg||"批量结算失败")})).catch((function(){t.settlementLoading=!1,t.$message.error("批量结算失败")}))},generateBatchNo:function(){var t=new Date,e=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),s=String(t.getDate()).padStart(2,"0"),n=String(t.getHours()).padStart(2,"0"),i=String(t.getMinutes()).padStart(2,"0"),l=String(t.getSeconds()).padStart(2,"0"),r=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return"BATCH".concat(e).concat(a).concat(s).concat(n).concat(i).concat(l).concat(r)}}}),l=i,r=(a("6f2d"),a("2877")),o=Object(r["a"])(l,s,n,!1,null,"2f870fba",null);e["default"]=o.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0dd14fe2","chunk-37a545c8"],{"1b69":function(t,i,e){"use strict";e.r(i);e("7f7f");var n,a=function(){var t=this,i=t._self._c;return i("div",{staticStyle:{background:"white","margin-bottom":"20px"}},[i("van-row",{attrs:{gutter:"20"}},[i("van-col",{attrs:{span:"16"}},[i("van-swipe",{staticStyle:{width:"100%"},attrs:{autoplay:3e3}},t._l(t.activityInfo.appFileList,(function(t,e){return i("van-swipe-item",{key:e},[i("van-image",{attrs:{width:"100%",src:t.url}})],1)})),1)],1),i("van-col",{attrs:{span:"8"}},[i("div",{staticStyle:{"margin-top":"20px"}},[i("van-cell",{attrs:{title:"会议名称",value:t.activityInfo.name}}),t.activityInfo.address?i("van-cell",{attrs:{title:"会议地点",value:t.activityInfo.address}}):t._e(),i("van-cell",{attrs:{title:"会议开始时间",value:t.activityInfo.startTime}}),i("van-cell",{attrs:{title:"会议结束时间",value:t.activityInfo.endTime}}),i("van-cell",{attrs:{title:"登录信息"},scopedSlots:t._u([{key:"default",fn:function(){return[t.userInfo?i("van-button",{attrs:{type:"primary",size:"small",round:""}},[t._v(t._s(t.userInfo.username)+"("+t._s(t.userInfo.mobile)+")")]):i("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.showLogin}},[t._v("未登录(点击登录)")])]},proxy:!0}])}),"1736999159118508033"!=t.activityId?i("van-cell",{attrs:{title:"报名状态"},scopedSlots:t._u([{key:"default",fn:function(){return[1==t.isPay?i("van-button",{attrs:{type:"primary",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名")]):2==t.isPay?i("van-button",{attrs:{type:"warning",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名未交费")]):i("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.turnApply}},[t._v("未报名")])]},proxy:!0}],null,!1,567203517)}):t._e()],1)])],1),i("van-tabs",{staticClass:"nav",on:{click:t.onClick},model:{value:t.cmsId,callback:function(i){t.cmsId=i},expression:"cmsId"}},t._l(t.cmsList,(function(t){return i("van-tab",{key:t.id,attrs:{title:t.title,name:t.id}})})),1),i("pclogin")],1)},s=[],o=e("ade3"),c=(e("a481"),e("6762"),e("2fdb"),e("cacf")),r=e("7dcb"),l=function(){var t=this,i=t._self._c;return i("van-dialog",{attrs:{title:"登录操作",width:"400px"},on:{confirm:t.login},model:{value:t.showPcLogin,callback:function(i){t.showPcLogin=i},expression:"showPcLogin"}},[i("div",{staticClass:"text-center padding"},[i("van-cell-group",{attrs:{inset:""}},[i("van-field",{attrs:{name:"手机号",label:"手机号",required:"",placeholder:"手机号",rules:[{required:!0,message:"请填写手机号"}]},model:{value:t.mobile,callback:function(i){t.mobile=i},expression:"mobile"}}),"1736999159118508033"!=t.activityId?i("van-field",{attrs:{center:"",clearable:"",maxlength:"6",label:"短信验证码",required:"",placeholder:"请输入短信验证码"},scopedSlots:t._u([{key:"button",fn:function(){return[i("van-button",{attrs:{size:"small",type:"primary",disabled:t.waiting},on:{click:function(i){return t.doSendSmsCode()}}},[t.waiting?i("span",[t._v(t._s(t.waitingTime)+"秒后重发")]):i("span",{staticStyle:{"font-size":"13px"}},[t._v("获取验证码")])])]},proxy:!0}],null,!1,850170227),model:{value:t.code,callback:function(i){t.code=i},expression:"code"}}):t._e()],1)],1)])},d=[],v={name:"pclogin",computed:{showPcLogin:{get:function(){return this.$store.state.user.showPcLogin},set:function(t){this.$store.commit("user/changePcLogin",t)}}},data:function(){return{activityId:void 0,waiting:!1,waitingTime:60,mobile:"",code:""}},mounted:function(){this.activityId=this.$route.query.id},methods:{login:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?this.code||0x181b0f883e693000==this.activityId?/^\d{6}$/.test(this.code)||0x181b0f883e693000==this.activityId?void this.$fly.post("/pyp/web/user/pcLogin",{mobile:this.mobile,code:0x181b0f883e693000==this.activityId?"121212":this.code}).then((function(i){i&&200===i.code?(vant.Toast("登录成功"),t.$store.commit("user/update",i.userInfo),t.showPcLogin=!1,"applyIndex"!=t.$route.name&&"livesDetail"!=t.$route.name||location.reload()):vant.Toast(i.msg)})):(vant.Toast("验证码错误"),!1):(vant.Toast("请输入验证码"),!1):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},doSendSmsCode:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.mobile,activityId:this.activityId}).then((function(i){i&&200===i.code?(t.countdown(),vant.Toast("发送验证码成功")):vant.Toast(i.msg)})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var t=this;this.waiting=!0;var i=window.setInterval((function(){t.waitingTime--,t.waitingTime<0&&(window.clearInterval(i),t.waitingTime=60,t.waiting=!1)}),1e3)}}},u=v,h=e("2877"),m=Object(h["a"])(u,l,d,!1,null,null,null),f=m.exports,p={components:{pclogin:f},data:function(){return{activityId:void 0,bannerIndex:0,cmsId:void 0,cmsList:[],cmsInfo:{},activityInfo:{}}},computed:{userInfo:{get:function(){return this.$store.state.user.userInfo},set:function(t){this.$store.commit("user/update",t)}},isPay:{get:function(){return this.$store.state.apply.isPay},set:function(t){this.$store.commit("apply/update",t)}}},mounted:function(){var t=(new Date).getTime();localStorage.setItem("logAddTime",t+36e5),this.openid=this.$cookie.get("openid"),this.activityId=this.$route.query.id,this.cmsId=sessionStorage.getItem("cmsId")||this.$route.query.cmsId,this.checkLogin(),this.getActivityInfo(),this.activityLogCount()},methods:(n={showLogin:function(){this.$store.commit("user/changePcLogin",!0)},checkLogin:function(){var t=this;this.$fly.get("/pyp/web/user/checkLogin").then((function(i){t.userInfo=i.result,t.userInfo?t.checkApply():t.isPay=0}))},bannerIndexChange:function(t){this.bannerIndex=t},checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(i){200==i.code?(t.isPay=i.isPay,1==t.isPay||2==t.isPay&&vant.Dialog.alert({title:"提示",message:"您有一笔注册费待支付，点击跳转"}).then((function(){t.$router.push({name:"applySuccess",query:{orderId:i.result,id:t.activityId}})}))):vant.Toast(i.msg)}))},changeShowType:function(t){this.$emit("changeShowType",t)},turnApply:function(){var t=this.cmsList.filter((function(t){return t.model&&t.model.includes("applyIndex")}));if(t){var i=t[0];sessionStorage.setItem("cmsId",i.id);var e=i.model.replace("${activityId}",i.activityId);this.$router.push(JSON.parse(e))}else vant.Toast("暂未开启报名")},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(i){if(t.loading=!1,200==i.code){t.activityInfo=i.activity,document.title=t.activityInfo.name;var e=t.activityInfo.startTime,n=new Date(e.replace(/-/g,"/")),a=new Date,s=n.getTime()-a.getTime();t.dateCompare=s>0?s:0,t.getCmsList()}}))},activityLogCount:function(){var t=localStorage.getItem("logAddTime"),i=(new Date).getTime();i>t&&this.$fly.post("/pyp/activity/activityviewlog/count",{activityId:this.activityId,device:r["a"].getVersion()}).then((function(t){}))},getCmsList:function(){var t=this;this.$fly.get("/pyp/cms/cms/findByActivityId/".concat(this.activityId)).then((function(i){t.loading=!1,200==i.code?(t.cmsList=i.result,t.cmsId?t.changeShowType(t.cmsId):t.changeShowType(t.cmsList[0].id)):(vant.Toast(i.msg),t.cmsList=[])}))},onClick:function(t){if(console.log(t),sessionStorage.setItem("cmsId",t),this.cmsInfo=this.cmsList.filter((function(i){return i.id==t}))[0],this.cmsInfo.url&&Object(c["d"])(this.cmsInfo.url))location.href=this.cmsInfo.url;else if(this.cmsInfo.model&&this.isJSON(this.cmsInfo.model)){var i=this.cmsInfo.model.replace("${activityId}",this.cmsInfo.activityId);this.$router.push(JSON.parse(i))}else"cmsIndex"==this.$route.name?this.changeShowType(t):this.$router.push({name:"cmsIndex",query:{id:this.activityId,cmsId:t}})}},Object(o["a"])(n,"bannerIndexChange",(function(t){this.bannerIndex=t})),Object(o["a"])(n,"isJSON",(function(t){try{return JSON.parse(t),!0}catch(i){return!1}})),n)},g=p,y=(e("dd7a"),Object(h["a"])(g,a,s,!1,null,"7bd3d808",null));i["default"]=y.exports},"386b":function(t,i,e){var n=e("5ca1"),a=e("79e5"),s=e("be13"),o=/"/g,c=function(t,i,e,n){var a=String(s(t)),c="<"+i;return""!==e&&(c+=" "+e+'="'+String(n).replace(o,"&quot;")+'"'),c+">"+a+"</"+i+">"};t.exports=function(t,i){var e={};e[t]=i(c),n(n.P+n.F*a((function(){var i=""[t]('"');return i!==i.toLowerCase()||i.split('"').length>3})),"String",e)}},"5fa1":function(t,i,e){"use strict";e.r(i);var n=function(){var t=this,i=t._self._c;return i("div",{staticClass:"invite-page"},[i("div",{staticClass:"custom-navbar"},[i("van-icon",{staticClass:"nav-back",attrs:{name:"arrow-left"},on:{click:function(i){return t.$router.go(-1)}}}),i("span",{staticClass:"nav-title"},[t._v("邀请业务员")])],1),t.loading?i("van-loading",{staticClass:"loading-center",attrs:{type:"spinner",color:"#ffffff",size:"24px"}},[t._v("\n    正在加载...\n  ")]):i("div",{staticClass:"invite-container"},[i("div",{staticClass:"hero-section"},[i("div",{staticClass:"hero-bg"},[i("div",{staticClass:"hero-content"},[i("div",{staticClass:"hero-icon"},[i("div",{staticClass:"icon-wrapper"},[i("van-icon",{attrs:{name:"friends-o",size:"32"}})],1)]),i("h1",{staticClass:"hero-title"},[t._v("邀请业务员")]),i("p",{staticClass:"hero-subtitle"},[t._v("分享您的专属邀请码，邀请朋友加入团队")]),i("div",{staticClass:"benefits-grid"},[i("div",{staticClass:"benefit-card"},[i("van-icon",{attrs:{name:"gold-coin-o",size:"20"}}),i("span",[t._v("获得佣金")])],1),i("div",{staticClass:"benefit-card"},[i("van-icon",{attrs:{name:"chart-trending-o",size:"20"}}),i("span",[t._v("团队业绩")])],1),i("div",{staticClass:"benefit-card"},[i("van-icon",{attrs:{name:"gift-o",size:"20"}}),i("span",[t._v("持续收益")])],1)])])])]),i("div",{staticClass:"content-section"},[i("div",{staticClass:"tab-switcher"},[i("div",{staticClass:"tab-item",class:{active:"link"===t.activeTab},on:{click:function(i){t.activeTab="link"}}},[i("van-icon",{attrs:{name:"link-o",size:"18"}}),i("span",[t._v("邀请链接")])],1),i("div",{staticClass:"tab-item",class:{active:"qrcode"===t.activeTab},on:{click:function(i){t.activeTab="qrcode"}}},[i("van-icon",{attrs:{name:"qr",size:"18"}}),i("span",[t._v("二维码")])],1)]),i("div",{directives:[{name:"show",rawName:"v-show",value:"link"===t.activeTab,expression:"activeTab === 'link'"}],staticClass:"tab-content"},[i("div",{staticClass:"invite-card"},[i("div",{staticClass:"card-header"},[i("div",{staticClass:"header-icon"},[i("van-icon",{attrs:{name:"link-o",size:"20"}})],1),i("div",{staticClass:"header-text"},[i("h3",[t._v("专属邀请链接")]),i("p",[t._v("复制链接分享给朋友")])])]),i("div",{staticClass:"link-section"},[i("div",{staticClass:"link-box"},[i("input",{ref:"linkInput",staticClass:"link-input",attrs:{type:"text",readonly:""},domProps:{value:t.inviteLink},on:{click:t.selectLink}}),i("button",{staticClass:"copy-button",on:{click:t.copyLink}},[i("van-icon",{attrs:{name:"copy",size:"16"}}),t._v("\n                复制\n              ")],1)])]),i("div",{staticClass:"action-section"},[i("button",{staticClass:"share-button",on:{click:t.shareLink}},[i("van-icon",{attrs:{name:"share-o",size:"18"}}),t._v("\n              分享邀请链接\n            ")],1)])])]),i("div",{directives:[{name:"show",rawName:"v-show",value:"qrcode"===t.activeTab,expression:"activeTab === 'qrcode'"}],staticClass:"tab-content"},[i("div",{staticClass:"invite-card"},[i("div",{staticClass:"card-header"},[i("div",{staticClass:"header-icon qrcode-icon"},[i("van-icon",{attrs:{name:"qr",size:"20"}})],1),i("div",{staticClass:"header-text"},[i("h3",[t._v("邀请二维码")]),i("p",[t._v("保存或分享二维码图片")])])]),i("div",{staticClass:"qrcode-section"},[i("div",{staticClass:"qrcode-container"},[t.qrcodeContent?i("div",{staticClass:"qrcode-wrapper"},[i("vue-qrcode",{staticClass:"qrcode-canvas",attrs:{value:t.qrcodeContent,options:{width:180,margin:1}}}),i("div",{staticClass:"qrcode-label"},[i("p",[t._v("扫码邀请业务员")]),i("span",[t._v("邀请人："+t._s(t.inviterName))])])],1):i("div",{staticClass:"qrcode-loading"},[i("van-loading",{attrs:{type:"spinner",size:"24px",color:"#667eea"}}),i("p",[t._v("二维码生成中...")])],1)])]),i("div",{staticClass:"action-section"},[i("div",{staticClass:"qrcode-actions"},[i("button",{staticClass:"action-button save-btn",on:{click:t.saveQrcode}},[i("van-icon",{attrs:{name:"photo-o",size:"16"}}),t._v("\n                保存图片\n              ")],1),i("button",{staticClass:"action-button share-btn",on:{click:t.shareQrcode}},[i("van-icon",{attrs:{name:"share-o",size:"16"}}),t._v("\n                分享二维码\n              ")],1)])])])])]),i("div",{staticClass:"help-section"},[i("button",{staticClass:"help-button",on:{click:function(i){t.showInstructions=!0}}},[i("van-icon",{attrs:{name:"question-o",size:"16"}}),t._v("\n        查看邀请说明\n      ")],1)])]),i("van-dialog",{attrs:{title:"邀请说明","show-cancel-button":!1,"confirm-button-text":"我知道了"},model:{value:t.showInstructions,callback:function(i){t.showInstructions=i},expression:"showInstructions"}},[i("div",{staticClass:"instructions"},[i("h4",[t._v("如何邀请业务员？")]),i("ol",[i("li",[t._v("复制邀请链接发送给朋友")]),i("li",[t._v("或保存二维码图片分享给朋友")]),i("li",[t._v("朋友通过链接或扫码进入注册页面")]),i("li",[t._v("填写基本信息完成注册")]),i("li",[t._v("注册成功后自动成为您的下级业务员")])]),i("h4",[t._v("邀请奖励")]),i("ul",[i("li",[t._v("下级业务员的业绩将计入您的团队业绩")]),i("li",[t._v("可获得相应的团队奖励和佣金分成")]),i("li",[t._v("建立稳定的业务团队，获得持续收益")])])])])],1)},a=[],s=(e("b54a"),e("96cf"),e("1da1")),o=e("cacf"),c=e("1b69"),r=e("b2e5"),l=e.n(r),d={components:{pcheader:c["default"],VueQrcode:l.a},data:function(){return{isMobilePhone:Object(o["c"])(),loading:!1,activeTab:"link",inviteLink:"",qrcodeContent:"",inviterName:"",inviterMobile:"",inviteStats:{},showInstructions:!1}},mounted:function(){document.title="邀请业务员",this.generateInviteLink()},methods:{generateInviteLink:function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(){var i,e=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.loading=!0,t.next=4,this.$fly.get("/pyp/web/salesman/generateInviteLink");case 4:i=t.sent,200===i.code?(this.inviteLink=i.inviteLink,this.qrcodeContent=i.qrcodeContent,this.inviterName=i.inviterName,this.inviterMobile=i.inviterMobile,this.$nextTick((function(){e.generateQrcode()})),this.shareLink(),this.inviteStats={totalInvited:0,activeInvited:0,totalReward:0,monthReward:0}):vant.Toast(i.msg||"生成邀请链接失败"),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](0),console.error("生成邀请链接失败:",t.t0),vant.Toast("生成邀请链接失败");case 12:return t.prev=12,this.loading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,this,[[0,8,12,15]])})));function i(){return t.apply(this,arguments)}return i}(),generateQrcode:function(){console.log("二维码内容:",this.qrcodeContent)},selectLink:function(){this.$refs.linkInput.select()},copyLink:function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(){var i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,!navigator.clipboard||!window.isSecureContext){t.next=7;break}return t.next=4,navigator.clipboard.writeText(this.inviteLink);case 4:vant.Toast("链接已复制到剪贴板"),t.next=11;break;case 7:this.$refs.linkInput.select(),this.$refs.linkInput.setSelectionRange(0,99999),i=document.execCommand("copy"),i?vant.Toast("链接已复制到剪贴板"):vant.Toast("复制失败，请手动复制");case 11:t.next=17;break;case 13:t.prev=13,t.t0=t["catch"](0),console.error("复制失败:",t.t0),vant.Toast("复制失败，请手动复制");case 17:case"end":return t.stop()}}),t,this,[[0,13]])})));function i(){return t.apply(this,arguments)}return i}(),shareLink:function(){this.$wxShare("邀请您成为业务员",this.inviteLink,this.$cookie.get("logo"),"".concat(this.inviterName," 邀请您成为业务员，点击链接了解详情"))},saveQrcode:function(){if(this.qrcodeContent){var t=this.$el.querySelector(".qrcode-image canvas");if(t){var i=document.createElement("a");i.download="业务员邀请二维码.png",i.href=t.toDataURL(),i.click(),vant.Toast("二维码已保存")}else vant.Toast("保存失败，请重试")}else vant.Toast("二维码还未生成")},shareQrcode:function(){var t=this;if(this.qrcodeContent){var i={title:"邀请您成为业务员",desc:"".concat(this.inviterName," 邀请您成为业务员，扫码了解详情"),link:this.qrcodeContent,imgUrl:""};this.$wxShare?this.$wxShare(i):navigator.share?navigator.share({title:i.title,text:i.desc,url:i.link}).catch((function(i){console.log("分享失败:",i),t.copyLink()})):this.copyLink()}else vant.Toast("二维码还未生成")}}},v=d,u=(e("f6e9"),e("2877")),h=Object(u["a"])(v,n,a,!1,null,"8c2dd2f0",null);i["default"]=h.exports},"7dcb":function(t,i,e){"use strict";e("a481"),e("4917");i["a"]={getVersion:function(){var t=navigator.userAgent||window.navigator.userAgent,i=/HUAWEI|HONOR/gi,e=/[^;]+(?= Build)/gi,n=/CPU iPhone OS \d[_\d]*/gi,a=/CPU OS \d[_\d]*/gi,s=/Windows NT \d[\.\d]*/gi,o=/Linux x\d[_\d]*/gi,c=/Mac OS X \d[_|\.\d]*/gi;return/Android/gi.test(t)?i.test(t)?t.match(i)[0]+t.match(e)[0]:t.match(e)[0]:/iPhone/gi.test(t)?t.match(n)[0].replace(/CPU iPhone OS/gi,"iPhone").replace(/_/g,"."):/iPad/gi.test(t)?t.match(a)[0].replace(/CPU OS/gi,"iPad").replace(/_/g,"."):/Windows/gi.test(t)?"Windows "+(Math.min(parseInt(t.match(s)[0].replace(/Windows NT /,""))+1,10)<7?"XP":Math.min(parseInt(t.match(s)[0].replace(/Windows NT /,""))+1,10)):/Linux/gi.test(t)?t.match(o)[0]:/Macintosh/gi.test(t)?t.match(c)[0].replace(/_/g,"."):"unknown"}}},ade3:function(t,i,e){"use strict";e.d(i,"a",(function(){return o}));var n=e("53ca");function a(t,i){if("object"!==Object(n["a"])(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var a=e.call(t,i||"default");if("object"!==Object(n["a"])(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===i?String:Number)(t)}function s(t){var i=a(t,"string");return"symbol"===Object(n["a"])(i)?i:String(i)}function o(t,i,e){return i=s(i),i in t?Object.defineProperty(t,i,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[i]=e,t}},b54a:function(t,i,e){"use strict";e("386b")("link",(function(t){return function(i){return t(this,"a","href",i)}}))},bd21:function(t,i,e){},cad8:function(t,i,e){},dd7a:function(t,i,e){"use strict";e("cad8")},f6e9:function(t,i,e){"use strict";e("bd21")}}]);
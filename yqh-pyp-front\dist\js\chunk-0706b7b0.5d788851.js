(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0706b7b0","chunk-37a545c8"],{"1b69":function(t,e,n){"use strict";n.r(e);n("7f7f");var i,a=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{background:"white","margin-bottom":"20px"}},[e("van-row",{attrs:{gutter:"20"}},[e("van-col",{attrs:{span:"16"}},[e("van-swipe",{staticStyle:{width:"100%"},attrs:{autoplay:3e3}},t._l(t.activityInfo.appFileList,(function(t,n){return e("van-swipe-item",{key:n},[e("van-image",{attrs:{width:"100%",src:t.url}})],1)})),1)],1),e("van-col",{attrs:{span:"8"}},[e("div",{staticStyle:{"margin-top":"20px"}},[e("van-cell",{attrs:{title:"会议名称",value:t.activityInfo.name}}),t.activityInfo.address?e("van-cell",{attrs:{title:"会议地点",value:t.activityInfo.address}}):t._e(),e("van-cell",{attrs:{title:"会议开始时间",value:t.activityInfo.startTime}}),e("van-cell",{attrs:{title:"会议结束时间",value:t.activityInfo.endTime}}),e("van-cell",{attrs:{title:"登录信息"},scopedSlots:t._u([{key:"default",fn:function(){return[t.userInfo?e("van-button",{attrs:{type:"primary",size:"small",round:""}},[t._v(t._s(t.userInfo.username)+"("+t._s(t.userInfo.mobile)+")")]):e("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.showLogin}},[t._v("未登录(点击登录)")])]},proxy:!0}])}),"1736999159118508033"!=t.activityId?e("van-cell",{attrs:{title:"报名状态"},scopedSlots:t._u([{key:"default",fn:function(){return[1==t.isPay?e("van-button",{attrs:{type:"primary",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名")]):2==t.isPay?e("van-button",{attrs:{type:"warning",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名未交费")]):e("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.turnApply}},[t._v("未报名")])]},proxy:!0}],null,!1,567203517)}):t._e()],1)])],1),e("van-tabs",{staticClass:"nav",on:{click:t.onClick},model:{value:t.cmsId,callback:function(e){t.cmsId=e},expression:"cmsId"}},t._l(t.cmsList,(function(t){return e("van-tab",{key:t.id,attrs:{title:t.title,name:t.id}})})),1),e("pclogin")],1)},s=[],o=n("ade3"),r=(n("a481"),n("6762"),n("2fdb"),n("cacf")),c=n("7dcb"),l=function(){var t=this,e=t._self._c;return e("van-dialog",{attrs:{title:"登录操作",width:"400px"},on:{confirm:t.login},model:{value:t.showPcLogin,callback:function(e){t.showPcLogin=e},expression:"showPcLogin"}},[e("div",{staticClass:"text-center padding"},[e("van-cell-group",{attrs:{inset:""}},[e("van-field",{attrs:{name:"手机号",label:"手机号",required:"",placeholder:"手机号",rules:[{required:!0,message:"请填写手机号"}]},model:{value:t.mobile,callback:function(e){t.mobile=e},expression:"mobile"}}),"1736999159118508033"!=t.activityId?e("van-field",{attrs:{center:"",clearable:"",maxlength:"6",label:"短信验证码",required:"",placeholder:"请输入短信验证码"},scopedSlots:t._u([{key:"button",fn:function(){return[e("van-button",{attrs:{size:"small",type:"primary",disabled:t.waiting},on:{click:function(e){return t.doSendSmsCode()}}},[t.waiting?e("span",[t._v(t._s(t.waitingTime)+"秒后重发")]):e("span",{staticStyle:{"font-size":"13px"}},[t._v("获取验证码")])])]},proxy:!0}],null,!1,850170227),model:{value:t.code,callback:function(e){t.code=e},expression:"code"}}):t._e()],1)],1)])},u=[],d={name:"pclogin",computed:{showPcLogin:{get:function(){return this.$store.state.user.showPcLogin},set:function(t){this.$store.commit("user/changePcLogin",t)}}},data:function(){return{activityId:void 0,waiting:!1,waitingTime:60,mobile:"",code:""}},mounted:function(){this.activityId=this.$route.query.id},methods:{login:function(){var t=this;return this.mobile?Object(r["b"])(this.mobile)?this.code||0x181b0f883e693000==this.activityId?/^\d{6}$/.test(this.code)||0x181b0f883e693000==this.activityId?void this.$fly.post("/pyp/web/user/pcLogin",{mobile:this.mobile,code:0x181b0f883e693000==this.activityId?"121212":this.code}).then((function(e){e&&200===e.code?(vant.Toast("登录成功"),t.$store.commit("user/update",e.userInfo),t.showPcLogin=!1,"applyIndex"!=t.$route.name&&"livesDetail"!=t.$route.name||location.reload()):vant.Toast(e.msg)})):(vant.Toast("验证码错误"),!1):(vant.Toast("请输入验证码"),!1):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},doSendSmsCode:function(){var t=this;return this.mobile?Object(r["b"])(this.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.mobile,activityId:this.activityId}).then((function(e){e&&200===e.code?(t.countdown(),vant.Toast("发送验证码成功")):vant.Toast(e.msg)})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var t=this;this.waiting=!0;var e=window.setInterval((function(){t.waitingTime--,t.waitingTime<0&&(window.clearInterval(e),t.waitingTime=60,t.waiting=!1)}),1e3)}}},m=d,f=n("2877"),h=Object(f["a"])(m,l,u,!1,null,null,null),v=h.exports,p={components:{pclogin:v},data:function(){return{activityId:void 0,bannerIndex:0,cmsId:void 0,cmsList:[],cmsInfo:{},activityInfo:{}}},computed:{userInfo:{get:function(){return this.$store.state.user.userInfo},set:function(t){this.$store.commit("user/update",t)}},isPay:{get:function(){return this.$store.state.apply.isPay},set:function(t){this.$store.commit("apply/update",t)}}},mounted:function(){var t=(new Date).getTime();localStorage.setItem("logAddTime",t+36e5),this.openid=this.$cookie.get("openid"),this.activityId=this.$route.query.id,this.cmsId=sessionStorage.getItem("cmsId")||this.$route.query.cmsId,this.checkLogin(),this.getActivityInfo(),this.activityLogCount()},methods:(i={showLogin:function(){this.$store.commit("user/changePcLogin",!0)},checkLogin:function(){var t=this;this.$fly.get("/pyp/web/user/checkLogin").then((function(e){t.userInfo=e.result,t.userInfo?t.checkApply():t.isPay=0}))},bannerIndexChange:function(t){this.bannerIndex=t},checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(e){200==e.code?(t.isPay=e.isPay,1==t.isPay||2==t.isPay&&vant.Dialog.alert({title:"提示",message:"您有一笔注册费待支付，点击跳转"}).then((function(){t.$router.push({name:"applySuccess",query:{orderId:e.result,id:t.activityId}})}))):vant.Toast(e.msg)}))},changeShowType:function(t){this.$emit("changeShowType",t)},turnApply:function(){var t=this.cmsList.filter((function(t){return t.model&&t.model.includes("applyIndex")}));if(t){var e=t[0];sessionStorage.setItem("cmsId",e.id);var n=e.model.replace("${activityId}",e.activityId);this.$router.push(JSON.parse(n))}else vant.Toast("暂未开启报名")},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(e){if(t.loading=!1,200==e.code){t.activityInfo=e.activity,document.title=t.activityInfo.name;var n=t.activityInfo.startTime,i=new Date(n.replace(/-/g,"/")),a=new Date,s=i.getTime()-a.getTime();t.dateCompare=s>0?s:0,t.getCmsList()}}))},activityLogCount:function(){var t=localStorage.getItem("logAddTime"),e=(new Date).getTime();e>t&&this.$fly.post("/pyp/activity/activityviewlog/count",{activityId:this.activityId,device:c["a"].getVersion()}).then((function(t){}))},getCmsList:function(){var t=this;this.$fly.get("/pyp/cms/cms/findByActivityId/".concat(this.activityId)).then((function(e){t.loading=!1,200==e.code?(t.cmsList=e.result,t.cmsId?t.changeShowType(t.cmsId):t.changeShowType(t.cmsList[0].id)):(vant.Toast(e.msg),t.cmsList=[])}))},onClick:function(t){if(console.log(t),sessionStorage.setItem("cmsId",t),this.cmsInfo=this.cmsList.filter((function(e){return e.id==t}))[0],this.cmsInfo.url&&Object(r["d"])(this.cmsInfo.url))location.href=this.cmsInfo.url;else if(this.cmsInfo.model&&this.isJSON(this.cmsInfo.model)){var e=this.cmsInfo.model.replace("${activityId}",this.cmsInfo.activityId);this.$router.push(JSON.parse(e))}else"cmsIndex"==this.$route.name?this.changeShowType(t):this.$router.push({name:"cmsIndex",query:{id:this.activityId,cmsId:t}})}},Object(o["a"])(i,"bannerIndexChange",(function(t){this.bannerIndex=t})),Object(o["a"])(i,"isJSON",(function(t){try{return JSON.parse(t),!0}catch(e){return!1}})),i)},g=p,y=(n("dd7a"),Object(f["a"])(g,a,s,!1,null,"7bd3d808",null));e["default"]=y.exports},"7dcb":function(t,e,n){"use strict";n("a481"),n("4917");e["a"]={getVersion:function(){var t=navigator.userAgent||window.navigator.userAgent,e=/HUAWEI|HONOR/gi,n=/[^;]+(?= Build)/gi,i=/CPU iPhone OS \d[_\d]*/gi,a=/CPU OS \d[_\d]*/gi,s=/Windows NT \d[\.\d]*/gi,o=/Linux x\d[_\d]*/gi,r=/Mac OS X \d[_|\.\d]*/gi;return/Android/gi.test(t)?e.test(t)?t.match(e)[0]+t.match(n)[0]:t.match(n)[0]:/iPhone/gi.test(t)?t.match(i)[0].replace(/CPU iPhone OS/gi,"iPhone").replace(/_/g,"."):/iPad/gi.test(t)?t.match(a)[0].replace(/CPU OS/gi,"iPad").replace(/_/g,"."):/Windows/gi.test(t)?"Windows "+(Math.min(parseInt(t.match(s)[0].replace(/Windows NT /,""))+1,10)<7?"XP":Math.min(parseInt(t.match(s)[0].replace(/Windows NT /,""))+1,10)):/Linux/gi.test(t)?t.match(o)[0]:/Macintosh/gi.test(t)?t.match(r)[0].replace(/_/g,"."):"unknown"}}},a1cf:function(t,e,n){},ade3:function(t,e,n){"use strict";n.d(e,"a",(function(){return o}));var i=n("53ca");function a(t,e){if("object"!==Object(i["a"])(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var a=n.call(t,e||"default");if("object"!==Object(i["a"])(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function s(t){var e=a(t,"string");return"symbol"===Object(i["a"])(e)?e:String(e)}function o(t,e,n){return e=s(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}},ae1c:function(t,e,n){"use strict";n("a1cf")},cad8:function(t,e,n){},dd7a:function(t,e,n){"use strict";n("cad8")},fabd:function(t,e,n){"use strict";n.r(e);n("7f7f");var i=function(){var t=this,e=t._self._c;return e("div",{class:t.isMobilePhone?"page":"page pc-container"},[t.isMobilePhone?t._e():e("pcheader"),e("van-nav-bar",{attrs:{title:"业务员注册","left-text":"返回","left-arrow":""},on:{"click-left":function(e){return t.$router.go(-1)}}}),t.checkingStatus?e("div",{staticStyle:{padding:"50px","text-align":"center"}},[e("van-loading",{attrs:{size:"24px",vertical:""}},[t._v("正在验证身份...")])],1):t.isSalesman?t._e():e("div",[t.inviterInfo||t.channelInfo?e("van-card",{staticStyle:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",margin:"20px","border-radius":"10px","box-shadow":"0 2px 8px rgba(0, 0, 0, 0.1)",color:"white"}},[e("div",{staticStyle:{"padding-top":"10px","padding-left":"10px","font-size":"18px","font-weight":"bold",color:"white"},attrs:{slot:"title"},slot:"title"},[e("van-icon",{staticStyle:{"margin-right":"8px"},attrs:{name:"salesman"===t.inviteType?"user-o":"shop-o"}}),t._v("\n        邀请信息\n      ")],1),e("div",{staticStyle:{padding:"10px"},attrs:{slot:"desc"},slot:"desc"},["salesman"===t.inviteType&&t.inviterInfo?e("div",{staticClass:"invite-info"},[e("p",[t._v(t._s(t.inviterInfo.name)+" 邀请您成为业务员")]),e("p",[t._v("手机号："+t._s(t.inviterInfo.mobile))]),e("p",[t._v("成为业务员后，您将获得推广权限和佣金收益")])]):t._e(),"channel"===t.inviteType&&t.channelInfo?e("div",{staticClass:"invite-info"},[e("p",[t._v(t._s(t.channelInfo.name)+" 邀请您成为业务员")]),e("p",[t._v("联系人："+t._s(t.channelInfo.contactName))]),t.channelInfo.contactMobile?e("p",[t._v("联系电话："+t._s(t.channelInfo.contactMobile))]):t._e(),e("p",[t._v("成为业务员后，您将获得推广权限和佣金收益")])]):t._e()])]):t._e(),e("div",{staticStyle:{margin:"20px"}},[e("van-form",{on:{submit:t.onSubmit}},[e("van-field",{attrs:{name:"name",label:"姓名",placeholder:"请输入您的姓名",rules:[{required:!0,message:"请输入姓名"}]},model:{value:t.form.name,callback:function(e){t.$set(t.form,"name",e)},expression:"form.name"}}),e("van-field",{attrs:{name:"mobile",label:"手机号",placeholder:"请输入手机号",rules:[{required:!0,message:"请输入手机号"},{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确"}]},model:{value:t.form.mobile,callback:function(e){t.$set(t.form,"mobile",e)},expression:"form.mobile"}}),e("van-field",{attrs:{name:"email",label:"邮箱",placeholder:"请输入邮箱（可选）"},model:{value:t.form.email,callback:function(e){t.$set(t.form,"email",e)},expression:"form.email"}}),e("van-field",{attrs:{name:"department",label:"部门",placeholder:"请输入所属部门（可选）"},model:{value:t.form.department,callback:function(e){t.$set(t.form,"department",e)},expression:"form.department"}}),e("van-field",{attrs:{name:"position",label:"职位",placeholder:"请输入职位（可选）"},model:{value:t.form.position,callback:function(e){t.$set(t.form,"position",e)},expression:"form.position"}}),e("div",{staticStyle:{margin:"30px 0"}},[e("van-button",{attrs:{round:"",block:"",type:"primary","native-type":"submit",loading:t.submitting}},[t._v("\n          "+t._s(t.submitting?"注册中...":"确认注册")+"\n        ")])],1)],1)],1),e("van-card",{staticStyle:{background:"white",margin:"20px","border-radius":"10px","box-shadow":"0 2px 8px rgba(0, 0, 0, 0.1)"}},[e("div",{staticStyle:{"padding-top":"10px","padding-left":"10px","font-size":"16px","font-weight":"bold",color:"#333"},attrs:{slot:"title"},slot:"title"},[e("van-icon",{staticStyle:{"margin-right":"8px",color:"#1989fa"},attrs:{name:"info-o"}}),t._v("\n      业务员权益\n    ")],1),e("div",{staticStyle:{padding:"10px"},attrs:{slot:"desc"},slot:"desc"},[e("div",{staticClass:"benefits"},[e("div",{staticClass:"benefit-item"},[e("van-icon",{attrs:{name:"gold-coin-o",color:"#ff976a"}}),e("span",[t._v("推广佣金：获得推广订单的佣金收益")])],1),e("div",{staticClass:"benefit-item"},[e("van-icon",{attrs:{name:"friends-o",color:"#07c160"}}),e("span",[t._v("团队建设：可以邀请下级业务员")])],1),e("div",{staticClass:"benefit-item"},[e("van-icon",{attrs:{name:"chart-trending-o",color:"#1989fa"}}),e("span",[t._v("业绩统计：实时查看个人和团队业绩")])],1),e("div",{staticClass:"benefit-item"},[e("van-icon",{attrs:{name:"gift-o",color:"#ee0a24"}}),e("span",[t._v("奖励机制：多种奖励和激励政策")])],1)])])])],1),e("van-dialog",{attrs:{title:"注册成功","show-cancel-button":!1,"confirm-button-text":"开始使用"},on:{confirm:t.goToSalesmanCenter},model:{value:t.showSuccess,callback:function(e){t.showSuccess=e},expression:"showSuccess"}},[e("div",{staticClass:"success-content"},[e("van-icon",{staticStyle:{display:"block",margin:"20px auto"},attrs:{name:"success",size:"60",color:"#07c160"}}),e("p",[t._v("恭喜您成功注册为业务员！")]),e("p",[t._v("您现在可以开始推广并获得佣金收益了")])],1)])],1)},a=[],s=(n("a481"),n("96cf"),n("1da1")),o=n("cacf"),r=n("1b69"),c={components:{pcheader:r["default"]},data:function(){return{isMobilePhone:Object(o["c"])(),inviterId:null,channelId:null,inviterInfo:null,channelInfo:null,inviteType:"salesman",submitting:!1,showSuccess:!1,userInfo:null,isSalesman:!1,checkingStatus:!0,form:{name:"",mobile:"",email:"",department:"",position:""}}},mounted:function(){if(document.title="业务员注册",this.inviterId=this.$route.query.inviterId,this.channelId=this.$route.query.channelId,this.inviterId)this.inviteType="salesman";else{if(!this.channelId)return vant.Toast("邀请链接无效"),void this.$router.go(-1);this.inviteType="channel"}this.getUserInfo()},methods:{getUserInfo:function(){var t=this;this.$fly.get("/pyp/wxUser/getUserInfo").then((function(e){200==e.code&&(t.userInfo=e.data,t.fillUserInfo(),t.checkSalesmanStatus(),t.loadInviterInfo())})).catch((function(t){console.error("获取用户信息失败:",t),vant.Toast("获取用户信息失败，请重试")}))},fillUserInfo:function(){this.userInfo&&(this.form.name=this.userInfo.nickname||"",this.form.mobile=this.userInfo.mobile||"")},checkSalesmanStatus:function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(){var e,n=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$fly.get("/pyp/web/salesman/scanByAuth");case 3:if(e=t.sent,200!==e.code){t.next=10;break}return this.isSalesman=!0,vant.Dialog.alert({title:"提示",message:"您已经是业务员，无需重复注册",confirmButtonText:"确定"}).then((function(){n.$router.push("/salesman/qrcode")})),t.abrupt("return");case 10:this.isSalesman=!1;case 11:t.next=17;break;case 13:t.prev=13,t.t0=t["catch"](0),console.error("检查业务员状态失败:",t.t0),this.isSalesman=!1;case 17:return t.prev=17,this.checkingStatus=!1,t.finish(17);case 20:case"end":return t.stop()}}),t,this,[[0,13,17,20]])})));function e(){return t.apply(this,arguments)}return e}(),loadInviterInfo:function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(){var e,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,"salesman"!==this.inviteType){t.next=8;break}return t.next=4,this.$fly.get("/pyp/web/salesman/info/".concat(this.inviterId));case 4:e=t.sent,200===e.code?this.inviterInfo=e.result:vant.Toast("获取邀请人信息失败"),t.next=13;break;case 8:if("channel"!==this.inviteType){t.next=13;break}return t.next=11,this.$fly.get("/pyp/web/channel/info/".concat(this.channelId));case 11:n=t.sent,200===n.code?this.channelInfo=n.result:vant.Toast("获取渠道信息失败");case 13:t.next=19;break;case 15:t.prev=15,t.t0=t["catch"](0),console.error("获取邀请信息失败:",t.t0),vant.Toast("获取邀请信息失败");case 19:case"end":return t.stop()}}),t,this,[[0,15]])})));function e(){return t.apply(this,arguments)}return e}(),onSubmit:function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(){var e,n,i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.submitting=!0,e={name:this.form.name,mobile:this.form.mobile,email:this.form.email,department:this.form.department,position:this.form.position},"salesman"===this.inviteType?(n="/pyp/web/salesman/processInvite",e.inviterId=this.inviterId):"channel"===this.inviteType&&(n="/pyp/web/salesman/processChannelInvite",e.channelId=this.channelId),t.next=6,this.$fly.get(n,e);case 6:i=t.sent,200===i.code?this.showSuccess=!0:vant.Toast(i.msg||"注册失败"),t.next=14;break;case 10:t.prev=10,t.t0=t["catch"](0),console.error("注册失败:",t.t0),vant.Toast("注册失败，请重试");case 14:return t.prev=14,this.submitting=!1,t.finish(14);case 17:case"end":return t.stop()}}),t,this,[[0,10,14,17]])})));function e(){return t.apply(this,arguments)}return e}(),goToSalesmanCenter:function(){this.$router.replace({name:"salesmanQrcode"})}}},l=c,u=(n("ae1c"),n("2877")),d=Object(u["a"])(l,i,a,!1,null,"2f2321f4",null);e["default"]=d.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-878ed12a","chunk-bc6d6208"],{"0c3d":function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("div",[e("el-dialog",{attrs:{title:"更改房型","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"}},[e("el-row",{staticClass:"row"},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"新的酒店",prop:"hotelActivityId"}},[e("el-select",{on:{change:t.hotelChange},model:{value:t.dataForm.hotelActivityId,callback:function(e){t.$set(t.dataForm,"hotelActivityId",e)},expression:"dataForm.hotelActivityId"}},t._l(t.hotels,(function(t){return e("el-option",{key:t.id,attrs:{label:t.hotelName,value:t.id}})})),1)],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"新的房型",prop:"hotelActivityRoomId"}},[e("el-select",{on:{change:t.roomChange},model:{value:t.dataForm.hotelActivityRoomId,callback:function(e){t.$set(t.dataForm,"hotelActivityRoomId",e)},expression:"dataForm.hotelActivityRoomId"}},t._l(t.rooms,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1)],1),e("el-form-item",{attrs:{label:"酒店时间",prop:"inDate"}},[e("el-date-picker",{attrs:{"picker-options":t.pickerOptions,"value-format":"yyyy/MM/dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.dateChange},model:{value:t.times,callback:function(e){t.times=e},expression:"times"}})],1),e("el-row",{staticClass:"row"},[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"新的房间号",prop:"roomNumber"}},[e("div",{staticStyle:{display:"flex","align-items":"center",width:"400px"}},[e("el-input",{attrs:{placeholder:"新的房间号"},model:{value:t.dataForm.roomNumber,callback:function(e){t.$set(t.dataForm,"roomNumber",e)},expression:"dataForm.roomNumber"}}),e("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:t.addRoomNumber}},[t._v("选择已有房间号")])],1)])],1),e("el-col",{attrs:{span:8}},[t.indexRoom&&t.indexRoom.bedNumber>1?e("el-form-item",{attrs:{label:"房间类型",prop:"roomType"}},[e("el-select",{model:{value:t.dataForm.roomType,callback:function(e){t.$set(t.dataForm,"roomType",e)},expression:"dataForm.roomType"}},t._l(t.roomType,(function(t){return e("el-option",{key:t.key,attrs:{label:t.value,value:t.key}})})),1)],1):t._e()],1),e("el-col",{attrs:{span:8}},[t.indexRoom&&t.indexRoom.bedNumber>1&&0!=t.dataForm.roomType?e("el-form-item",{attrs:{label:"校验男女",prop:"checkSex"}},[e("el-switch",{model:{value:t.dataForm.checkSex,callback:function(e){t.$set(t.dataForm,"checkSex",e)},expression:"dataForm.checkSex"}})],1):t._e()],1)],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.select}},[t._v("确认")])],1)],1),t.hotelactivityroomassignselectrommnumberVisible?e("hotelactivityroomassignselectrommnumber",{ref:"hotelactivityroomassignselectrommnumber",on:{select:t.selectRoomNumber}}):t._e()],1)},o=[],n=(a("4de4"),a("d3b7"),a("ac1f"),a("00b4"),a("3ca3"),a("0643"),a("2382"),a("ddb0"),a("b42b")),r=a("7de9"),l=a("34ae"),s={data:function(){var t=this;return{hotelactivityroomassignselectrommnumberVisible:!1,times:[],hotels:[],rooms:[],indexRoom:{},roomType:l["b"],roomAssignStatus:l["a"],orderStatus:r["e"],visible:!1,dataForm:{contact:"",mobile:"",hotelActivityId:"",hotelActivityRoomId:"",roomNumber:"",activityId:"",inDate:"",outDate:"",roomType:0,dayNumber:1,checkSex:!0},dataList:[],dataListLoading:!1,dataRule:{hotelActivityRoomId:[{required:!0,message:"会议酒店房型id不能为空",trigger:"blur"}],activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],roomNumber:[{required:!0,message:"新的房间号不能为空",trigger:"blur"}],hotelActivityId:[{required:!0,message:"会议酒店id不能为空",trigger:"blur"}],roomType:[{required:!0,message:"房间类型不能为空",trigger:"blur"}],inDate:[{required:!0,message:"入住日期不能为空",trigger:"blur"}],outDate:[{required:!0,message:"退房日期不能为空",trigger:"blur"}],checkSex:[{required:!0,message:"检验男女不能为空",trigger:"blur"}]},timeOptionRange:"",pickerOptions:{onPick:function(e){var a=e.maxDate,i=e.minDate;i&&!a&&(t.timeOptionRange=i),a&&(t.timeOptionRange=null)},disabledDate:function(e){var a=t.timeOptionRange;if(a)return e.getTime()===a.getTime()}}}},components:{tagsEditor:function(){return a.e("chunk-4dba3ada").then(a.bind(null,"a55c"))},hotelactivityroomassignselectrommnumber:n["default"]},methods:{addRoomNumber:function(){var t=this;this.hotelactivityroomassignselectrommnumberVisible=!0,this.$nextTick((function(){t.$refs.hotelactivityroomassignselectrommnumber.init(t.dataForm.activityId,t.dataForm.hotelActivityId,t.dataForm.hotelActivityRoomId)}))},selectRoomNumber:function(t){this.dataForm.roomNumber=t},dateChange:function(t){this.dataForm.inDate=t[0],this.dataForm.outDate=t[1],console.log(t);var e=new Date(t[1]).getTime()/1e3-new Date(t[0]).getTime()/1e3;this.dataForm.dayNumber=parseInt(e/60/60/24)},init:function(t){var e=this;this.dataForm.id=t,this.$http({url:this.$http.adornUrl("/hotel/hotelactivityroomassign/info/".concat(this.dataForm.id)),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm=a.hotelActivityRoomAssign,e.times=[a.hotelActivityRoomAssign.inDate,a.hotelActivityRoomAssign.outDate],e.$set(e.dataForm,"checkSex",!0),e.dataForm.hotelActivityId&&e.findRoom(e.dataForm.hotelActivityId),e.findHotel())})),this.visible=!0},findHotel:function(){var t=this;this.$http({url:this.$http.adornUrl("/hotel/hotelactivity/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.hotels=a.result)}))},hotelChange:function(t){this.dataForm.hotelActivityRoomId="",this.findRoom(t)},findRoom:function(t){var e=this;this.$http({url:this.$http.adornUrl("/hotel/hotelactivityroom/findByHotelActivityId/".concat(t)),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.rooms=a.result,e.dataForm.hotelActivityRoomId&&(e.indexRoom=e.rooms.filter((function(t){return t.id==e.dataForm.hotelActivityRoomId}))[0]))}))},roomChange:function(t){this.indexRoom=this.rooms.filter((function(e){return e.id==t}))[0],this.dataForm.roomType=this.indexRoom.bedNumber>1?this.dataForm.roomType:0,this.dataForm.checkSex=!(this.indexRoom.bedNumber>1)||this.dataForm.checkSex},select:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$confirm("确认修改分房信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:t.$http.adornUrl("/hotel/hotelactivityroomassign/change"),method:"post",data:t.$http.adornData(t.dataForm)}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))}))},closeDialog:function(){this.$emit("refreshDataList")},isImageUrl:function(t){return t&&/.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(t)}}},d=s,m=a("2877"),c=Object(m["a"])(d,i,o,!1,null,null,null);e["default"]=c.exports},"34ae":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n})),a.d(e,"d",(function(){return r}));var i=[{key:0,value:"整间"},{key:1,value:"男床位"},{key:2,value:"女床位"}],o=[{key:0,value:"整间"},{key:1,value:"拼住"},{key:2,value:"拼住"}],n=[{key:0,value:"已取消"},{key:1,value:"已入住"}],r=[{key:0,value:"未开启"},{key:1,value:"已开启"}]},"7de9":function(t,e,a){"use strict";a.d(e,"g",(function(){return i})),a.d(e,"f",(function(){return o})),a.d(e,"e",(function(){return n})),a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return l})),a.d(e,"c",(function(){return s})),a.d(e,"d",(function(){return d}));var i=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],o=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],n=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],r=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],l=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],s=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],d=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]},b42b:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",[e("el-dialog",{attrs:{title:"选择房间号","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"房间号",clearable:""},model:{value:t.dataForm.number,callback:function(e){t.$set(t.dataForm,"number",e)},expression:"dataForm.number"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{size:"mini",data:t.dataList,border:""}},[e("el-table-column",{attrs:{prop:"roomName","header-align":"center",align:"center",label:"房型名称"}}),e("el-table-column",{attrs:{prop:"number","header-align":"center",align:"center",label:"房号"}}),e("el-table-column",{attrs:{prop:"isAssign","header-align":"center",align:"center",label:"是否分配"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-tag",{class:"tag-color tag-color-"+a.row.isAssign,attrs:{type:"primary"}},[t._v(t._s(t.yesOrNo[a.row.isAssign].value))])],1)}}])}),t.dataList.length>0?e("div",t._l(t.dataList[0].assignVos,(function(a,i){return e("el-table-column",{key:i,attrs:{"header-align":"center",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(t._s(a.row.assignVos[i].number)+" "),a.row.assignVos[i].number>0?e("span",[t._v("("+t._s(a.row.assignVos[i].number<1?t.roomType[a.row.assignVos[i].roomType].value:"满")+")")]):t._e()]}}],null,!0)},[e("template",{slot:"header"},[t._v(" "+t._s(a.date)+" ")])],2)})),1):t._e(),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"180",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{staticStyle:{color:"red"},attrs:{type:"text",size:"small"},on:{click:function(e){return t.assignHandle(a.row)}}},[t._v("选择")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}})],1)],1)},o=[],n=(a("d3b7"),a("3ca3"),a("ddb0"),a("34ae")),r=a("7de9"),l={data:function(){return{visible:!1,appid:"",roomType:n["b"],hotels:[],rooms:[],yesOrNo:r["g"],dataForm:{activityId:"",hotelActivityId:"",hotelActivityRoomId:"",number:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,addnumberVisible:!1,assignVisible:!1,assignpeopleVisible:!1}},components:{tagsEditor:function(){return a.e("chunk-4dba3ada").then(a.bind(null,"a55c"))}},methods:{init:function(t,e,a){this.dataForm.activityId=t,this.dataForm.hotelActivityId=e,this.dataForm.hotelActivityRoomId=a,this.visible=!0,this.getDataList()},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/hotel/hotelactivityroomnumber/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,number:this.dataForm.number,activityId:this.dataForm.activityId,hotelActivityId:this.dataForm.hotelActivityId,hotelActivityRoomId:this.dataForm.hotelActivityRoomId})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},assignHandle:function(t){this.$emit("select",t.number),this.visible=!1}}},s=l,d=a("2877"),m=Object(d["a"])(s,i,o,!1,null,null,null);e["default"]=m.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-332fba7f"],{4567:function(t,e,a){"use strict";a("f11a")},"66be":function(t,e,a){"use strict";a.r(e);a("a481"),a("7f7f");var i=function(){var t=this,e=t._self._c;return e("div",{class:t.isMobilePhone?"":"pc-container"},[t.isMobilePhone?t._e():e("pcheader"),e("van-card",{staticStyle:{background:"white"},attrs:{thumb:t.guestInfo.avatar?t.guestInfo.avatar:"http://mpjoy.oss-cn-beijing.aliyuncs.com/20240917/d9301e81ab674112b3acf4a84aff4e96.png"}},[e("div",{staticStyle:{"font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.guestInfo.name))]),e("div",{staticStyle:{"padding-top":"10px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[t.guestInfo.unit?e("van-tag",{staticStyle:{margin:"5px 10px 5px 0px"},attrs:{size:"medium",round:"",type:"primary",plain:""}},[t._v(t._s(t.guestInfo.unit))]):t._e(),t.guestInfo.duties?e("van-tag",{staticStyle:{margin:"5px 10px 5px 0px"},attrs:{size:"medium",round:"",type:"warning",plain:""}},[t._v(t._s(t.guestInfo.duties))]):t._e()],1)]),t._m(0),e("van-cell-group",{attrs:{inset:""}},[e("van-field",{attrs:{name:"联系方式",label:"联系方式",required:"",rules:[{required:!0,message:"请填写联系方式"}]},model:{value:t.guestInfo.mobile,callback:function(e){t.$set(t.guestInfo,"mobile",e)},expression:"guestInfo.mobile"}}),e("van-field",{attrs:{name:"工作单位",label:"工作单位",required:"",rules:[{required:!0,message:"请填写工作单位"}]},model:{value:t.guestInfo.unit,callback:function(e){t.$set(t.guestInfo,"unit",e)},expression:"guestInfo.unit"}}),e("van-cell",{attrs:{title:"身份证类型",value:t.guestInfo.idCardType,"is-link":"",required:""},on:{click:function(e){t.idCardTypeShow=!0}}}),e("van-field",{attrs:{name:"身份证",label:"身份证",required:"",rules:[{required:!0,message:"请填写身份证"}]},model:{value:t.guestInfo.idCard,callback:function(e){t.$set(t.guestInfo,"idCard",e)},expression:"guestInfo.idCard"}}),e("van-cell",{attrs:{required:"",title:"地区","is-link":""},on:{click:function(e){t.areaShow=!0}},model:{value:t.guestInfo.areaName,callback:function(e){t.$set(t.guestInfo,"areaName",e)},expression:"guestInfo.areaName"}}),e("van-field",{attrs:{name:"银行卡号",label:"银行卡号",required:"",rules:[{required:!0,message:"请填写银行卡号"}]},model:{value:t.guestInfo.bank,callback:function(e){t.$set(t.guestInfo,"bank",e)},expression:"guestInfo.bank"}}),e("div",{staticStyle:{"font-size":"14px",color:"red","margin-left":"10px"}},[t._v("开户行（具体到支行）")]),e("van-field",{attrs:{name:"开户行",label:"开户行",required:"",rules:[{required:!0,message:"请填写开户行(精确到支行)"}]},model:{value:t.guestInfo.kaihuhang,callback:function(e){t.$set(t.guestInfo,"kaihuhang",e)},expression:"guestInfo.kaihuhang"}}),e("van-cell",{attrs:{title:"身份证正面",required:"",rules:[{required:!0,message:"请上传身份证正面"}]}},[e("van-uploader",{attrs:{"after-read":t.afterRead,name:"idCardZheng","before-read":t.beforeRead,accept:"image/*"}},[t.guestInfo.idCardZheng?e("van-image",{attrs:{height:"50px",src:t.guestInfo.idCardZheng,fit:"contain"}}):e("van-icon",{staticStyle:{"margin-left":"5px"},attrs:{slot:"default",name:"plus",size:"50px"},slot:"default"})],1)],1),e("van-cell",{attrs:{title:"身份证反面",required:"",rules:[{required:!0,message:"请上传身份证反面"}]}},[e("van-uploader",{attrs:{"after-read":t.afterRead,name:"idCardFan","before-read":t.beforeRead,accept:"image/*"}},[t.guestInfo.idCardFan?e("van-image",{attrs:{height:"50px",src:t.guestInfo.idCardFan,fit:"contain"}}):e("van-icon",{staticStyle:{"margin-left":"5px"},attrs:{slot:"default",name:"plus",size:"50px"},slot:"default"})],1)],1)],1),e("div",{staticStyle:{margin:"16px"}},[e("van-button",{attrs:{round:"",block:"",type:"info",loading:t.loading,"loading-text":"提交中"},on:{click:t.submit}},[t._v("核对无误，请点击确认")])],1),e("div",{staticStyle:{margin:"16px"}},[e("van-button",{attrs:{round:"",block:"",type:"primary"},on:{click:function(e){return t.$router.replace({path:"/schedules/expertIndex",query:{detailId:t.id}})}}},[t._v("返回上一页面")])],1),e("van-popup",{style:{height:"45%"},attrs:{position:"bottom"},model:{value:t.areaShow,callback:function(e){t.areaShow=e},expression:"areaShow"}},[e("van-area",{attrs:{title:"区域选择","area-list":t.areaList,value:t.areaCode},on:{cancel:function(e){t.areaShow=!1},confirm:t.areaSelect}})],1),e("van-action-sheet",{attrs:{actions:t.idCardType},on:{select:t.idCardTypeSelect},model:{value:t.idCardTypeShow,callback:function(e){t.idCardTypeShow=e},expression:"idCardTypeShow"}}),e("div",{directives:[{name:"show",rawName:"v-show",value:t.signVisible,expression:"signVisible"}],staticClass:"sign"},[e("sign-canvas",{ref:"SignCanvas",staticClass:"sign-canvas",attrs:{options:t.options},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}}),e("div",{staticStyle:{height:"60px",width:"100%",position:"absolute","z-index":"999",bottom:"0","background-color":"#f1f1f1",display:"flex","align-items":"center","justify-content":"space-around"}},[e("div",{staticClass:"button",staticStyle:{background:"#07c160"},on:{click:function(e){t.signVisible=!1}}},[t._v("\n        返回\n      ")]),e("div",{staticClass:"button",staticStyle:{background:"#66c6f2"},on:{click:t.cleanSign}},[t._v("\n        清空\n      ")]),e("div",{staticClass:"button",on:{click:t.submitSign}},[t._v("保存")])])],1)],1)},n=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"nav-title",staticStyle:{"margin-top":"8px"}},[e("div",{staticClass:"color"}),e("div",{staticClass:"text"},[t._v("劳务费信息填写")])])}],s=(a("34ef"),a("4917"),a("28a5"),a("6762"),a("2fdb"),a("66c7")),o=a("cacf"),r=a("1b69"),c=a("f4b0"),l=a.n(c),d=a("7de9"),u=a("434d"),f={components:{pcheader:r["default"],SignCanvas:l.a},data:function(){return{areaList:u["a"],areaCode:"",idCardType:d["c"],loading:!1,areaShow:!1,idCardTypeShow:!1,signVisible:!1,isMobilePhone:Object(o["c"])(),openid:void 0,activeName:["1","2","3"],activityId:void 0,id:void 0,guestInfo:{},schedule:[],scheduleDiscuss:[],scheduleSpeaker:[],topic:[],topicSpeaker:[],activityInfo:{},value:null,options:{isFullScreen:!0,isFullCover:!1,isDpr:!1,lastWriteSpeed:1,lastWriteWidth:2,lineCap:"round",lineJoin:"bevel",canvasWidth:350,canvasHeight:370,isShowBorder:!1,bgColor:"#ffffff",borderWidth:1,borderColor:"#ff787f",writeWidth:5,maxWriteWidth:30,minWriteWidth:5,writeColor:"#101010",isSign:!0,imgType:"png"}}},mounted:function(){this.id=this.$route.query.detailId,this.openid=this.$cookie.get("openid"),this.getActivityList(),this.getTopicAndSchedule()},methods:{preImage:function(t){vant.ImagePreview({images:[t],closeable:!0})},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(e){if(t.loading=!1,200==e.code){t.activityInfo=e.activity,t.activityInfo.backImg=t.activityInfo.backImg||"http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png",document.title="劳务费信息填写-"+t.guestInfo.name;var a=s["a"].formatDate.format(new Date(t.activityInfo.startTime),"yyyy年MM月dd日"),i=s["a"].formatDate.format(new Date(t.activityInfo.endTime),"MM月dd日");if(a.includes(i)){var n="时间:"+a+"\n地址:"+t.activityInfo.address;t.$wxShare(t.guestInfo.name+"-劳务费信息填写-"+t.activityInfo.name,t.activityInfo.shareUrl?t.activityInfo.shareUrl:t.activityInfo.mobileBanner.split(",")[0],n)}else{var o="时间:"+a+"-"+i+"\n地址:"+t.activityInfo.address;t.$wxShare(t.guestInfo.name+"-劳务费信息填写-"+t.activityInfo.name,t.activityInfo.shareUrl?t.activityInfo.shareUrl:t.activityInfo.mobileBanner.split(",")[0],o)}}else vant.Toast(e.msg),t.activityInfo={}}))},getTopicAndSchedule:function(){var t=this;this.$fly.get("/pyp/web/activity/activityguest/getTopicAndSchedule/".concat(this.id)).then((function(e){t.loading=!1,200==e.code?(t.topic=e.result.topic,t.topicSpeaker=e.result.topicSpeaker,t.schedule=e.result.schedule,t.scheduleSpeaker=e.result.scheduleSpeaker,t.scheduleDiscuss=e.result.scheduleDiscuss):(vant.Toast(e.msg),t.activityInfo={})}))},getActivityList:function(){var t=this;this.$fly.get("/pyp/web/activity/activityguest/getById/".concat(this.id)).then((function(e){200==e.code?(t.guestInfo=e.result,t.activityId=e.result.activityId,t.getActivityInfo()):(vant.Toast(e.msg),t.guestInfo={})}))},showTask:function(){this.$router.push({name:"schedulesExpertDetail",query:{detailId:this.id,id:this.activityId}})},cmsTurnBack:function(){this.activityInfo.backUrl?window.open(this.activityInfo.backUrl):this.$router.replace({name:"cmsIndex",query:{id:this.activityInfo.id}})},afterRead:function(t,e){var a=this;console.log(t);var i=e.name;t.status="uploading",t.message="上传中...";var n=new FormData;n.append("file",t.file),this.$fly.post("/pyp/web/upload",n).then((function(t){t&&200===t.code&&a.$set(a.guestInfo,i,t.result)}))},beforeRead:function(t){return!0},cleanSign:function(){this.$refs.SignCanvas.canvasClear()},submitSign:function(){var t=this,e=this.$refs.SignCanvas.saveAsImg(),a=e.split(","),i=a[0].match(/:(.*?);/)[1],n=i.split("/")[1],s=atob(a[1]),o=s.length,r=new Uint8Array(o);while(o--)r[o]=s.charCodeAt(o);var c=new File([r],"sign.".concat(n),{type:i}),l=new FormData;l.append("file",c),this.$fly.post("/pyp/web/upload",l).then((function(e){e&&200===e.code&&(t.signVisible=!1,t.$set(t.guestInfo,"serviceSign",e.result))}))},idCardTypeSelect:function(t){this.idCardTypeShow=!1,this.guestInfo.idCardType=t.name},areaSelect:function(t){var e=t[0].code+","+t[1].code+","+t[2].code,a=t[0].name+","+t[1].name+","+t[2].name;this.areaCode=t[2].code,this.$set(this.guestInfo,"area",e),this.$set(this.guestInfo,"areaName",a),this.areaShow=!1},submit:function(){var t=this;return this.guestInfo.mobile?Object(o["b"])(this.guestInfo.mobile)?this.guestInfo.unit?this.guestInfo.idCard?Object(o["a"])(this.guestInfo.idCard)?this.guestInfo.bank?this.guestInfo.kaihuhang?(this.guestInfo.isService=1,this.guestInfo.isServiceTime=s["a"].formatDate.format(new Date,"yyyy/MM/dd hh:mm:ss"),this.loading=!0,void this.$fly.post("/pyp/web/activity/activityguest/updateInfo",this.guestInfo).then((function(e){t.loading=!1,e&&200===e.code?vant.Dialog.confirm({title:"更新成功",message:"点击确定，返回继续完善其他信息"}).then((function(){t.$router.replace({path:"/schedules/expertIndex",query:{detailId:t.id}})})).catch((function(){t.getActivityList()})):vant.Toast(e.msg)}))):(vant.Toast("请输入开户行"),!1):(vant.Toast("请输入银行卡号"),!1):(vant.Toast("请输入正确的身份证"),!1):(vant.Toast("请输入身份证"),!1):(vant.Toast("请输入工作单位"),!1):(vant.Toast("请输入正确的手机号"),!1):(vant.Toast("请输入联系方式"),!1)}}},g=f,v=(a("4567"),a("2877")),p=Object(v["a"])(g,i,n,!1,null,"601567cd",null);e["default"]=p.exports},f11a:function(t,e,a){}}]);
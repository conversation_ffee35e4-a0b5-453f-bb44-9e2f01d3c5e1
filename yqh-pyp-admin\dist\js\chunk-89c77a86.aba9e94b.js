(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-89c77a86"],{"7c2d":function(t,e,a){"use strict";a("d728")},"9f7b":function(t,e,a){"use strict";a.r(e);a("b0c0");var r=function(){var t=this,e=t._self._c;return e("div",[e("el-dialog",{attrs:{title:"二维码管理","close-on-click-modal":!1,visible:t.visible,width:"80%"},on:{"update:visible":function(e){t.visible=e}}},[e("div",{staticClass:"qrcode-manage"},[e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-select",{attrs:{placeholder:"选择活动（可选）",clearable:""},model:{value:t.dataForm.activityId,callback:function(e){t.$set(t.dataForm,"activityId",e)},expression:"dataForm.activityId"}},t._l(t.activityList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.generateQrcode()}}},[t._v("生成二维码")]),e("el-button",{on:{click:function(e){return t.getQrcodeList()}}},[t._v("刷新")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.qrcodeList,border:""}},[e("el-table-column",{attrs:{prop:"qrcodeContent","header-align":"center",align:"center",width:"120",label:"二维码"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticStyle:{cursor:"pointer"},on:{click:function(e){return t.previewQrcode(a.row.qrcodeContent)}}},[e("qrcode-generator",{attrs:{value:a.row.qrcodeContent,size:80}})],1)]}}])}),e("el-table-column",{attrs:{prop:"activityName","header-align":"center",align:"center",label:"关联活动"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.activityName||"通用二维码")+" ")]}}])}),e("el-table-column",{attrs:{prop:"scanCount","header-align":"center",align:"center",label:"扫码次数"}}),e("el-table-column",{attrs:{prop:"orderCount","header-align":"center",align:"center",label:"订单数量"}}),e("el-table-column",{attrs:{prop:"totalAmount","header-align":"center",align:"center",label:"总金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" ¥"+t._s(e.row.totalAmount)+" ")]}}])}),e("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",label:"状态"},scopedSlots:t._u([{key:"default",fn:function(a){return[0===a.row.status?e("el-tag",{attrs:{size:"small",type:"danger"}},[t._v("禁用")]):e("el-tag",{attrs:{size:"small",type:"success"}},[t._v("启用")])]}}])}),e("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",width:"180",label:"创建时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.copyQrcodeUrl(a.row.qrcodeContent)}}},[t._v("复制链接")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.updateStatus(a.row.id,1===a.row.status?0:1)}}},[t._v(" "+t._s(1===a.row.status?"禁用":"启用")+" ")])]}}])})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("关闭")])],1)]),e("el-dialog",{attrs:{title:"二维码预览",visible:t.previewVisible,width:"400px"},on:{"update:visible":function(e){t.previewVisible=e}}},[e("div",{staticStyle:{"text-align":"center"}},[e("qrcode-generator",{ref:"previewQrcode",attrs:{value:t.previewContent,size:300}}),e("div",{staticStyle:{"margin-top":"10px"}},[e("el-button",{attrs:{type:"primary"},on:{click:t.downloadQrcode}},[t._v("下载二维码")]),e("el-button",{on:{click:function(e){return t.copyQrcodeUrl(t.previewContent)}}},[t._v("复制链接")])],1)],1)])],1)},i=[],n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"qrcode-generator"},[e("vue-qrcode",{ref:"qrcode",attrs:{value:t.value,options:t.qrcodeOptions},on:{ready:t.onQrcodeReady}})],1)},o=[],s=(a("a9e3"),a("b2e5")),c=a.n(s),l={name:"QrcodeGenerator",components:{VueQrcode:c.a},props:{value:{type:String,required:!0},size:{type:Number,default:200},level:{type:String,default:"M"}},computed:{qrcodeOptions:function(){return{width:this.size,height:this.size,errorCorrectionLevel:this.level,type:"image/png",quality:.92,margin:1,color:{dark:"#000000",light:"#FFFFFF"}}}},methods:{onQrcodeReady:function(){},getDataURL:function(){if(this.$refs.qrcode&&this.$refs.qrcode.$el){var t=this.$refs.qrcode.$el.querySelector("canvas");if(t)return t.toDataURL("image/png")}return null},download:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"qrcode.png",e=this.getDataURL();if(e){var a=document.createElement("a");a.download=t,a.href=e,a.click()}else console.error("无法获取二维码数据")}}},d=l,u=(a("abab"),a("2877")),p=Object(u["a"])(d,n,o,!1,null,"6a10c7d4",null),m=p.exports,v={components:{QrcodeGenerator:m},data:function(){return{visible:!1,salesmanId:0,dataForm:{activityId:""},qrcodeList:[],activityList:[],dataListLoading:!1,previewVisible:!1,previewContent:""}},methods:{init:function(t){var e=this;this.salesmanId=t,this.visible=!0,this.$nextTick((function(){e.getActivityList(),e.getQrcodeList()}))},getActivityList:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activity/list"),method:"get",params:this.$http.adornParams({page:1,limit:1e3})}).then((function(e){var a=e.data;a&&200===a.code&&(t.activityList=a.page.list)}))},getQrcodeList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/salesman/qrcode/findBySalesmanId/".concat(this.salesmanId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code?t.qrcodeList=a.result:t.qrcodeList=[],t.dataListLoading=!1}))},generateQrcode:function(){var t=this;this.$http({url:this.$http.adornUrl("/salesman/qrcode/generate"),method:"post",params:this.$http.adornParams({salesmanId:this.salesmanId,activityId:this.dataForm.activityId||void 0})}).then((function(e){var a=e.data;a&&200===a.code?(t.$message({message:"生成二维码成功",type:"success",duration:1500}),t.getQrcodeList()):t.$message.error(a.msg)}))},previewQrcode:function(t){this.previewContent=t,this.previewVisible=!0},downloadQrcode:function(){this.$refs.previewQrcode&&this.$refs.previewQrcode.download("salesman-qrcode.png")},copyQrcodeUrl:function(t){var e=document.createElement("input");e.value=t,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),this.$message({message:"链接已复制到剪贴板",type:"success",duration:1500})},updateStatus:function(t,e){var a=this;this.$http({url:this.$http.adornUrl("/salesman/qrcode/updateStatus"),method:"post",params:this.$http.adornParams({id:t,status:e})}).then((function(t){var e=t.data;e&&200===e.code?(a.$message({message:"操作成功",type:"success",duration:1500}),a.getQrcodeList()):a.$message.error(e.msg)}))}}},h=v,f=(a("7c2d"),Object(u["a"])(h,r,i,!1,null,"9bccc802",null));e["default"]=f.exports},a090:function(t,e,a){},abab:function(t,e,a){"use strict";a("a090")},d728:function(t,e,a){}}]);
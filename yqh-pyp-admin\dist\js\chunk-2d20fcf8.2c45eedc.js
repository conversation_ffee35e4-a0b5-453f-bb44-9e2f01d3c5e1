(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d20fcf8"],{b4e5:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-log"},[e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"用户名／用户操作",clearable:""},model:{value:t.dataForm.key,callback:function(e){t.$set(t.dataForm,"key",e)},expression:"dataForm.key"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""}},[e("el-table-column",{attrs:{prop:"id","header-align":"center",align:"center",width:"80",label:"ID"}}),e("el-table-column",{attrs:{prop:"username","header-align":"center",align:"center",label:"用户名"}}),e("el-table-column",{attrs:{prop:"operation","header-align":"center",align:"center",label:"用户操作"}}),e("el-table-column",{attrs:{prop:"method","header-align":"center",align:"center",width:"150","show-overflow-tooltip":!0,label:"请求方法"}}),e("el-table-column",{attrs:{prop:"params","header-align":"center",align:"center",width:"150","show-overflow-tooltip":!0,label:"请求参数"}}),e("el-table-column",{attrs:{prop:"time","header-align":"center",align:"center",label:"执行时长(毫秒)"}}),e("el-table-column",{attrs:{prop:"ip","header-align":"center",align:"center",width:"150",label:"IP地址"}}),e("el-table-column",{attrs:{prop:"createDate","header-align":"center",align:"center",width:"180",label:"创建时间"}})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalCount,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}})],1)},l=[],i={data:function(){return{dataForm:{key:""},dataList:[],pageIndex:1,pageSize:10,totalCount:0,dataListLoading:!1,selectionDataList:[]}},created:function(){this.getDataList()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/sys/log/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,key:this.dataForm.key,sidx:"id",order:"desc"})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalCount=a.page.totalCount):(t.dataList=[],t.totalCount=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()}}},r=i,o=a("2877"),s=Object(o["a"])(r,n,l,!1,null,null,null);e["default"]=s.exports}}]);
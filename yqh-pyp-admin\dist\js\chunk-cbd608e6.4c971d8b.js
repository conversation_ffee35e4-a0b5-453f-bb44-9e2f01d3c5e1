(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-cbd608e6","chunk-89c77a86","chunk-53a28028","chunk-10e871e3","chunk-0506e191","chunk-0506e191"],{"0ccb":function(t,e,a){"use strict";var s=a("e330"),n=a("50c4"),i=a("577e"),r=a("1148"),l=a("1d80"),o=s(r),c=s("".slice),d=Math.ceil,u=function(t){return function(e,a,s){var r,u,m=i(l(e)),p=n(a),h=m.length,g=void 0===s?" ":i(s);return p<=h||""===g?m:(r=p-h,u=o(g,d(r/g.length)),u.length>r&&(u=c(u,0,r)),t?m+u:u+m)}};t.exports={start:u(!1),end:u(!0)}},1148:function(t,e,a){"use strict";var s=a("5926"),n=a("577e"),i=a("1d80"),r=RangeError;t.exports=function(t){var e=n(i(this)),a="",l=s(t);if(l<0||l===1/0)throw new r("Wrong number of repetitions");for(;l>0;(l>>>=1)&&(e+=e))1&l&&(a+=e);return a}},"284d":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"".concat(t.salesmanName," - 绑定客户列表"),"close-on-click-modal":!1,visible:t.visible,width:"80%"},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{staticClass:"search-form",attrs:{inline:!0,model:t.searchForm}},[e("el-form-item",{attrs:{label:"客户昵称"}},[e("el-input",{attrs:{placeholder:"请输入客户昵称",clearable:""},model:{value:t.searchForm.wxUserName,callback:function(e){t.$set(t.searchForm,"wxUserName",e)},expression:"searchForm.wxUserName"}})],1),e("el-form-item",{attrs:{label:"手机号"}},[e("el-input",{attrs:{placeholder:"请输入手机号",clearable:""},model:{value:t.searchForm.mobile,callback:function(e){t.$set(t.searchForm,"mobile",e)},expression:"searchForm.mobile"}})],1),e("el-form-item",{attrs:{label:"绑定状态"}},[e("el-select",{attrs:{placeholder:"全部",clearable:""},model:{value:t.searchForm.status,callback:function(e){t.$set(t.searchForm,"status",e)},expression:"searchForm.status"}},[e("el-option",{attrs:{label:"有效",value:1}}),e("el-option",{attrs:{label:"已失效",value:0}}),e("el-option",{attrs:{label:"已解绑",value:2}})],1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.getCustomerList()}}},[t._v("查询")]),e("el-button",{on:{click:function(e){return t.resetSearch()}}},[t._v("重置")])],1)],1),e("div",{staticClass:"stats-overview"},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v(t._s(t.customerStats.totalCustomers||0))]),e("div",{staticClass:"stats-label"},[t._v("总客户数")])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v(t._s(t.customerStats.activeCustomers||0))]),e("div",{staticClass:"stats-label"},[t._v("有效绑定")])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v(t._s(t.customerStats.todayBindings||0))]),e("div",{staticClass:"stats-label"},[t._v("今日新增")])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v("¥"+t._s(t.customerStats.totalOrderAmount||0))]),e("div",{staticClass:"stats-label"},[t._v("订单总额")])])])],1)],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:t.customerList,border:""}},[e("el-table-column",{attrs:{prop:"wxUserName","header-align":"center",align:"center",label:"客户信息"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticClass:"customer-info"},[e("div",{staticClass:"customer-name"},[t._v(t._s(a.row.wxUserName))]),e("div",{staticClass:"customer-mobile"},[t._v(t._s(a.row.wxUserMobile||"未绑定手机"))])])]}}])}),e("el-table-column",{attrs:{prop:"bindingType","header-align":"center",align:"center",label:"绑定方式"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:t.getBindingTypeTagType(a.row.bindingType)}},[t._v(" "+t._s(t.getBindingTypeText(a.row.bindingType))+" ")])]}}])}),e("el-table-column",{attrs:{prop:"bindingTime","header-align":"center",align:"center",width:"150",label:"绑定时间"}}),e("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",label:"状态"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:t.getStatusTagType(a.row.status)}},[t._v(" "+t._s(t.getStatusText(a.row.status))+" ")])]}}])}),e("el-table-column",{attrs:{prop:"expiryTime","header-align":"center",align:"center",width:"150",label:"失效时间"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.expiryTime?e("span",[t._v(t._s(a.row.expiryTime))]):e("span",{staticStyle:{color:"#67C23A"}},[t._v("永久有效")])]}}])}),e("el-table-column",{attrs:{"header-align":"center",align:"center",label:"订单数量"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticStyle:{color:"#409EFF"}},[t._v(t._s(a.row.orderCount))])]}}])}),e("el-table-column",{attrs:{"header-align":"center",align:"center",label:"订单金额"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticStyle:{color:"#E6A23C","font-weight":"bold"}},[t._v("¥"+t._s(a.row.orderAmount))])]}}])}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.viewOrdersHandle(a.row)}}},[t._v("查看订单")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.viewHistoryHandle(a.row)}}},[t._v("绑定历史")])]}}])})],1),e("el-pagination",{staticStyle:{"margin-top":"20px"},attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.exportCustomers()}}},[t._v("导出客户")]),e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("关闭")])],1)],1)},n=[],i=(a("14d9"),a("b680"),{data:function(){return{visible:!1,salesmanId:null,salesmanName:"",searchForm:{wxUserName:"",mobile:"",status:""},customerList:[],customerStats:{},listLoading:!1,pageIndex:1,pageSize:10,totalPage:0}},methods:{init:function(t,e){this.salesmanId=t,this.salesmanName=e,this.visible=!0,this.resetSearch(),this.getCustomerList(),this.getCustomerStats()},getCustomerList:function(){var t=this;this.listLoading=!0,this.$http({url:this.$http.adornUrl("/salesman/wxuserbinding/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,salesmanId:this.salesmanId,wxUserName:this.searchForm.wxUserName,mobile:this.searchForm.mobile,status:this.searchForm.status})}).then((function(e){var a=e.data;a&&200===a.code?(t.customerList=a.page.list||[],t.totalPage=a.page.totalCount||0):(t.customerList=[],t.totalPage=0),t.listLoading=!1})).catch((function(){t.listLoading=!1}))},getCustomerStats:function(){var t=this;this.$http({url:this.$http.adornUrl("/salesman/wxuserbinding/customerStats"),method:"get",params:this.$http.adornParams({salesmanId:this.salesmanId})}).then((function(e){var a=e.data;a&&200===a.code&&(t.customerStats=a.stats||{})}))},resetSearch:function(){this.searchForm={wxUserName:"",mobile:"",status:""},this.pageIndex=1},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getCustomerList()},currentChangeHandle:function(t){this.pageIndex=t,this.getCustomerList()},viewOrdersHandle:function(t){var e=this;this.visible=!1,this.$nextTick((function(){e.$router.push({path:"/salesman-order",query:{userId:t.wxUserId,salesmanId:e.salesmanId}})}))},viewHistoryHandle:function(t){var e=this;this.visible=!1,this.$nextTick((function(){e.$router.push({path:"/salesman-wx-user-binding",query:{wxUserId:t.wxUserId}})}))},exportCustomers:function(){this.$message.info("导出功能开发中...")},getBindingTypeText:function(t){var e={1:"二维码扫描",2:"邀请链接",3:"手动绑定",4:"系统分配"};return e[t]||"未知"},getBindingTypeTagType:function(t){var e={1:"primary",2:"success",3:"warning",4:"info"};return e[t]||""},getStatusText:function(t){var e={0:"已失效",1:"有效",2:"已解绑"};return e[t]||"未知"},getStatusTagType:function(t){var e={0:"danger",1:"success",2:"info"};return e[t]||""},getOrderCount:function(t){return Math.floor(20*Math.random())},getOrderAmount:function(t){return(1e4*Math.random()).toFixed(2)}}}),r=i,l=(a("4538"),a("2877")),o=Object(l["a"])(r,s,n,!1,null,"2ce1a99e",null);e["default"]=o.exports},"33ad":function(t,e,a){},4538:function(t,e,a){"use strict";a("33ad")},"498a":function(t,e,a){"use strict";var s=a("23e7"),n=a("58a8").trim,i=a("c8d2");s({target:"String",proto:!0,forced:i("trim")},{trim:function(){return n(this)}})},"4d90":function(t,e,a){"use strict";var s=a("23e7"),n=a("0ccb").start,i=a("9a0c");s({target:"String",proto:!0,forced:i},{padStart:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}})},"54d9":function(t,e,a){"use strict";a("8be4")},"7c2d":function(t,e,a){"use strict";a("d728")},"7db0":function(t,e,a){"use strict";var s=a("23e7"),n=a("b727").find,i=a("44d2"),r="find",l=!0;r in[]&&Array(1)[r]((function(){l=!1})),s({target:"Array",proto:!0,forced:l},{find:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}}),i(r)},"8be4":function(t,e,a){},"9a0c":function(t,e,a){"use strict";var s=a("b5db");t.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(s)},"9f7b":function(t,e,a){"use strict";a.r(e);a("b0c0");var s=function(){var t=this,e=t._self._c;return e("div",[e("el-dialog",{attrs:{title:"二维码管理","close-on-click-modal":!1,visible:t.visible,width:"80%"},on:{"update:visible":function(e){t.visible=e}}},[e("div",{staticClass:"qrcode-manage"},[e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-select",{attrs:{placeholder:"选择活动（可选）",clearable:""},model:{value:t.dataForm.activityId,callback:function(e){t.$set(t.dataForm,"activityId",e)},expression:"dataForm.activityId"}},t._l(t.activityList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.generateQrcode()}}},[t._v("生成二维码")]),e("el-button",{on:{click:function(e){return t.getQrcodeList()}}},[t._v("刷新")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.qrcodeList,border:""}},[e("el-table-column",{attrs:{prop:"qrcodeContent","header-align":"center",align:"center",width:"120",label:"二维码"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticStyle:{cursor:"pointer"},on:{click:function(e){return t.previewQrcode(a.row.qrcodeContent)}}},[e("qrcode-generator",{attrs:{value:a.row.qrcodeContent,size:80}})],1)]}}])}),e("el-table-column",{attrs:{prop:"activityName","header-align":"center",align:"center",label:"关联活动"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.activityName||"通用二维码")+" ")]}}])}),e("el-table-column",{attrs:{prop:"scanCount","header-align":"center",align:"center",label:"扫码次数"}}),e("el-table-column",{attrs:{prop:"orderCount","header-align":"center",align:"center",label:"订单数量"}}),e("el-table-column",{attrs:{prop:"totalAmount","header-align":"center",align:"center",label:"总金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" ¥"+t._s(e.row.totalAmount)+" ")]}}])}),e("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",label:"状态"},scopedSlots:t._u([{key:"default",fn:function(a){return[0===a.row.status?e("el-tag",{attrs:{size:"small",type:"danger"}},[t._v("禁用")]):e("el-tag",{attrs:{size:"small",type:"success"}},[t._v("启用")])]}}])}),e("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",width:"180",label:"创建时间"}}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.copyQrcodeUrl(a.row.qrcodeContent)}}},[t._v("复制链接")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.updateStatus(a.row.id,1===a.row.status?0:1)}}},[t._v(" "+t._s(1===a.row.status?"禁用":"启用")+" ")])]}}])})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("关闭")])],1)]),e("el-dialog",{attrs:{title:"二维码预览",visible:t.previewVisible,width:"400px"},on:{"update:visible":function(e){t.previewVisible=e}}},[e("div",{staticStyle:{"text-align":"center"}},[e("qrcode-generator",{ref:"previewQrcode",attrs:{value:t.previewContent,size:300}}),e("div",{staticStyle:{"margin-top":"10px"}},[e("el-button",{attrs:{type:"primary"},on:{click:t.downloadQrcode}},[t._v("下载二维码")]),e("el-button",{on:{click:function(e){return t.copyQrcodeUrl(t.previewContent)}}},[t._v("复制链接")])],1)],1)])],1)},n=[],i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"qrcode-generator"},[e("vue-qrcode",{ref:"qrcode",attrs:{value:t.value,options:t.qrcodeOptions},on:{ready:t.onQrcodeReady}})],1)},r=[],l=(a("a9e3"),a("b2e5")),o=a.n(l),c={name:"QrcodeGenerator",components:{VueQrcode:o.a},props:{value:{type:String,required:!0},size:{type:Number,default:200},level:{type:String,default:"M"}},computed:{qrcodeOptions:function(){return{width:this.size,height:this.size,errorCorrectionLevel:this.level,type:"image/png",quality:.92,margin:1,color:{dark:"#000000",light:"#FFFFFF"}}}},methods:{onQrcodeReady:function(){},getDataURL:function(){if(this.$refs.qrcode&&this.$refs.qrcode.$el){var t=this.$refs.qrcode.$el.querySelector("canvas");if(t)return t.toDataURL("image/png")}return null},download:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"qrcode.png",e=this.getDataURL();if(e){var a=document.createElement("a");a.download=t,a.href=e,a.click()}else console.error("无法获取二维码数据")}}},d=c,u=(a("abab"),a("2877")),m=Object(u["a"])(d,i,r,!1,null,"6a10c7d4",null),p=m.exports,h={components:{QrcodeGenerator:p},data:function(){return{visible:!1,salesmanId:0,dataForm:{activityId:""},qrcodeList:[],activityList:[],dataListLoading:!1,previewVisible:!1,previewContent:""}},methods:{init:function(t){var e=this;this.salesmanId=t,this.visible=!0,this.$nextTick((function(){e.getActivityList(),e.getQrcodeList()}))},getActivityList:function(){var t=this;this.$http({url:this.$http.adornUrl("/activity/activity/list"),method:"get",params:this.$http.adornParams({page:1,limit:1e3})}).then((function(e){var a=e.data;a&&200===a.code&&(t.activityList=a.page.list)}))},getQrcodeList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/salesman/qrcode/findBySalesmanId/".concat(this.salesmanId)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code?t.qrcodeList=a.result:t.qrcodeList=[],t.dataListLoading=!1}))},generateQrcode:function(){var t=this;this.$http({url:this.$http.adornUrl("/salesman/qrcode/generate"),method:"post",params:this.$http.adornParams({salesmanId:this.salesmanId,activityId:this.dataForm.activityId||void 0})}).then((function(e){var a=e.data;a&&200===a.code?(t.$message({message:"生成二维码成功",type:"success",duration:1500}),t.getQrcodeList()):t.$message.error(a.msg)}))},previewQrcode:function(t){this.previewContent=t,this.previewVisible=!0},downloadQrcode:function(){this.$refs.previewQrcode&&this.$refs.previewQrcode.download("salesman-qrcode.png")},copyQrcodeUrl:function(t){var e=document.createElement("input");e.value=t,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),this.$message({message:"链接已复制到剪贴板",type:"success",duration:1500})},updateStatus:function(t,e){var a=this;this.$http({url:this.$http.adornUrl("/salesman/qrcode/updateStatus"),method:"post",params:this.$http.adornParams({id:t,status:e})}).then((function(t){var e=t.data;e&&200===e.code?(a.$message({message:"操作成功",type:"success",duration:1500}),a.getQrcodeList()):a.$message.error(e.msg)}))}}},g=h,f=(a("7c2d"),Object(u["a"])(g,s,n,!1,null,"9bccc802",null));e["default"]=f.exports},a090:function(t,e,a){},abab:function(t,e,a){"use strict";a("a090")},b126:function(t,e,a){"use strict";a.r(e);a("b0c0");var s=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"80px"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.dataFormSubmit()}}},[e("el-form-item",{attrs:{label:"姓名",prop:"name"}},[e("el-input",{attrs:{placeholder:"业务员姓名"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",{attrs:{label:"编号",prop:"code"}},[e("el-input",{attrs:{placeholder:"业务员编号"},model:{value:t.dataForm.code,callback:function(e){t.$set(t.dataForm,"code",e)},expression:"dataForm.code"}},[e("el-button",{attrs:{slot:"append",loading:t.generating},on:{click:t.generateCode},slot:"append"},[t._v(" "+t._s(t.generating?"生成中":"自动生成")+" ")])],1)],1),e("el-form-item",{attrs:{label:"手机号",prop:"mobile"}},[e("el-input",{attrs:{placeholder:"手机号"},model:{value:t.dataForm.mobile,callback:function(e){t.$set(t.dataForm,"mobile",e)},expression:"dataForm.mobile"}})],1),e("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[e("el-input",{attrs:{placeholder:"邮箱"},model:{value:t.dataForm.email,callback:function(e){t.$set(t.dataForm,"email",e)},expression:"dataForm.email"}})],1),e("el-form-item",{attrs:{label:"所属渠道",prop:"channelId"}},[e("el-select",{attrs:{placeholder:"请选择所属渠道",clearable:""},model:{value:t.dataForm.channelId,callback:function(e){t.$set(t.dataForm,"channelId",e)},expression:"dataForm.channelId"}},t._l(t.channelList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"部门",prop:"department"}},[e("el-input",{attrs:{placeholder:"所属部门"},model:{value:t.dataForm.department,callback:function(e){t.$set(t.dataForm,"department",e)},expression:"dataForm.department"}})],1),e("el-form-item",{attrs:{label:"职位",prop:"position"}},[e("el-input",{attrs:{placeholder:"职位"},model:{value:t.dataForm.position,callback:function(e){t.$set(t.dataForm,"position",e)},expression:"dataForm.position"}})],1),e("el-form-item",{attrs:{label:"状态",prop:"status"}},[e("el-radio-group",{model:{value:t.dataForm.status,callback:function(e){t.$set(t.dataForm,"status",e)},expression:"dataForm.status"}},[e("el-radio",{attrs:{label:0}},[t._v("禁用")]),e("el-radio",{attrs:{label:1}},[t._v("启用")])],1)],1),e("el-form-item",{attrs:{label:"标签",prop:"tags"}},[e("el-input",{attrs:{placeholder:"请输入标签，多个标签用逗号分隔"},model:{value:t.dataForm.tags,callback:function(e){t.$set(t.dataForm,"tags",e)},expression:"dataForm.tags"}})],1),e("el-form-item",{attrs:{label:"上级业务员",prop:"parentId"}},[e("el-select",{attrs:{placeholder:"请选择上级业务员",clearable:"",filterable:""},model:{value:t.dataForm.parentId,callback:function(e){t.$set(t.dataForm,"parentId",e)},expression:"dataForm.parentId"}},t._l(t.parentSalesmanList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name+" ("+t.code+")",value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"备注",prop:"remarks"}},[e("el-input",{attrs:{type:"textarea",placeholder:"备注"},model:{value:t.dataForm.remarks,callback:function(e){t.$set(t.dataForm,"remarks",e)},expression:"dataForm.remarks"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)},n=[],i=a("ade3"),r=(a("99af"),a("4de4"),a("d3b7"),a("4d90"),a("0643"),a("2382"),{data:function(){return Object(i["a"])(Object(i["a"])({visible:!1,dataForm:{id:0,name:"",code:"",mobile:"",email:"",department:"",position:"",status:1,tags:"",parentId:"",channelId:"",remarks:""},channelList:[],parentSalesmanList:[],generating:!1},"generating",!1),"dataRule",{name:[{required:!0,message:"业务员姓名不能为空",trigger:"blur"}],code:[{required:!0,message:"业务员编号不能为空",trigger:"blur"}],mobile:[{required:!0,message:"手机号不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确",trigger:"blur"}],email:[{type:"email",message:"邮箱格式不正确",trigger:"blur"}]})},methods:{init:function(t){var e=this;this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.getChannelList(),e.getParentSalesmanList(),e.dataForm.id&&e.$http({url:e.$http.adornUrl("/salesman/salesman/info/".concat(e.dataForm.id)),method:"get",params:e.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm.name=a.salesman.name,e.dataForm.code=a.salesman.code,e.dataForm.mobile=a.salesman.mobile,e.dataForm.email=a.salesman.email,e.dataForm.department=a.salesman.department,e.dataForm.position=a.salesman.position,e.dataForm.status=a.salesman.status,e.dataForm.tags=a.salesman.tags,e.dataForm.channelId=a.salesman.channelId,e.dataForm.parentId=a.salesman.parentId,e.dataForm.remarks=a.salesman.remarks)}))}))},getChannelList:function(){var t=this;this.$http({url:this.$http.adornUrl("/channel/channel/select"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.channelList=a.channelList||[])}))},getParentSalesmanList:function(){var t=this;this.$http({url:this.$http.adornUrl("/salesman/salesman/findByAppid"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.parentSalesmanList=a.result.filter((function(e){return e.id!==t.dataForm.id})))}))},generateCode:function(){var t=this;this.generating=!0;var e=new Date,a=e.getFullYear(),s=String(e.getMonth()+1).padStart(2,"0"),n=String(e.getDate()).padStart(2,"0"),i=String(Math.floor(1e4*Math.random())).padStart(4,"0"),r="YW".concat(a).concat(s).concat(n).concat(i);this.$http({url:this.$http.adornUrl("/salesman/salesman/checkCode"),method:"get",params:this.$http.adornParams({code:r,excludeId:this.dataForm.id||null})}).then((function(e){var a=e.data;t.generating=!1,a&&200===a.code&&a.exists?t.generateCode():(t.dataForm.code=r,t.$message.success("编号生成成功"))})).catch((function(){t.generating=!1,t.dataForm.code=r,t.$message.success("编号生成成功")}))},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&t.$http({url:t.$http.adornUrl("/salesman/salesman/".concat(t.dataForm.id?"update":"save")),method:"post",data:t.$http.adornData({id:t.dataForm.id||void 0,name:t.dataForm.name,code:t.dataForm.code,mobile:t.dataForm.mobile,email:t.dataForm.email,department:t.dataForm.department,position:t.dataForm.position,status:t.dataForm.status,channelId:t.dataForm.channelId,tags:t.dataForm.tags,parentId:t.dataForm.parentId,remarks:t.dataForm.remarks})}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(a.msg)}))}))}}}),l=r,o=a("2877"),c=Object(o["a"])(l,s,n,!1,null,null,null);e["default"]=c.exports},b680:function(t,e,a){"use strict";var s=a("23e7"),n=a("e330"),i=a("5926"),r=a("408a"),l=a("1148"),o=a("d039"),c=RangeError,d=String,u=Math.floor,m=n(l),p=n("".slice),h=n(1..toFixed),g=function(t,e,a){return 0===e?a:e%2===1?g(t,e-1,a*t):g(t*t,e/2,a)},f=function(t){var e=0,a=t;while(a>=4096)e+=12,a/=4096;while(a>=2)e+=1,a/=2;return e},v=function(t,e,a){var s=-1,n=a;while(++s<6)n+=e*t[s],t[s]=n%1e7,n=u(n/1e7)},b=function(t,e){var a=6,s=0;while(--a>=0)s+=t[a],t[a]=u(s/e),s=s%e*1e7},y=function(t){var e=6,a="";while(--e>=0)if(""!==a||0===e||0!==t[e]){var s=d(t[e]);a=""===a?s:a+m("0",7-s.length)+s}return a},w=o((function(){return"0.000"!==h(8e-5,3)||"1"!==h(.9,0)||"1.25"!==h(1.255,2)||"1000000000000000128"!==h(0xde0b6b3a7640080,0)}))||!o((function(){h({})}));s({target:"Number",proto:!0,forced:w},{toFixed:function(t){var e,a,s,n,l=r(this),o=i(t),u=[0,0,0,0,0,0],h="",w="0";if(o<0||o>20)throw new c("Incorrect fraction digits");if(l!==l)return"NaN";if(l<=-1e21||l>=1e21)return d(l);if(l<0&&(h="-",l=-l),l>1e-21)if(e=f(l*g(2,69,1))-69,a=e<0?l*g(2,-e,1):l/g(2,e,1),a*=4503599627370496,e=52-e,e>0){v(u,0,a),s=o;while(s>=7)v(u,1e7,0),s-=7;v(u,g(10,s,1),0),s=e-1;while(s>=23)b(u,1<<23),s-=23;b(u,1<<s),v(u,1,1),b(u,2),w=y(u)}else v(u,0,a),v(u,1<<-e,0),w=y(u)+m("0",o);return o>0?(n=w.length,w=h+(n<=o?"0."+m("0",o-n)+w:p(w,0,n-o)+"."+p(w,n-o))):w=h+w,w}})},c8d2:function(t,e,a){"use strict";var s=a("5e77").PROPER,n=a("d039"),i=a("5899"),r="​᠎";t.exports=function(t){return n((function(){return!!i[t]()||r[t]()!==r||s&&i[t].name!==t}))}},d728:function(t,e,a){},da9b:function(t,e,a){"use strict";a.r(e);a("14d9"),a("b0c0"),a("498a");var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.getDataList()}}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"业务员姓名",clearable:""},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",[e("el-input",{attrs:{placeholder:"手机号",clearable:""},model:{value:t.dataForm.mobile,callback:function(e){t.$set(t.dataForm,"mobile",e)},expression:"dataForm.mobile"}})],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"所属渠道",clearable:""},model:{value:t.dataForm.channelId,callback:function(e){t.$set(t.dataForm,"channelId",e)},expression:"dataForm.channelId"}},t._l(t.channelList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增业务员")]),e("el-button",{attrs:{type:"warning"},on:{click:function(e){return t.inviteSalesmanHandle()}}},[t._v("邀请业务员")]),e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]),e("el-button",{on:{click:function(e){return t.exportStats()}}},[t._v("导出统计")]),e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.showSalesmanQrcode()}}},[t._v("业务员页面")]),e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.push({name:"salesman-commission-config"})}}},[t._v("提成配置")]),e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.push({name:"salesman-commission-settlement"})}}},[t._v("佣金结算")])],1),e("el-form-item",{staticStyle:{float:"right"}},[e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1),e("div",{staticClass:"stats-overview",staticStyle:{"margin-bottom":"20px"}},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:5}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v(t._s(t.overallStats.totalSalesmen||0))]),e("div",{staticClass:"stats-label"},[t._v("活跃业务员")])])])],1),e("el-col",{attrs:{span:5}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v(t._s(t.overallStats.totalOrders||0))]),e("div",{staticClass:"stats-label"},[t._v("总订单数")])])])],1),e("el-col",{attrs:{span:5}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v("¥"+t._s(t.overallStats.totalAmount||0))]),e("div",{staticClass:"stats-label"},[t._v("总销售额")])])])],1),e("el-col",{attrs:{span:5}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v("¥"+t._s(t.overallStats.totalPayAmount||0))]),e("div",{staticClass:"stats-label"},[t._v("已收款金额")])])])],1),e("el-col",{attrs:{span:4}},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v("¥"+t._s(t.overallStats.totalCommission||0))]),e("div",{staticClass:"stats-label"},[t._v("总佣金")])])])],1)],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"业务员姓名"}}),e("el-table-column",{attrs:{prop:"mobile","header-align":"center",align:"center",label:"手机号"}}),e("el-table-column",{attrs:{prop:"code","header-align":"center",align:"center",width:"120",label:"业务员编号"}}),e("el-table-column",{attrs:{prop:"channelName","header-align":"center",align:"center",label:"所属渠道"}}),e("el-table-column",{attrs:{prop:"department","header-align":"center",align:"center",width:"120",label:"部门"}}),e("el-table-column",{attrs:{prop:"position","header-align":"center",align:"center",width:"100",label:"职位"}}),e("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",width:"80",label:"状态"},scopedSlots:t._u([{key:"default",fn:function(a){return[0===a.row.status?e("el-tag",{attrs:{size:"small",type:"danger"}},[t._v("禁用")]):e("el-tag",{attrs:{size:"small",type:"success"}},[t._v("启用")])]}}])}),e("el-table-column",{attrs:{prop:"tags","header-align":"center",align:"center",width:"150",label:"标签"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.tags?e("span",t._l(a.row.tags.split(","),(function(a){return e("el-tag",{key:a,staticStyle:{"margin-right":"5px"},attrs:{size:"mini"}},[t._v(" "+t._s(a.trim())+" ")])})),1):e("span",[t._v("-")])]}}])}),e("el-table-column",{attrs:{prop:"parentName","header-align":"center",align:"center",width:"120",label:"上级业务员"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.parentName||"-")+" ")]}}])}),e("el-table-column",{attrs:{prop:"level","header-align":"center",align:"center",width:"80",label:"层级"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{size:"mini",type:t.getLevelType(a.row.level)}},[t._v(" L"+t._s(a.row.level||1)+" ")])]}}])}),e("el-table-column",{attrs:{prop:"childrenCount","header-align":"center",align:"center",width:"100",label:"下级数量"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticStyle:{display:"inline-block",position:"relative"}},[e("el-tag",{attrs:{size:"mini",type:a.row.childrenCount>0?"success":"info"}},[t._v(" "+t._s(a.row.childrenCount||0)+" 人 ")])],1)]}}])}),e("el-table-column",{attrs:{prop:"totalOrders","header-align":"center",align:"center",width:"100",label:"总订单数"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:"primary"}},[t._v(t._s(a.row.totalOrders||0))])]}}])}),e("el-table-column",{attrs:{prop:"totalAmount","header-align":"center",align:"center",label:"销售总额"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticStyle:{color:"#E6A23C","font-weight":"bold"}},[t._v("¥"+t._s(a.row.totalAmount||0))])]}}])}),e("el-table-column",{attrs:{prop:"totalPayAmount","header-align":"center",align:"center",label:"已收金额"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticStyle:{color:"#E6A23C","font-weight":"bold"}},[t._v("¥"+t._s(a.row.totalPayAmount||0))])]}}])}),e("el-table-column",{attrs:{prop:"totalCommission","header-align":"center",align:"center",label:"佣金总额"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticStyle:{color:"#67C23A","font-weight":"bold"}},[t._v("¥"+t._s(a.row.totalCommission||0))])]}}])}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"420",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.viewOrdersHandle(a.row.id,a.row.name)}}},[t._v("订单")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.commissionConfigHandle(a.row.id,a.row.name)}}},[t._v("佣金配置")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.viewCommissionRecordsHandle(a.row.id,a.row.name)}}},[t._v("佣金记录")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.viewBindingCustomersHandle(a.row.id,a.row.name)}}},[t._v("绑定客户")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),e("el-dialog",{attrs:{title:"业务员详细统计",visible:t.detailsDialogVisible,width:"800px"},on:{"update:visible":function(e){t.detailsDialogVisible=e}}},[t.selectedSalesmanStats?e("div",[e("h4",[t._v(t._s(t.selectedSalesmanName)+" - 业绩详情")]),e("el-row",{staticStyle:{"margin-bottom":"20px"},attrs:{gutter:20}},[e("el-col",{attrs:{span:8}},[e("el-card",{staticClass:"detail-stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v(t._s(t.selectedSalesmanStats.totalOrders||0))]),e("div",{staticClass:"stats-label"},[t._v("总订单数")])])])],1),e("el-col",{attrs:{span:8}},[e("el-card",{staticClass:"detail-stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v("¥"+t._s(t.selectedSalesmanStats.totalAmount||0))]),e("div",{staticClass:"stats-label"},[t._v("销售总额")])])])],1),e("el-col",{attrs:{span:8}},[e("el-card",{staticClass:"detail-stats-card"},[e("div",{staticClass:"stats-item"},[e("div",{staticClass:"stats-value"},[t._v("¥"+t._s(t.selectedSalesmanStats.totalCommission||0))]),e("div",{staticClass:"stats-label"},[t._v("佣金总额")])])])],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-card",[e("div",{attrs:{slot:"header"},slot:"header"},[t._v("订单类型分布")]),e("div",{staticClass:"order-type-stats"},[e("div",{staticClass:"order-type-item"},[e("span",[t._v("活动订单：")]),e("el-tag",{attrs:{type:"success"}},[t._v(t._s(t.selectedSalesmanStats.activityOrders||0))])],1),e("div",{staticClass:"order-type-item"},[e("span",[t._v("充值订单：")]),e("el-tag",{attrs:{type:"warning"}},[t._v(t._s(t.selectedSalesmanStats.rechargeOrders||0))])],1)])])],1),e("el-col",{attrs:{span:12}},[e("el-card",[e("div",{attrs:{slot:"header"},slot:"header"},[t._v("佣金信息")]),e("div",{staticClass:"commission-stats"},[e("div",{staticClass:"commission-item"},[e("span",[t._v("平均佣金率：")]),e("span",{staticStyle:{color:"#409EFF"}},[t._v(t._s(t.getAverageCommissionRate())+"%")])]),e("div",{staticClass:"commission-item"},[e("span",[t._v("单笔平均佣金：")]),e("span",{staticStyle:{color:"#67C23A"}},[t._v("¥"+t._s(t.getAverageCommission()))])])])])],1)],1)],1):t._e(),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{type:"primary"},on:{click:function(e){t.detailsDialogVisible=!1}}},[t._v("确定")])],1)]),t.addOrUpdateVisible?e("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:t.getDataList}}):t._e(),t.qrcodeManageVisible?e("qrcode-manage",{ref:"qrcodeManage"}):t._e(),t.bindingCustomersVisible?e("binding-customers",{ref:"bindingCustomers"}):t._e(),e("el-dialog",{attrs:{title:"业务员页面二维码",visible:t.salesmanQrcodeVisible,width:"450px",center:""},on:{"update:visible":function(e){t.salesmanQrcodeVisible=e}}},[e("div",{staticStyle:{"text-align":"center"}},[e("canvas",{ref:"qrcodeCanvas",staticStyle:{border:"1px solid #ddd","border-radius":"8px"},attrs:{width:"300",height:"350"}}),e("p",{staticStyle:{"margin-top":"20px",color:"#666"}},[t._v("扫描二维码访问业务员页面")])]),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:t.copyQrcodeImage}},[t._v("复制图片")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){t.salesmanQrcodeVisible=!1}}},[t._v("确定")])],1)]),e("el-dialog",{attrs:{title:"邀请业务员",visible:t.inviteDialogVisible,width:"500px",center:""},on:{"update:visible":function(e){t.inviteDialogVisible=e}}},[t.channelList.length>1?e("div",{staticStyle:{"margin-bottom":"20px"}},[e("el-form",{attrs:{"label-width":"80px"}},[e("el-form-item",{attrs:{label:"选择渠道："}},[e("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择渠道"},model:{value:t.selectedChannelId,callback:function(e){t.selectedChannelId=e},expression:"selectedChannelId"}},t._l(t.channelList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1)],1):t._e(),t.inviteQrcodeUrl?e("div",{staticStyle:{"text-align":"center"}},[e("canvas",{ref:"inviteQrcodeCanvas",staticStyle:{border:"1px solid #ddd","border-radius":"8px",background:"white"},attrs:{width:"300",height:"380"}}),e("p",{staticStyle:{"margin-top":"20px",color:"#666"}},[t._v("扫描二维码邀请业务员注册")]),e("div",{staticStyle:{"margin-top":"20px"}},[e("el-input",{attrs:{readonly:""},model:{value:t.inviteQrcodeUrl,callback:function(e){t.inviteQrcodeUrl=e},expression:"inviteQrcodeUrl"}},[e("el-button",{attrs:{slot:"append"},on:{click:t.copyInviteLink},slot:"append"},[t._v("复制链接")])],1)],1)]):t.channelList.length>1&&!t.selectedChannelId?e("div",{staticStyle:{"text-align":"center",color:"#999",padding:"40px"}},[e("i",{staticClass:"el-icon-info",staticStyle:{"font-size":"24px","margin-bottom":"10px",display:"block"}}),e("p",[t._v("请先选择渠道生成邀请二维码")])]):t._e(),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t.inviteQrcodeUrl?e("el-button",{on:{click:t.copyInviteQrcodeImage}},[t._v("复制图片")]):t._e(),e("el-button",{attrs:{type:"primary"},on:{click:function(e){t.inviteDialogVisible=!1}}},[t._v("确定")])],1)])],1)},n=[],i=a("c7eb"),r=a("1da1"),l=(a("99af"),a("7db0"),a("a15b"),a("d81d"),a("b680"),a("d3b7"),a("0643"),a("fffc"),a("a573"),a("b126")),o=a("9f7b"),c=a("284d"),d=a("b2e5"),u=a.n(d),m={components:{AddOrUpdate:l["default"],QrcodeManage:o["default"],BindingCustomers:c["default"],VueQrcode:u.a},data:function(){return{channelList:[],salesmanQrcodeVisible:!1,inviteDialogVisible:!1,selectedChannelId:null,selectedChannelName:"",inviteQrcodeUrl:"",dataForm:{name:"",channelId:"",mobile:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],overallStats:{},detailsDialogVisible:!1,selectedSalesmanStats:null,selectedSalesmanName:"",addOrUpdateVisible:!1,qrcodeManageVisible:!1,bindingCustomersVisible:!1,selectedSalesmanId:null,salesmanPageUrl:"https://yqihua.com/p_front/#/salesman/qrcode"}},activated:function(){this.getDataList(),this.getOverallStats(),this.getChannelList()},watch:{selectedChannelId:function(t){t?this.generateInviteQrcode():this.inviteQrcodeUrl=""}},methods:{viewCommissionRecordsHandle:function(t,e){this.$router.push({name:"salesman-commission-record",query:{salesmanId:t,salesmanName:e}})},getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/salesman/salesman/listWithStats"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,name:this.dataForm.name,channelId:this.dataForm.channelId,mobile:this.dataForm.mobile})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list||[],t.totalPage=t.dataList.length):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},getOverallStats:function(){var t=this;this.$http({url:this.$http.adornUrl("/salesman/salesman/orderStats"),params:this.$http.adornParams({name:this.dataForm.name,channelId:this.dataForm.channelId,mobile:this.dataForm.mobile}),method:"get"}).then((function(e){var a=e.data;a&&200===a.code&&(t.overallStats=a.stats)}))},getChannelList:function(){var t=this;this.$http({url:this.$http.adornUrl("/channel/channel/select"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.channelList=a.channelList||[])}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},viewOrdersHandle:function(t,e){this.$router.push({name:"salesman-order",query:{salesmanId:t,salesmanName:e}})},viewDetailsHandle:function(t,e){var a=this;this.selectedSalesmanName=e,this.$http({url:this.$http.adornUrl("/salesman/order/statsBySalesman/".concat(t)),method:"get"}).then((function(t){var e=t.data;e&&200===e.code&&(a.selectedSalesmanStats=e.stats,a.detailsDialogVisible=!0)}))},getAverageCommissionRate:function(){if(!this.selectedSalesmanStats||!this.selectedSalesmanStats.totalOrders||0===this.selectedSalesmanStats.totalOrders)return"0.00";var t=this.selectedSalesmanStats.totalCommission/this.selectedSalesmanStats.totalAmount*100;return t.toFixed(2)},getAverageCommission:function(){if(!this.selectedSalesmanStats||!this.selectedSalesmanStats.totalOrders||0===this.selectedSalesmanStats.totalOrders)return"0.00";var t=this.selectedSalesmanStats.totalCommission/this.selectedSalesmanStats.totalOrders;return t.toFixed(2)},addOrUpdateHandle:function(t){var e=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.addOrUpdate.init(t)}))},qrcodeHandle:function(t){var e=this;this.qrcodeManageVisible=!0,this.$nextTick((function(){e.$refs.qrcodeManage.init(t)}))},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/salesman/salesman/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},getLevelType:function(t){switch(t){case 1:return"success";case 2:return"primary";case 3:return"warning";default:return"info"}},exportStats:function(){this.$message.info("导出功能开发中...")},showSalesmanQrcode:function(){var t=this;this.salesmanQrcodeVisible=!0,this.$nextTick((function(){t.generateQrcodeCanvas()}))},generateQrcodeCanvas:function(){var t=this;return Object(r["a"])(Object(i["a"])().mark((function e(){var s,n,r,l,o;return Object(i["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return s=t.$refs.qrcodeCanvas,n=s.getContext("2d"),n.clearRect(0,0,s.width,s.height),n.fillStyle="#ffffff",n.fillRect(0,0,s.width,s.height),e.prev=5,r=a("d055"),e.next=9,r.toDataURL(t.salesmanPageUrl,{width:200,height:200,margin:1,color:{dark:"#000000",light:"#FFFFFF"}});case 9:l=e.sent,o=new Image,o.onload=function(){var t=200,e=(s.width-t)/2,a=30;n.drawImage(o,e,a,t,t),n.fillStyle="#333333",n.font="bold 16px Arial, sans-serif",n.textAlign="center",n.textBaseline="middle";var i=a+t+40;n.fillText("扫描二维码访问业务员页面",s.width/2,i)},o.onerror=function(){t.drawErrorMessage(n,s,"二维码图片加载失败")},o.src=l,e.next=20;break;case 16:e.prev=16,e.t0=e["catch"](5),console.error("生成二维码失败:",e.t0),t.drawErrorMessage(n,s,"二维码生成失败");case 20:case"end":return e.stop()}}),e,null,[[5,16]])})))()},drawErrorMessage:function(t,e,a){t.fillStyle="#ff0000",t.font="16px Arial",t.textAlign="center",t.textBaseline="middle",t.fillText(a,e.width/2,e.height/2)},copyQrcodeImage:function(){var t=this,e=this.$refs.qrcodeCanvas;e.toBlob((function(a){if(navigator.clipboard&&window.ClipboardItem){var s=new ClipboardItem({"image/png":a});navigator.clipboard.write([s]).then((function(){t.$message.success("图片已复制到剪贴板")})).catch((function(){t.fallbackCopyImage(e)}))}else t.fallbackCopyImage(e)}),"image/png")},fallbackCopyImage:function(t){try{var e=t.toDataURL("image/png"),a=document.createElement("a");a.download="易企化业务员二维码.png",a.href=e,a.click(),this.$message.success("图片已下载到本地")}catch(s){this.$message.error("复制失败，请手动截图保存")}},commissionConfigHandle:function(t,e){this.$router.push({path:"/salesman-commission-config",query:{salesmanId:t+"",salesmanName:e}})},viewBindingCustomersHandle:function(t,e){var a=this;this.selectedSalesmanId=t,this.selectedSalesmanName=e,this.bindingCustomersVisible=!0,this.$nextTick((function(){a.$refs.bindingCustomers.init(t,e)}))},inviteSalesmanHandle:function(){0!==this.channelList.length?(1===this.channelList.length?(this.selectedChannelId=this.channelList[0].id,this.generateInviteQrcode()):(this.selectedChannelId=null,this.inviteQrcodeUrl=""),this.inviteDialogVisible=!0):this.$message.error("没有可用的渠道")},generateInviteQrcode:function(){var t=this;if(this.selectedChannelId){var e=this.channelList.find((function(e){return e.id===t.selectedChannelId}));this.selectedChannelName=e?e.name:"";var a="https://yqihua.com/p_front/#/salesman/register";this.inviteQrcodeUrl="".concat(a,"?channelId=").concat(this.selectedChannelId),this.$nextTick((function(){t.generateInviteQrcodeCanvas()}))}},copyInviteLink:function(){var t=this;navigator.clipboard?navigator.clipboard.writeText(this.inviteQrcodeUrl).then((function(){t.$message.success("邀请链接已复制到剪贴板")})).catch((function(){t.fallbackCopyText(t.inviteQrcodeUrl)})):this.fallbackCopyText(this.inviteQrcodeUrl)},fallbackCopyText:function(t){try{var e=document.createElement("textarea");e.value=t,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),this.$message.success("邀请链接已复制到剪贴板")}catch(a){this.$message.error("复制失败，请手动复制链接")}},generateInviteQrcodeCanvas:function(){var t=this;return Object(r["a"])(Object(i["a"])().mark((function e(){var s,n,r,l,o;return Object(i["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(s=t.$refs.inviteQrcodeCanvas,s){e.next=3;break}return e.abrupt("return");case 3:return n=s.getContext("2d"),n.clearRect(0,0,s.width,s.height),n.fillStyle="#ffffff",n.fillRect(0,0,s.width,s.height),e.prev=7,r=a("d055"),e.next=11,r.toDataURL(t.inviteQrcodeUrl,{width:200,height:200,margin:1,color:{dark:"#000000",light:"#FFFFFF"}});case 11:l=e.sent,o=new Image,o.onload=function(){var e=200,a=(s.width-e)/2,i=40;n.drawImage(o,a,i,e,e),n.fillStyle="#333333",n.font="bold 18px Arial, sans-serif",n.textAlign="center",n.textBaseline="middle",n.fillText("扫描二维码邀请业务员",s.width/2,i+e+40),t.selectedChannelName&&(n.fillStyle="#666666",n.font="14px Arial, sans-serif",n.fillText("渠道：".concat(t.selectedChannelName),s.width/2,i+e+70))},o.onerror=function(){t.drawErrorMessage(n,s,"二维码图片加载失败")},o.src=l,e.next=22;break;case 18:e.prev=18,e.t0=e["catch"](7),console.error("生成邀请二维码失败:",e.t0),t.drawErrorMessage(n,s,"二维码生成失败");case 22:case"end":return e.stop()}}),e,null,[[7,18]])})))()},copyInviteQrcodeImage:function(){var t=this,e=this.$refs.inviteQrcodeCanvas;e&&e.toBlob((function(a){if(navigator.clipboard&&window.ClipboardItem){var s=new ClipboardItem({"image/png":a});navigator.clipboard.write([s]).then((function(){t.$message.success("邀请二维码图片已复制到剪贴板")})).catch((function(){t.fallbackCopyInviteImage(e)}))}else t.fallbackCopyInviteImage(e)}),"image/png")},fallbackCopyInviteImage:function(t){try{var e=t.toDataURL("image/png"),a=document.createElement("a");a.download="邀请业务员二维码-".concat(this.selectedChannelName||"默认渠道",".png"),a.href=e,a.click(),this.$message.success("邀请二维码图片已下载到本地")}catch(s){this.$message.error("复制失败，请手动截图保存")}}}},p=m,h=(a("54d9"),a("2877")),g=Object(h["a"])(p,s,n,!1,null,"2ab9e41e",null);e["default"]=g.exports},f665:function(t,e,a){"use strict";var s=a("23e7"),n=a("2266"),i=a("59ed"),r=a("825a"),l=a("46c4");s({target:"Iterator",proto:!0,real:!0},{find:function(t){r(this),i(t);var e=l(this),a=0;return n(e,(function(e,s){if(t(e,a++))return s(e)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},fffc:function(t,e,a){"use strict";a("f665")}}]);
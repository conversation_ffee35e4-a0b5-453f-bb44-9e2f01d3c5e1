(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-09f0415c"],{"3a14":function(a,s,e){"use strict";e("3e78")},"3e78":function(a,s,e){},dbc4:function(a,s,e){"use strict";e.r(s);e("7f7f");var t=function(){var a=this,s=a._self._c;return s("div",{staticClass:"payment-success"},[s("div",{staticClass:"success-icon"},[s("van-icon",{attrs:{name:"checked",size:"60",color:"#07c160"}})],1),s("div",{staticClass:"success-info"},[s("h2",[a._v("支付成功")]),s("p",{staticClass:"success-desc"},[a._v(a._s(a.successMessage))])]),a.loading?a._e():s("div",{staticClass:"order-summary"},[s("div",{staticClass:"summary-item"},[s("span",{staticClass:"label"},[a._v("订单类型：")]),s("span",{staticClass:"value"},[a._v(a._s(a.orderTypeText))])]),s("div",{staticClass:"summary-item"},[s("span",{staticClass:"label"},[a._v("套餐名称：")]),s("span",{staticClass:"value"},[a._v(a._s(a.orderInfo.packageName))])]),s("div",{staticClass:"summary-item"},[s("span",{staticClass:"label"},[a._v(a._s("activity"===a.orderType?"可创建活动：":"充值次数："))]),s("span",{staticClass:"value"},[a._v(a._s(a.orderInfo.countValue)+a._s("activity"===a.orderType?"个":"次"))])]),s("div",{staticClass:"summary-item"},[s("span",{staticClass:"label"},[a._v("支付金额：")]),s("span",{staticClass:"value amount"},[a._v("¥"+a._s(a.payAmount))])]),s("div",{staticClass:"summary-item"},[s("span",{staticClass:"label"},[a._v("订单编号：")]),s("span",{staticClass:"value"},[a._v(a._s(a.orderSn))])]),s("div",{staticClass:"summary-item"},[s("span",{staticClass:"label"},[a._v("支付时间：")]),s("span",{staticClass:"value"},[a._v(a._s(a.payTime))])])]),a.loading?s("div",{staticClass:"loading-container"},[s("van-loading",{attrs:{size:"24px",vertical:""}},[a._v("加载订单信息中...")])],1):a._e(),a.salesmanInfo&&!a.loading?s("div",{staticClass:"salesman-thanks"},[s("div",{staticClass:"thanks-card"},[s("div",{staticClass:"avatar"},[s("img",{attrs:{src:a.salesmanInfo.avatar||"../../assets/mine.png",alt:"业务员头像"}})]),s("div",{staticClass:"thanks-content"},[s("h4",[a._v("感谢您的信任")]),s("p",[a._v("推荐业务员："+a._s(a.salesmanInfo.name||a.salesmanInfo.realName))]),s("p",[a._v("联系方式："+a._s(a.salesmanInfo.mobile||a.salesmanInfo.phone))]),a.salesmanInfo.company?s("p",[a._v("所属公司："+a._s(a.salesmanInfo.company))]):a._e()])])]):a._e(),s("div",{staticClass:"action-buttons"},[s("van-button",{attrs:{type:"primary",size:"large"},on:{click:a.goToHome}},[a._v("\n      返回首页\n    ")]),s("van-button",{staticStyle:{"margin-top":"10px"},attrs:{size:"large"},on:{click:a.viewOrder}},[a._v("\n      查看订单\n    ")])],1),a.loading?a._e():s("div",{staticClass:"tips"},[s("h4",[a._v("温馨提示")]),s("ul",["recharge"===a.orderType?s("li",[a._v("充值成功后，次数已自动添加到您的账户")]):a._e(),"activity"===a.orderType?s("li",[a._v("活动套餐购买成功，可在首页创建活动")]):a._e(),a.salesmanInfo?s("li",[a._v("如有疑问，请联系您的专属业务员："+a._s(a.salesmanInfo.name||a.salesmanInfo.realName))]):s("li",[a._v("如有疑问，请联系客服")]),s("li",[a._v("感谢您的支持与信任")])])])])},n=[],r={name:"PaymentSuccess",data:function(){return{orderType:"",orderInfo:{packageName:"",countValue:0,amount:0,orderSn:"",payTime:"",createTime:""},salesmanInfo:null,loading:!0}},computed:{orderTypeText:function(){return"recharge"===this.orderType?"充值订单":"活动套餐"},successMessage:function(){return"recharge"===this.orderType?"充值成功，次数已添加到您的账户":"购买成功，活动套餐已激活"},payAmount:function(){return this.orderInfo.amount||0},orderSn:function(){return this.orderInfo.orderSn||""},payTime:function(){return this.orderInfo.payTime||this.orderInfo.createTime||""}},mounted:function(){this.loadOrderInfo()},methods:{loadOrderInfo:function(){var a=this,s=this.$route.query.orderId,e=this.$route.query.type,t=this.$route.query.from,n=this.$route.query.salesmanId;if(this.orderType=e||"recharge",!s)return this.$toast("订单ID不能为空"),void this.$router.push({name:"home"});var r="/pyp/web/salesman/getRechargeOrderInfo";"account"===t&&(r="/pyp/web/account/getOrderInfo"),this.$fly.get(r,{orderId:s}).then((function(s){var e,n;200===s.code?("account"===t?(e=s.orderInfo||s.data,n=s.packageInfo||s.data):(e=s.orderInfo,n=s.packageInfo),a.orderInfo={packageName:n.name||n.packageName||"未知套餐",countValue:n.countValue||0,amount:e.payAmount||e.amount||e.totalAmount||0,orderSn:e.orderSn||"",payTime:e.payTime||e.updateTime||"",createTime:e.createTime||""}):a.$toast(s.msg||"加载订单信息失败");a.loading=!1})).catch((function(s){console.error("加载订单信息失败:",s),a.$toast("加载订单信息失败"),a.loading=!1})),n&&this.loadSalesmanInfo(n)},loadSalesmanInfo:function(a){var s=this;this.$fly.get("/pyp/web/salesman/info/".concat(a)).then((function(a){200===a.code?s.salesmanInfo=a.result||a.salesman:console.error("加载业务员信息失败:",a.msg)})).catch((function(a){console.error("加载业务员信息失败:",a)}))},goToHome:function(){this.$router.push({name:"home"})},viewOrder:function(){"recharge"===this.orderType?this.$router.push({name:"rechargeOrders"}):this.$router.push({name:"myOrders"})}}},o=r,i=(e("3a14"),e("2877")),c=Object(i["a"])(o,t,n,!1,null,"3107f83b",null);s["default"]=c.exports}}]);
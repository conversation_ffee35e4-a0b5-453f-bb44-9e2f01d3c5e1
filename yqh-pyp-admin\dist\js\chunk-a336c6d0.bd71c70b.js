(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a336c6d0","chunk-278cc848"],{"68ea":function(e,a,t){"use strict";t.r(a);t("b0c0");var r=function(){var e=this,a=e._self._c;return a("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(a){e.visible=a}}},[a("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"150px"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.dataFormSubmit()}}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"供应商名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"供应商名称"},model:{value:e.dataForm.name,callback:function(a){e.$set(e.dataForm,"name",a)},expression:"dataForm.name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"供应商类别",prop:"supplierTypeId"}},[a("el-select",{model:{value:e.dataForm.supplierTypeId,callback:function(a){e.$set(e.dataForm,"supplierTypeId",a)},expression:"dataForm.supplierTypeId"}},e._l(e.supplierTypes,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"联系人",prop:"username"}},[a("el-input",{attrs:{placeholder:"联系人"},model:{value:e.dataForm.username,callback:function(a){e.$set(e.dataForm,"username",a)},expression:"dataForm.username"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"联系方式",prop:"mobile"}},[a("el-input",{attrs:{placeholder:"联系方式"},model:{value:e.dataForm.mobile,callback:function(a){e.$set(e.dataForm,"mobile",a)},expression:"dataForm.mobile"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"区域",prop:"area"}},[a("el-cascader",{staticStyle:{width:"100%"},attrs:{size:"large",options:e.options},on:{change:e.handleChange},model:{value:e.dataForm.area,callback:function(a){e.$set(e.dataForm,"area",a)},expression:"dataForm.area"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"地址",prop:"address"}},[a("el-input",{attrs:{placeholder:"地址"},model:{value:e.dataForm.address,callback:function(a){e.$set(e.dataForm,"address",a)},expression:"dataForm.address"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[a("el-input",{attrs:{placeholder:"邮箱"},model:{value:e.dataForm.email,callback:function(a){e.$set(e.dataForm,"email",a)},expression:"dataForm.email"}})],1)],1)],1),a("el-form-item",{attrs:{label:"银行账户信息",prop:"isBank"}},[a("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#6f6f6f"},model:{value:e.dataForm.isBank,callback:function(a){e.$set(e.dataForm,"isBank",a)},expression:"dataForm.isBank"}})],1),e.dataForm.isBank?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"银行卡号",prop:"bankAccount"}},[a("el-input",{attrs:{placeholder:"银行卡号"},model:{value:e.dataForm.bankAccount,callback:function(a){e.$set(e.dataForm,"bankAccount",a)},expression:"dataForm.bankAccount"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"开户行",prop:"bankAddress"}},[a("el-input",{attrs:{placeholder:"开户行"},model:{value:e.dataForm.bankAddress,callback:function(a){e.$set(e.dataForm,"bankAddress",a)},expression:"dataForm.bankAddress"}})],1)],1)],1):e._e(),a("el-form-item",{attrs:{label:"发票信息",prop:"isInvoice"}},[a("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#6f6f6f"},model:{value:e.dataForm.isInvoice,callback:function(a){e.$set(e.dataForm,"isInvoice",a)},expression:"dataForm.isInvoice"}})],1),e.dataForm.isInvoice?a("el-form-item",{attrs:{label:"统一社会编码",prop:"code"}},[a("el-input",{attrs:{placeholder:"统一社会编码"},model:{value:e.dataForm.code,callback:function(a){e.$set(e.dataForm,"code",a)},expression:"dataForm.code"}})],1):e._e(),e.dataForm.isInvoice?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"注册地址（专票）",prop:"registerAddress"}},[a("el-input",{attrs:{placeholder:"注册地址（专票）"},model:{value:e.dataForm.registerAddress,callback:function(a){e.$set(e.dataForm,"registerAddress",a)},expression:"dataForm.registerAddress"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"注册电话（专票）",prop:"registerTelephone"}},[a("el-input",{attrs:{placeholder:"注册电话（专票）"},model:{value:e.dataForm.registerTelephone,callback:function(a){e.$set(e.dataForm,"registerTelephone",a)},expression:"dataForm.registerTelephone"}})],1)],1)],1):e._e(),e.dataForm.isInvoice?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"注册银行（专票）",prop:"registerBank"}},[a("el-input",{attrs:{placeholder:"注册银行（专票）"},model:{value:e.dataForm.registerBank,callback:function(a){e.$set(e.dataForm,"registerBank",a)},expression:"dataForm.registerBank"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"注册账户（专票）",prop:"registerAccount"}},[a("el-input",{attrs:{placeholder:"注册账户（专票）"},model:{value:e.dataForm.registerAccount,callback:function(a){e.$set(e.dataForm,"registerAccount",a)},expression:"dataForm.registerAccount"}})],1)],1)],1):e._e(),a("el-form-item",{attrs:{label:"备注",prop:"remarks"}},[a("el-input",{attrs:{placeholder:"备注"},model:{value:e.dataForm.remarks,callback:function(a){e.$set(e.dataForm,"remarks",a)},expression:"dataForm.remarks"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){e.visible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:function(a){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},o=[],n=(t("a15b"),t("ef6c")),l={data:function(){return{options:n["regionData"],loading:!1,visible:!1,dataForm:{id:0,name:"",username:"",code:"",address:"",email:"",supplierTypeId:"",mobile:"",registerAddress:"",registerTelephone:"",registerBank:"",registerAccount:"",remarks:"",isBank:!0,isInvoice:!1,bankAccount:"",bankAddress:"",repeatToken:"",area:""},supplierTypes:[],dataRule:{name:[{required:!0,message:"供应商名称不能为空",trigger:"blur"}],username:[{required:!0,message:"联系人不能为空",trigger:"blur"}]}}},methods:{init:function(e){var a=this;this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id&&a.$http({url:a.$http.adornUrl("/supplier/supplier/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(e){var t=e.data;t&&200===t.code&&(a.dataForm.name=t.supplier.name,a.dataForm.username=t.supplier.username,a.dataForm.code=t.supplier.code,a.dataForm.address=t.supplier.address,a.dataForm.email=t.supplier.email,a.dataForm.supplierTypeId=t.supplier.supplierTypeId,a.dataForm.mobile=t.supplier.mobile,a.dataForm.registerAddress=t.supplier.registerAddress,a.dataForm.registerTelephone=t.supplier.registerTelephone,a.dataForm.registerBank=t.supplier.registerBank,a.dataForm.registerAccount=t.supplier.registerAccount,a.dataForm.remarks=t.supplier.remarks,a.dataForm.bankAccount=t.supplier.bankAccount,a.dataForm.bankAddress=t.supplier.bankAddress,a.dataForm.area=t.supplier.area?t.supplier.area.split(","):[],a.dataForm.bankAccount&&(a.dataForm.isBank=!0),a.dataForm.code&&(a.dataForm.isInvoice=!0))}))})),this.findSupplierType(),this.getToken()},getToken:function(){var e=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(a){var t=a.data;t&&200===t.code&&(e.dataForm.repeatToken=t.result)}))},findSupplierType:function(){var e=this;this.$http({url:this.$http.adornUrl("/supplier/suppliertype/findAll"),method:"get",params:this.$http.adornParams()}).then((function(a){var t=a.data;t&&200===t.code&&(e.supplierTypes=t.result)}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(a){a&&(e.loading=!0,e.$http({url:e.$http.adornUrl("/supplier/supplier/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,name:e.dataForm.name,username:e.dataForm.username,code:e.dataForm.code,address:e.dataForm.address,email:e.dataForm.email,supplierTypeId:e.dataForm.supplierTypeId,mobile:e.dataForm.mobile,registerAddress:e.dataForm.registerAddress,registerTelephone:e.dataForm.registerTelephone,registerBank:e.dataForm.registerBank,appid:e.$cookie.get("appid"),registerAccount:e.dataForm.registerAccount,remarks:e.dataForm.remarks,bankAddress:e.dataForm.bankAddress,bankAccount:e.dataForm.bankAccount,area:e.dataForm.area?e.dataForm.area.join(","):"",repeatToken:e.dataForm.repeatToken})}).then((function(a){var t=a.data;t&&200===t.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList",t.result)}}):e.$message.error(t.msg),e.loading=!1})))}))}}},i=l,s=t("2877"),d=Object(s["a"])(i,r,o,!1,null,null,null);a["default"]=d.exports},a15b:function(e,a,t){"use strict";var r=t("23e7"),o=t("e330"),n=t("44ad"),l=t("fc6a"),i=t("a640"),s=o([].join),d=n!==Object,p=d||!i("join",",");r({target:"Array",proto:!0,forced:p},{join:function(e){return s(l(this),void 0===e?",":e)}})},a573:function(e,a,t){"use strict";t("ab43")},ab43:function(e,a,t){"use strict";var r=t("23e7"),o=t("d024"),n=t("c430");r({target:"Iterator",proto:!0,real:!0,forced:n},{map:o})},d024:function(e,a,t){"use strict";var r=t("c65b"),o=t("59ed"),n=t("825a"),l=t("46c4"),i=t("c5cc"),s=t("9bdd"),d=i((function(){var e=this.iterator,a=n(r(this.next,e)),t=this.done=!!a.done;if(!t)return s(e,this.mapper,[a.value,this.counter++],!0)}));e.exports=function(e){return n(this),o(e),new d(l(this),{mapper:e})}},d81d:function(e,a,t){"use strict";var r=t("23e7"),o=t("b727").map,n=t("1dde"),l=n("map");r({target:"Array",proto:!0,forced:!l},{map:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}})},eb07:function(e,a,t){"use strict";t.r(a);t("b0c0");var r=function(){var e=this,a=e._self._c;return a("div",{staticClass:"mod-config"},[a("el-form",{attrs:{inline:!0,model:e.dataForm},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.onSearch()}}},[a("el-form-item",[a("el-select",{attrs:{placeholder:"供应商类别"},model:{value:e.dataForm.supplierTypeId,callback:function(a){e.$set(e.dataForm,"supplierTypeId",a)},expression:"dataForm.supplierTypeId"}},e._l(e.supplierTypes,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),a("el-form-item",[a("el-input",{attrs:{placeholder:"供应商名称",clearable:""},model:{value:e.dataForm.name,callback:function(a){e.$set(e.dataForm,"name",a)},expression:"dataForm.name"}})],1),a("el-form-item",[a("el-input",{attrs:{placeholder:"联系人",clearable:""},model:{value:e.dataForm.username,callback:function(a){e.$set(e.dataForm,"username",a)},expression:"dataForm.username"}})],1),a("el-form-item",[a("el-input",{attrs:{placeholder:"联系方式",clearable:""},model:{value:e.dataForm.mobile,callback:function(a){e.$set(e.dataForm,"mobile",a)},expression:"dataForm.mobile"}})],1),a("el-form-item",[a("el-button",{on:{click:function(a){return e.onSearch()}}},[e._v("查询")]),e.isAuth("supplier:supplier:save")?a("el-button",{attrs:{type:"primary"},on:{click:function(a){return e.addOrUpdateHandle()}}},[e._v("新增")]):e._e(),e.isAuth("supplier:supplier:delete")?a("el-button",{attrs:{type:"danger",disabled:e.dataListSelections.length<=0},on:{click:function(a){return e.deleteHandle()}}},[e._v("批量删除")]):e._e(),a("el-button",{attrs:{type:"primary"},on:{click:function(a){return e.exportHandle()}}},[e._v("导出")]),a("el-button",{attrs:{type:"success"},on:{click:function(a){return e.downloadExampleHandle()}}},[e._v("模板下载")]),a("el-button",{attrs:{type:"primary"}},[a("Upload",{attrs:{url:"/supplier/supplier/importExcel?appid="+e.$cookie.get("appid"),name:"客户数据导入"},on:{uploaded:e.getDataList}})],1)],1)],1),a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.dataList,border:"",size:"mini"},on:{"selection-change":e.selectionChangeHandle}},[a("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),a("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"供应商名称"}}),a("el-table-column",{attrs:{prop:"bankAccount","header-align":"center",align:"center",label:"银行账户"}}),a("el-table-column",{attrs:{prop:"bankAddress","header-align":"center",align:"center",label:"开户行"}}),a("el-table-column",{attrs:{prop:"code","header-align":"center",align:"center",label:"统一社会信用代码"}}),a("el-table-column",{attrs:{prop:"supplierTypeName","header-align":"center",align:"center",label:"供应商类别"}}),a("el-table-column",{attrs:{prop:"username","header-align":"center",align:"center",label:"联系人"}}),a("el-table-column",{attrs:{prop:"address","header-align":"center",align:"center",label:"地址"}}),a("el-table-column",{attrs:{prop:"mobile","header-align":"center",align:"center",label:"联系方式"}}),a("el-table-column",{attrs:{prop:"email","header-align":"center",align:"center",label:"邮箱"}}),a("el-table-column",{attrs:{prop:"areaName","header-align":"center",align:"center",label:"区域"}}),a("el-table-column",{attrs:{prop:"remarks","header-align":"center",align:"center",label:"备注"}}),a("el-table-column",{attrs:{prop:"createOn","show-overflow-tooltip":!0,"header-align":"center",align:"center",label:"创建时间"}}),a("el-table-column",{attrs:{prop:"updateOn","show-overflow-tooltip":!0,"header-align":"center",align:"center",label:"更新时间"}}),a("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"180",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.thingHandle(t.row.id)}}},[e._v("供应商文件")]),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.addOrUpdateHandle(t.row.id)}}},[e._v("修改")]),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.deleteHandle(t.row.id)}}},[e._v("删除")])]}}])})],1),a("el-pagination",{attrs:{"current-page":e.pageIndex,"page-sizes":[10,20,50,100],"page-size":e.pageSize,total:e.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle}}),e.addOrUpdateVisible?a("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:e.getDataList}}):e._e()],1)},o=[],n=(t("99af"),t("a15b"),t("d81d"),t("14d9"),t("d3b7"),t("3ca3"),t("a573"),t("ddb0"),t("68ea")),l={data:function(){return{supplierTypes:[],dataForm:{supplierTypeId:"",name:"",username:"",mobile:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListSelections:[],addOrUpdateVisible:!1}},components:{AddOrUpdate:n["default"],Upload:function(){return t.e("chunk-043b0b7f").then(t.bind(null,"9dac"))}},activated:function(){this.getDataList(),this.findSupplierType()},methods:{onSearch:function(){this.pageIndex=1,this.getDataList()},getDataList:function(){var e=this;this.$http({url:this.$http.adornUrl("/supplier/supplier/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,supplierTypeId:this.dataForm.supplierTypeId,name:this.dataForm.name,mobile:this.dataForm.mobile,username:this.dataForm.username,appid:this.$cookie.get("appid")})}).then((function(a){var t=a.data;t&&200===t.code?(e.dataList=t.page.list,e.totalPage=t.page.totalCount):(e.dataList=[],e.totalPage=0)}))},sizeChangeHandle:function(e){this.pageSize=e,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(e){this.pageIndex=e,this.getDataList()},selectionChangeHandle:function(e){this.dataListSelections=e},addOrUpdateHandle:function(e){var a=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){a.$refs.addOrUpdate.init(e)}))},bankHandle:function(e){this.$router.push({name:"supplierbank",query:{id:e}})},thingHandle:function(e){this.$router.push({name:"supplierthing",query:{id:e}})},findSupplierType:function(){var e=this;this.$http({url:this.$http.adornUrl("/supplier/suppliertype/findAll"),method:"get",params:this.$http.adornParams()}).then((function(a){var t=a.data;t&&200===t.code&&(e.supplierTypes=t.result)}))},downloadExampleHandle:function(){var e=this.$http.adornUrl("/supplier/supplier/exportExcelExample?"+["token="+this.$cookie.get("token")].join("&"));window.open(e)},deleteHandle:function(e){var a=this,t=e?[e]:this.dataListSelections.map((function(e){return e.id}));this.$confirm("确定对[id=".concat(t.join(","),"]进行[").concat(e?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.$http({url:a.$http.adornUrl("/supplier/supplier/delete"),method:"post",data:a.$http.adornData(t,!1)}).then((function(e){var t=e.data;t&&200===t.code?a.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){a.getDataList()}}):a.$message.error(t.msg)}))}))},exportHandle:function(){var e=this.$http.adornUrl("/supplier/supplier/export?"+["page=1","limit=100000","supplierTypeId="+this.dataForm.supplierTypeId,"name="+this.dataForm.name,"mobile="+this.dataForm.mobile,"username="+this.dataForm.username,"appid="+this.$cookie.get("appid"),"token="+this.$cookie.get("token")].join("&"));window.open(e)}}},i=l,s=t("2877"),d=Object(s["a"])(i,r,o,!1,null,null,null);a["default"]=d.exports}}]);
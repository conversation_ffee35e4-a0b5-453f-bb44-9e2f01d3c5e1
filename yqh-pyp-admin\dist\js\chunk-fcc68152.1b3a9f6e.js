(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-fcc68152","chunk-2d0df425"],{"02a5":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"mod-config"},[t("el-form",{attrs:{inline:!0,model:e.dataForm}},[t("el-form-item",[t("el-input",{attrs:{placeholder:"场景值",clearable:""},model:{value:e.dataForm.sceneStr,callback:function(t){e.$set(e.dataForm,"sceneStr",t)},expression:"dataForm.sceneStr"}})],1),t("el-form-item",[t("el-button",{on:{click:function(t){return e.getDataList()}}},[e._v("查询")]),e.isAuth("wx:wxqrcode:save")?t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addOrUpdateHandle()}}},[e._v("新增")]):e._e(),e.isAuth("wx:wxqrcode:delete")?t("el-button",{attrs:{type:"danger",disabled:e.dataListSelections.length<=0},on:{click:function(t){return e.deleteHandle()}}},[e._v("批量删除")]):e._e()],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:e.dataList,border:""},on:{"selection-change":e.selectionChangeHandle}},[t("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),t("el-table-column",{attrs:{prop:"id","header-align":"center",align:"center",label:"ID"}}),t("el-table-column",{attrs:{prop:"isTemp","header-align":"center",align:"center",label:"类型"},scopedSlots:e._u([{key:"default",fn:function(a){return t("span",{},[e._v(e._s(a.row.isTemp?"临时":"永久"))])}}])}),t("el-table-column",{attrs:{prop:"sceneStr","header-align":"center",align:"center",label:"场景值"}}),t("el-table-column",{attrs:{prop:"ticket","header-align":"center",align:"center","show-overflow-tooltip":"",label:"二维码图片"},scopedSlots:e._u([{key:"default",fn:function(a){return t("a",{attrs:{href:"https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket="+a.row.ticket}},[e._v(e._s(a.row.ticket))])}}])}),t("el-table-column",{attrs:{prop:"url","header-align":"center",align:"center","show-overflow-tooltip":"",label:"解析后的地址"},scopedSlots:e._u([{key:"default",fn:function(a){return t("a",{attrs:{href:a.row.url}},[e._v(e._s(a.row.url))])}}])}),t("el-table-column",{attrs:{prop:"expireTime","header-align":"center",align:"center",width:"100",label:"失效时间"}}),t("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHandle(a.row.id)}}},[e._v("删除")])]}}])})],1),t("el-pagination",{attrs:{"current-page":e.pageIndex,"page-sizes":[10,20,50,100],"page-size":e.pageSize,total:e.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle}}),e.addOrUpdateVisible?t("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:e.getDataList}}):e._e()],1)},n=[],i=(a("99af"),a("a15b"),a("d81d"),a("a573"),a("88c2")),o={data:function(){return{dataForm:{sceneStr:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1}},components:{AddOrUpdate:i["default"]},activated:function(){this.getDataList()},methods:{getDataList:function(){var e=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/manage/wxQrCode/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,sceneStr:this.dataForm.sceneStr,sidx:"id",order:"desc"})}).then((function(t){var a=t.data;a&&200===a.code?(e.dataList=a.page.list,e.totalPage=a.page.totalCount):(e.dataList=[],e.totalPage=0),e.dataListLoading=!1}))},sizeChangeHandle:function(e){this.pageSize=e,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(e){this.pageIndex=e,this.getDataList()},selectionChangeHandle:function(e){this.dataListSelections=e},addOrUpdateHandle:function(e){var t=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){t.$refs.addOrUpdate.init(e)}))},deleteHandle:function(e){var t=this,a=e?[e]:this.dataListSelections.map((function(e){return e.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(e?"删除":"批量删除","]操作?（仅删存档）"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:t.$http.adornUrl("/manage/wxQrCode/delete"),method:"post",data:t.$http.adornData(a,!1)}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){return t.getDataList()}}):t.$message.error(a.msg)}))}))}}},s=o,l=a("2877"),d=Object(l["a"])(s,r,n,!1,null,null,null);t["default"]=d.exports},"88c2":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"二维码类型",prop:"isTemp"}},[t("el-radio",{attrs:{label:!0},model:{value:e.dataForm.isTemp,callback:function(t){e.$set(e.dataForm,"isTemp",t)},expression:"dataForm.isTemp"}},[e._v("临时")]),t("el-radio",{attrs:{label:!1},model:{value:e.dataForm.isTemp,callback:function(t){e.$set(e.dataForm,"isTemp",t)},expression:"dataForm.isTemp"}},[e._v("永久")]),t("div",[t("a",{directives:[{name:"show",rawName:"v-show",value:!e.dataForm.isTemp,expression:"!dataForm.isTemp"}],staticClass:"text-warning",attrs:{target:"_blank",href:"https://developers.weixin.qq.com/doc/offiaccount/Account_Management/Generating_a_Parametric_QR_Code.html"}},[e._v("注意永久二维码上限10万个，且暂时无法删除旧的二维码")])])],1),t("el-form-item",{attrs:{label:"场景值",prop:"sceneStr"}},[t("el-input",{attrs:{placeholder:"任意字符串",maxlength:"64"},model:{value:e.dataForm.sceneStr,callback:function(t){e.$set(e.dataForm,"sceneStr",t)},expression:"dataForm.sceneStr"}})],1),e.dataForm.isTemp?t("el-form-item",{attrs:{label:"失效时间/秒",prop:"expireSeconds"}},[t("el-input",{attrs:{placeholder:"单位：秒，最大2592000（30天）"},model:{value:e.dataForm.expireSeconds,callback:function(t){e.$set(e.dataForm,"expireSeconds",t)},expression:"dataForm.expireSeconds"}}),t("div",[e._v("最大30天，当前设置："),t("span",{staticClass:"text-warning"},[e._v(e._s(e.dataForm.expireSeconds/86400)+"天")])])],1):e._e()],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},n=[],i={data:function(){return{visible:!1,dataForm:{isTemp:!0,sceneStr:"",expireSeconds:2592e3},dataRule:{isTemp:[{required:!0,message:"二维码类型不能为空",trigger:"blur"}],sceneStr:[{required:!0,message:"场景值ID不能为空",trigger:"blur"}],expireSeconds:[{required:!0,message:"该二维码失效时间不能为空",trigger:"blur"}]}}},methods:{init:function(e){var t=this;this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){t.$refs["dataForm"].resetFields()}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$http({url:e.$http.adornUrl("/manage/wxQrCode/createTicket"),method:"post",data:e.$http.adornData(e.dataForm)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(a.msg)}))}))}}},o=i,s=a("2877"),l=Object(s["a"])(o,r,n,!1,null,null,null);t["default"]=l.exports},a15b:function(e,t,a){"use strict";var r=a("23e7"),n=a("e330"),i=a("44ad"),o=a("fc6a"),s=a("a640"),l=n([].join),d=i!==Object,c=d||!s("join",",");r({target:"Array",proto:!0,forced:c},{join:function(e){return l(o(this),void 0===e?",":e)}})},a573:function(e,t,a){"use strict";a("ab43")},ab43:function(e,t,a){"use strict";var r=a("23e7"),n=a("d024"),i=a("c430");r({target:"Iterator",proto:!0,real:!0,forced:i},{map:n})},d024:function(e,t,a){"use strict";var r=a("c65b"),n=a("59ed"),i=a("825a"),o=a("46c4"),s=a("c5cc"),l=a("9bdd"),d=s((function(){var e=this.iterator,t=i(r(this.next,e)),a=this.done=!!t.done;if(!a)return l(e,this.mapper,[t.value,this.counter++],!0)}));e.exports=function(e){return i(this),n(e),new d(o(this),{mapper:e})}},d81d:function(e,t,a){"use strict";var r=a("23e7"),n=a("b727").map,i=a("1dde"),o=i("map");r({target:"Array",proto:!0,forced:!o},{map:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})}}]);
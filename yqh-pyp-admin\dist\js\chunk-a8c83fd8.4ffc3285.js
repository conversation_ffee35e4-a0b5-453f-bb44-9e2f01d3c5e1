(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a8c83fd8"],{"55e3":function(s,t,a){},"702d":function(s,t,a){"use strict";a.r(t);a("b0c0");var i=function(){var s=this,t=s._self._c;return t("el-dialog",{attrs:{title:"开发接入信息","close-on-click-modal":!1,visible:s.visible},on:{"update:visible":function(t){s.visible=t}}},[t("div",[t("div",{staticClass:"list-item"},[t("span",{staticClass:"label"},[s._v("公众号:")]),s._v(s._s(s.account.name))]),t("div",{staticClass:"list-item"},[t("span",{staticClass:"label"},[s._v("token:")]),s._v(s._s(s.account.token))]),t("div",{staticClass:"list-item"},[t("span",{staticClass:"label"},[s._v("aesKey:")]),s._v(s._s(s.account.aesKey))]),t("div",{staticClass:"list-item"},[t("span",{staticClass:"label"},[s._v("接入链接:")]),t("span",{domProps:{innerHTML:s._s(s.accessUrl)}})])])])},c=[],n=(a("ac1f"),a("00b4"),{data:function(){return{visible:!1,account:{}}},computed:{accessUrl:function(){var s=location.host;return/^(\d(.\d){3})|localhost/.test(s)&&(s='<span class="text-red">正式域名</span>'),location.protocol+"//"+s+"/wx/wx/msg/"+this.account.appid}},methods:{init:function(s){this.visible=!0,s&&s.appid&&(this.account=s)}}}),e=n,l=(a("d606"),a("2877")),o=Object(l["a"])(e,i,c,!1,null,"da2595c2",null);t["default"]=o.exports},d606:function(s,t,a){"use strict";a("55e3")}}]);
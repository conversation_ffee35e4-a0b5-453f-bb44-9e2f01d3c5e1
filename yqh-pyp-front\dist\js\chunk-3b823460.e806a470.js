(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3b823460"],{"20e9":function(t,e,a){"use strict";a("537c")},2909:function(t,e,a){"use strict";function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,r=new Array(e);a<e;a++)r[a]=t[a];return r}function s(t){if(Array.isArray(t))return r(t)}function n(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function i(t,e){if(t){if("string"===typeof t)return r(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?r(t,e):void 0}}function o(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t){return s(t)||n(t)||i(t)||o()}a.d(e,"a",(function(){return u}))},"3e5a":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"customer-orders-page"},[e("van-nav-bar",{attrs:{title:"".concat(t.customerName,"的订单"),"left-text":"返回","left-arrow":""},on:{"click-left":function(e){return t.$router.go(-1)}}}),e("div",{staticClass:"filter-section"},[e("van-tabs",{attrs:{color:"#1989fa","title-active-color":"#1989fa"},on:{change:t.onFilterChange},model:{value:t.statusFilter,callback:function(e){t.statusFilter=e},expression:"statusFilter"}},[e("van-tab",{attrs:{title:"全部",name:"all"}}),e("van-tab",{attrs:{title:"待支付",name:"pending"}}),e("van-tab",{attrs:{title:"已支付",name:"paid"}}),e("van-tab",{attrs:{title:"已取消",name:"cancelled"}})],1)],1),e("div",{staticClass:"orders-list"},[e("van-list",{attrs:{finished:t.finished,"finished-text":"没有更多订单了"},on:{load:t.loadOrders},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},t._l(t.ordersList,(function(a){return e("div",{key:a.id,staticClass:"order-item"},[e("div",{staticClass:"order-header"},[e("div",{staticClass:"order-info"},[e("div",{staticClass:"order-number"},[t._v("订单号："+t._s(a.orderSn))]),e("div",{staticClass:"order-time"},[t._v(t._s(t.formatTime(a.createTime)))])]),e("div",{staticClass:"order-status"},[e("van-tag",{attrs:{type:t.getOrderStatusTagType(a.status),size:"small",round:""}},[t._v("\n              "+t._s(t.getOrderStatusText(a.status))+"\n            ")])],1)]),e("div",{staticClass:"order-content"},[e("div",{staticClass:"order-detail"},[e("div",{staticClass:"detail-row"},[e("span",{staticClass:"label"},[t._v("订单类型：")]),e("span",{staticClass:"value"},[t._v(t._s(t.getOrderTypeText(a.rechargeType)))])]),a.countValue?e("div",{staticClass:"detail-row"},[e("span",{staticClass:"label"},[t._v("充值次数：")]),e("span",{staticClass:"value"},[t._v(t._s(a.countValue)+"次")])]):t._e(),e("div",{staticClass:"detail-row"},[e("span",{staticClass:"label"},[t._v("订单金额：")]),e("span",{staticClass:"value amount"},[t._v("¥"+t._s(a.amount||0))])]),a.payAmount&&1===a.status?e("div",{staticClass:"detail-row"},[e("span",{staticClass:"label"},[t._v("实付金额：")]),e("span",{staticClass:"value paid"},[t._v("¥"+t._s(a.payAmount||0))])]):t._e()])])])})),0),0!==t.ordersList.length||t.loading?t._e():e("van-empty",{attrs:{description:"暂无订单数据",image:"https://img.yzcdn.cn/vant/custom-empty-image.png"}})],1)],1)},s=[],n=a("2909"),i=(a("96cf"),a("1da1")),o={name:"CustomerOrders",data:function(){return{customerId:null,customerName:"客户",statusFilter:"all",ordersList:[],loading:!1,finished:!1,page:1}},created:function(){this.customerId=this.$route.query.customerId,this.customerName=this.$route.query.customerName||"客户",this.loadOrders()},methods:{loadOrders:function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(){var e,a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!this.loading){t.next=2;break}return t.abrupt("return");case 2:return this.loading=!0,t.prev=3,t.next=6,this.$fly.get("/pyp/web/salesman/getCustomerOrders",{customerId:this.customerId,page:this.page,limit:10,status:"all"===this.statusFilter?null:this.getOrderStatusValue(this.statusFilter)});case 6:e=t.sent,200===e.code?(a=(e.result||[]).map((function(t){return{id:t.id,orderSn:t.orderSn,rechargeType:t.rechargeType,countValue:t.countValue,amount:t.amount,payAmount:t.payAmount,status:t.status,createTime:t.createTime||t.createOn,payTime:t.payTime}})),1===this.page?this.ordersList=a:(r=this.ordersList).push.apply(r,Object(n["a"])(a)),this.page++,this.finished=a.length<10):(this.$toast(e.msg||"加载订单失败"),this.finished=!0),t.next=14;break;case 10:t.prev=10,t.t0=t["catch"](3),console.error("加载订单失败:",t.t0),this.finished=!0;case 14:return t.prev=14,this.loading=!1,t.finish(14);case 17:case"end":return t.stop()}}),t,this,[[3,10,14,17]])})));function e(){return t.apply(this,arguments)}return e}(),onFilterChange:function(){this.ordersList=[],this.page=1,this.finished=!1,this.loadOrders()},formatTime:function(t){if(!t)return"未知时间";try{var e=new Date(t);return e.toLocaleString()}catch(a){return t}},getOrderStatusText:function(t){var e={0:"待支付",1:"已支付",2:"已取消",3:"已退款"};return e[t]||"未知"},getOrderStatusTagType:function(t){var e={0:"warning",1:"success",2:"danger",3:"default"};return e[t]||"default"},getOrderTypeText:function(t){var e={1:"套餐充值",2:"自定义充值",3:"系统赠送",4:"创建活动套餐"};return e[t]||"未知类型"},getOrderStatusValue:function(t){var e={pending:0,paid:1,cancelled:2};return e[t]}}},u=o,l=(a("20e9"),a("2877")),c=Object(l["a"])(u,r,s,!1,null,"6649eb84",null);e["default"]=c.exports},"537c":function(t,e,a){}}]);
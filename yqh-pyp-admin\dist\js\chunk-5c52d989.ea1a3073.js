(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5c52d989","chunk-1a562b71"],{"592f":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"mod-config"},[t("el-form",{attrs:{inline:!0,model:e.dataForm}},[t("el-form-item",[t("el-input",{attrs:{placeholder:"参数名",clearable:""},model:{value:e.dataForm.name,callback:function(t){e.$set(e.dataForm,"name",t)},expression:"dataForm.name"}})],1),t("el-form-item",[t("el-button",{on:{click:function(t){return e.getDataList()}}},[e._v("查询")]),e.isAuth("activity:activityrole:save")?t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addOrUpdateHandle()}}},[e._v("新增")]):e._e(),e.isAuth("activity:activityrole:delete")?t("el-button",{attrs:{type:"danger",disabled:e.dataListSelections.length<=0},on:{click:function(t){return e.deleteHandle()}}},[e._v("批量删除")]):e._e()],1),t("el-form-item",{staticStyle:{float:"right"}},[t("el-button",{attrs:{type:"success"},on:{click:function(t){return e.$router.go(-1)}}},[e._v("返回")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:e.dataList,border:""},on:{"selection-change":e.selectionChangeHandle}},[t("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),t("el-table-column",{attrs:{prop:"username","header-align":"center",align:"center",label:"用户"}}),t("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",label:"状态"},scopedSlots:e._u([{key:"default",fn:function(a){return[0===a.row.status?t("el-tag",{attrs:{type:"danger"}},[e._v("禁用")]):t("el-tag",{attrs:{type:"primary"}},[e._v("正常")])]}}])}),t("el-table-column",{attrs:{prop:"roleId","header-align":"center",align:"center",label:"类型"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[t("el-tag",{class:"tag-color tag-color-"+a.row.roleId,attrs:{type:"primary"}},[e._v(e._s(null==a.row.roleId?"空":e.activityRole[a.row.roleId].value))])],1)}}])}),t("el-table-column",{attrs:{prop:"workPlace","header-align":"center",align:"center",label:"工作板块"}}),t("el-table-column",{attrs:{prop:"bad","header-align":"center",align:"center",label:"不足之处"}}),t("el-table-column",{attrs:{prop:"good","header-align":"center",align:"center",label:"亮点"}}),t("el-table-column",{attrs:{prop:"better","header-align":"center",align:"center",label:"改进地方"}}),t("el-table-column",{attrs:{prop:"createOn","header-align":"center",align:"center",label:"创建时间"}}),t("el-table-column",{attrs:{prop:"updateOn","header-align":"center",align:"center",label:"更新时间"}}),t("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.addOrUpdateHandle(a.row.id)}}},[e._v("修改")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHandle(a.row.id)}}},[e._v("删除")])]}}])})],1),t("el-pagination",{attrs:{"current-page":e.pageIndex,"page-sizes":[10,20,50,100],"page-size":e.pageSize,total:e.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle}}),e.addOrUpdateVisible?t("add-or-update",{ref:"addOrUpdate",on:{refreshDataList:e.getDataList}}):e._e()],1)},l=[],o=(a("99af"),a("a15b"),a("d81d"),a("a573"),a("593c")),i=a("fce4"),n={data:function(){return{activityRole:o["b"],dataForm:{name:"",activityId:void 0},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1}},components:{AddOrUpdate:i["default"]},activated:function(){this.dataForm.activityId=this.$route.query.activityId,this.getDataList()},methods:{getDataList:function(){var e=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/activity/activityrole/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,activityId:this.dataForm.activityId,name:this.dataForm.name})}).then((function(t){var a=t.data;a&&200===a.code?(e.dataList=a.page.list,e.totalPage=a.page.totalCount):(e.dataList=[],e.totalPage=0),e.dataListLoading=!1}))},sizeChangeHandle:function(e){this.pageSize=e,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(e){this.pageIndex=e,this.getDataList()},selectionChangeHandle:function(e){this.dataListSelections=e},addOrUpdateHandle:function(e){var t=this;this.addOrUpdateVisible=!0,this.$nextTick((function(){t.$refs.addOrUpdate.init(e,t.dataForm.activityId)}))},deleteHandle:function(e){var t=this,a=e?[e]:this.dataListSelections.map((function(e){return e.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(e?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:t.$http.adornUrl("/activity/activityrole/delete"),method:"post",data:t.$http.adornData(a,!1)}).then((function(e){var a=e.data;a&&200===a.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.getDataList()}}):t.$message.error(a.msg)}))}))}}},d=n,s=a("2877"),u=Object(s["a"])(d,r,l,!1,null,null,null);t["default"]=u.exports},"593c":function(e,t,a){"use strict";a.d(t,"h",(function(){return r})),a.d(t,"i",(function(){return l})),a.d(t,"n",(function(){return o})),a.d(t,"f",(function(){return i})),a.d(t,"o",(function(){return n})),a.d(t,"c",(function(){return d})),a.d(t,"m",(function(){return s})),a.d(t,"b",(function(){return u})),a.d(t,"g",(function(){return c})),a.d(t,"j",(function(){return v})),a.d(t,"d",(function(){return m})),a.d(t,"k",(function(){return y})),a.d(t,"l",(function(){return p})),a.d(t,"a",(function(){return f})),a.d(t,"e",(function(){return g}));var r=[{key:0,value:"后台签到"},{key:1,value:"微信扫码签到"}],l=[{key:0,value:"未签到"},{key:1,value:"已签到"},{key:2,value:"已签退"}],o=[{key:0,value:"未报名"},{key:1,value:"已报名"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],i=[{key:0,value:"无需"},{key:1,value:"统一"},{key:2,value:"随机"},{key:3,value:"统一(多个)"}],n=[{key:0,value:"未审核"},{key:1,value:"审核通过"},{key:2,value:"审核不通过"}],d=[{key:0,value:"飞机"},{key:1,value:"火车"},{key:2,value:"其他"}],s=[{key:0,value:"会务组出票"},{key:1,value:"自行出票"}],u=[{key:0,value:"主控"},{key:1,value:"学员管理"},{key:2,value:"专家管理"},{key:3,value:"技术"},{key:4,value:"业务员"},{key:5,value:"兼职"}],c=[{key:0,value:"项目收支"},{key:1,value:"服务费"}],v=[{key:0,value:"大会出票"},{key:1,value:"自行购买，凭票大会报销"}],m=[{key:0,value:"未出票"},{key:1,value:"已出票"}],y=[{key:0,value:"已提交"},{key:1,value:"已预订"},{key:2,value:"已支付,待出票"},{key:4,value:"已出票"},{key:5,value:"不能出票待退款"},{key:7,value:"不能出票已退款"},{key:13,value:"(退票)审核不通过交易结束"},{key:15,value:"(退票)申请中"},{key:16,value:"(退票)已退款交易结束"},{key:22,value:"(改签)审核不通过交易结束"},{key:23,value:"(改签)需补款待支付"},{key:26,value:"(改签)无法改签已退款交易结束"},{key:27,value:"(改签)改签成功交易结束"},{key:28,value:"(改签)改签已取消交易结束"},{key:29,value:"(改签)改签处理中"},{key:99,value:"已取消"}],p=[{key:0,value:"已提交"},{key:1,value:"已预订"},{key:2,value:"占位成功"},{key:4,value:"占位失败"},{key:5,value:"已取消"},{key:8,value:"出票成功"},{key:9,value:"出票失败"},{key:31,value:"改签占位成功"},{key:32,value:"改签占位失败"},{key:34,value:"改签确认出票成功"},{key:35,value:"改签确认出票失败"},{key:37,value:"改签已取消"},{key:21,value:"退票成功"},{key:22,value:"退票失败"},{key:23,value:"已退票未退款"}],f=[{key:0,value:"专家信息"},{key:1,value:"专家任务确认"},{key:2,value:"专家行程"},{key:3,value:"专家接送"},{key:4,value:"专家劳务费信息"},{key:5,value:"专家劳务费签字"},{key:6,value:"其他"},{key:7,value:"活动即将过期"},{key:8,value:"活动已过期"},{key:9,value:"活动续费成功"}],g=[{key:0,value:"未读"},{key:1,value:"已读"}]},a15b:function(e,t,a){"use strict";var r=a("23e7"),l=a("e330"),o=a("44ad"),i=a("fc6a"),n=a("a640"),d=l([].join),s=o!==Object,u=s||!n("join",",");r({target:"Array",proto:!0,forced:u},{join:function(e){return d(i(this),void 0===e?",":e)}})},a573:function(e,t,a){"use strict";a("ab43")},ab43:function(e,t,a){"use strict";var r=a("23e7"),l=a("d024"),o=a("c430");r({target:"Iterator",proto:!0,real:!0,forced:o},{map:l})},d024:function(e,t,a){"use strict";var r=a("c65b"),l=a("59ed"),o=a("825a"),i=a("46c4"),n=a("c5cc"),d=a("9bdd"),s=n((function(){var e=this.iterator,t=o(r(this.next,e)),a=this.done=!!t.done;if(!a)return d(e,this.mapper,[t.value,this.counter++],!0)}));e.exports=function(e){return o(this),l(e),new s(i(this),{mapper:e})}},d81d:function(e,t,a){"use strict";var r=a("23e7"),l=a("b727").map,o=a("1dde"),i=o("map");r({target:"Array",proto:!0,forced:!i},{map:function(e){return l(this,e,arguments.length>1?arguments[1]:void 0)}})},fce4:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"用户",prop:"userId"}},[t("el-select",{attrs:{placeholder:"用户",filterable:""},model:{value:e.dataForm.userId,callback:function(t){e.$set(e.dataForm,"userId",t)},expression:"dataForm.userId"}},e._l(e.sysuser,(function(e){return t("el-option",{key:e.userId,attrs:{label:e.username,value:e.userId}})})),1)],1),t("el-form-item",{attrs:{label:"角色",prop:"roleId"}},[t("el-select",{attrs:{placeholder:"角色",filterable:""},model:{value:e.dataForm.roleId,callback:function(t){e.$set(e.dataForm,"roleId",t)},expression:"dataForm.roleId"}},e._l(e.activityRole,(function(e){return t("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"状态",prop:"status"}},[t("el-radio-group",{model:{value:e.dataForm.status,callback:function(t){e.$set(e.dataForm,"status",t)},expression:"dataForm.status"}},[t("el-radio",{attrs:{label:0}},[e._v("禁用")]),t("el-radio",{attrs:{label:1}},[e._v("正常")])],1)],1),t("el-form-item",{attrs:{label:"工作板块",prop:"workPlace"}},[t("el-input",{attrs:{placeholder:"工作板块"},model:{value:e.dataForm.workPlace,callback:function(t){e.$set(e.dataForm,"workPlace",t)},expression:"dataForm.workPlace"}})],1),t("el-form-item",{attrs:{label:"不足之处",prop:"bad"}},[t("el-input",{attrs:{placeholder:"不足之处"},model:{value:e.dataForm.bad,callback:function(t){e.$set(e.dataForm,"bad",t)},expression:"dataForm.bad"}})],1),t("el-form-item",{attrs:{label:"亮点",prop:"good"}},[t("el-input",{attrs:{placeholder:"亮点"},model:{value:e.dataForm.good,callback:function(t){e.$set(e.dataForm,"good",t)},expression:"dataForm.good"}})],1),t("el-form-item",{attrs:{label:"改进地方",prop:"better"}},[t("el-input",{attrs:{placeholder:"改进地方"},model:{value:e.dataForm.better,callback:function(t){e.$set(e.dataForm,"better",t)},expression:"dataForm.better"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},l=[],o=a("593c"),i={data:function(){return{activityRole:o["b"],sysuser:[],visible:!1,dataForm:{id:0,activityId:"",userId:"",roleId:"",workPlace:"",bad:"",good:"",better:"",status:1},dataRule:{activityId:[{required:!0,message:"活动表id不能为空",trigger:"blur"}],userId:[{required:!0,message:"用户ID不能为空",trigger:"blur"}],roleId:[{required:!0,message:"权限ID不能为空",trigger:"blur"}],status:[{required:!0,message:"状态，0-禁用，1-启用不能为空",trigger:"blur"}]}}},methods:{init:function(e,t){var a=this;this.dataForm.id=e||0,this.visible=!0,this.$nextTick((function(){a.$refs["dataForm"].resetFields(),a.dataForm.id?a.$http({url:a.$http.adornUrl("/activity/activityrole/info/".concat(a.dataForm.id)),method:"get",params:a.$http.adornParams()}).then((function(e){var t=e.data;t&&200===t.code&&(a.dataForm.activityId=t.activityRole.activityId,a.dataForm.userId=t.activityRole.userId,a.dataForm.roleId=t.activityRole.roleId,a.dataForm.status=t.activityRole.status,a.dataForm.workPlace=t.activityRole.workPlace,a.dataForm.bad=t.activityRole.bad,a.dataForm.good=t.activityRole.good,a.dataForm.better=t.activityRole.better)})):a.dataForm.activityId=t})),this.getSysUser()},getSysUser:function(){var e=this;this.$http({url:this.$http.adornUrl("/sys/user/findByAppid"),method:"get"}).then((function(t){var a=t.data;a&&200===a.code?e.sysuser=a.result:e.sysuser=[]}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$http({url:e.$http.adornUrl("/activity/activityrole/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,activityId:e.dataForm.activityId,userId:e.dataForm.userId,roleId:e.dataForm.roleId,workPlace:e.dataForm.workPlace,bad:e.dataForm.bad,good:e.dataForm.good,better:e.dataForm.better,status:e.dataForm.status})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(a.msg)}))}))}}},n=i,d=a("2877"),s=Object(d["a"])(n,r,l,!1,null,null,null);t["default"]=s.exports}}]);
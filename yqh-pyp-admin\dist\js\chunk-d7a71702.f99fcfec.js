(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d7a71702"],{"72c3":function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"名称",clearable:""},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")]),t.isAuth("activity:activity:save")?e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addOrUpdateHandle()}}},[t._v("新增")]):t._e(),e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.showMerchantQrcode()}}},[t._v("商家登录二维码")]),t.isAuth("activity:activity:delete")?e("el-button",{attrs:{type:"danger",disabled:t.dataListSelections.length<=0},on:{click:function(e){return t.deleteHandle()}}},[t._v("批量删除")]):t._e()],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,height:"800px",border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"活动名称"}}),e("el-table-column",{attrs:{prop:"pvCount","header-align":"center",align:"center",label:"点击数"}}),e("el-table-column",{attrs:{prop:"uvCount","header-align":"center",align:"center",label:"访问数"}}),e("el-table-column",{attrs:{prop:"expirationTime","header-align":"center",align:"center",label:"过期时间",width:"180"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.expirationTime?e("span",[t._v(" "+t._s(t.formatDate(a.row.expirationTime))+" ")]):e("span",{staticStyle:{color:"#67C23A"}},[t._v("永不过期")])]}}])}),e("el-table-column",{attrs:{prop:"isExpired","header-align":"center",align:"center",label:"过期状态",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[1===a.row.isExpired?e("el-tag",{attrs:{type:"danger"}},[t._v("已过期")]):t.isExpiringSoon(a.row)?e("el-tag",{attrs:{type:"warning"}},[t._v("即将过期")]):e("el-tag",{attrs:{type:"success"}},[t._v("正常")])]}}])}),e("el-table-column",{attrs:{"header-align":"center",align:"center",label:"充值次数",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticStyle:{color:"#409EFF","font-weight":"bold"}},[t._v(t._s(a.row.allCount||0))])]}}])}),e("el-table-column",{attrs:{"header-align":"center",align:"center",label:"已使用",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticStyle:{color:"#E6A23C","font-weight":"bold"}},[t._v(t._s(a.row.useCount||0))])]}}])}),e("el-table-column",{attrs:{"header-align":"center",align:"center",label:"未使用",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticStyle:{color:"#67C23A","font-weight":"bold"}},[t._v(t._s((a.row.allCount||0)-(a.row.useCount||0)))])]}}])}),e("el-table-column",{attrs:{prop:"pvCount",width:"150px","header-align":"center",align:"center",label:"活动网站"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{on:{click:function(e){return t.openUrl("https://yqihua.com/p_front/#/cms/index?id="+a.row.id)}}},[e("vue-qrcode",{attrs:{options:{width:120},value:"https://yqihua.com/p_front/#/cms/index?id="+a.row.id}})],1)}}])}),e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"350",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.acitivityConfig(a.row.id)}}},[t._v("活动配置")]),t.isAuth("activity:activity:update")?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrUpdateHandle(a.row.id)}}},[t._v("修改")]):t._e(),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.manageExpiration(a.row)}}},[t._v("过期管理")]),t.isAuth("activity:activity:delete")?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")]):t._e()]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),e("el-dialog",{attrs:{title:"商家登录二维码",visible:t.merchantQrcodeVisible,width:"450px",center:""},on:{"update:visible":function(e){t.merchantQrcodeVisible=e}}},[e("div",{staticStyle:{"text-align":"center"}},[e("canvas",{ref:"merchantQrcodeCanvas",staticStyle:{border:"1px solid #ddd","border-radius":"8px"},attrs:{width:"300",height:"350"}}),e("p",{staticStyle:{"margin-top":"20px",color:"#666"}},[t._v("扫描二维码进入商家登录页面")])]),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:t.copyMerchantQrcodeImage}},[t._v("复制图片")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){t.merchantQrcodeVisible=!1}}},[t._v("确定")])],1)]),e("el-dialog",{attrs:{title:"活动过期管理",visible:t.expirationManageVisible,width:"600px"},on:{"update:visible":function(e){t.expirationManageVisible=e}}},[t.currentActivity?e("div",[e("el-form",{attrs:{model:t.expirationForm,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"活动名称"}},[e("span",[t._v(t._s(t.currentActivity.name))])]),e("el-form-item",{attrs:{label:"当前状态"}},[1===t.currentActivity.isExpired?e("el-tag",{attrs:{type:"danger"}},[t._v("已过期")]):t.isExpiringSoon(t.currentActivity)?e("el-tag",{attrs:{type:"warning"}},[t._v("即将过期")]):e("el-tag",{attrs:{type:"success"}},[t._v("正常")])],1),e("el-form-item",{attrs:{label:"当前过期时间"}},[t.currentActivity.expirationTime?e("span",[t._v(" "+t._s(t.formatDate(t.currentActivity.expirationTime))+" ")]):e("span",{staticStyle:{color:"#67C23A"}},[t._v("永不过期")])]),e("el-form-item",{attrs:{label:"设置过期时间"}},[e("el-date-picker",{attrs:{type:"datetime",placeholder:"选择过期时间",format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:t.expirationForm.expirationTime,callback:function(e){t.$set(t.expirationForm,"expirationTime",e)},expression:"expirationForm.expirationTime"}}),e("el-button",{attrs:{type:"text"},on:{click:function(e){t.expirationForm.expirationTime=null}}},[t._v("设为永不过期")])],1),e("el-form-item",{attrs:{label:"或延长天数"}},[e("el-input-number",{attrs:{min:1,max:3650,placeholder:"延长天数"},model:{value:t.expirationForm.extendDays,callback:function(e){t.$set(t.expirationForm,"extendDays",e)},expression:"expirationForm.extendDays"}}),e("span",{staticStyle:{"margin-left":"10px"}},[t._v("天")])],1)],1)],1):t._e(),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.expirationManageVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary",loading:t.expirationLoading},on:{click:t.setExpirationTime}},[t._v("设置过期时间")]),e("el-button",{attrs:{type:"success",loading:t.expirationLoading},on:{click:t.extendExpiration}},[t._v("延长有效期")])],1)])],1)},n=[],r=a("c7eb"),o=a("1da1"),l=(a("99af"),a("a15b"),a("d81d"),a("14d9"),a("a573"),a("b2e5")),c=a.n(l),s=a("7de9"),d={data:function(){return{client:[],appid:"",wxAccount:{},yesOrNo:s["g"],dataForm:{name:"",clientId:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1,dataListSelections:[],addOrUpdateVisible:!1,merchantQrcodeVisible:!1,merchantLoginUrl:"https://yqihua.com/p_front/#/",expirationManageVisible:!1,expirationLoading:!1,currentActivity:null,expirationForm:{expirationTime:null,extendDays:30}}},components:{VueQrcode:c.a},activated:function(){this.appid=this.$cookie.get("appid"),this.getDataList(),this.getAccountInfo(),this.findClient()},methods:{getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/activity/activity/list"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,clientId:this.dataForm.clientId,name:this.dataForm.name})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},findClient:function(){var t=this;this.$http({url:this.$http.adornUrl("/client/client/findAll"),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.client=a.result)}))},getAccountInfo:function(){var t=this;this.$http({url:this.$http.adornUrl("/manage/wxAccount/info/".concat(this.appid)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.wxAccount=a.wxAccount)}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},selectionChangeHandle:function(t){this.dataListSelections=t},addOrUpdateHandle:function(t){this.addOrUpdateVisible=!0,this.$router.push({name:"activityAddOrUpdate",query:{id:t}})},deleteHandle:function(t){var e=this,a=t?[t]:this.dataListSelections.map((function(t){return t.id}));this.$confirm("确定对[id=".concat(a.join(","),"]进行[").concat(t?"删除":"批量删除","]操作?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/activity/activity/delete"),method:"post",data:e.$http.adornData(a,!1)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},copy:function(t){var e=this;this.$confirm("确定复制会议操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/activity/activity/copy"),method:"get",params:e.$http.adornParams({id:t})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1e3,onClose:function(){e.getDataList()}}):e.$message.error(a.msg)}))}))},acitivityConfig:function(t){this.$router.push({name:"activityConfig",query:{activityId:t}})},openUrl:function(t){window.open(t)},showMerchantQrcode:function(){var t=this;this.merchantQrcodeVisible=!0,this.$nextTick((function(){t.generateMerchantQrcodeCanvas()}))},generateMerchantQrcodeCanvas:function(){var t=this;return Object(o["a"])(Object(r["a"])().mark((function e(){var i,n,o,l,c;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return i=t.$refs.merchantQrcodeCanvas,n=i.getContext("2d"),n.clearRect(0,0,i.width,i.height),n.fillStyle="#ffffff",n.fillRect(0,0,i.width,i.height),e.prev=5,o=a("d055"),e.next=9,o.toDataURL(t.merchantLoginUrl,{width:200,height:200,margin:1,color:{dark:"#000000",light:"#FFFFFF"}});case 9:l=e.sent,c=new Image,c.onload=function(){var t=200,e=(i.width-t)/2,a=30;n.drawImage(c,e,a,t,t),n.fillStyle="#333333",n.font="bold 16px Arial, sans-serif",n.textAlign="center",n.textBaseline="middle";var r=a+t+40;n.fillText("易企化AI爆店码商家端",i.width/2,r)},c.onerror=function(){t.drawErrorMessage(n,i,"二维码图片加载失败")},c.src=l,e.next=20;break;case 16:e.prev=16,e.t0=e["catch"](5),console.error("生成二维码失败:",e.t0),t.drawErrorMessage(n,i,"二维码生成失败");case 20:case"end":return e.stop()}}),e,null,[[5,16]])})))()},drawErrorMessage:function(t,e,a){t.fillStyle="#ff0000",t.font="16px Arial",t.textAlign="center",t.textBaseline="middle",t.fillText(a,e.width/2,e.height/2)},copyMerchantQrcodeImage:function(){var t=this,e=this.$refs.merchantQrcodeCanvas;e.toBlob((function(a){if(navigator.clipboard&&window.ClipboardItem){var i=new ClipboardItem({"image/png":a});navigator.clipboard.write([i]).then((function(){t.$message.success("图片已复制到剪贴板")})).catch((function(){t.fallbackCopyMerchantImage(e)}))}else t.fallbackCopyMerchantImage(e)}),"image/png")},fallbackCopyMerchantImage:function(t){try{var e=t.toDataURL("image/png"),a=document.createElement("a");a.download="易企化商家登录二维码.png",a.href=e,a.click(),this.$message.success("图片已下载到本地")}catch(i){this.$message.error("复制失败，请手动截图保存")}},manageExpiration:function(t){this.currentActivity=t,this.expirationForm.expirationTime=t.expirationTime,this.expirationForm.extendDays=30,this.expirationManageVisible=!0},setExpirationTime:function(){var t=this;this.currentActivity&&(this.expirationLoading=!0,this.$http({url:this.$http.adornUrl("/activity/expiration/setExpirationTime"),method:"post",data:this.$http.adornData({activityId:this.currentActivity.id,expirationTime:this.expirationForm.expirationTime?new Date(this.expirationForm.expirationTime).getTime():null})}).then((function(e){var a=e.data;t.expirationLoading=!1,a&&200===a.code?(t.$message.success("设置成功"),t.expirationManageVisible=!1,t.getDataList()):t.$message.error(a.msg||"设置失败")})).catch((function(){t.expirationLoading=!1,t.$message.error("设置失败")})))},extendExpiration:function(){var t=this;this.currentActivity&&this.expirationForm.extendDays?(this.expirationLoading=!0,this.$http({url:this.$http.adornUrl("/activity/expiration/extend"),method:"post",data:this.$http.adornData({activityId:this.currentActivity.id,days:this.expirationForm.extendDays})}).then((function(e){var a=e.data;t.expirationLoading=!1,a&&200===a.code?(t.$message.success("延长成功"),t.expirationManageVisible=!1,t.getDataList()):t.$message.error(a.msg||"延长失败")})).catch((function(){t.expirationLoading=!1,t.$message.error("延长失败")}))):this.$message.warning("请输入延长天数")},formatDate:function(t){if(!t)return"";var e=new Date(t);return e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})},isExpiringSoon:function(t){if(!t.expirationTime||1===t.isExpired)return!1;var e=new Date,a=new Date(t.expirationTime),i=a.getTime()-e.getTime(),n=Math.ceil(i/864e5);return n<=7&&n>0}}},u=d,p=a("2877"),g=Object(p["a"])(u,i,n,!1,null,null,null);e["default"]=g.exports},"7de9":function(t,e,a){"use strict";a.d(e,"g",(function(){return i})),a.d(e,"f",(function(){return n})),a.d(e,"e",(function(){return r})),a.d(e,"a",(function(){return o})),a.d(e,"b",(function(){return l})),a.d(e,"c",(function(){return c})),a.d(e,"d",(function(){return s}));var i=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],n=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],r=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],o=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],l=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],c=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],s=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-68734aaf"],{"11e9":function(t,e,a){var i=a("52a7"),s=a("4630"),c=a("6821"),n=a("6a99"),r=a("69a8"),o=a("c69a"),u=Object.getOwnPropertyDescriptor;e.f=a("9e1e")?u:function(t,e){if(t=c(t),e=n(e,!0),o)try{return u(t,e)}catch(a){}if(r(t,e))return s(!i.f.call(t,e),t[e])}},"4f36":function(t,e,a){"use strict";a.r(e);a("7f7f");var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"account-page"},[e("div",{staticClass:"activity-selector"},[e("van-dropdown-menu",[e("van-dropdown-item",{attrs:{options:t.activityOptions},on:{change:t.onActivityChange},scopedSlots:t._u([{key:"title",fn:function(){return[e("span",{staticClass:"activity-title"},[t._v(t._s(t.currentActivityName||"选择活动"))]),e("van-icon",{attrs:{name:"arrow-down"}})]},proxy:!0}]),model:{value:t.selectedActivityId,callback:function(e){t.selectedActivityId=e},expression:"selectedActivityId"}})],1)],1),e("div",{staticClass:"balance-card"},[e("div",{staticClass:"balance-header"},[e("h2",[t._v("我的账户")]),e("van-icon",{attrs:{name:"question-o"},on:{click:t.showHelp}})],1),e("div",{staticClass:"balance-content"},[e("div",{staticClass:"balance-item"},[e("div",{staticClass:"balance-value"},[t._v(t._s(t.accountInfo.allCount||0))]),e("div",{staticClass:"balance-label"},[t._v("总次数")])]),e("div",{staticClass:"balance-item"},[e("div",{staticClass:"balance-value"},[t._v(t._s(t.accountInfo.useCount||0))]),e("div",{staticClass:"balance-label"},[t._v("已使用")])]),e("div",{staticClass:"balance-item"},[e("div",{staticClass:"balance-value"},[t._v(t._s((t.accountInfo.allCount||0)-(t.accountInfo.useCount||0)))]),e("div",{staticClass:"balance-label"},[t._v("剩余次数")])])]),e("div",{staticClass:"balance-actions"},[e("van-button",{attrs:{type:"primary",size:"large"},on:{click:t.togglePackages}},[e("van-icon",{attrs:{name:"plus"}}),t._v("\n        "+t._s(t.showPackages?"收起套餐":"立即充值")+"\n      ")],1)],1)]),t.showPackages?e("div",{staticClass:"packages-section"},[e("div",{staticClass:"section-header"},[e("h3",[t._v("充值套餐")]),e("van-button",{attrs:{size:"small"},on:{click:function(e){t.showPackages=!1}}},[t._v("收起")])],1),t.rechargeCountPackages.length>0?e("div",{staticClass:"package-group"},[e("h4",[t._v("次数充值")]),e("div",{staticClass:"package-list"},t._l(t.rechargeCountPackages,(function(a){return e("div",{key:a.id,staticClass:"package-item",on:{click:function(e){return t.selectPackage(a)}}},[e("div",{staticClass:"package-header"},[e("div",{staticClass:"package-name"},[t._v(t._s(a.name))]),e("div",{staticClass:"package-price"},[t._v("¥"+t._s(a.price))])]),e("div",{staticClass:"package-content"},[e("div",{staticClass:"package-count"},[t._v(t._s(a.countValue)+"次")]),e("div",{staticClass:"package-desc"},[t._v(t._s(a.description||"暂无描述"))])])])})),0)]):t._e(),t.createActivityPackages.length>0?e("div",{staticClass:"package-group"},[e("h4",[t._v("创建活动套餐")]),e("div",{staticClass:"package-list"},t._l(t.createActivityPackages,(function(a){return e("div",{key:a.id,staticClass:"package-item",on:{click:function(e){return t.selectPackage(a)}}},[e("div",{staticClass:"package-header"},[e("div",{staticClass:"package-name"},[t._v(t._s(a.name))]),e("div",{staticClass:"package-price"},[t._v("¥"+t._s(a.price))])]),e("div",{staticClass:"package-content"},[e("div",{staticClass:"package-count"},[t._v(t._s(a.countValue)+"次")]),e("div",{staticClass:"package-desc"},[t._v(t._s(a.description||"暂无描述"))])])])})),0)]):t._e()]):t._e(),e("div",{staticClass:"record-tabs"},[e("van-tabs",{on:{click:t.handleTabClick},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[e("van-tab",{attrs:{title:"充值记录",name:"recharge"}},[e("van-tabs",{staticClass:"status-sub-tabs",on:{click:t.handleRechargeStatusTabClick},model:{value:t.rechargeStatusTab,callback:function(e){t.rechargeStatusTab=e},expression:"rechargeStatusTab"}},[e("van-tab",{attrs:{title:"已支付",name:"paid"}},[e("div",{staticClass:"record-list"},[e("van-list",{attrs:{finished:t.paidRechargeFinished,"finished-text":"没有更多了"},on:{load:t.loadPaidRechargeRecords},model:{value:t.paidRechargeLoading,callback:function(e){t.paidRechargeLoading=e},expression:"paidRechargeLoading"}},t._l(t.paidRechargeList,(function(a){return e("div",{key:a.id,staticClass:"record-item"},[e("div",{staticClass:"record-header"},[e("div",{staticClass:"record-title"},[t._v(t._s(a.orderSn))]),e("div",{staticClass:"record-amount"},[t._v("+"+t._s(a.countValue)+"次")])]),e("div",{staticClass:"record-content"},[e("div",{staticClass:"record-type"},[e("van-tag",{attrs:{type:t.getRechargeTypeTag(a.rechargeType).type}},[t._v("\n                        "+t._s(t.getRechargeTypeTag(a.rechargeType).text)+"\n                      ")])],1),e("div",{staticClass:"record-price"},[t._v("¥"+t._s(a.payAmount||a.amount))])]),e("div",{staticClass:"record-usage"},[e("div",{staticClass:"usage-item"},[e("span",{staticClass:"usage-label"},[t._v("已使用：")]),e("span",{staticClass:"usage-value used"},[t._v(t._s(a.usedCount||0)+"次")])]),e("div",{staticClass:"usage-item"},[e("span",{staticClass:"usage-label"},[t._v("剩余：")]),e("span",{staticClass:"usage-value remaining"},[t._v(t._s((a.countValue||0)-(a.usedCount||0))+"次")])]),e("div",{staticClass:"usage-item"},[e("span",{staticClass:"usage-label"},[t._v("使用率：")]),e("span",{staticClass:"usage-value rate"},[t._v(t._s(t.getUsagePercentage(a))+"%")])])]),e("div",{staticClass:"record-footer"},[e("div",{staticClass:"record-time"},[t._v(t._s(t.formatDate(a.createOn)))]),e("div",{staticClass:"record-actions"},[e("van-tag",{staticClass:"status-tag",attrs:{type:t.getRechargeStatusTag(a.status).type}},[t._v("\n                        "+t._s(t.getRechargeStatusTag(a.status).text)+"\n                      ")]),t.canRefund(a)?e("div",{class:["refund-btn",t.getRefundBtnClass(a)],on:{click:function(e){return t.handleRefund(a)}}},[e("van-icon",{attrs:{name:"refund-o"}}),e("span",[t._v("退款")])],1):t._e()],1)])])})),0)],1)]),e("van-tab",{attrs:{title:"退款订单",name:"refund"}},[e("div",{staticClass:"record-list"},[e("van-list",{attrs:{finished:t.refundRechargeFinished,"finished-text":"没有更多了"},on:{load:t.loadRefundRechargeRecords},model:{value:t.refundRechargeLoading,callback:function(e){t.refundRechargeLoading=e},expression:"refundRechargeLoading"}},t._l(t.refundRechargeList,(function(a){return e("div",{key:a.id,staticClass:"record-item"},[e("div",{staticClass:"record-header"},[e("div",{staticClass:"record-title"},[t._v(t._s(a.orderSn))]),e("div",{staticClass:"record-amount"},[t._v("+"+t._s(a.countValue)+"次")])]),e("div",{staticClass:"record-content"},[e("div",{staticClass:"record-type"},[e("van-tag",{attrs:{type:t.getRechargeTypeTag(a.rechargeType).type}},[t._v("\n                        "+t._s(t.getRechargeTypeTag(a.rechargeType).text)+"\n                      ")])],1),e("div",{staticClass:"record-price"},[t._v("¥"+t._s(a.payAmount||a.amount))])]),3===a.status?e("div",{staticClass:"record-usage"},[e("div",{staticClass:"usage-item"},[e("span",{staticClass:"usage-label"},[t._v("已使用：")]),e("span",{staticClass:"usage-value used"},[t._v(t._s(a.usedCount||0)+"次")])]),e("div",{staticClass:"usage-item"},[e("span",{staticClass:"usage-label"},[t._v("退款前剩余：")]),e("span",{staticClass:"usage-value remaining"},[t._v(t._s((a.countValue||0)-(a.usedCount||0))+"次")])]),e("div",{staticClass:"usage-item"},[e("span",{staticClass:"usage-label"},[t._v("使用率：")]),e("span",{staticClass:"usage-value rate"},[t._v(t._s(t.getUsagePercentage(a))+"%")])])]):t._e(),e("div",{staticClass:"record-footer"},[e("div",{staticClass:"record-time"},[t._v(t._s(t.formatDate(a.createOn)))]),e("div",{staticClass:"record-actions"},[e("van-tag",{staticClass:"status-tag",attrs:{type:t.getRechargeStatusTag(a.status).type}},[t._v("\n                        "+t._s(t.getRechargeStatusTag(a.status).text)+"\n                      ")])],1)])])})),0)],1)]),e("van-tab",{attrs:{title:"全部",name:"all"}},[e("div",{staticClass:"record-list"},[e("van-list",{attrs:{finished:t.allRechargeFinished,"finished-text":"没有更多了"},on:{load:t.loadAllRechargeRecords},model:{value:t.allRechargeLoading,callback:function(e){t.allRechargeLoading=e},expression:"allRechargeLoading"}},t._l(t.allRechargeList,(function(a){return e("div",{key:a.id,staticClass:"record-item"},[e("div",{staticClass:"record-header"},[e("div",{staticClass:"record-title"},[t._v(t._s(a.orderSn))]),e("div",{staticClass:"record-amount"},[t._v("+"+t._s(a.countValue)+"次")])]),e("div",{staticClass:"record-content"},[e("div",{staticClass:"record-type"},[e("van-tag",{attrs:{type:t.getRechargeTypeTag(a.rechargeType).type}},[t._v("\n                        "+t._s(t.getRechargeTypeTag(a.rechargeType).text)+"\n                      ")])],1),e("div",{staticClass:"record-price"},[t._v("¥"+t._s(a.payAmount||a.amount))])]),1===a.status||3===a.status?e("div",{staticClass:"record-usage"},[e("div",{staticClass:"usage-item"},[e("span",{staticClass:"usage-label"},[t._v("已使用：")]),e("span",{staticClass:"usage-value used"},[t._v(t._s(a.usedCount||0)+"次")])]),e("div",{staticClass:"usage-item"},[e("span",{staticClass:"usage-label"},[t._v(t._s(3===a.status?"退款前剩余：":"剩余："))]),e("span",{staticClass:"usage-value remaining"},[t._v(t._s((a.countValue||0)-(a.usedCount||0))+"次")])]),e("div",{staticClass:"usage-item"},[e("span",{staticClass:"usage-label"},[t._v("使用率：")]),e("span",{staticClass:"usage-value rate"},[t._v(t._s(t.getUsagePercentage(a))+"%")])])]):t._e(),e("div",{staticClass:"record-footer"},[e("div",{staticClass:"record-time"},[t._v(t._s(t.formatDate(a.createOn)))]),e("div",{staticClass:"record-actions"},[e("van-tag",{staticClass:"status-tag",attrs:{type:t.getRechargeStatusTag(a.status).type}},[t._v("\n                        "+t._s(t.getRechargeStatusTag(a.status).text)+"\n                      ")]),0===a.status?e("van-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.goToPay(a)}}},[t._v("\n                        去支付\n                      ")]):t._e(),t.canRefund(a)?e("div",{class:["refund-btn",t.getRefundBtnClass(a)],on:{click:function(e){return t.handleRefund(a)}}},[e("van-icon",{attrs:{name:"refund-o"}}),e("span",[t._v("退款")])],1):t._e()],1)])])})),0)],1)])],1)],1),e("van-tab",{attrs:{title:"使用记录",name:"usage"}},[e("div",{staticClass:"record-list"},[e("van-list",{attrs:{finished:t.usageFinished,"finished-text":"没有更多了"},on:{load:t.loadUsageRecords},model:{value:t.usageLoading,callback:function(e){t.usageLoading=e},expression:"usageLoading"}},t._l(t.usageList,(function(a){return e("div",{key:a.id,staticClass:"record-item"},[e("div",{staticClass:"record-header"},[e("div",{staticClass:"record-title"},[e("van-tag",{attrs:{type:t.getUsageTypeTag(a.usageType).type}},[t._v("\n                    "+t._s(t.getUsageTypeTag(a.usageType).text)+"\n                  ")])],1),e("div",{staticClass:"record-amount"},[t._v("-"+t._s(a.usageCount)+"次")])]),e("div",{staticClass:"record-content"},[e("div",{staticClass:"record-desc"},[t._v(t._s(a.description||"暂无描述"))])]),e("div",{staticClass:"record-footer"},[e("div",{staticClass:"record-time"},[t._v(t._s(t.formatDate(a.createOn)))])])])})),0)],1)])],1)],1),e("van-dialog",{attrs:{title:"创建活动套餐","show-cancel-button":"","confirm-button-text":"确认购买","cancel-button-text":"取消"},on:{confirm:t.confirmCreateActivity,cancel:t.cancelCreateActivity},model:{value:t.showActivityNameDialogVisible,callback:function(e){t.showActivityNameDialogVisible=e},expression:"showActivityNameDialogVisible"}},[e("div",{staticClass:"activity-name-dialog"},[t.selectedPackageForActivity?e("div",{staticClass:"package-info"},[e("p",[e("strong",[t._v("套餐：")]),t._v(t._s(t.selectedPackageForActivity.name))]),e("p",[e("strong",[t._v("价格：")]),t._v("¥"+t._s(t.selectedPackageForActivity.price))]),e("p",[e("strong",[t._v("次数：")]),t._v(t._s(t.selectedPackageForActivity.countValue)+"次")])]):t._e(),e("div",{staticClass:"input-section"},[e("van-field",{attrs:{label:"活动名称",placeholder:"请输入活动名称",maxlength:"50","show-word-limit":"","error-message":t.activityNameError},on:{input:t.validateActivityName},model:{value:t.activityNameInput,callback:function(e){t.activityNameInput=e},expression:"activityNameInput"}})],1)])])],1)},s=[],c=a("ade3"),n=(a("6b54"),a("96cf"),a("1da1")),r=(a("a481"),a("ac6a"),a("5df3"),a("f400"),a("66c7")),o=a("3e34"),u={name:"Account",data:function(){return{activityId:null,accountInfo:{allCount:0,useCount:0,activityName:"",userId:null},activeTab:"recharge",showPackages:!1,recommendedPackages:[],rechargeCountPackages:[],createActivityPackages:[],rechargeStatusTab:"paid",paidRechargeList:[],paidRechargeLoading:!1,paidRechargeFinished:!1,paidRechargePage:1,refundRechargeList:[],refundRechargeLoading:!1,refundRechargeFinished:!1,refundRechargePage:1,allRechargeList:[],allRechargeLoading:!1,allRechargeFinished:!1,allRechargePage:1,rechargeList:[],rechargeLoading:!1,rechargeFinished:!1,rechargePage:1,usageList:[],usageLoading:!1,usageFinished:!1,usagePage:1,isLoadingRecords:!1,rechargeTypeMap:{1:{text:"套餐充值",type:"primary"},2:{text:"自定义充值",type:"success"},3:{text:"系统赠送",type:"warning"},4:{text:"创建活动套餐",type:"danger"}},rechargeStatusMap:{0:{text:"待支付",type:"warning"},1:{text:"已支付",type:"success"},2:{text:"已取消",type:"danger"},3:{text:"已退款",type:"warning"},4:{text:"退款中",type:"warning"}},usageTypeMap:{1:{text:"生成文案",type:"primary"},2:{text:"生成视频",type:"success"},4:{text:"生成图文成品",type:"success"},3:{text:"转发",type:"warning"}},showActivityNameDialogVisible:!1,selectedPackageForActivity:null,activityNameInput:"",activityNameError:"",refundPermissionCache:new Map}},computed:{userActivities:function(){return this.$store.state.activity.userActivities},selectedActivityId:function(){return this.$store.state.activity.selectedActivityId},currentActivity:function(){return this.$store.state.activity.currentActivity},activityOptions:function(){return this.$store.state.activity.activityOptions},currentActivityName:function(){return this.$store.getters["activity/currentActivityName"]},hasActivities:function(){return this.$store.getters["activity/hasActivities"]}},watch:{currentActivity:{handler:function(t,e){t&&e&&t.id!==e.id&&(console.log("Account页面检测到活动变化:",t),this.activityId=t.id,this.initializeData())},deep:!0},selectedActivityId:function(t,e){t!==e&&t&&(console.log("Account页面检测到选中活动ID变化:",t),this.activityId=t)}},mounted:function(){document.title="我的账户",console.log("=== Account页面 mounted ==="),console.log("URL中的activityId:",this.$route.query.activityId),console.log("Store中的selectedActivityId:",this.$store.state.activity.selectedActivityId);var t=this.$route.query.activityId;if(t)this.activityId=t,console.log("使用URL中的activityId:",this.activityId);else{var e=this.$store.state.activity.selectedActivityId;e?(this.activityId=e,console.log("使用Store中的selectedActivityId:",this.activityId)):console.log("没有找到任何activityId")}this.checkMobileBinding(),"true"===this.$route.query.refresh&&(this.$toast.success("支付成功，账户已更新"),this.$router.replace({name:"account",query:{activityId:this.activityId}}))},activated:function(){console.log("Account页面被激活，检查活动状态同步"),this.checkActivitySync()},methods:Object(c["a"])({checkMobileBinding:function(){var t=this;console.log("开始检查用户手机号绑定状态"),Object(o["checkAndRedirectToBindMobile"])(window.location.href).then((function(e){e&&(console.log("用户已绑定手机号，继续加载页面数据"),t.initActivity())})).catch((function(e){console.error("检查手机号绑定状态失败:",e),t.initActivity()}))},formatDate:function(t){return t?r["a"].formatDate.format(new Date(t),"yyyy-MM-dd hh:mm"):""},initActivity:function(){var t=Object(n["a"])(regeneratorRuntime.mark((function t(){var e,a,i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return console.log("开始初始化活动数据...","preferredActivityId:",this.activityId),t.prev=1,e={api:this.$fly,toast:this.$toast},a=this.$route.query.activityId,a&&"undefined"!==a&&null!==a?(e.preferredActivityId=parseInt(a),console.log("使用URL参数作为首选活动ID:",e.preferredActivityId)):console.log("不传递preferredActivityId，让系统使用缓存的活动ID"),t.next=7,this.$store.dispatch("activity/initializeActivity",e);case 7:i=t.sent,i.success?(console.log("活动数据初始化成功:",i.data),this.activityId=this.selectedActivityId,console.log("设置activityId为:",this.activityId),this.initializeData()):console.error("活动数据初始化失败:",i.error),t.next=14;break;case 11:t.prev=11,t.t0=t["catch"](1),console.error("初始化活动数据时发生错误:",t.t0);case 14:case"end":return t.stop()}}),t,this,[[1,11]])})));function e(){return t.apply(this,arguments)}return e}(),initializeData:function(){this.activityId?(console.log("开始初始化数据，activityId:",this.activityId),this.getAccountInfo(),this.loadPackages(),this.loadPaidRechargeRecords()):console.log("activityId为空，等待活动初始化完成")},refreshRecords:function(){var t=this;this.isLoadingRecords?console.log("记录正在加载中，跳过重复调用"):(console.log("刷新记录数据"),this.isLoadingRecords=!0,this.paidRechargeList=[],this.paidRechargePage=1,this.paidRechargeFinished=!1,this.refundRechargeList=[],this.refundRechargePage=1,this.refundRechargeFinished=!1,this.allRechargeList=[],this.allRechargePage=1,this.allRechargeFinished=!1,this.rechargeList=[],this.rechargePage=1,this.rechargeFinished=!1,"paid"===this.rechargeStatusTab?this.loadPaidRechargeRecords():"refund"===this.rechargeStatusTab?this.loadRefundRechargeRecords():"all"===this.rechargeStatusTab?this.loadAllRechargeRecords():this.loadPaidRechargeRecords(),this.usageList=[],this.usagePage=1,this.usageFinished=!1,"usage"===this.activeTab&&this.loadUsageRecords(),setTimeout((function(){t.isLoadingRecords=!1}),1e3))},checkActivitySync:function(){this.hasActivities?this.currentActivity&&this.activityId!==this.currentActivity.id&&(console.log("Account页面检测到活动状态不一致，同步状态"),this.activityId=this.currentActivity.id,this.initializeData()):(console.log("Account页面检测到没有活动数据，重新初始化"),this.initActivity())},onActivityChange:function(){var t=Object(n["a"])(regeneratorRuntime.mark((function t(e){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return console.log("Activity changed to:",e),t.prev=1,t.next=4,this.$store.dispatch("activity/switchActivity",e);case 4:a=t.sent,a.success?(console.log("活动切换成功:",a.activity),this.activityId=e,this.getAccountInfo(),this.refreshRecords()):(console.error("活动切换失败:",a.error),this.$toast.fail("切换活动失败")),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](1),console.error("切换活动时发生错误:",t.t0),this.$toast.fail("切换活动失败");case 12:case"end":return t.stop()}}),t,this,[[1,8]])})));function e(e){return t.apply(this,arguments)}return e}(),getAccountInfo:function(){var t=this;this.activityId?this.$fly.get("/pyp/web/account/info",{activityId:this.activityId}).then((function(e){200===e.code?t.accountInfo=e.accountInfo:Object(o["handleMobileError"])(e,window.location.href)||console.error("获取账户信息失败:",e.msg)})).catch((function(t){console.error("获取账户信息失败:",t)})):console.log("activityId为空，跳过获取账户信息")},loadPackages:function(){var t=this;this.$fly.get("/pyp/web/account/packages/recommended").then((function(e){200===e.code&&(t.recommendedPackages=e.packages||[])})).catch((function(t){console.error("加载推荐套餐失败:",t)})),this.$fly.get("/pyp/web/account/packages/rechargeCount").then((function(e){200===e.code&&(t.rechargeCountPackages=e.packages||[])})).catch((function(t){console.error("加载充值次数套餐失败:",t)})),this.$fly.get("/pyp/web/account/packages/createActivity").then((function(e){200===e.code&&(t.createActivityPackages=e.packages||[])})).catch((function(t){console.error("加载创建活动套餐失败:",t)}))},togglePackages:function(){this.showPackages=!this.showPackages},selectPackage:function(t){var e=this;2===t.packageType?this.showActivityNameDialog(t):this.$dialog.confirm({title:"确认充值",message:"确定要购买 ".concat(t.name," 吗？\n价格：¥").concat(t.price,"\n次数：").concat(t.countValue,"次")}).then((function(){e.createOrder(t)})).catch((function(){}))},showActivityNameDialog:function(t){this.selectedPackageForActivity=t,this.activityNameInput="",this.activityNameError="",this.showActivityNameDialogVisible=!0},validateActivityName:function(){var t=this.activityNameInput.trim();return t?t.length<2?(this.activityNameError="活动名称至少2个字符",!1):t.length>50?(this.activityNameError="活动名称不能超过50个字符",!1):(this.activityNameError="",!0):(this.activityNameError="活动名称不能为空",!1)},confirmCreateActivity:function(){this.validateActivityName()&&(this.showActivityNameDialogVisible=!1,this.createActivityPackageOrder(this.selectedPackageForActivity,this.activityNameInput.trim()))},cancelCreateActivity:function(){this.showActivityNameDialogVisible=!1,this.selectedPackageForActivity=null,this.activityNameInput="",this.activityNameError=""},createOrder:function(t){var e=this,a={packageId:t.id,activityId:this.activityId,rechargeType:t.packageType,appid:this.$cookie.get("appid"),repeatToken:this.generateToken()};this.$fly.post("/pyp/web/account/recharge",a).then((function(t){200===t.code?(e.$toast.success("订单创建成功"),e.$router.push({name:"rechargePayment",query:{orderId:t.orderId||t.result,from:"account"}})):e.$toast.fail(t.msg||"订单创建失败")})).catch((function(t){console.error("创建订单失败:",t),e.$toast.fail("订单创建失败")}))},createActivityPackageOrder:function(t,e){var a=this,i={packageId:t.id,activityName:e,appid:this.$cookie.get("appid"),repeatToken:this.generateToken()};this.$fly.post("/pyp/web/account/createActivityPackage",i).then((function(t){200===t.code?(a.$toast.success("活动套餐订单创建成功"),a.$router.push({name:"rechargePayment",query:{orderId:t.orderId||t.result,from:"account"}})):a.$toast.fail(t.msg||"订单创建失败")})).catch((function(t){console.error("创建活动套餐订单失败:",t),a.$toast.fail("订单创建失败")}))},generateToken:function(){return Math.random().toString(36).substring(2,17)+Date.now().toString(36)},handleTabClick:function(t){this.activityId?"usage"===t&&0===this.usageList.length&&this.loadUsageRecords():console.log("activityId为空，无法切换tab")},handleRechargeStatusTabClick:function(t){this.activityId?"paid"===t&&0===this.paidRechargeList.length?this.loadPaidRechargeRecords():"refund"===t&&0===this.refundRechargeList.length?this.loadRefundRechargeRecords():"all"===t&&0===this.allRechargeList.length&&this.loadAllRechargeRecords():console.log("activityId为空，无法切换tab")},loadPaidRechargeRecords:function(){var t=this;this.paidRechargeFinished||(this.activityId?(this.paidRechargeLoading=!0,console.log("加载已支付充值记录 - 页码:",this.paidRechargePage,"活动ID:",this.activityId),this.$fly.get("/pyp/web/account/records",{activityId:this.activityId,page:this.paidRechargePage,limit:10,status:1}).then((function(e){if(200===e.code){var a=e.page.list||[];console.log("获取到已支付充值记录:",a.length,"条");var i=a.filter((function(e){return!t.paidRechargeList.some((function(t){return t.id===e.id||t.orderSn&&e.orderSn&&t.orderSn===e.orderSn}))}));t.paidRechargeList=t.paidRechargeList.concat(i),a.length<10?t.paidRechargeFinished=!0:t.paidRechargePage++}else t.paidRechargeFinished=!0;t.paidRechargeLoading=!1})).catch((function(e){console.error("加载已支付充值记录失败:",e),t.paidRechargeLoading=!1,t.paidRechargeFinished=!0}))):console.log("activityId为空，跳过加载已支付充值记录"))},loadRefundRechargeRecords:function(){var t=this;this.refundRechargeFinished||(this.activityId?(this.refundRechargeLoading=!0,console.log("加载退款订单记录 - 页码:",this.refundRechargePage,"活动ID:",this.activityId),this.$fly.get("/pyp/web/account/records",{activityId:this.activityId,page:this.refundRechargePage,limit:10,statusList:"2,3,4"}).then((function(e){if(200===e.code){var a=e.page.list||[];console.log("获取到退款订单记录:",a.length,"条");var i=a.filter((function(e){return!t.refundRechargeList.some((function(t){return t.id===e.id||t.orderSn&&e.orderSn&&t.orderSn===e.orderSn}))}));t.refundRechargeList=t.refundRechargeList.concat(i),a.length<10?t.refundRechargeFinished=!0:t.refundRechargePage++}else t.refundRechargeFinished=!0;t.refundRechargeLoading=!1})).catch((function(e){console.error("加载退款订单记录失败:",e),t.refundRechargeLoading=!1,t.refundRechargeFinished=!0}))):console.log("activityId为空，跳过加载退款订单记录"))},loadAllRechargeRecords:function(){var t=this;this.allRechargeFinished||(this.activityId?(this.allRechargeLoading=!0,console.log("加载全部充值记录 - 页码:",this.allRechargePage,"活动ID:",this.activityId),this.$fly.get("/pyp/web/account/records",{activityId:this.activityId,page:this.allRechargePage,limit:10}).then((function(e){if(200===e.code){var a=e.page.list||[];console.log("获取到全部充值记录:",a.length,"条");var i=a.filter((function(e){return!t.allRechargeList.some((function(t){return t.id===e.id||t.orderSn&&e.orderSn&&t.orderSn===e.orderSn}))}));t.allRechargeList=t.allRechargeList.concat(i),a.length<10?t.allRechargeFinished=!0:t.allRechargePage++}else t.allRechargeFinished=!0;t.allRechargeLoading=!1})).catch((function(e){console.error("加载全部充值记录失败:",e),t.allRechargeLoading=!1,t.allRechargeFinished=!0}))):console.log("activityId为空，跳过加载全部充值记录"))},loadRechargeRecords:function(){this.loadPaidRechargeRecords()},loadUsageRecords:function(){var t=this;this.usageFinished||(this.activityId?(this.usageLoading=!0,console.log("加载使用记录 - 页码:",this.usagePage,"活动ID:",this.activityId),this.$fly.get("/pyp/web/activity/recharge/usageRecords",{activityId:this.activityId,page:this.usagePage,limit:10}).then((function(e){if(200===e.code){var a=e.page.list||[];console.log("获取到使用记录:",a.length,"条");var i=a.filter((function(e){return!t.usageList.some((function(t){return t.id===e.id}))}));console.log("去重后的记录:",i.length,"条"),t.usageList=t.usageList.concat(i),a.length<10?t.usageFinished=!0:t.usagePage++}else t.usageFinished=!0;t.usageLoading=!1})).catch((function(e){console.error("加载使用记录失败:",e),t.usageLoading=!1,t.usageFinished=!0}))):console.log("activityId为空，跳过加载使用记录"))},showHelp:function(){this.$dialog.alert({title:"账户说明",message:"• 总次数：您购买的所有次数总和\n• 已使用：您已经使用的次数\n• 剩余次数：您还可以使用的次数\n\n充值记录显示您的所有充值历史，使用记录显示您的使用详情。"})},getRechargeTypeTag:function(t){return this.rechargeTypeMap[t]||{text:"未知",type:"default"}},getRechargeStatusTag:function(t){return this.rechargeStatusMap[t]||{text:"未知",type:"default"}},getUsageTypeTag:function(t){return this.usageTypeMap[t]||{text:"未知",type:"default"}},goToUserAgreement:function(){this.$router.push({name:"userAgreement"})},goToPrivacyPolicy:function(){this.$router.push({name:"privacyPolicy"})},getUsagePercentage:function(t){var e=t.countValue||0,a=t.usedCount||0;return 0===e?0:Math.round(a/e*100)},canRefund:function(t){if(1!==t.status)return!1;if(1!==t.rechargeType&&2!==t.rechargeType)return!1;if(t.source&&1!==t.source)return!1;var e=(t.countValue||0)-(t.usedCount||0);if(e<=0)return!1;if(1===t.refundEligible)return!0;var a="refund_permission_".concat(t.id);if(this.refundPermissionCache.has(a)){var i=this.refundPermissionCache.get(a);return i.hasPermission}return this.checkRefundPermissionAsync(t.id),!1},checkRefundPermissionAsync:function(){var t=Object(n["a"])(regeneratorRuntime.mark((function t(e){var a,i,s,c;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,a="refund_permission_".concat(e),!this.refundPermissionCache.has(a)){t.next=4;break}return t.abrupt("return");case 4:return this.refundPermissionCache.set(a,{hasPermission:!1,checking:!0}),t.next=7,this.$http.get("/web/activity/account/checkRefundPermission?orderId=".concat(e));case 7:i=t.sent,200===i.data.code&&i.data.data&&(s=i.data.data,this.refundPermissionCache.set(a,{hasPermission:s.hasPermission,reason:s.reason,channelId:s.channelId,channelName:s.channelName,checking:!1}),this.$forceUpdate()),t.next=16;break;case 11:t.prev=11,t.t0=t["catch"](0),console.error("检查退款权限失败:",t.t0),c="refund_permission_".concat(e),this.refundPermissionCache.set(c,{hasPermission:!1,reason:"权限检查失败",checking:!1});case 16:case"end":return t.stop()}}),t,this,[[0,11]])})));function e(e){return t.apply(this,arguments)}return e}(),getRefundBtnClass:function(t){var e=t.usedCount||0;return 0===e?"unused":"partial-used"},handleRefund:function(){var t=Object(n["a"])(regeneratorRuntime.mark((function t(e){var a,i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.$toast.loading({message:"检查退款权限...",forbidClick:!0,duration:0}),t.next=4,this.$http.get("/web/activity/account/checkRefundPermission?orderId=".concat(e.id));case 4:if(a=t.sent,this.$toast.clear(),200===a.data.code&&a.data.data){t.next=9;break}return this.$toast.fail("权限检查失败，请稍后重试"),t.abrupt("return");case 9:if(i=a.data.data,i.hasPermission){t.next=13;break}return this.$dialog.alert({title:"❌ 无法退款",message:"很抱歉，该订单不符合退款条件：\n\n".concat(i.reason,"\n\n").concat(i.channelName?"关联渠道：".concat(i.channelName):""),confirmButtonText:"我知道了",confirmButtonColor:"#ff6b6b"}),t.abrupt("return");case 13:this.proceedWithRefund(e,i),t.next=21;break;case 16:t.prev=16,t.t0=t["catch"](0),this.$toast.clear(),console.error("检查退款权限失败:",t.t0),this.$toast.fail("权限检查失败，请稍后重试");case 21:case"end":return t.stop()}}),t,this,[[0,16]])})));function e(e){return t.apply(this,arguments)}return e}(),proceedWithRefund:function(t,e){var a=this,i=t.usedCount||0,s=t.countValue||0,c=s-i;if(i>0){var n=t.payAmount||t.amount,r=(c/s*100).toFixed(1),o=(n*c/s).toFixed(2),u=e.channelName?"\n🏢 关联渠道：".concat(e.channelName):"";this.$dialog.confirm({title:"💰 部分退款确认",message:"📊 使用情况：\n• 总次数：".concat(s,"次\n• 已使用：").concat(i,"次\n• 剩余：").concat(c,"次\n\n💵 退款信息：\n• 原支付金额：¥").concat(n,"\n• 可退款比例：").concat(r,"%\n• 预计退款：¥").concat(o).concat(u,"\n\n确认申请退款吗？"),confirmButtonText:"确认退款",cancelButtonText:"再想想",confirmButtonColor:"#ff6b6b"}).then((function(){a.submitRefund(t)})).catch((function(){}))}else{var l=e.channelName?"\n🏢 关联渠道：".concat(e.channelName):"";this.$dialog.confirm({title:"💚 全额退款确认",message:"🎯 订单信息：\n• 订单号：".concat(t.orderSn,"\n• 充值次数：").concat(s,"次\n• 使用情况：未使用\n\n💵 退款金额：¥").concat(t.payAmount||t.amount).concat(l,"\n\n确认申请全额退款吗？"),confirmButtonText:"确认退款",cancelButtonText:"再想想",confirmButtonColor:"#4caf50"}).then((function(){a.submitRefund(t)})).catch((function(){}))}},submitRefund:function(t){var e=this;this.$toast.loading({message:"退款申请中...",forbidClick:!0,duration:0}),this.$fly.post("/pyp/web/account/refund",{rechargeRecordId:t.id,orderSn:t.orderSn}).then((function(t){if(e.$toast.clear(),200===t.code){var a=t.refundAmount||"计算中";e.$toast.success({message:"✅ 退款申请已提交\n预计退款：¥".concat(a),duration:3e3}),e.refreshRecords()}else e.$toast.fail(t.msg||"退款申请失败")})).catch((function(t){e.$toast.clear(),console.error("退款申请失败:",t),e.$toast.fail("❌ 退款申请失败，请稍后重试")}))},goToPay:function(t){this.$router.push({name:"rechargePayment",query:{orderId:t.id||t.orderSn,from:"account"}})}},"refreshRecords",(function(){this.refundPermissionCache.clear(),this.paidRechargePage=1,this.refundRechargePage=1,this.allRechargePage=1,this.usagePage=1,this.paidRechargeFinished=!1,this.refundRechargeFinished=!1,this.allRechargeFinished=!1,this.usageFinished=!1,this.paidRechargeList=[],this.refundRechargeList=[],this.allRechargeList=[],this.usageList=[],"paid"===this.rechargeStatusTab?this.loadPaidRechargeRecords():"refund"===this.rechargeStatusTab?this.loadRefundRechargeRecords():"all"===this.rechargeStatusTab&&this.loadAllRechargeRecords(),"usage"===this.activeTab&&this.loadUsageRecords()}))},l=u,d=(a("baa7"),a("2877")),h=Object(d["a"])(l,i,s,!1,null,"616bd232",null);e["default"]=h.exports},"5dbc":function(t,e,a){var i=a("d3f4"),s=a("8b97").set;t.exports=function(t,e,a){var c,n=e.constructor;return n!==a&&"function"==typeof n&&(c=n.prototype)!==a.prototype&&i(c)&&s&&s(t,c),t}},"5df3":function(t,e,a){"use strict";var i=a("02f4")(!0);a("01f9")(String,"String",(function(t){this._t=String(t),this._i=0}),(function(){var t,e=this._t,a=this._i;return a>=e.length?{value:void 0,done:!0}:(t=i(e,a),this._i+=t.length,{value:t,done:!1})}))},"66c7":function(t,e,a){"use strict";a("4917"),a("a481");var i=/([yMdhsm])(\1*)/g,s="yyyy-MM-dd";function c(t,e){e-=(t+"").length;for(var a=0;a<e;a++)t="0"+t;return t}e["a"]={formatDate:{format:function(t,e){return e=e||s,e.replace(i,(function(e){switch(e.charAt(0)){case"y":return c(t.getFullYear(),e.length);case"M":return c(t.getMonth()+1,e.length);case"d":return c(t.getDate(),e.length);case"w":return t.getDay()+1;case"h":return c(t.getHours(),e.length);case"m":return c(t.getMinutes(),e.length);case"s":return c(t.getSeconds(),e.length)}}))},parse:function(t,e){var a=e.match(i),s=t.match(/(\d)+/g);if(a.length==s.length){for(var c=new Date(1970,0,1),n=0;n<a.length;n++){var r=parseInt(s[n]),o=a[n];switch(o.charAt(0)){case"y":c.setFullYear(r);break;case"M":c.setMonth(r-1);break;case"d":c.setDate(r);break;case"h":c.setHours(r);break;case"m":c.setMinutes(r);break;case"s":c.setSeconds(r);break}}return c}return null},toWeek:function(t){var e=new Date(t).getDay(),a="";switch(e){case 0:a="s";break;case 1:a="m";break;case 2:a="t";break;case 3:a="w";break;case 4:a="t";break;case 5:a="f";break;case 6:a="s";break}return a}},toUserLook:function(t){var e=Math.floor(t/3600%24),a=Math.floor(t/60%60);return e<1?a+"分":e+"时"+a+"分"}}},"67ab":function(t,e,a){var i=a("ca5a")("meta"),s=a("d3f4"),c=a("69a8"),n=a("86cc").f,r=0,o=Object.isExtensible||function(){return!0},u=!a("79e5")((function(){return o(Object.preventExtensions({}))})),l=function(t){n(t,i,{value:{i:"O"+ ++r,w:{}}})},d=function(t,e){if(!s(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!c(t,i)){if(!o(t))return"F";if(!e)return"E";l(t)}return t[i].i},h=function(t,e){if(!c(t,i)){if(!o(t))return!0;if(!e)return!1;l(t)}return t[i].w},g=function(t){return u&&v.NEED&&o(t)&&!c(t,i)&&l(t),t},v=t.exports={KEY:i,NEED:!1,fastKey:d,getWeak:h,onFreeze:g}},"8b97":function(t,e,a){var i=a("d3f4"),s=a("cb7c"),c=function(t,e){if(s(t),!i(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,i){try{i=a("9b43")(Function.call,a("11e9").f(Object.prototype,"__proto__").set,2),i(t,[]),e=!(t instanceof Array)}catch(s){e=!0}return function(t,a){return c(t,a),e?t.__proto__=a:i(t,a),t}}({},!1):void 0),check:c}},ac6a:function(t,e,a){for(var i=a("cadf"),s=a("0d58"),c=a("2aba"),n=a("7726"),r=a("32e9"),o=a("84f2"),u=a("2b4c"),l=u("iterator"),d=u("toStringTag"),h=o.Array,g={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},v=s(g),f=0;f<v.length;f++){var p,y=v[f],m=g[y],R=n[y],_=R&&R.prototype;if(_&&(_[l]||r(_,l,h),_[d]||r(_,d,y),o[y]=h,m))for(p in i)_[p]||c(_,p,i[p],!0)}},ade3:function(t,e,a){"use strict";a.d(e,"a",(function(){return n}));var i=a("53ca");function s(t,e){if("object"!==Object(i["a"])(t)||null===t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var s=a.call(t,e||"default");if("object"!==Object(i["a"])(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function c(t){var e=s(t,"string");return"symbol"===Object(i["a"])(e)?e:String(e)}function n(t,e,a){return e=c(e),e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}},b39a:function(t,e,a){var i=a("d3f4");t.exports=function(t,e){if(!i(t)||t._t!==e)throw TypeError("Incompatible receiver, "+e+" required!");return t}},baa7:function(t,e,a){"use strict";a("f9d2")},c26b:function(t,e,a){"use strict";var i=a("86cc").f,s=a("2aeb"),c=a("dcbc"),n=a("9b43"),r=a("f605"),o=a("4a59"),u=a("01f9"),l=a("d53b"),d=a("7a56"),h=a("9e1e"),g=a("67ab").fastKey,v=a("b39a"),f=h?"_s":"size",p=function(t,e){var a,i=g(e);if("F"!==i)return t._i[i];for(a=t._f;a;a=a.n)if(a.k==e)return a};t.exports={getConstructor:function(t,e,a,u){var l=t((function(t,i){r(t,l,e,"_i"),t._t=e,t._i=s(null),t._f=void 0,t._l=void 0,t[f]=0,void 0!=i&&o(i,a,t[u],t)}));return c(l.prototype,{clear:function(){for(var t=v(this,e),a=t._i,i=t._f;i;i=i.n)i.r=!0,i.p&&(i.p=i.p.n=void 0),delete a[i.i];t._f=t._l=void 0,t[f]=0},delete:function(t){var a=v(this,e),i=p(a,t);if(i){var s=i.n,c=i.p;delete a._i[i.i],i.r=!0,c&&(c.n=s),s&&(s.p=c),a._f==i&&(a._f=s),a._l==i&&(a._l=c),a[f]--}return!!i},forEach:function(t){v(this,e);var a,i=n(t,arguments.length>1?arguments[1]:void 0,3);while(a=a?a.n:this._f){i(a.v,a.k,this);while(a&&a.r)a=a.p}},has:function(t){return!!p(v(this,e),t)}}),h&&i(l.prototype,"size",{get:function(){return v(this,e)[f]}}),l},def:function(t,e,a){var i,s,c=p(t,e);return c?c.v=a:(t._l=c={i:s=g(e,!0),k:e,v:a,p:i=t._l,n:void 0,r:!1},t._f||(t._f=c),i&&(i.n=c),t[f]++,"F"!==s&&(t._i[s]=c)),t},getEntry:p,setStrong:function(t,e,a){u(t,e,(function(t,a){this._t=v(t,e),this._k=a,this._l=void 0}),(function(){var t=this,e=t._k,a=t._l;while(a&&a.r)a=a.p;return t._t&&(t._l=a=a?a.n:t._t._f)?l(0,"keys"==e?a.k:"values"==e?a.v:[a.k,a.v]):(t._t=void 0,l(1))}),a?"entries":"values",!a,!0),d(e)}}},e0b8:function(t,e,a){"use strict";var i=a("7726"),s=a("5ca1"),c=a("2aba"),n=a("dcbc"),r=a("67ab"),o=a("4a59"),u=a("f605"),l=a("d3f4"),d=a("79e5"),h=a("5cc5"),g=a("7f20"),v=a("5dbc");t.exports=function(t,e,a,f,p,y){var m=i[t],R=m,_=p?"set":"add",b=R&&R.prototype,C={},k=function(t){var e=b[t];c(b,t,"delete"==t||"has"==t?function(t){return!(y&&!l(t))&&e.call(this,0===t?0:t)}:"get"==t?function(t){return y&&!l(t)?void 0:e.call(this,0===t?0:t)}:"add"==t?function(t){return e.call(this,0===t?0:t),this}:function(t,a){return e.call(this,0===t?0:t,a),this})};if("function"==typeof R&&(y||b.forEach&&!d((function(){(new R).entries().next()})))){var I=new R,P=I[_](y?{}:-0,1)!=I,A=d((function(){I.has(1)})),L=h((function(t){new R(t)})),T=!y&&d((function(){var t=new R,e=5;while(e--)t[_](e,e);return!t.has(-0)}));L||(R=e((function(e,a){u(e,R,t);var i=v(new m,e,R);return void 0!=a&&o(a,p,i[_],i),i})),R.prototype=b,b.constructor=R),(A||T)&&(k("delete"),k("has"),p&&k("get")),(T||P)&&k(_),y&&b.clear&&delete b.clear}else R=f.getConstructor(e,t,p,_),n(R.prototype,a),r.NEED=!0;return g(R,t),C[t]=R,s(s.G+s.W+s.F*(R!=m),C),y||f.setStrong(R,t,p),R}},f400:function(t,e,a){"use strict";var i=a("c26b"),s=a("b39a"),c="Map";t.exports=a("e0b8")(c,(function(t){return function(){return t(this,arguments.length>0?arguments[0]:void 0)}}),{get:function(t){var e=i.getEntry(s(this,c),t);return e&&e.v},set:function(t,e){return i.def(s(this,c),0===t?0:t,e)}},i,!0)},f9d2:function(t,e,a){}}]);
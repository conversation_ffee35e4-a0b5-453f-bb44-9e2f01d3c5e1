(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d216783"],{c330:function(t,e,a){"use strict";a.r(e);a("b0c0");var i=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:t.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:t.visible},on:{closed:t.closeDialog,"update:visible":function(e){t.visible=e}}},[e("el-form",{attrs:{inline:!0,model:t.dataForm}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"酒店名称",clearable:""},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.getDataList()}}},[t._v("查询")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""}},[e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"酒店名称"}}),e("el-table-column",{attrs:{prop:"star","header-align":"center",align:"center",label:"星级",width:"150px"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-rate",{attrs:{disabled:"",colors:["#99A9BF","#F7BA2A","#FF9900"]},model:{value:a.row.star,callback:function(e){t.$set(a.row,"star",e)},expression:"scope.row.star"}})],1)}}])}),e("el-table-column",{attrs:{prop:"mobile","header-align":"center",align:"center",label:"联系方式"}}),e("el-table-column",{attrs:{prop:"provinceName","header-align":"center",align:"center",label:"省份"}}),e("el-table-column",{attrs:{prop:"cityName","header-align":"center",align:"center",label:"城市"}}),e("el-table-column",{attrs:{prop:"address","header-align":"center",align:"center",label:"详细地址"}}),e("el-table-column",{attrs:{prop:"imageUrl","header-align":"center",align:"center",label:"图片"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[t.isImageUrl(a.row.imageUrl)?e("img",{staticClass:"image-sm",attrs:{src:a.row.imageUrl}}):e("a",{attrs:{href:a.row.imageUrl,target:"_blank"}},[t._v(t._s(a.row.imageUrl))])])}}])}),t.isAuth("hotel:hotelactivity:save")?e("el-table-column",{attrs:{fixed:"right","header-align":"center",align:"center",width:"150",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.isSelect?e("el-button",{attrs:{type:"text",size:"small",disabled:""}},[t._v("已选择")]):e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.select(a.row)}}},[t._v("选择")])]}}],null,!1,565229855)}):t._e()],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("关闭页面")])],1)],1)},n=[],l=(a("ac1f"),a("00b4"),{data:function(){return{visible:!1,dataForm:{name:"",appid:"",activityId:""},dataList:[],pageIndex:1,pageSize:10,totalPage:0,dataListLoading:!1}},methods:{init:function(t){this.dataForm.activityId=t,this.visible=!0,this.getDataList()},getDataList:function(){var t=this;this.dataListLoading=!0,this.$http({url:this.$http.adornUrl("/hotel/hotelactivity/selectList"),method:"get",params:this.$http.adornParams({page:this.pageIndex,limit:this.pageSize,activityId:this.dataForm.activityId,appid:this.$cookie.get("appid"),name:this.dataForm.name})}).then((function(e){var a=e.data;a&&200===a.code?(t.dataList=a.page.list,t.totalPage=a.page.totalCount):(t.dataList=[],t.totalPage=0),t.dataListLoading=!1}))},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},select:function(t){var e=this;this.$confirm("确定选择酒店?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:e.$http.adornUrl("/hotel/hotelactivity/save"),method:"post",data:e.$http.adornData({activityId:e.dataForm.activityId,hotelId:t.id,address:t.address,mobile:t.mobile,status:0,orderBy:0})}).then((function(t){var a=t.data;a&&200===a.code?(e.$message({message:"操作成功",type:"success",duration:1500}),e.getDataList()):e.$message.error(a.msg)}))}))},closeDialog:function(){this.$emit("refreshDataList")},isImageUrl:function(t){return t&&/.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(t)}}}),r=l,s=a("2877"),o=Object(s["a"])(r,i,n,!1,null,null,null);e["default"]=o.exports}}]);
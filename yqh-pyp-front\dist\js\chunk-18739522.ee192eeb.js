(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-18739522"],{"11e9":function(t,e,r){var n=r("52a7"),o=r("4630"),a=r("6821"),c=r("6a99"),i=r("69a8"),s=r("c69a"),f=Object.getOwnPropertyDescriptor;e.f=r("9e1e")?f:function(t,e){if(t=a(t),e=c(e,!0),s)try{return f(t,e)}catch(r){}if(i(t,e))return o(!n.f.call(t,e),t[e])}},"347c":function(t,e,r){},"5dbc":function(t,e,r){var n=r("d3f4"),o=r("8b97").set;t.exports=function(t,e,r){var a,c=e.constructor;return c!==r&&"function"==typeof c&&(a=c.prototype)!==r.prototype&&n(a)&&o&&o(t,a),t}},"67b2":function(t,e,r){"use strict";r("347c")},"8b97":function(t,e,r){var n=r("d3f4"),o=r("cb7c"),a=function(t,e){if(o(t),!n(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,n){try{n=r("9b43")(Function.call,r("11e9").f(Object.prototype,"__proto__").set,2),n(t,[]),e=!(t instanceof Array)}catch(o){e=!0}return function(t,r){return a(t,r),e?t.__proto__=r:n(t,r),t}}({},!1):void 0),check:a}},9093:function(t,e,r){var n=r("ce10"),o=r("e11e").concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return n(t,o)}},a33f:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"list-container"},[e("ul",t._l(t.items,(function(r,n){return e("li",{key:n,class:{selected:t.value===n},on:{click:function(e){return t.selectItem(n)}}},[e("div",{staticClass:"item-content"},[e("span",{staticClass:"title"},[t._v(t._s(r.filename))]),e("span",{staticClass:"duration"},[t._v(t._s(t._f("formatDuration")(r.duration)))])])])})),0)])},o=[],a=(r("c5f6"),{name:"ListPage",props:{items:{type:Array,required:!0},value:{type:Number,default:0}},filters:{formatDuration:function(t){var e=Math.floor(t/3600),r=Math.floor(t%3600/60),n=t%60,o=e>0?e+"小时 ":"",a=r>0?r+"分钟 ":"",c=n>0?n+"秒":"";return o+a+c||"0秒"}},methods:{selectItem:function(t){this.value!==t&&(this.$emit("input",t),this.$emit("item-selected",t))}}}),c=a,i=(r("67b2"),r("2877")),s=Object(i["a"])(c,n,o,!1,null,"1c1766a4",null);e["default"]=s.exports},aa77:function(t,e,r){var n=r("5ca1"),o=r("be13"),a=r("79e5"),c=r("fdef"),i="["+c+"]",s="​",f=RegExp("^"+i+i+"*"),u=RegExp(i+i+"*$"),p=function(t,e,r){var o={},i=a((function(){return!!c[t]()||s[t]()!=s})),f=o[t]=i?e(l):c[t];r&&(o[r]=f),n(n.P+n.F*i,"String",o)},l=p.trim=function(t,e){return t=String(o(t)),1&e&&(t=t.replace(f,"")),2&e&&(t=t.replace(u,"")),t};t.exports=p},c5f6:function(t,e,r){"use strict";var n=r("7726"),o=r("69a8"),a=r("2d95"),c=r("5dbc"),i=r("6a99"),s=r("79e5"),f=r("9093").f,u=r("11e9").f,p=r("86cc").f,l=r("aa77").trim,_="Number",v=n[_],d=v,h=v.prototype,b=a(r("2aeb")(h))==_,y="trim"in String.prototype,I=function(t){var e=i(t,!1);if("string"==typeof e&&e.length>2){e=y?e.trim():l(e,3);var r,n,o,a=e.charCodeAt(0);if(43===a||45===a){if(r=e.charCodeAt(2),88===r||120===r)return NaN}else if(48===a){switch(e.charCodeAt(1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+e}for(var c,s=e.slice(2),f=0,u=s.length;f<u;f++)if(c=s.charCodeAt(f),c<48||c>o)return NaN;return parseInt(s,n)}}return+e};if(!v(" 0o1")||!v("0b1")||v("+0x1")){v=function(t){var e=arguments.length<1?0:t,r=this;return r instanceof v&&(b?s((function(){h.valueOf.call(r)})):a(r)!=_)?c(new d(I(e)),r,v):I(e)};for(var N,m=r("9e1e")?f(d):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),g=0;m.length>g;g++)o(d,N=m[g])&&!o(v,N)&&p(v,N,u(d,N));v.prototype=h,h.constructor=v,r("2aba")(n,_,v)}},fdef:function(t,e){t.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"}}]);
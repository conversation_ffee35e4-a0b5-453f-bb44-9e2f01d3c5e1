(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6bef0301","chunk-37a545c8"],{"1b69":function(t,e,i){"use strict";i.r(e);i("7f7f");var n,o=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{background:"white","margin-bottom":"20px"}},[e("van-row",{attrs:{gutter:"20"}},[e("van-col",{attrs:{span:"16"}},[e("van-swipe",{staticStyle:{width:"100%"},attrs:{autoplay:3e3}},t._l(t.activityInfo.appFileList,(function(t,i){return e("van-swipe-item",{key:i},[e("van-image",{attrs:{width:"100%",src:t.url}})],1)})),1)],1),e("van-col",{attrs:{span:"8"}},[e("div",{staticStyle:{"margin-top":"20px"}},[e("van-cell",{attrs:{title:"会议名称",value:t.activityInfo.name}}),t.activityInfo.address?e("van-cell",{attrs:{title:"会议地点",value:t.activityInfo.address}}):t._e(),e("van-cell",{attrs:{title:"会议开始时间",value:t.activityInfo.startTime}}),e("van-cell",{attrs:{title:"会议结束时间",value:t.activityInfo.endTime}}),e("van-cell",{attrs:{title:"登录信息"},scopedSlots:t._u([{key:"default",fn:function(){return[t.userInfo?e("van-button",{attrs:{type:"primary",size:"small",round:""}},[t._v(t._s(t.userInfo.username)+"("+t._s(t.userInfo.mobile)+")")]):e("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.showLogin}},[t._v("未登录(点击登录)")])]},proxy:!0}])}),"1736999159118508033"!=t.activityId?e("van-cell",{attrs:{title:"报名状态"},scopedSlots:t._u([{key:"default",fn:function(){return[1==t.isPay?e("van-button",{attrs:{type:"primary",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名")]):2==t.isPay?e("van-button",{attrs:{type:"warning",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名未交费")]):e("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.turnApply}},[t._v("未报名")])]},proxy:!0}],null,!1,567203517)}):t._e()],1)])],1),e("van-tabs",{staticClass:"nav",on:{click:t.onClick},model:{value:t.cmsId,callback:function(e){t.cmsId=e},expression:"cmsId"}},t._l(t.cmsList,(function(t){return e("van-tab",{key:t.id,attrs:{title:t.title,name:t.id}})})),1),e("pclogin")],1)},a=[],s=i("ade3"),c=(i("a481"),i("6762"),i("2fdb"),i("cacf")),r=i("7dcb"),u=function(){var t=this,e=t._self._c;return e("van-dialog",{attrs:{title:"登录操作",width:"400px"},on:{confirm:t.login},model:{value:t.showPcLogin,callback:function(e){t.showPcLogin=e},expression:"showPcLogin"}},[e("div",{staticClass:"text-center padding"},[e("van-cell-group",{attrs:{inset:""}},[e("van-field",{attrs:{name:"手机号",label:"手机号",required:"",placeholder:"手机号",rules:[{required:!0,message:"请填写手机号"}]},model:{value:t.mobile,callback:function(e){t.mobile=e},expression:"mobile"}}),"1736999159118508033"!=t.activityId?e("van-field",{attrs:{center:"",clearable:"",maxlength:"6",label:"短信验证码",required:"",placeholder:"请输入短信验证码"},scopedSlots:t._u([{key:"button",fn:function(){return[e("van-button",{attrs:{size:"small",type:"primary",disabled:t.waiting},on:{click:function(e){return t.doSendSmsCode()}}},[t.waiting?e("span",[t._v(t._s(t.waitingTime)+"秒后重发")]):e("span",{staticStyle:{"font-size":"13px"}},[t._v("获取验证码")])])]},proxy:!0}],null,!1,850170227),model:{value:t.code,callback:function(e){t.code=e},expression:"code"}}):t._e()],1)],1)])},l=[],d={name:"pclogin",computed:{showPcLogin:{get:function(){return this.$store.state.user.showPcLogin},set:function(t){this.$store.commit("user/changePcLogin",t)}}},data:function(){return{activityId:void 0,waiting:!1,waitingTime:60,mobile:"",code:""}},mounted:function(){this.activityId=this.$route.query.id},methods:{login:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?this.code||0x181b0f883e693000==this.activityId?/^\d{6}$/.test(this.code)||0x181b0f883e693000==this.activityId?void this.$fly.post("/pyp/web/user/pcLogin",{mobile:this.mobile,code:0x181b0f883e693000==this.activityId?"121212":this.code}).then((function(e){e&&200===e.code?(vant.Toast("登录成功"),t.$store.commit("user/update",e.userInfo),t.showPcLogin=!1,"applyIndex"!=t.$route.name&&"livesDetail"!=t.$route.name||location.reload()):vant.Toast(e.msg)})):(vant.Toast("验证码错误"),!1):(vant.Toast("请输入验证码"),!1):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},doSendSmsCode:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.mobile,activityId:this.activityId}).then((function(e){e&&200===e.code?(t.countdown(),vant.Toast("发送验证码成功")):vant.Toast(e.msg)})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var t=this;this.waiting=!0;var e=window.setInterval((function(){t.waitingTime--,t.waitingTime<0&&(window.clearInterval(e),t.waitingTime=60,t.waiting=!1)}),1e3)}}},v=d,f=i("2877"),m=Object(f["a"])(v,u,l,!1,null,null,null),h=m.exports,p={components:{pclogin:h},data:function(){return{activityId:void 0,bannerIndex:0,cmsId:void 0,cmsList:[],cmsInfo:{},activityInfo:{}}},computed:{userInfo:{get:function(){return this.$store.state.user.userInfo},set:function(t){this.$store.commit("user/update",t)}},isPay:{get:function(){return this.$store.state.apply.isPay},set:function(t){this.$store.commit("apply/update",t)}}},mounted:function(){var t=(new Date).getTime();localStorage.setItem("logAddTime",t+36e5),this.openid=this.$cookie.get("openid"),this.activityId=this.$route.query.id,this.cmsId=sessionStorage.getItem("cmsId")||this.$route.query.cmsId,this.checkLogin(),this.getActivityInfo(),this.activityLogCount()},methods:(n={showLogin:function(){this.$store.commit("user/changePcLogin",!0)},checkLogin:function(){var t=this;this.$fly.get("/pyp/web/user/checkLogin").then((function(e){t.userInfo=e.result,t.userInfo?t.checkApply():t.isPay=0}))},bannerIndexChange:function(t){this.bannerIndex=t},checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(e){200==e.code?(t.isPay=e.isPay,1==t.isPay||2==t.isPay&&vant.Dialog.alert({title:"提示",message:"您有一笔注册费待支付，点击跳转"}).then((function(){t.$router.push({name:"applySuccess",query:{orderId:e.result,id:t.activityId}})}))):vant.Toast(e.msg)}))},changeShowType:function(t){this.$emit("changeShowType",t)},turnApply:function(){var t=this.cmsList.filter((function(t){return t.model&&t.model.includes("applyIndex")}));if(t){var e=t[0];sessionStorage.setItem("cmsId",e.id);var i=e.model.replace("${activityId}",e.activityId);this.$router.push(JSON.parse(i))}else vant.Toast("暂未开启报名")},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(e){if(t.loading=!1,200==e.code){t.activityInfo=e.activity,document.title=t.activityInfo.name;var i=t.activityInfo.startTime,n=new Date(i.replace(/-/g,"/")),o=new Date,a=n.getTime()-o.getTime();t.dateCompare=a>0?a:0,t.getCmsList()}}))},activityLogCount:function(){var t=localStorage.getItem("logAddTime"),e=(new Date).getTime();e>t&&this.$fly.post("/pyp/activity/activityviewlog/count",{activityId:this.activityId,device:r["a"].getVersion()}).then((function(t){}))},getCmsList:function(){var t=this;this.$fly.get("/pyp/cms/cms/findByActivityId/".concat(this.activityId)).then((function(e){t.loading=!1,200==e.code?(t.cmsList=e.result,t.cmsId?t.changeShowType(t.cmsId):t.changeShowType(t.cmsList[0].id)):(vant.Toast(e.msg),t.cmsList=[])}))},onClick:function(t){if(console.log(t),sessionStorage.setItem("cmsId",t),this.cmsInfo=this.cmsList.filter((function(e){return e.id==t}))[0],this.cmsInfo.url&&Object(c["d"])(this.cmsInfo.url))location.href=this.cmsInfo.url;else if(this.cmsInfo.model&&this.isJSON(this.cmsInfo.model)){var e=this.cmsInfo.model.replace("${activityId}",this.cmsInfo.activityId);this.$router.push(JSON.parse(e))}else"cmsIndex"==this.$route.name?this.changeShowType(t):this.$router.push({name:"cmsIndex",query:{id:this.activityId,cmsId:t}})}},Object(s["a"])(n,"bannerIndexChange",(function(t){this.bannerIndex=t})),Object(s["a"])(n,"isJSON",(function(t){try{return JSON.parse(t),!0}catch(e){return!1}})),n)},y=p,g=(i("dd7a"),Object(f["a"])(y,o,a,!1,null,"7bd3d808",null));e["default"]=g.exports},"479f":function(t,e,i){},"7dcb":function(t,e,i){"use strict";i("a481"),i("4917");e["a"]={getVersion:function(){var t=navigator.userAgent||window.navigator.userAgent,e=/HUAWEI|HONOR/gi,i=/[^;]+(?= Build)/gi,n=/CPU iPhone OS \d[_\d]*/gi,o=/CPU OS \d[_\d]*/gi,a=/Windows NT \d[\.\d]*/gi,s=/Linux x\d[_\d]*/gi,c=/Mac OS X \d[_|\.\d]*/gi;return/Android/gi.test(t)?e.test(t)?t.match(e)[0]+t.match(i)[0]:t.match(i)[0]:/iPhone/gi.test(t)?t.match(n)[0].replace(/CPU iPhone OS/gi,"iPhone").replace(/_/g,"."):/iPad/gi.test(t)?t.match(o)[0].replace(/CPU OS/gi,"iPad").replace(/_/g,"."):/Windows/gi.test(t)?"Windows "+(Math.min(parseInt(t.match(a)[0].replace(/Windows NT /,""))+1,10)<7?"XP":Math.min(parseInt(t.match(a)[0].replace(/Windows NT /,""))+1,10)):/Linux/gi.test(t)?t.match(s)[0]:/Macintosh/gi.test(t)?t.match(c)[0].replace(/_/g,"."):"unknown"}}},a1e0:function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{class:t.isMobilePhone?"page":"page pc-container"},[t.isMobilePhone?t._e():e("pcheader"),e("van-card",{staticStyle:{background:"white"},attrs:{thumb:t.userInfo.avatar?t.userInfo.avatar:"photo-o"}},[e("div",{staticStyle:{"font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.userInfo.contact))]),e("div",{staticStyle:{"padding-top":"10px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[t._v("\n      "+t._s(t.userInfo.mobile)+"\n    ")]),e("div",{attrs:{slot:"tag"},slot:"tag"},[1==t.userInfo.status?e("van-tag",{attrs:{type:"primary",mark:""}},[t._v("已报名")]):e("van-tag",{attrs:{type:"danger",mark:""}},[t._v("未报名")])],1),e("div",{attrs:{slot:"price"},slot:"price"},[e("van-tag",{attrs:{plain:"",size:"medium",type:"danger"}},[t._v(t._s(t.userInfo.activityName))])],1)]),e("div",{staticClass:"footer-box"},[e("div",{staticClass:"item",on:{click:function(e){return t.$router.push({name:"account",query:{activityId:t.activityId}})}}},[t._v("我的账户")]),1==t.userInfo.status?e("div",{staticClass:"item",on:{click:function(e){return t.$router.push({name:"meApplyInfo",query:{id:t.activityId}})}}},[t._v("报名信息")]):e("div",{staticClass:"item",on:{click:function(e){return t.$router.push({name:"applyIndex",query:{id:t.activityId}})}}},[t._v("参会报名")]),1==t.userInfo.status?e("div",{staticClass:"item",on:{click:function(e){return t.$router.push({name:"applyQrCode",query:{id:t.activityId}})}}},[t._v("报名二维码")]):t._e(),e("div",{staticClass:"item",on:{click:function(e){return t.$router.push({name:"meApplyList",query:{id:t.activityId}})}}},[t._v("参会记录")]),e("div",{staticClass:"item",on:{click:function(e){return t.$router.push({name:"meHotelList",query:{id:t.activityId}})}}},[t._v("酒店订单")]),t.userInfo&&1==t.userInfo.roleId?e("div",{staticClass:"item",on:{click:t.showScan}},[t._v("扫码签到")]):t._e()])],1)},o=[],a=i("cacf"),s=i("1b69"),c=i("3e34"),r={components:{pcheader:s["default"]},data:function(){return{isMobilePhone:Object(a["c"])(),activityId:void 0,userInfo:{}}},mounted:function(){document.title="个人中心",this.$wxShare(this.$cookie.get("accountName"),this.$cookie.get("logo"),this.$cookie.get("slog")),this.activityId=this.$route.query.id,this.checkMobileBinding()},methods:{checkMobileBinding:function(){var t=this;console.log("开始检查用户手机号绑定状态"),Object(c["checkAndRedirectToBindMobile"])(window.location.href).then((function(e){e&&(console.log("用户已绑定手机号，继续加载页面数据"),t.getUserActivityInfo())})).catch((function(e){console.error("检查手机号绑定状态失败:",e),t.getUserActivityInfo()}))},getUserActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activityuser/meMine",{activityId:this.activityId}).then((function(e){200==e.code?t.userInfo=e.result:Object(c["handleMobileError"])(e,window.location.href)||vant.Toast(e.msg)}))},showScan:function(){var t=this;wx.scanQRCode({needResult:1,scanType:["qrCode","barCode"],success:function(e){var i=e.resultStr;console.log(i),t.$fly.get("/pyp/web/activity/activityuserapplyorder/getByActivityUserIdNotCancel",{activityUserId:i}).then((function(e){200==e.code?0!=e.signType?vant.Dialog.confirm({title:"提示",message:"“"+e.username+"”已签到",confirmButtonText:"确定",showCancelButton:!1}).then((function(){})).catch((function(){})):1==e.result.status?vant.Dialog.confirm({title:"提示",message:"确认为 “"+e.username+"”签到？",confirmButtonText:"确定",cancelButtonText:"取消"}).then((function(){t.$fly.get("/pyp/web/activityusersign/sign",{activityUserId:i,type:1}).then((function(t){200==t.code?vant.Dialog.confirm({title:"提示",message:"“"+e.username+"”签到成功",confirmButtonText:"确定",showCancelButton:!1}).then((function(){})).catch((function(){})):vant.Dialog.confirm({title:"失败",message:t.msg,confirmButtonText:"确定",showCancelButton:!1}).then((function(){})).catch((function(){}))}))})).catch((function(){})):vant.Dialog.confirm({title:"提示",message:"“"+e.username+"“还未报名",confirmButtonText:"确定",showCancelButton:!1}).then((function(){})).catch((function(){})):vant.Dialog.confirm({title:"失败",message:e.msg,confirmButtonText:"确定",showCancelButton:!1}).then((function(){})).catch((function(){}))}))}})}}},u=r,l=(i("c564"),i("2877")),d=Object(l["a"])(u,n,o,!1,null,"9f99f2d4",null);e["default"]=d.exports},ade3:function(t,e,i){"use strict";i.d(e,"a",(function(){return s}));var n=i("53ca");function o(t,e){if("object"!==Object(n["a"])(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var o=i.call(t,e||"default");if("object"!==Object(n["a"])(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function a(t){var e=o(t,"string");return"symbol"===Object(n["a"])(e)?e:String(e)}function s(t,e,i){return e=a(e),e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}},c564:function(t,e,i){"use strict";i("479f")},cad8:function(t,e,i){},dd7a:function(t,e,i){"use strict";i("cad8")}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d22c4f3"],{f34d:function(e,t,a){"use strict";a.r(t);a("b0c0");var i=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"日程名称",prop:"name"}},[t("el-input",{attrs:{placeholder:"日程名称"},model:{value:e.dataForm.name,callback:function(t){e.$set(e.dataForm,"name",t)},expression:"dataForm.name"}})],1),t("el-form-item",{attrs:{label:"会议日期",prop:"times"}},[t("el-date-picker",{staticStyle:{windth:"100%"},attrs:{"picker-options":e.pickerOptions,"default-value":e.activityInfo.startTime,type:"datetimerange","value-format":"yyyy/MM/dd HH:mm:ss","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:e.dateChange},model:{value:e.dataForm.times,callback:function(t){e.$set(e.dataForm,"times",t)},expression:"dataForm.times"}})],1),t("el-form-item",{attrs:{label:"会议主题",prop:"placeActivityTopicId"}},[t("el-select",{attrs:{placeholder:"会议主题",filterable:""},model:{value:e.dataForm.placeActivityTopicId,callback:function(t){e.$set(e.dataForm,"placeActivityTopicId",t)},expression:"dataForm.placeActivityTopicId"}},e._l(e.placeTopicList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"讲者",prop:"scheduleGuestIds"}},[t("el-select",{attrs:{multiple:"",placeholder:"讲者",filterable:""},model:{value:e.dataForm.scheduleGuestIds,callback:function(t){e.$set(e.dataForm,"scheduleGuestIds",t)},expression:"dataForm.scheduleGuestIds"}},e._l(e.guestList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"主持",prop:"scheduleSpeakerIds"}},[t("el-select",{attrs:{multiple:"",placeholder:"主持",filterable:""},model:{value:e.dataForm.scheduleSpeakerIds,callback:function(t){e.$set(e.dataForm,"scheduleSpeakerIds",t)},expression:"dataForm.scheduleSpeakerIds"}},e._l(e.guestList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"讨论",prop:"scheduleDiscussIds"}},[t("el-select",{attrs:{multiple:"",placeholder:"讨论",filterable:""},model:{value:e.dataForm.scheduleDiscussIds,callback:function(t){e.$set(e.dataForm,"scheduleDiscussIds",t)},expression:"dataForm.scheduleDiscussIds"}},e._l(e.guestList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"讲者别名",prop:"aliasGuestName"}},[t("el-input",{attrs:{placeholder:"讲者别名"},model:{value:e.dataForm.aliasGuestName,callback:function(t){e.$set(e.dataForm,"aliasGuestName",t)},expression:"dataForm.aliasGuestName"}})],1),t("el-form-item",{attrs:{label:"主持别名",prop:"aliasSpeakerName"}},[t("el-input",{attrs:{placeholder:"主持别名"},model:{value:e.dataForm.aliasSpeakerName,callback:function(t){e.$set(e.dataForm,"aliasSpeakerName",t)},expression:"dataForm.aliasSpeakerName"}})],1),t("el-form-item",{attrs:{label:"讨论别名",prop:"aliasDiscussName"}},[t("el-input",{attrs:{placeholder:"讨论别名"},model:{value:e.dataForm.aliasDiscussName,callback:function(t){e.$set(e.dataForm,"aliasDiscussName",t)},expression:"dataForm.aliasDiscussName"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},s=[],l={data:function(){var e=this;return{visible:!1,placeTopicList:[],guestList:[],activityInfo:{},dataForm:{id:0,activityId:"",orderBy:0,placeActivityTopicId:"",name:"",startTime:"",endTime:"",aliasGuestName:"",aliasSpeakerName:"",aliasDiscussName:"",scheduleGuestIds:[],scheduleSpeakerIds:[],scheduleDiscussIds:[],times:[],videoUrl:""},dataRule:{activityId:[{required:!0,message:"会议id不能为空",trigger:"blur"}],times:[{required:!0,message:"会议时间不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序，数值越小越靠前不能为空",trigger:"blur"}],placeActivityTopicId:[{required:!0,message:"主题ID不能为空",trigger:"blur"}],name:[{required:!0,message:"日程名称不能为空",trigger:"blur"}],startTime:[{required:!0,message:"开始时间不能为空",trigger:"blur"}],endTime:[{required:!0,message:"结束时间不能为空",trigger:"blur"}]},pickerOptions:{disabledDate:function(t){return e.dealDisabledDate(t)}}}},methods:{init:function(e,t,a){var i=this;this.dataForm.activityId=e,this.dataForm.placeActivityTopicId=t||void 0,this.dataForm.id=a||0,this.visible=!0,this.$nextTick((function(){i.$refs["dataForm"].resetFields(),i.dataForm.id&&i.$http({url:i.$http.adornUrl("/place/placeactivitytopicschedule/info/".concat(i.dataForm.id)),method:"get",params:i.$http.adornParams()}).then((function(e){var t=e.data;t&&200===t.code&&(i.$set(i.dataForm,"times",[t.placeActivityTopicSchedule.startTime,t.placeActivityTopicSchedule.endTime]),i.dataForm.activityId=t.placeActivityTopicSchedule.activityId,i.dataForm.placeId=t.placeActivityTopicSchedule.placeId,i.dataForm.orderBy=t.placeActivityTopicSchedule.orderBy,i.dataForm.placeActivityTopicId=t.placeActivityTopicSchedule.placeActivityTopicId,i.dataForm.name=t.placeActivityTopicSchedule.name,i.dataForm.scheduleGuestIds=t.placeActivityTopicSchedule.scheduleGuestIds,i.dataForm.scheduleSpeakerIds=t.placeActivityTopicSchedule.scheduleSpeakerIds,i.dataForm.scheduleDiscussIds=t.placeActivityTopicSchedule.scheduleDiscussIds,i.dataForm.startTime=t.placeActivityTopicSchedule.startTime,i.dataForm.endTime=t.placeActivityTopicSchedule.endTime,i.dataForm.videoUrl=t.placeActivityTopicSchedule.videoUrl,i.dataForm.aliasGuestName=t.placeActivityTopicSchedule.aliasGuestName,i.dataForm.aliasSpeakerName=t.placeActivityTopicSchedule.aliasSpeakerName,i.dataForm.aliasDiscussName=t.placeActivityTopicSchedule.aliasDiscussName)}))})),this.getPlaceTopic(),this.getGuest(),this.getActivity()},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$http({url:e.$http.adornUrl("/place/placeactivitytopicschedule/".concat(e.dataForm.id?"update":"save")),method:"post",data:e.$http.adornData({id:e.dataForm.id||void 0,activityId:e.dataForm.activityId,placeId:e.dataForm.placeId,orderBy:e.dataForm.orderBy,placeActivityTopicId:e.dataForm.placeActivityTopicId,name:e.dataForm.name,startTime:e.dataForm.startTime,endTime:e.dataForm.endTime,scheduleGuestIds:e.dataForm.scheduleGuestIds,scheduleSpeakerIds:e.dataForm.scheduleSpeakerIds,scheduleDiscussIds:e.dataForm.scheduleDiscussIds,aliasGuestName:e.dataForm.aliasGuestName,aliasSpeakerName:e.dataForm.aliasSpeakerName,aliasDiscussName:e.dataForm.aliasDiscussName,videoUrl:e.dataForm.videoUrl})}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(a.msg)}))}))},getPlace:function(){var e=this;this.$http({url:this.$http.adornUrl("/place/placeactivity/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.placeList=a.result)}))},getPlaceTopic:function(){var e=this;this.$http({url:this.$http.adornUrl("/place/placeactivitytopic/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.placeTopicList=a.result)}))},getGuest:function(){var e=this;this.$http({url:this.$http.adornUrl("/activity/activityguest/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.guestList=a.result)}))},getActivity:function(){var e=this;this.$http({url:this.$http.adornUrl("/activity/activity/info/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.activityInfo=a.activity)}))},dealDisabledDate:function(e){if(null!=this.activityInfo.endTime&&null!=this.activityInfo.startTime)return e.getTime()+864e5<new Date(this.activityInfo.startTime).getTime()||e.getTime()>=new Date(this.activityInfo.endTime).getTime()},dateChange:function(e){this.dataForm.startTime=e[0],this.dataForm.endTime=e[1],console.log(e)}}},r=l,c=a("2877"),d=Object(c["a"])(r,i,s,!1,null,null,null);t["default"]=d.exports}}]);
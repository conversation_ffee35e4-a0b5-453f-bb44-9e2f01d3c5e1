.transparent[data-v-7a4993ad] .van-collapse-item__content{background:transparent}.content[data-v-7a4993ad] img{max-width:100%;height:auto}.van-card__thumb[data-v-7a4993ad] img{-o-object-fit:contain!important;object-fit:contain!important}.van-cell__title[data-v-7a4993ad]{-webkit-box-flex:0;-ms-flex:none;flex:none;-webkit-box-sizing:border-box;box-sizing:border-box;width:6.2em;margin-right:12px;color:#646566;text-align:left;word-wrap:break-word;line-height:33px}.van-cell__value[data-v-7a4993ad]{text-align:left;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.sign[data-v-7a4993ad]{position:fixed;top:0;background:#fff;height:100%;width:100%}.sign .button[data-v-7a4993ad]{width:30%;height:40px;line-height:40px;background:#4485ff;border-radius:20px;text-align:center;color:#fff}.sign-btns[data-v-7a4993ad]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}.sign-btns #clear1[data-v-7a4993ad],.sign-btns #clear[data-v-7a4993ad],.sign-btns #save[data-v-7a4993ad]{display:inline-block;padding:5px 10px;width:76px;height:40px;line-height:40px;border:1px solid #eee;background:#e1e1e1;border-radius:10px;text-align:center;margin:20px auto;cursor:pointer}.flight[data-v-7a4993ad]{margin:10px 13px;background-color:#fff;padding:10px;border-radius:8px}.flight-bottom[data-v-7a4993ad],.flight-info[data-v-7a4993ad]{display:-webkit-box;display:-ms-flexbox;display:flex}.bottom-baggage[data-v-7a4993ad],.flight-baggage[data-v-7a4993ad]{text-align:right}.bottom-exit[data-v-7a4993ad],.bottom-num[data-v-7a4993ad],.flight-exit[data-v-7a4993ad],.flight-num[data-v-7a4993ad]{text-align:center}.flight-baggage[data-v-7a4993ad],.flight-date[data-v-7a4993ad],.flight-exit[data-v-7a4993ad],.flight-num[data-v-7a4993ad]{-webkit-box-flex:1;-ms-flex:1;flex:1;font-size:12px;font-family:Roboto-Regular;font-weight:400;color:#333;line-height:12px}.file-info[data-v-7a4993ad]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;padding:10px;border:1px solid #eaeaea;border-radius:5px;margin-top:10px}.file-text[data-v-7a4993ad]{margin-left:5px;-webkit-box-flex:1;-ms-flex:1;flex:1}.remove-icon[data-v-7a4993ad]{margin-left:10px;cursor:pointer;-webkit-transition:color .3s;transition:color .3s}.remove-icon[data-v-7a4993ad]:hover{color:#ff4d4f}.flight-card[data-v-7a4993ad]{margin:16px;background-color:#fff;padding:16px;border-radius:12px;-webkit-box-shadow:0 2px 12px rgba(0,0,0,.08);box-shadow:0 2px 12px rgba(0,0,0,.08);-webkit-transition:all .3s ease;transition:all .3s ease}.flight-card[data-v-7a4993ad]:hover{-webkit-transform:translateY(-2px);transform:translateY(-2px);-webkit-box-shadow:0 4px 16px rgba(0,0,0,.12);box-shadow:0 4px 16px rgba(0,0,0,.12)}.flight-top[data-v-7a4993ad]{position:relative;border-bottom:1px dashed #9f9f9f;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;padding-bottom:12px;margin-bottom:12px}.flight-status-tags[data-v-7a4993ad],.flight-top-name[data-v-7a4993ad],.flight-top[data-v-7a4993ad]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.flight-top-name[data-v-7a4993ad]{height:33px}.flight-icon[data-v-7a4993ad]{width:29px!important;height:19px;vertical-align:middle}.flight-no[data-v-7a4993ad]{font-size:14px;font-family:SourceHanSansSC-Regular;font-weight:400;color:#000;margin-left:8px}.flight-top-arow[data-v-7a4993ad]{width:5px;height:9px}.flight-middle[data-v-7a4993ad]{height:67px;display:-webkit-box;display:-ms-flexbox;display:flex;padding:8px 0}.flight-end[data-v-7a4993ad],.flight-start[data-v-7a4993ad],.flight-status[data-v-7a4993ad]{-webkit-box-flex:1;-ms-flex:1;flex:1}.flight-end-time[data-v-7a4993ad],.flight-start-time[data-v-7a4993ad]{margin-top:15px;font-size:25px;font-family:Roboto-Regular;font-weight:400;color:#000;line-height:17px}.flight-end-a[data-v-7a4993ad],.flight-end-time[data-v-7a4993ad],.flight-num[data-v-7a4993ad],.flight-top-arow[data-v-7a4993ad]{float:right}.flight-status[data-v-7a4993ad]{margin-top:15px;position:relative}.flight-status-show[data-v-7a4993ad]{width:88px;height:25px;text-align:center;border-radius:16px;font-size:11px;font-family:SourceHanSansSC-Regular;font-weight:400;line-height:25px;position:absolute;top:-5px;left:50%;margin-left:-43.5px;z-index:2}.flight-status-show[data-v-7a4993ad],.plane-type[data-v-7a4993ad]{background-color:#e8f3ff;color:#1989fa}.train-type[data-v-7a4993ad]{background-color:#f0f9eb;color:#67c23a}.flight-end-a[data-v-7a4993ad],.flight-start-a[data-v-7a4993ad]{margin-top:6px;font-size:13px;font-family:SourceHanSansSC-Regular;font-weight:400;color:#424242;line-height:17px}.flight-end[data-v-7a4993ad]{position:relative}.flight-end-a[data-v-7a4993ad]{position:absolute;right:0;top:31px}.flight-status-arow2[data-v-7a4993ad]{position:absolute;width:8px;height:0;margin:0 auto;right:-2px;top:-4px;border:1px solid #666;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.flight-status-arow1[data-v-7a4993ad]{position:absolute;width:115px;height:0;margin:0 auto;margin-top:14px;border:1px solid #666;left:50%;top:8%;margin-left:-57.5px}.empty-trip[data-v-7a4993ad]{margin:40px 0;text-align:center;padding:20px;background-color:#f8f8f8;border-radius:8px}.flight-actions[data-v-7a4993ad]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-ms-flex-pack:end;justify-content:flex-end;margin-top:12px;padding-top:8px;border-top:1px solid #f5f5f5}
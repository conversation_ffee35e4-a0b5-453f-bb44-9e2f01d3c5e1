(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5f412cf3"],{"34ae":function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return i})),a.d(t,"d",(function(){return n}));var r=[{key:0,value:"整间"},{key:1,value:"男床位"},{key:2,value:"女床位"}],o=[{key:0,value:"整间"},{key:1,value:"拼住"},{key:2,value:"拼住"}],i=[{key:0,value:"已取消"},{key:1,value:"已入住"}],n=[{key:0,value:"未开启"},{key:1,value:"已开启"}]},"7de9":function(e,t,a){"use strict";a.d(t,"g",(function(){return r})),a.d(t,"f",(function(){return o})),a.d(t,"e",(function(){return i})),a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return l})),a.d(t,"c",(function(){return s})),a.d(t,"d",(function(){return d}));var r=[{key:0,keyString:"0",value:"否"},{key:1,keyString:"1",value:"是"}],o=[{key:0,value:"微信"},{key:1,value:"支付宝"},{key:2,value:"银行转账"},{key:3,value:"现场支付"}],i=[{key:0,value:"待付款"},{key:1,value:"已付款"},{key:2,value:"已取消"},{key:3,value:"退款中"},{key:4,value:"退款成功"},{key:5,value:"退款失败"}],n=[{key:"animate-flip",value:"快速翻转"},{key:"animate-flipInX",value:"快速翻转-X"},{key:"animate-flipInY",value:"快速翻转-Y"},{key:"animate-rotateIn",value:"旋转进入"},{key:"animate-zoomIn",value:"中心放大"},{key:"animate-backInDown",value:"backInDown"},{key:"animate-backInLeft",value:"backInLeft"},{key:"animate-backInRight",value:"backInRight"},{key:"animate-backInUp",value:"backInUp"},{key:"animate-bounceInDown",value:"bounceInDown"},{key:"animate-bounceInLeft",value:"bounceInLeft"},{key:"animate-bounceInRight",value:"bounceInRight"},{key:"animate-bounceInUp",value:"bounceInUp"},{key:"animate-fadeInDown",value:"fadeInDown"},{key:"animate-fadeInLeft",value:"fadeInLeft"},{key:"animate-fadeInRight",value:"fadeInRight"},{key:"animate-fadeInUp",value:"fadeInUp"},{key:"animate-slideInDown",value:"slideInDown"},{key:"animate-slideInLeft",value:"slideInLeft"},{key:"animate-slideInRight",value:"slideInRight"},{key:"animate-slideInUp",value:"slideInUp"}],l=["linear-gradient(to right, #7C4DFF, #9F7AEA)","linear-gradient(to right, #E91E63, #F06292)","linear-gradient(to right, #673AB7, #9C27B0)","linear-gradient(to right, #3F51B5, #5C6BC0)","linear-gradient(to right, #009688, #4DB6AC)","linear-gradient(to right, #4CAF50, #81CDBE)","linear-gradient(to right, #FFEB3B, #F57F17)","linear-gradient(to right, #FF9800, #F44336)","linear-gradient(to right, #2196F3, #64B5F6)","linear-gradient(to right, #03A9F4, #4FC3E9)","linear-gradient(to right, #0097A7, #00BFA5)","linear-gradient(to right, #4E8C9A, #5FB7B5)","linear-gradient(to right, #F44336, #E91E63)","linear-gradient(to right, #9C27B0, #7E57C2)","linear-gradient(to right, #3F51B5, #4D4D4D)","linear-gradient(to right, #F57F17, #FBC312)","linear-gradient(to right, #4DB6AC, #80DEEA)","linear-gradient(to right, #5C6BC0, #6C71C4)","linear-gradient(to right, #81CDBE, #B2DFDB)","linear-gradient(to right, #F06292, #C2185B)"],s=[{name:"身份证"},{name:"军官证"},{name:"台胞证"},{name:"护照"},{name:"其他"}],d=[{key:0,value:"未完成比对"},{key:1,value:"已完成比对"},{key:2,value:"部分比对"}]},a725:function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.id?"修改":"新增","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"80px"}},[t("el-row",{staticClass:"row"},[t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"选择酒店",prop:"hotelActivityId"}},[t("el-select",{on:{change:e.hotelChange},model:{value:e.dataForm.hotelActivityId,callback:function(t){e.$set(e.dataForm,"hotelActivityId",t)},expression:"dataForm.hotelActivityId"}},e._l(e.hotels,(function(e){return t("el-option",{key:e.id,attrs:{label:e.hotelName,value:e.id}})})),1)],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"选择房型",prop:"hotelActivityRoomId"}},[t("el-select",{on:{change:e.roomChange},model:{value:e.dataForm.hotelActivityRoomId,callback:function(t){e.$set(e.dataForm,"hotelActivityRoomId",t)},expression:"dataForm.hotelActivityRoomId"}},e._l(e.rooms,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),1==e.indexRoom.bedStatus&&e.indexRoom.bedNumber>1?t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"入住类型",prop:"roomType"}},[t("el-select",{on:{change:e.countPrice},model:{value:e.dataForm.roomType,callback:function(t){e.$set(e.dataForm,"roomType",t)},expression:"dataForm.roomType"}},e._l(e.roomType,(function(e){return t("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1)],1):e._e()],1),t("el-row",{staticClass:"row"},[t("el-col",{attrs:{span:16}},[t("el-form-item",{attrs:{label:"酒店时间",prop:"inDate"}},[t("el-date-picker",{attrs:{"picker-options":e.pickerOptions,"value-format":"yyyy/MM/dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:e.dateChange},model:{value:e.times,callback:function(t){e.times=t},expression:"times"}})],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"数量",prop:"number"}},[t("el-input",{attrs:{placeholder:"数量",clearable:""},on:{input:e.countPrice},model:{value:e.dataForm.number,callback:function(t){e.$set(e.dataForm,"number",t)},expression:"dataForm.number"}})],1)],1)],1),t("el-row",{staticClass:"row"},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"联系人",prop:"contact"}},[t("el-input",{attrs:{placeholder:"联系人",clearable:""},model:{value:e.dataForm.contact,callback:function(t){e.$set(e.dataForm,"contact",t)},expression:"dataForm.contact"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"联系方式",prop:"mobile"}},[t("el-input",{attrs:{placeholder:"联系方式",clearable:""},model:{value:e.dataForm.mobile,callback:function(t){e.$set(e.dataForm,"mobile",t)},expression:"dataForm.mobile"}})],1)],1)],1),t("el-row",{staticClass:"row"},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"订单状态",prop:"status"}},[t("el-select",{attrs:{placeholder:"订单状态",filterable:""},model:{value:e.dataForm.status,callback:function(t){e.$set(e.dataForm,"status",t)},expression:"dataForm.status"}},e._l(e.orderStatus,(function(e){return t("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"支付来源",prop:"source"}},[t("el-select",{attrs:{placeholder:"支付来源",filterable:""},model:{value:e.dataForm.source,callback:function(t){e.$set(e.dataForm,"source",t)},expression:"dataForm.source"}},e._l(e.sources,(function(e){return t("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1)],1)],1),t("el-form-item",{attrs:{label:"订单金额",prop:"price"}},[t("el-input",{attrs:{disabled:"",placeholder:"订单总金额"},model:{value:e.dataForm.price,callback:function(t){e.$set(e.dataForm,"price",t)},expression:"dataForm.price"}})],1),t("el-form-item",{attrs:{label:"备注",prop:"remarks"}},[t("el-input",{attrs:{placeholder:"备注"},model:{value:e.dataForm.remarks,callback:function(t){e.$set(e.dataForm,"remarks",t)},expression:"dataForm.remarks"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},o=[],i=a("ade3"),n=(a("4de4"),a("d3b7"),a("0643"),a("2382"),a("7de9")),l=a("34ae"),s={data:function(){var e=this;return{times:[],indexRoom:{},roomType:l["b"],roomTypeFjsd:l["c"],sources:n["f"],orderStatus:n["e"],hotels:[],rooms:[],visible:!1,dataForm:{id:0,activityId:"",status:0,source:0,price:0,remarks:"",hotelId:"",hotelActivityId:"",hotelActivityRoomId:"",contact:"",mobile:"",inDate:"",outDate:"",dayNumber:"",roomType:0,number:1},timeOptionRange:"",pickerOptions:Object(i["a"])({onPick:function(t){var a=t.maxDate,r=t.minDate;r&&!a&&(e.timeOptionRange=r),a&&(e.timeOptionRange=null)},disabledDate:function(t){var a=e.timeOptionRange;if(a)return t.getTime()===a.getTime()}},"disabledDate",(function(t){if(null!=e.indexRoom.inDate&&null!=e.indexRoom.outDate)return t.getTime()<new Date(e.indexRoom.inDate).getTime()||t.getTime()>=new Date(e.indexRoom.outDate).getTime()+864e5})),dataRule:{hotelActivityId:[{required:!0,message:"会议酒店不能为空",trigger:"blur"}],hotelActivityRoomId:[{required:!0,message:"会议酒店房型不能为空",trigger:"blur"}],status:[{required:!0,message:"订单状态不能为空",trigger:"blur"}],contact:[{required:!0,message:"联系人不能为空",trigger:"blur"}],mobile:[{required:!0,message:"联系方式不能为空",trigger:"blur"}],source:[{required:!0,message:"支付来源不能为空",trigger:"blur"}],price:[{required:!0,message:"订单总金额不能为空",trigger:"blur"}],activityUserId:[{required:!0,message:"参会表id不能为空",trigger:"blur"}],payAccount:[{required:!0,message:"付款人账号不能为空",trigger:"blur"}],payTransaction:[{required:!0,message:"付款人支付流水号不能为空",trigger:"blur"}],outDate:[{required:!0,message:"支付时间不能为空",trigger:"blur"}],number:[{required:!0,message:"数量不能为空",trigger:"blur"}],inDate:[{required:!0,message:"支付时间不能为空",trigger:"blur"}]}}},methods:{dateChange:function(e){this.dataForm.inDate=e[0],this.dataForm.outDate=e[1];var t=new Date(e[1]).getTime()/1e3-new Date(e[0]).getTime()/1e3;this.dataForm.dayNumber=parseInt(t/60/60/24),this.countPrice()},countPrice:function(){this.dataForm.hotelActivityRoomId&&this.dataForm.outDate&&this.dataForm.inDate&&null!=this.dataForm.roomType&&""!==this.dataForm.roomType&&(0==this.dataForm.roomType?this.dataForm.price=this.dataForm.dayNumber*this.indexRoom.price*this.dataForm.number:this.dataForm.price=this.dataForm.dayNumber*this.indexRoom.bedPrice*this.dataForm.number)},roomChange:function(e){this.indexRoom=this.rooms.filter((function(t){return t.id==e}))[0]},init:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs["dataForm"].resetFields(),t.times=[],t.indexRoom={},t.dataForm={id:0,activityId:"",status:0,source:0,price:0,remarks:"",hotelId:"",hotelActivityId:"",hotelActivityRoomId:"",contact:"",mobile:"",inDate:"",outDate:"",dayNumber:"",roomType:0,number:1},t.dataForm.activityId=e||0,t.findHotel()}))},findHotel:function(){var e=this;this.$http({url:this.$http.adornUrl("/hotel/hotelactivity/findByActivityId/".concat(this.dataForm.activityId)),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.hotels=a.result,1==e.hotels.length&&(e.dataForm.hotelActivityId=e.hotels[0].id,e.findRoom(e.dataForm.hotelActivityId)))}))},hotelChange:function(e){this.dataForm.hotelActivityRoomId="",this.dataForm.roomType=0,this.findRoom(e)},findRoom:function(e){var t=this;this.$http({url:this.$http.adornUrl("/hotel/hotelactivityroom/findByHotelActivityId/".concat(e)),method:"get",params:this.$http.adornParams()}).then((function(e){var a=e.data;a&&200===a.code&&(t.rooms=a.result,1==t.rooms.length&&(t.indexRoom=t.rooms[0],t.dataForm.hotelActivityRoomId=t.rooms[0].id,t.countPrice()))}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$http({url:e.$http.adornUrl("/hotel/hotelorder/createOrder"),method:"post",data:e.$http.adornData(e.dataForm)}).then((function(t){var a=t.data;a&&200===a.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(a.msg)}))}))}}},d=s,u=a("2877"),m=Object(u["a"])(d,r,o,!1,null,null,null);t["default"]=m.exports}}]);